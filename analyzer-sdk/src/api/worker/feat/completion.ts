import type {TextDocument, Position} from 'vscode-languageserver-textdocument';
import {
  EVENT_PROCESS_COMPLETION_SYNTAX_CHECK,
  type SyntaxCheckResult,
} from '../../../core/feat/completion/post/syntaxCheck';
import {
  type SyntaxFixResult,
  EVENT_PROCESS_COMPLETION_SYNTAX_FIX,
} from "../../../core/feat/completion/post/syntaxFix";
import {Logger} from "../../../utils/logger";
import {
  EVENT_NAME_ERROR,
  EVENT_NAME_TIMEOUT,
  TimeoutError,
} from "../../../utils/error";
import {EVENT_CATEGORY_COMPLETION, reportEvent} from "../../../utils/event";
import {CompletionContextResult} from "../../../core/feat/completion/context/share";
import {MainHandler} from "../../../worker/main";

export async function completionSyntaxCheck(document: TextDocument, position: Position, completionText: string, timeoutMs: number): Promise<SyntaxCheckResult> {
  try {
    return MainHandler.INSTANCE.completionSyntaxCheck({document, position, completionText, timeoutMs});
  } catch (e) {
    if (e instanceof TimeoutError) {
      Logger.warn(`completionSyntaxCheck: execution timeout for '${document.uri}' with ${document.getText().split("\n").length} lines: ${e}`);
      reportEvent(document.languageId, EVENT_CATEGORY_COMPLETION, EVENT_PROCESS_COMPLETION_SYNTAX_CHECK, EVENT_NAME_TIMEOUT).then();
    } else {
      Logger.error(`completionSyntaxCheck: exception for '${document.uri}': ${e}`);
      Logger.error(e.stack);
      reportEvent(document.languageId, EVENT_CATEGORY_COMPLETION, EVENT_PROCESS_COMPLETION_SYNTAX_CHECK, EVENT_NAME_ERROR).then();
    }
    return null;
  }
}

export async function completionSyntaxFix(document: TextDocument, position: Position, completionText: string, timeoutMs: number): Promise<SyntaxFixResult> {
  try {
    return MainHandler.INSTANCE.completionSyntaxFix({document, position, completionText, timeoutMs});
  } catch (e) {
    if (e instanceof TimeoutError) {
      Logger.warn(`completionSyntaxFix: execution timeout for '${document.uri}' with ${document.getText().split("\n").length} lines: ${e}`);
      reportEvent(document.languageId, EVENT_CATEGORY_COMPLETION, EVENT_PROCESS_COMPLETION_SYNTAX_FIX, EVENT_NAME_TIMEOUT).then();
    } else {
      Logger.error(`completionSyntaxFix: exception for '${document.uri}': ${e}`);
      Logger.error(e.stack);
      reportEvent(document.languageId, EVENT_CATEGORY_COMPLETION, EVENT_PROCESS_COMPLETION_SYNTAX_FIX, EVENT_NAME_ERROR).then();
    }
    return null;
  }
}

export async function extractCompletionContext(document: TextDocument, position: Position): Promise<CompletionContextResult[]> {
  try {
    return MainHandler.INSTANCE.extractCompletionContext({document, position});
  } catch (e) {
    Logger.error(`extractCompletionContext: exception for '${document.uri}': ${e}`);
    Logger.error(e.stack);
    return [];
  }
}