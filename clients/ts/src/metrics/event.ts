import { SourceProductType } from "../process/process";
import { Logger } from "./logger";

export enum CKG_API {
  INIT = "init",
  QUERY = "query",
  RETRIEVE_ENTITY = "retrieveEntity",
  RETRIEVE_RELATION = "retrieveRelation",
  RETRIEVE_RELEVANT_SNIPPET = "retrieveRelevantSnippet",
  REFRESH_TOKEN = "refreshToken",
  DELETE_INDEX = "deleteIndex",
  GET_BUILD_STATUS = "getBuildStatus",
  IS_CKG_ENABLED_FOR_NON_WORKSPACE_SCENARIO = "isCKGEnabledForNonWorkspaceScenario",
  INIT_VIRTUAL_PROJECTS = "initVirtualProjects",
  SETUP = "setUp",
  RERANK_SNIPPET = "rerankSnippet",
  GET_DOCUMENTS_INDEX_STATUS = "getDocumentsIndexStatus",
  IS_VERSION_MATCHED = "isVersionMatched",
}
// 上报需要和插件的变量命名相同
export interface ClientAPIEventData {
  ckg_client_api: CKG_API;
  api_result_code: number;
  resolver_name?: string | undefined;
  source_product: SourceProductType;
  os_type: string;
}

export interface CKGStartEventData {
  ide_version: string;
  ckg_version: string;
  extension_version: string;
  ai_backend_host: string;
  start_status: boolean;
  create_client_failed: boolean;
  os_type: string;
  region: string;
  source_product: SourceProductType;
  error_code?: number;
  error_msg?: string;
  create_process_cost: number;
  create_client_cost: number;
}


export class CKGEventManager {
  private _clientEventConsumer: ((data: ClientAPIEventData) => void) | null = null;
  private _ckgStartEventConsumer: ((data: CKGStartEventData) => void) | null = null;

  public setClientEventConsumer(clientConsumer: (data: ClientAPIEventData) => void): void {
    this._clientEventConsumer = clientConsumer;
    Logger.info("setClientEventConsumer: client event consumer");
  }

  public setCkgStartEventConsumer(startConsumer: (data: CKGStartEventData) => void): void {
    this._ckgStartEventConsumer = startConsumer;
    Logger.info(" setCkgStartEventConsumer: ckg start event consumer set");
  }

  private _isClientAPIEventData(data: any): data is ClientAPIEventData {
    return 'ckg_client_api' in data && 'api_result_code' in data;
  }

  private _isCkgStartEventData(data: any): data is CKGStartEventData {
    return 'start_status' in data;
  }

  public async reportEvent(eventData: ClientAPIEventData | CKGStartEventData): Promise<void> {
    if (this._isClientAPIEventData(eventData)) {
      if (this._clientEventConsumer) {
        this._clientEventConsumer(eventData);
        Logger.info(" reportEvent: ckg client api, event data: " + JSON.stringify(eventData));
      } else {
        Logger.info(" reportEvent: client event consumer is not set");
      }
    } else if (this._isCkgStartEventData(eventData)) {
      if (this._ckgStartEventConsumer) {
        this._ckgStartEventConsumer(eventData);
        Logger.info(" reportEvent: ckg start, event data: " + JSON.stringify(eventData));
      } else {
        Logger.info(" reportEvent: ckg start event consumer is not set");
      }
    } else {
      Logger.warn(" reportEvent: Unknown event type");
    }
  }
}
