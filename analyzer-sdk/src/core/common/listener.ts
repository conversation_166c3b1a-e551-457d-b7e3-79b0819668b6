import {TextDocument} from "vscode-languageserver-textdocument";
import {EditorSelectionInfo, FILE_URI_PREFIX, FileEditInfo} from "../../utils/common";
import {completeUpdateCachedTree, getActiveFileCachedTreeAndDocument, incrementalUpdateCachedTree} from "../cache/astCache";
import {partialOndemandTypeAnalysis} from "../async/definition/partial";
import {debugDefinitionAsync, debugDefinitionSync, debugUnitTestContext} from "../debug";
import {Tree} from "web-tree-sitter";
import {completeOndemandTypeAnalysis} from "../async/definition/complete";
import {incrementalOndemandTypeAnalysis} from "../async/definition/incremental";
import {FeatureGateManager, FeatureName} from "./config";

export async function doFileActiveListener(document: TextDocument): Promise<void> {
  if (!document.uri.startsWith(FILE_URI_PREFIX)) {
    return;
  }

  if (FeatureGateManager.INSTANCE.isFeatureOpen(FeatureName.ENABLE_AST_CACHE_ANALYSIS)) {
    const promiseAfterFileActive = completeUpdateCachedTree(document).then(tree => afterFileActiveTreeUpdated(document, tree));

    await Promise.all([promiseAfterFileActive]);
  }
}

export async function afterFileActiveTreeUpdated(document: TextDocument, tree: Tree): Promise<void> {
  const promises = [];
  if (FeatureGateManager.INSTANCE.isFeatureOpen(FeatureName.ENABLE_DEFINITION_ANALYSIS)) {
    promises.push(completeOndemandTypeAnalysis(document, tree));
  }

  await Promise.all(promises);
}

export async function doFileEditListener(editInfo: FileEditInfo): Promise<void> {
  if (!editInfo.document.uri.startsWith(FILE_URI_PREFIX)) {
    return;
  }

  if (editInfo.contentChanges.length === 0) {
    return;
  }

  if (editInfo.contentChanges.length === 1 && editInfo.contentChanges[0].removeLength === 0 && editInfo.contentChanges[0].text.length === 0) {
    return;
  }

  if (FeatureGateManager.INSTANCE.isFeatureOpen(FeatureName.ENABLE_AST_CACHE_ANALYSIS)) {
    const documentBeforeEdit = getActiveFileCachedTreeAndDocument(editInfo.document)?.document;
    const promiseIncrementalUpdateCachedTree = incrementalUpdateCachedTree(editInfo).then(tree => afterFileEditTreeUpdated(editInfo, tree, documentBeforeEdit));

    await Promise.all([promiseIncrementalUpdateCachedTree]);
  }
}

export async function afterFileEditTreeUpdated(editInfo: FileEditInfo, tree: Tree, documentBeforeEdit: TextDocument): Promise<void> {
  const promises = [];
  if (FeatureGateManager.INSTANCE.isFeatureOpen(FeatureName.ENABLE_DEFINITION_ANALYSIS)) {
    promises.push(incrementalOndemandTypeAnalysis(editInfo, tree, documentBeforeEdit));
  }

  await Promise.all(promises);
}

export async function doEditorSelectionListener(selectionInfo: EditorSelectionInfo): Promise<void> {
  if (!selectionInfo.document.uri.startsWith(FILE_URI_PREFIX)) {
    return;
  }

  const promises = [];
  if (FeatureGateManager.INSTANCE.isFeatureOpen(FeatureName.ENABLE_DEFINITION_ANALYSIS)) {
    promises.push(partialOndemandTypeAnalysis(selectionInfo));
  }
  // promises.push(debugDefinitionAsync(selectionInfo));
  // promises.push(debugDefinitionSync(selectionInfo));
  // promises.push(debugUnitTestContext(selectionInfo));

  await Promise.all(promises);
}