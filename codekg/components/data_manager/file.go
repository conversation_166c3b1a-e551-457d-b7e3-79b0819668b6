package data_manager

import (
	"context"
	"math"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/data_storage/consts"
	"ide/ckg/codekg/components/env"
	bizErr "ide/ckg/codekg/components/error"
	"ide/ckg/codekg/components/ignore_service"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/tasks"
	"ide/ckg/codekg/components/validator"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/util"

	"ide/ckg/codekg/components/logs"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (m *manager) getCreateFilesStatus(ctx context.Context, filePaths []string, documents []model.Document) ([]*model.URIStatus, error) {
	result := make([]*model.URIStatus, 0)
	folderMap := make(map[string]bool, 0)
	parsedURIs := make(map[string]bool, 0)

	// 兼容原来只有 filePath 的逻辑
	for _, filePath := range filePaths {
		documents = append(documents, model.Document{
			Uri:       util.GetCanonicalURIFromFilePath(filePath, false),
			Name:      m.fs.Base(filePath),
			ProjectID: m.getProjectID(filePath),
		})
	}

	for _, doc := range documents {
		if _, ok := parsedURIs[doc.Uri]; ok {
			continue
		}
		parsedURIs[doc.Uri] = true

		if doc.Content != "" {
			uriStatus, err := m.createDocumentWithContentInfo(ctx, &doc)
			if err != nil {
				logs.CtxError(ctx, "createDocumentWithContentInfo err is %v path is %v", err, doc.Uri)
				continue
			}
			result = append(result, uriStatus)
			continue
		}

		projectType := m.GetProjectType(ctx, doc.ProjectID)

		// 兼容原来只有 filePath 的逻辑
		filePath := util.GetFilePathFromURI(doc.Uri, projectType == model.ProjectTypeVirtual)
		info, err := m.fs.Stat(filePath)
		if err != nil {
			continue
		}

		projectAbsPath := m.getProjectPathFromProjectID(doc.ProjectID)
		if len(projectAbsPath) == 0 {
			projectAbsPath = doc.ProjectID
			if len(projectAbsPath) == 0 {
				// TODO(ldx): 待明确 projectID、projectURI 和 projectPath 的含义并废弃 getProjectID 方法
				projectAbsPath = m.getProjectID(filePath)
				if len(projectAbsPath) == 0 {
					continue
				}
			}
		}

		relPath, err := m.fs.Rel(projectAbsPath, filePath)
		if err != nil {
			logs.CtxError(ctx, "failed to relative path %s, project: %s", filePath, projectAbsPath)
			continue
		}

		if !info.IsDir() {
			fileInfo, err := m.createFileInfo(ctx, projectAbsPath, filePath)
			if err != nil {
				logs.CtxError(ctx, "createFileInfo err is %v path is %v", err, filePath)
				continue
			}
			result = append(result, &model.URIStatus{
				AbsPath:      filePath,
				RelPath:      relPath,
				ContentHash:  fileInfo.hash,
				UniqueID:     fileInfo.uniqueID,
				ProjectID:    doc.ProjectID,
				UriCanonical: util.GetCanonicalURIFromFilePath(filePath, projectType == model.ProjectTypeVirtual),
			})
		} else {
			folderInfo, err := m.createFolderInfo(ctx, filePath)
			if err != nil {
				logs.CtxError(ctx, "createFolderInfo err is %v path is %v", err, filePath)
				continue
			}
			result = append(result, &model.URIStatus{
				AbsPath:      filePath,
				RelPath:      relPath,
				ContentHash:  folderInfo.hash,
				ProjectID:    doc.ProjectID,
				UriCanonical: util.GetCanonicalURIFromFilePath(filePath, projectType == model.ProjectTypeVirtual),
			})
		}

		dirPath := m.fs.Dir(filePath)
		folderInfo, err := m.createFolderInfo(ctx, dirPath)
		if err != nil {
			logs.CtxError(ctx, "createFolderInfo err is %v path is %v", err, dirPath)
			continue
		}

		_, ok := folderMap[dirPath]
		if ok {
			continue
		}

		relPath, err = m.fs.Rel(projectAbsPath, dirPath)
		if err != nil {
			logs.CtxError(ctx, "failed to relative path %s, project: %s", dirPath, projectAbsPath)
			continue
		}

		result = append(result, &model.URIStatus{
			AbsPath:      dirPath,
			RelPath:      relPath,
			ContentHash:  folderInfo.hash,
			ProjectID:    doc.ProjectID,
			UriCanonical: util.GetCanonicalURIFromFilePath(dirPath, projectType == model.ProjectTypeVirtual),
		})
		folderMap[dirPath] = true
	}

	return result, nil
}

func (m *manager) getChangeFileStatus(ctx context.Context, uris []string) []*model.URIStatus {
	files := make([]*model.URIStatus, 0)
	for _, uri := range uris {
		info, err := m.fs.Stat(uri)
		if err != nil {
			logs.CtxError(ctx, "FileCreated Stat err is %s", err)
			continue
		}

		project := m.getProjectID(uri)
		if len(project) == 0 {
			continue
		}

		projectType := m.GetProjectType(ctx, project)

		relPath, err := m.fs.Rel(project, uri)
		if err != nil {
			logs.CtxError(ctx, "failed to relative path %s, project: %s", uri, project)
			continue
		}

		if !info.IsDir() {
			fileInfo, err := m.createFileInfo(ctx, project, uri)
			if err != nil {
				logs.CtxError(ctx, "createFileInfo err is %v path is %v", err, uri)
				continue
			}

			uriStatus := &model.URIStatus{
				AbsPath:      uri,
				RelPath:      relPath,
				ContentHash:  fileInfo.hash,
				UniqueID:     fileInfo.uniqueID,
				ProjectID:    project,
				UriCanonical: util.GetCanonicalURIFromFilePath(uri, projectType == model.ProjectTypeVirtual),
			}
			files = append(files, uriStatus)
		} else {
			logs.CtxWarn(ctx, "cannot get change file status for directory, uri: %s", uri)
		}
	}
	return files
}

func (m *manager) FileCreated(ctx context.Context, cli knowledgebase.Client, filePaths []string, userID string, documents []model.Document) error {
	fileCreatedEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGSync, userID, "", string(env.GetSourceProduct()))
	defer fileCreatedEvent.Report(ctx, false)
	fileCreatedEvent.AddTeaParam(ckg_metrics.TeaParamSyncInputNum, len(filePaths))
	fileCreatedEvent.AddTeaParam(ckg_metrics.TeaParamSyncDocNum, len(documents))
	fileCreatedEvent.AddTeaParam(ckg_metrics.TeaParamSyncTag, syncTagFileCreated)

	for _, filePath := range filePaths {
		err := m.ignoreService.CreateOrChangeURI(ctx, filePath)
		if err != nil {
			fileCreatedEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
			logs.CtxError(ctx, "failed to update project ignores, err: %v", err)
			return err
		}
	}

	config := m.config.GetTCCConfig(ctx, cli, userID)
	useV2Ignore := m.config.IsFeatureEnabled(ctx, userID, ckg_config.UseV2Ignore)

	filePaths = lo.Filter(filePaths, func(item string, _ int) bool {
		// 兼容原来请求中传多个 project 的 files 以及 projectID 为空的逻辑
		fileProjectID := m.getProjectID(item)
		if len(fileProjectID) == 0 {
			return false
		}
		if useV2Ignore {
			// 更新 gitignore 规则
			relPath, err := filepath.Rel(fileProjectID, item)
			if err != nil {
				return false
			}
			err = m.ignoreServiceV2.UpdateGitIgnoreRulesWhenUpsert(ctx, userID, fileProjectID, relPath)
			if err != nil {
				logs.CtxError(ctx, "failed to update project ignores, err: %v", err)
				return false
			}
			// 判断是否该被 ignore
			info, err := os.Stat(item)
			if err != nil {
				return false
			}
			ignored, _, err := m.ignoreServiceV2.IsIgnore(ctx, userID, fileProjectID, item, info)
			if err != nil {
				logs.CtxError(ctx, "error check valid file, err: %v", err)
			}
			return !ignored && err == nil
		} else {
			valid, err := validator.IsValidForKnowledgeGraph(ctx, fileProjectID, item, config.FileSizeThreshold, m.ignoreService, m.fs)
			if err != nil {
				logs.CtxError(ctx, "error check valid file, err: %v", err)
			}
			return valid || err != nil
		}
	})
	fileCreatedEvent.AddTeaParam(ckg_metrics.TeaParamSyncInputNumAfterIgnore, len(filePaths))

	var gitIgnoreService ignore_service.IgnoreService
	if useV2Ignore {
		gitIgnoreService = m.ignoreServiceV2.GetGitignoreService(ctx, userID)
	} else {
		gitIgnoreService = m.ignoreService
	}
	documents = lo.Filter(documents, func(doc model.Document, _ int) bool {
		projectID := doc.ProjectID
		if len(projectID) == 0 {
			return false
		}
		projectType := m.GetProjectType(ctx, projectID)
		// 目前 Document list 类型传参只支持 virtual project
		if projectType == model.ProjectTypeVirtual {
			if config.VirtualProjectConfig.DisableVirtualProjectIndexing {
				return false
			}

			if doc.Content == "" {
				valid, err := validator.IsValidForKnowledgeGraph(ctx, projectID, util.GetFilePathFromURI(doc.Uri, true), int64(config.VirtualProjectConfig.FileSizeLimit), gitIgnoreService, m.fs)
				if err != nil || !valid {
					logs.CtxError(ctx, "error check valid virtual document, err: %v", err)
					return false
				}
			}
			valid, err := validator.IsValidVirtualProjectDocumentContent(ctx, projectID, doc.Uri, doc.Content, int(config.VirtualProjectConfig.FileSizeLimit))
			if err != nil || !valid {
				logs.CtxError(ctx, "error check valid virtual document, err: %v", err)
				return false
			}
		}
		return true
	})
	fileCreatedEvent.AddTeaParam(ckg_metrics.TeaParamSyncDocNumAfterIgnore, len(documents))

	if len(filePaths) == 0 && len(documents) == 0 {
		return nil
	}

	for _, filePath := range filePaths {
		filePath := filePath
		_ = m.changePreTaskQueue.Push(ctx, tasks.NewLocalChangedPreTask(filePath, func(ctx context.Context) {
			files, err := m.getCreateFilesStatus(ctx, []string{filePath}, []model.Document{}) // TODO
			if err != nil {
				logs.CtxError(ctx, "updateFilesStatus err is %v", err)
				return
			}

			folderCount := 0
			fileCount := 0
			wg := sync.WaitGroup{}
			for _, file := range files {
				info, err := m.fs.Stat(file.AbsPath)
				if err != nil {
					logs.CtxError(ctx, "FileCreated Stat err is %s", err)
					continue
				}
				err = m.fileChangedAndCreateCallback(ctx, cli, userID, file, &wg)
				if err != nil {
					logs.CtxError(ctx, "fileChangedAndCreateCallback err file is %v, err: %v", file, err)
					continue
				}
				if info.IsDir() {
					folderCount++
				} else {
					fileCount++
				}
			}
			util.SafeGo(ctx, func() {
				wg.Wait()
				logs.CtxDebug(ctx, "file %v created", filePath)
			})
		}), 0)
	}

	for _, document := range documents {
		document := document
		_ = m.changePreTaskQueue.Push(ctx, tasks.NewLocalChangedPreTask(document.Uri, func(ctx context.Context) {
			files, err := m.getCreateFilesStatus(ctx, []string{}, []model.Document{document})
			if err != nil {
				logs.CtxError(ctx, "updateFilesStatus err is %v", err)
				return
			}

			folderCount := 0
			fileCount := 0
			wg := sync.WaitGroup{}
			for _, file := range files {
				if file.Content != "" {
					if err = m.fileChangedAndCreateCallback(ctx, cli, userID, file, &wg); err != nil {
						logs.CtxError(ctx, "fileChangedAndCreateCallback err file is %v, err: %v", file, err)
					}
					continue
				}

				info, err := m.fs.Stat(file.AbsPath)
				if err != nil {
					logs.CtxError(ctx, "FileCreated Stat err is %s", err)
					continue
				}

				err = m.fileChangedAndCreateCallback(ctx, cli, userID, file, &wg)
				if err != nil {
					logs.CtxError(ctx, "fileChangedAndCreateCallback err file is %v, err: %v", file, err)
					continue
				}

				if info.IsDir() {
					folderCount++
				} else {
					fileCount++
				}
			}
			util.SafeGo(ctx, func() {
				wg.Wait()
				logs.CtxDebug(ctx, "document %v created", document.Uri)
			})
		}), 0)
	}

	return nil
}

func (m *manager) deleteURIData(ctx context.Context, storage data_storage.Storage,
	embeddingStorage data_storage.EmbeddingStorage, uriMeta *data_storage.StorageURIMeta) error {
	if storage == nil || uriMeta == nil {
		return bizErr.ErrParamNil
	}

	err := storage.GetConn().Transaction(func(tx *gorm.DB) error {
		// 首先确定待删除的 entities 和 vectors 的 ID
		entities, err := storage.SearchEntitiesByURIMeta(ctx, tx, uriMeta)
		if err != nil {
			logs.CtxError(ctx, "SearchEntitiesByURIMeta err %v", err)
			return err
		}
		if embeddingStorage != nil {
			if embeddingStorage.GetVersion() == consts.StorageVersionV1 {
				option := data_storage.NewDeleteDocumentOptionBuilder().Build()
				if err = embeddingStorage.DeleteDocumentsByEntity(ctx, entities, storage, tx, option); err != nil {
					return err
				}
			} else if embeddingStorage.GetVersion() == consts.StorageVersionV2 {
				entityIds := lo.Map(entities, func(e data_storage.StorageEntity, _ int) string { return e.EntityID })
				vectors, err := storage.SearchVectorIDsByEntityIDs(ctx, tx, entityIds)
				if err != nil {
					logs.CtxError(ctx, "SearchVectorIDsByEntityIDs err %v", err)
					return err
				}
				// 先在向量库中删除，再删除 EntityStorage 中的关系
				option := data_storage.NewDeleteDocumentOptionBuilder().Build()
				err = embeddingStorage.DeleteDocumentsByIds(ctx, option, vectors...)
				if err != nil {
					return err
				}
			}

		}

		// 先删除 vector_to_entity，再删除 entity，因为需要先根据 uriMeta.Uri 找到对应的 entity 中的 entities，
		// 这样才能先在 vector_to_entity 中删除对应的记录。
		if err = storage.DeleteVectorToEntityFromURIMeta(ctx, tx, uriMeta); err != nil {
			return err
		}

		err = storage.DeleteEntitiesFromURIMeta(ctx, tx, uriMeta)
		if err != nil {
			return err
		}
		err = storage.DeleteRelationsFromURIMeta(ctx, tx, uriMeta)
		if err != nil {
			return err
		}
		err = storage.DeleteAliasFromURIMeta(ctx, tx, uriMeta)
		if err != nil {
			return err
		}
		return nil
	})

	return err
}

func (m *manager) fileChangedAndCreateCallback(ctx context.Context, cli knowledgebase.Client, userID string, uriStatus *model.URIStatus, wg *sync.WaitGroup) (err error) {
	fileProjectID := uriStatus.ProjectID
	if len(fileProjectID) == 0 {
		fileProjectID = m.getProjectID(uriStatus.AbsPath)
	}
	if len(fileProjectID) == 0 {
		return bizErr.ErrProjectNotFound
	}

	entityStorage, _ := m.GetEntityStorage(ctx, fileProjectID)
	if entityStorage == nil {
		return nil // storage 已经被删除，需要重新 init
	}
	embeddingStorage, _ := m.GetLocalEmbeddingStorage(ctx, fileProjectID)
	if embeddingStorage == nil {
		return nil // embedding storage 已经被删除，需要重新 init
	}

	isDir := false
	projectType := m.GetProjectType(ctx, fileProjectID)
	filePath := util.GetFilePathFromURI(uriStatus.UriCanonical, projectType == model.ProjectTypeVirtual)
	if len(filePath) > 0 {
		info, err := m.fs.Stat(filePath)
		if err != nil {
			return err
		}
		isDir = info.IsDir()
	}

	return m.fileChangedAndCreateCallbackLocal(ctx, cli, userID, uriStatus, isDir, entityStorage, embeddingStorage, wg)
}

func (m *manager) fileChangedAndCreateCallbackLocal(ctx context.Context, cli knowledgebase.Client, userID string, uriStatus *model.URIStatus, isDir bool,
	entityStorage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage, wg *sync.WaitGroup) error {
	var pushed bool
	wg.Add(1)
	defer func() {
		if !pushed {
			wg.Done()
		}
	}()
	projectId := uriStatus.ProjectID
	projectType := m.GetProjectType(ctx, projectId)
	tccConfig := m.config.GetTCCConfig(ctx, cli, userID)
	useV2Ignore := m.config.IsFeatureEnabled(ctx, userID, ckg_config.UseV2Ignore)
	if isDir {
		pushed = m.indexFolderTaskQueue.Push(ctx, tasks.NewIndexFolderTasks(userID, uriStatus, func(ctx context.Context, uriData []*model.URIData) {
			defer wg.Done()

			if len(uriData) == 0 || entityStorage.IsDeleted() || embeddingStorage.IsDeleted() {
				return
			}

			startTime := time.Now()
			for _, data := range uriData {
				err := data_storage.InsertStorage(ctx, entityStorage, data)
				if err != nil {
					logs.CtxError(ctx, "insertStorage err is %v path is %v", err, uriStatus.AbsPath)
					return
				}
				if err = data_storage.MarkFileAsEmbedded(ctx, entityStorage, data); err != nil {
					logs.CtxError(ctx, "updateFileEmbeddingStatus err is %v path is %s", err, uriStatus.AbsPath)
					return
				}
				if err := entityStorage.UpdateURIMetaStatus(ctx, entityStorage.GetConn(), data.Status.AbsPath, int(model.BuildStatusFinished)); err != nil {
					logs.CtxError(ctx, "markURIMetaAsIndexed err is %v path is %s", err, uriStatus.AbsPath)
					return
				}
			}

			taskDuration := time.Since(startTime)
			sleepFactor := tccConfig.TaskSleepTimeFactor
			sleepTime := time.Duration(math.Floor(sleepFactor*float64(taskDuration.Milliseconds()))) * time.Millisecond

			logs.CtxInfo(ctx, "folder %s sync succeed, task time: %d ms, sleep time: %d ms", uriStatus.AbsPath, taskDuration.Milliseconds(), sleepTime.Milliseconds())

			if sleepTime > 0 {
				time.Sleep(sleepTime)
			}
		}, cli, projectId, useV2Ignore, m.ignoreService, m.ignoreServiceV2, m.fs, entityStorage, embeddingStorage), 0)
	} else {
		tccVirtualProjectConfig := tccConfig.VirtualProjectConfig
		// Virtual project 文件首先选择 batch 队列索引，否则选择单文件索引
		if uriStatus.Content != "" {
			// 现在 submitIndexVirtualTaskToQueue 有 wg.Add(1)，不需要外面管
			m.submitIndexVirtualTaskToQueue(ctx, wg, userID, entityStorage, embeddingStorage, projectId, cli, uriStatus, nil, tccVirtualProjectConfig)
		} else {
			info, err := m.createFileInfo(ctx, projectId, uriStatus.AbsPath)
			if err != nil {
				logs.CtxError(ctx, "createFileInfo err is %v", err)
				// pushed 为 false，会在 defer 中 Done
				return err
			}
			// 这里可能会有问题。如果一个文件 < 5000 行，当他大于 5000 行后，就不更新了
			if info.lineCount >= tccConfig.SingleFileLineThreshold {
				logs.CtxWarn(ctx, "file %s ignored, too long: %d", uriStatus.AbsPath, info.lineCount)
				// pushed 为 false，会在 defer 中 Done
				return err
			}
			indexTaskAttribute := &model.IndexTaskAttribute{
				ChunkingMethod:      tccVirtualProjectConfig.ChunkingMethodBaseline,
				ChunkingSize:        tccVirtualProjectConfig.ChunkingSize,
				ChunkingOverlap:     tccVirtualProjectConfig.ChunkingOverlap,
				RemoveEntityContent: tccVirtualProjectConfig.RemoveEntityContent,
			}
			pushed = m.localIndexFileTaskQueue.Push(ctx, tasks.NewLocalIndexFileTasks(entityStorage, embeddingStorage, userID, ckg_metrics.EventNameCKGSync, uriStatus,
				func(ctx context.Context, uriData []*model.URIData, embeddingData []*model.EmbeddingData) {
					defer wg.Done()
					if len(uriData) == 0 || entityStorage.IsDeleted() || embeddingStorage.IsDeleted() {
						return
					}

					startTime := time.Now()
					for _, data := range uriData {
						err := data_storage.InsertStorageWithEmbedding(ctx, entityStorage, embeddingStorage, data)
						if err != nil {
							logs.CtxError(ctx, "insertStorage err is %v path is %v", err, uriStatus.AbsPath)
							return
						}
					}
					if len(embeddingData) == 0 {
						return
					}
					for _, eData := range embeddingData {
						if err := data_storage.InsertEmbeddingStorage(ctx, entityStorage, embeddingStorage, eData); err != nil {
							logs.CtxError(ctx, "insertEmbeddingStorage err is %v path is %v", err, uriStatus.AbsPath)
							return
						}
					}
					for _, data := range uriData {
						if err = data_storage.MarkFileAsEmbedded(ctx, entityStorage, data); err != nil {
							logs.CtxError(ctx, "updateFileEmbeddingStatus err is %v path is %s", err, uriStatus.AbsPath)
							return
						}
						if err := entityStorage.UpdateURIMetaStatus(ctx, entityStorage.GetConn(), data.Status.AbsPath, int(model.BuildStatusFinished)); err != nil {
							logs.CtxError(ctx, "markURIMetaAsIndexed err is %v path is %s", err, uriStatus.AbsPath)
							return
						}
					}

					taskDuration := time.Since(startTime)
					sleepFactor := tccConfig.TaskSleepTimeFactor
					sleepTime := time.Duration(math.Floor(sleepFactor*float64(taskDuration.Milliseconds()))) * time.Millisecond

					logs.CtxTrace(ctx, "file %s init succeed, task time: %d ms, sleep time: %d ms", uriStatus.AbsPath, taskDuration.Milliseconds(), sleepTime.Milliseconds())

					if sleepTime > 0 {
						time.Sleep(sleepTime)
					}
				}, cli, projectId, projectType, useV2Ignore, m.ignoreService, m.ignoreServiceV2, m.config, m.fs, indexTaskAttribute), 0)
		}
	}
	return nil
}

func (m *manager) fileChangedAndCreateCallbackRemote(ctx context.Context, cli knowledgebase.Client, knowledgebaseID, userID string, uriStatus *model.URIStatus, isDir bool,
	entityStorage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage, wg *sync.WaitGroup) error {
	var pushed bool
	defer func() {
		if !pushed {
			wg.Done()
		}
	}()
	projectId := uriStatus.ProjectID
	if isDir {
		useV2Ignore := m.config.IsFeatureEnabled(ctx, userID, ckg_config.UseV2Ignore)
		pushed = m.indexFolderTaskQueue.Push(ctx, tasks.NewIndexFolderTasks(userID, uriStatus, func(ctx context.Context, uriData []*model.URIData) {
			defer wg.Done()
			if len(uriData) == 0 || entityStorage.IsDeleted() {
				return
			}
			for _, data := range uriData {
				err := data_storage.InsertStorage(ctx, entityStorage, data)
				if err != nil {
					logs.CtxError(ctx, "insertStorage err is %v path is %v", err, uriStatus.AbsPath)
					return
				}
			}

			logs.CtxInfo(ctx, "folder %s sync succeed", uriStatus.AbsPath)
		}, cli, projectId, useV2Ignore, m.ignoreService, m.ignoreServiceV2, m.fs, entityStorage, embeddingStorage), 0)
	} else {
		pushed = m.indexFileTaskQueue.Push(ctx, tasks.NewIndexFileTasks(knowledgebaseID, userID, ckg_metrics.EventNameCKGSync, uriStatus, func(ctx context.Context, uriData []*model.URIData) {
			defer wg.Done()

			if len(uriData) == 0 || entityStorage.IsDeleted() {
				return
			}

			for _, data := range uriData {
				err := data_storage.InsertStorage(ctx, entityStorage, data)
				if err != nil {
					logs.CtxError(ctx, "insertStorage err is %v path is %v", err, uriStatus.AbsPath)
					return
				}
			}

			logs.CtxInfo(ctx, "file %s sync succeed", uriStatus.AbsPath)
		}, cli, projectId, 1, m.fs, entityStorage), 0)
	}
	return nil
}

const (
	syncTagFileChanged = "file_changed"
	syncTagFileDeleted = "file_deleted"
	syncTagFileCreated = "file_created"
)

func (m *manager) FileChanged(ctx context.Context, cli knowledgebase.Client, uris []string, userID string) error {
	logs.CtxInfo(ctx, "before clean: %+v", uris)
	fileChangeEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGSync, userID, "", string(env.GetSourceProduct()))
	defer fileChangeEvent.Report(ctx, false)
	fileChangeEvent.AddTeaParam(ckg_metrics.TeaParamSyncInputNum, len(uris))
	fileChangeEvent.AddTeaParam(ckg_metrics.TeaParamSyncTag, syncTagFileChanged)

	// Debounce: 延迟 3s 再处理，
	config := m.config.GetTCCConfig(ctx, cli, userID)
	debounceTime := time.Duration(config.DebounceTime)
	time.Sleep(debounceTime * time.Second)

	if m.config.IsFeatureEnabled(ctx, userID, ckg_config.UseV2Ignore) {
		uris = lo.Filter(uris, func(item string, _ int) bool {
			projectId := m.getProjectID(item)
			relPath, err := filepath.Rel(projectId, item)
			if err != nil {
				logs.CtxError(ctx, "cannot calculate relative path (%s:%s), err: %v", relPath, projectId, err)
				return false
			}
			// 更新 ignore service
			err = m.ignoreServiceV2.UpdateGitIgnoreRulesWhenUpsert(ctx, userID, projectId, relPath)
			if err != nil {
				logs.CtxError(ctx, "failed to update ignore rules, err: %v", err)
				return false
			}
			// 检查文件是否被 ignore
			info, err := os.Stat(item)
			if err != nil {
				return false
			}
			ignored, _, err := m.ignoreServiceV2.IsIgnore(ctx, userID, projectId, item, info)
			if err != nil {
				logs.CtxError(ctx, "error check valid file, err: %v", err)
			}
			return !ignored && err == nil
		})
	} else {
		for _, uri := range uris {
			err := m.ignoreService.CreateOrChangeURI(ctx, uri)
			if err != nil {
				fileChangeEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
				logs.CtxError(ctx, "failed to update project ignores, err: %v", err)
				return err
			}
		}
		uris = lo.Filter(uris, func(item string, _ int) bool {
			projectId := m.getProjectID(item)
			valid, err := validator.IsValidForKnowledgeGraph(ctx, projectId, item, config.FileSizeThreshold, m.ignoreService, m.fs)
			if err != nil {
				logs.CtxError(ctx, "error check valid file, err: %v", err)
			}
			return valid || err != nil
		})
	}

	if len(uris) == 0 {
		logs.CtxInfo(ctx, "empty uris, skip FileChanged, original input: %v", uris)
		return nil
	}

	for _, uri := range uris {
		uri := uri
		_ = m.changePreTaskQueue.Push(ctx, tasks.NewLocalChangedPreTask(uri, func(ctx context.Context) {
			files := m.getChangeFileStatus(ctx, []string{uri})
			folderCount := 0
			fileCount := 0
			wg := sync.WaitGroup{}
			for _, file := range files {
				fileInfo, err := m.fs.Stat(file.AbsPath)
				if err != nil {
					logs.CtxError(ctx, "FileCreated Stat err is %s", err)
					return
				}
				err = m.fileChangedAndCreateCallback(ctx, cli, userID, file, &wg)
				if err != nil {
					logs.CtxError(ctx, "fileChangedAndCreateCallback err file is %v", file)
					return
				}
				if fileInfo.IsDir() {
					folderCount++
				} else {
					fileCount++
				}
			}
			util.SafeGo(ctx, func() {
				wg.Wait()
				logs.CtxInfo(ctx, "file %s changed", uri)
			})
		}), 0)
	}
	return nil
}

func (m *manager) getAllDeleteFile(ctx context.Context, filePaths []string, documents []model.Document) ([]*model.URIStatus, error) {
	result := make([]*model.URIStatus, 0)
	parsedURIs := make(map[string]bool)

	// 兼容原来只有 filePath 的逻辑
	for _, filePath := range filePaths {
		documents = append(documents, model.Document{
			Uri:       util.GetCanonicalURIFromFilePath(filePath, false),
			Name:      m.fs.Base(filePath),
			ProjectID: m.getProjectID(filePath),
		})
	}

	for _, doc := range documents {
		if _, ok := parsedURIs[doc.Uri]; ok {
			continue
		}
		parsedURIs[doc.Uri] = true

		projectType := m.GetProjectType(ctx, doc.ProjectID)
		filePath := util.GetFilePathFromURI(doc.Uri, projectType == model.ProjectTypeVirtual)
		if projectType != model.ProjectTypeVirtual {
			// 兼容原来只有 filePath 的逻辑
			if len(filePath) == 0 {
				continue
			}
		}

		fileProjectID := doc.ProjectID
		if len(fileProjectID) == 0 {
			// TODO(ldx): 待明确 projectID、projectURI 和 projectPath 的含义并废弃 getProjectID 方法
			fileProjectID = m.getProjectID(filePath)
			if len(fileProjectID) == 0 {
				continue
			}
		}

		storage, _ := m.GetEntityStorage(ctx, fileProjectID)
		if storage == nil {
			logs.CtxInfo(ctx, "getAllDeleteFile storage is nil, file is %v", doc)
			continue
		}

		allURIMeta, err := storage.GetAllURIMetaWithoutContent(ctx, storage.GetConn())
		if err != nil {
			return nil, errors.WithMessagef(err, "GetAllURIMetaWithoutContent err")
		}

		if projectType == model.ProjectTypeVirtual {
			for _, uriMeta := range allURIMeta {
				if uriMeta.UriCanonical == doc.Uri {
					result = append(result, &model.URIStatus{
						AbsPath:      uriMeta.Uri,
						ContentHash:  uriMeta.ContentHash,
						UriCanonical: doc.Uri,
						ProjectID:    fileProjectID,
					})
				}
			}
		} else {
			separator := string(os.PathSeparator)
			tmpFile := strings.TrimSuffix(filePath, separator)
			tmpFile = tmpFile + separator
			for _, uriMeta := range allURIMeta {
				if strings.HasPrefix(uriMeta.Uri, tmpFile) || filePath == uriMeta.Uri || filePath == uriMeta.Uri+separator {
					result = append(result, &model.URIStatus{
						AbsPath:     uriMeta.Uri,
						ContentHash: uriMeta.ContentHash,
						// 对于普通 project，uriMeta 表中没有设置 uriCanonical，所以需要使用 uri 来设置
						UriCanonical: util.GetCanonicalURIFromFilePath(uriMeta.Uri, false),
						ProjectID:    fileProjectID,
					})
				}
			}
		}
	}

	return result, nil
}

func (m *manager) getDeleteURIStatus(ctx context.Context, userId string, filePaths []string,
	config *model.CodeKGTCCConfig, documents []model.Document) ([]*model.URIStatus, []*model.URIStatus, error) {
	resultFolder := make([]*model.URIStatus, 0)
	folderMap := make(map[string]bool)
	allDeleteFile, err := m.getAllDeleteFile(ctx, filePaths, documents)
	if err != nil {
		return nil, nil, err
	}

	var gitIgnoreService ignore_service.IgnoreService
	if m.config.IsFeatureEnabled(ctx, userId, ckg_config.UseV2Ignore) {
		gitIgnoreService = m.ignoreServiceV2.GetGitignoreService(ctx, userId)
	} else {
		gitIgnoreService = m.ignoreService
	}

	for _, doc := range documents {
		if len(doc.ProjectID) == 0 {
			continue
		}

		projectType := m.GetProjectType(ctx, doc.ProjectID)
		if projectType == model.ProjectTypeVirtual {
			continue
		}

		filePath := util.GetFilePathFromURI(doc.Uri, false)
		if len(filePath) == 0 {
			continue
		}

		dirPath := m.fs.Dir(filePath)
		valid, err := validator.IsValidForKnowledgeGraph(ctx, doc.ProjectID, dirPath, config.FileSizeThreshold, gitIgnoreService, m.fs)
		if err != nil {
			logs.CtxError(ctx, "error check valid file, err: %v", err)
		}
		if err == nil && !valid {
			continue
		}

		folderInfo, err := m.createFolderInfo(ctx, dirPath)
		if err != nil {
			logs.CtxError(ctx, "createFolderInfo err is %v path is %v", err, filePath)
			continue
		}

		_, ok := folderMap[dirPath]
		if ok {
			continue
		}

		resultFolder = append(resultFolder, &model.URIStatus{
			AbsPath:      dirPath,
			ContentHash:  folderInfo.hash,
			UriCanonical: util.GetCanonicalURIFromFilePath(dirPath, false),
		})
		folderMap[dirPath] = true
	}

	return allDeleteFile, resultFolder, nil
}

func (m *manager) FileDeleted(ctx context.Context, cli knowledgebase.Client, uris []string, userID string, documents []model.Document) error {
	fileDeleteEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGSync, userID, "", string(env.GetSourceProduct()))
	defer fileDeleteEvent.Report(ctx, false)
	fileDeleteEvent.AddTeaParam(ckg_metrics.TeaParamSyncInputNum, len(uris))
	fileDeleteEvent.AddTeaParam(ckg_metrics.TeaParamSyncDocNum, len(documents))
	fileDeleteEvent.AddTeaParam(ckg_metrics.TeaParamSyncTag, syncTagFileDeleted)

	knowledgebaseID, err := env.GetKnowledgebaseID(ctx, userID)
	if err != nil {
		fileDeleteEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		logs.CtxError(ctx, "file delete failed, getKnowledgebaseID err is %v", err)
		return err
	}

	config := m.config.GetTCCConfig(ctx, cli, userID)
	useV2Ignore := m.config.IsFeatureEnabled(ctx, userID, ckg_config.UseV2Ignore)
	for _, uri := range uris {
		uri := uri
		m.changePreTaskQueue.Push(ctx, tasks.NewLocalChangedPreTask(uri, func(ctx context.Context) {
			files, folders, err := m.getDeleteURIStatus(ctx, userID, []string{uri}, config, []model.Document{})
			if err != nil {
				logs.CtxError(ctx, "getDeleteURIStatus err is %v", err)
				return
			}

			wg := sync.WaitGroup{}
			for _, file := range files {
				file := file
				projectID := file.ProjectID
				if len(projectID) == 0 {
					projectID = m.getProjectID(file.AbsPath)
					if len(projectID) == 0 {
						continue
					}
				}

				projectType := m.GetProjectType(ctx, projectID)
				if projectType != model.ProjectTypeVirtual {
					if useV2Ignore {
						err := m.ignoreServiceV2.UpdateGitIgnoreRulesWhenDelete(ctx, userID, file.ProjectID, file.RelPath)
						if err != nil {
							continue
						}
					} else {
						err := m.ignoreService.DeleteURI(ctx, file.AbsPath)
						if err != nil {
							continue
						}
					}
				}

				entityStorage, _ := m.GetEntityStorage(ctx, projectID)
				if entityStorage == nil {
					logs.CtxError(ctx, "file not match storage path is %v", file.AbsPath)
					continue
				}

				embeddingStorageType := EmbeddingStorageTypeRemote
				embeddingStorage, _ := m.GetLocalEmbeddingStorage(ctx, projectID)
				if storageType, ok := m.projects.Load(entityStorage.GetProjectID()); ok {
					embeddingStorageType = storageType.(EmbeddingStorageType)
				}

				uriMeta, err := entityStorage.GetURIMetaFromURIWithoutContent(ctx, entityStorage.GetConn(), file)
				if err != nil {
					logs.CtxError(ctx, "GetURIMetaFromURIWithoutContent err is %v", err)
					continue
				}

				err = entityStorage.DeleteURIMetaFromURI(ctx, entityStorage.GetConn(), file)
				if err != nil {
					logs.CtxError(ctx, "DeleteURIMetaFromURI err is %v", err)
					continue
				}

				wg.Add(1)
				var pushed bool
				if env.LocalEmbedding && embeddingStorageType == EmbeddingStorageTypeLocal {
					if embeddingStorage == nil {
						logs.CtxError(ctx, "delete file: local embedding storage not found for is %v", file.AbsPath)
						continue
					}
					pushed = m.deleteTaskQueue.Push(ctx, tasks.NewLocalDeleteTasks(uriMeta, file, projectID,
						func(ctx context.Context, uriMeta *data_storage.StorageURIMeta, uri *model.URIStatus) {
							defer wg.Done()
							if entityStorage.IsDeleted() || embeddingStorage.IsDeleted() {
								return
							}

							startTime := time.Now()
							err = m.deleteURIData(ctx, entityStorage, embeddingStorage, uriMeta)
							if err != nil {
								return
							}

							taskDuration := time.Since(startTime)
							sleepFactor := config.TaskSleepTimeFactor
							sleepTime := time.Duration(math.Floor(sleepFactor*float64(taskDuration.Milliseconds()))) * time.Millisecond

							logs.CtxInfo(ctx, "file %s is deleted, task time: %d ms, sleep time: %d ms", file.AbsPath, taskDuration.Milliseconds(), sleepTime.Milliseconds())

							if sleepTime > 0 {
								time.Sleep(sleepTime)
							}
						}, entityStorage, embeddingStorage), 0)
				} else {
					pushed = m.deleteTaskQueue.Push(ctx, tasks.NewDeleteTasks(knowledgebaseID, userID, uriMeta, file,
						func(ctx context.Context, uriMeta *data_storage.StorageURIMeta, uri *model.URIStatus) {
							defer wg.Done()
							if entityStorage.IsDeleted() {
								return
							}
							err = m.deleteURIData(ctx, entityStorage, nil, uriMeta)
							if err != nil {
								return
							}
							logs.CtxInfo(ctx, "file %s is deleted", file.AbsPath)
						}, projectID, cli, entityStorage, embeddingStorage), 0)
				}
				if !pushed {
					wg.Done()
				}
			}

			for _, folder := range folders {
				err := m.fileChangedAndCreateCallback(ctx, cli, userID, folder, &wg)
				if err != nil {
					logs.CtxError(ctx, "fileChangedAndCreateCallback err path is %v, err: %v", folder.AbsPath, err)
					return
				}
			}

			util.SafeGo(ctx, func() {
				wg.Wait()
			})
		}), 0)
	}

	for _, doc := range documents {
		doc := doc
		m.changePreTaskQueue.Push(ctx, tasks.NewLocalChangedPreTask(doc.Uri, func(ctx context.Context) {
			files, folders, err := m.getDeleteURIStatus(ctx, userID, []string{}, config, []model.Document{doc})
			if err != nil {
				logs.CtxError(ctx, "getDeleteURIStatus err is %v", err)
				return
			}

			wg := sync.WaitGroup{}
			for _, file := range files {
				file := file
				projectID := file.ProjectID
				if len(projectID) == 0 {
					projectID = m.getProjectID(file.AbsPath)
					if len(projectID) == 0 {
						continue
					}
				}

				projectType := m.GetProjectType(ctx, projectID)
				if projectType != model.ProjectTypeVirtual {
					if useV2Ignore {
						err := m.ignoreServiceV2.UpdateGitIgnoreRulesWhenDelete(ctx, userID, projectID, file.RelPath)
						if err != nil {
							continue
						}
					} else {
						err := m.ignoreService.DeleteURI(ctx, file.AbsPath)
						if err != nil {
							continue
						}
					}
				}

				entityStorage, _ := m.GetEntityStorage(ctx, projectID)
				if entityStorage == nil {
					logs.CtxError(ctx, "file not match storage path is %v", file.AbsPath)
					continue
				}

				embeddingStorageType := EmbeddingStorageTypeRemote
				embeddingStorage, _ := m.GetLocalEmbeddingStorage(ctx, projectID)
				if storageType, ok := m.projects.Load(entityStorage.GetProjectID()); ok {
					embeddingStorageType = storageType.(EmbeddingStorageType)
				}

				uriMeta, err := entityStorage.GetURIMetaFromURIWithoutContent(ctx, entityStorage.GetConn(), file)
				if err != nil {
					logs.CtxError(ctx, "GetURIMetaFromURIWithoutContent err is %v", err)
					continue
				}

				err = entityStorage.DeleteURIMetaFromURI(ctx, entityStorage.GetConn(), file)
				if err != nil {
					logs.CtxError(ctx, "DeleteURIMetaFromURI err is %v", err)
					continue
				}

				wg.Add(1)
				var pushed bool
				if env.LocalEmbedding && embeddingStorageType == EmbeddingStorageTypeLocal {
					if embeddingStorage == nil {
						logs.CtxError(ctx, "delete file: local embedding storage not found for is %v", file.AbsPath)
						continue
					}
					pushed = m.deleteTaskQueue.Push(ctx, tasks.NewLocalDeleteTasks(uriMeta, file, projectID,
						func(ctx context.Context, uriMeta *data_storage.StorageURIMeta, uri *model.URIStatus) {
							defer wg.Done()
							if entityStorage.IsDeleted() || embeddingStorage.IsDeleted() {
								return
							}
							err = m.deleteURIData(ctx, entityStorage, embeddingStorage, uriMeta)
							if err != nil {
								return
							}
							logs.CtxInfo(ctx, "file %s is deleted", file.AbsPath)
						}, entityStorage, embeddingStorage), 0)
				} else {
					pushed = m.deleteTaskQueue.Push(ctx, tasks.NewDeleteTasks(knowledgebaseID, userID, uriMeta, file,
						func(ctx context.Context, uriMeta *data_storage.StorageURIMeta, uri *model.URIStatus) {
							defer wg.Done()
							if entityStorage.IsDeleted() {
								return
							}
							err = m.deleteURIData(ctx, entityStorage, nil, uriMeta)
							if err != nil {
								return
							}
							logs.CtxInfo(ctx, "file %s is deleted", file.AbsPath)
						}, projectID, cli, entityStorage, embeddingStorage), 0)
				}
				if !pushed {
					wg.Done()
				}
			}

			for _, folder := range folders {
				err := m.fileChangedAndCreateCallback(ctx, cli, userID, folder, &wg)
				if err != nil {
					logs.CtxError(ctx, "fileChangedAndCreateCallback err path is %v, err: %v", folder.AbsPath, err)
					return
				}
			}

			util.SafeGo(ctx, func() {
				wg.Wait()
			})
		}), 0)
	}

	return nil
}
