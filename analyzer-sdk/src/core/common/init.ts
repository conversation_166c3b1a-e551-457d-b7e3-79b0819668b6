import {WorkspaceFolder} from "vscode-languageserver-types";
import {Logger, setClientLogger} from "../../utils/logger";
import {eventConsumerInitialized, EventData, setEventConsumer} from "../../utils/event";
import {CKG} from "@byted-ckg/client";
import {LSPParser, LSPProxy} from "../parse/lsp/parser";
import {PSIParser, PSIProxy} from "../parse/psi/parser";
import {CKGWrapper} from "../../utils/ckgWrapper";
import {TreeSitterParser} from "../parse/treesitter/parser";
import {MainHandler} from "../../worker/main";
import {FeatureGateManager, FeatureName} from "./config";
import {setWorkspaceFolderPath} from "../../utils/path";

export interface InitOptions {
  workspaceFolders: WorkspaceFolder[];
  workerJsDir: string;
  logger: Logger | null;
  eventConsumer: (data: EventData) => void | null;
  featureChangeListener: {
    featureMap: Map<string, FeatureName>,
    listener: (feature: string, onFetchSuccess: (enable: boolean) => void) => void
  } | null;
  ckg: CKG | null;
  treeSitterResourceDir: string | null;
  lspProxy: LSPProxy | null;
  psiProxy: PSIProxy | null;
}

export async function doInitAnalyzer(initOptions: InitOptions): Promise<void> {
  if (initOptions.logger) {
    setClientLogger(initOptions.logger);
    Logger.info(`doInitAnalyzer: client logger is initialized`);
  }

  if (initOptions.eventConsumer) {
    setEventConsumer(initOptions.eventConsumer);
    Logger.info(`doInitAnalyzer: event consumer is initialized`);
  }

  if (initOptions.ckg) {
    CKGWrapper.setCKG(initOptions.ckg);
    Logger.info(`doInitAnalyzer: CKG Parser is initialized`);
  }

  if (initOptions.treeSitterResourceDir) {
    await TreeSitterParser.INSTANCE.init(initOptions.treeSitterResourceDir);
    Logger.info(`doInitAnalyzer: TreeSitter Parser is initialized`);
  }

  if (initOptions.lspProxy) {
    LSPParser.INSTANCE.init(initOptions.lspProxy);
    Logger.info(`doInitAnalyzer: LSP Parser is initialized`);
  }

  if (initOptions.psiProxy) {
    PSIParser.INSTANCE.init(initOptions.psiProxy);
    Logger.info(`doInitAnalyzer: PSI Parser is initialized`);
  }

  const workspaceFolderPath = initOptions.workspaceFolders?.[0]?.uri ?? "";
  setWorkspaceFolderPath(workspaceFolderPath);
  MainHandler.INSTANCE.init_(initOptions.workerJsDir, workspaceFolderPath, PSIParser.INSTANCE.initialized(), LSPParser.INSTANCE.initialized(), eventConsumerInitialized());
  Logger.info(`doInitAnalyzer: worker is initialized with (workerJsDir: '${initOptions.workerJsDir}', WorkspaceFolderPath: '${workspaceFolderPath}', PsiInitialized: ${PSIParser.INSTANCE.initialized()}, LspInitialized: ${LSPParser.INSTANCE.initialized()})`);

  if (initOptions.featureChangeListener) {
    handleFeatureChangeListener(initOptions.featureChangeListener.featureMap, initOptions.featureChangeListener.listener);
    Logger.info(`doInitAnalyzer: feature change listener is initialized`);
  }

  await MainHandler.INSTANCE.initAnalyzer({
    ckg: initOptions.ckg,
    treeSitterResourceDir: initOptions.treeSitterResourceDir,
  });

  Logger.info(`doInitAnalyzer: Analyzer initialization is completed`);
}

export function handleFeatureChangeListener(featureMap: Map<string, FeatureName>, listener: (feature: string, onFetchSuccess: (enable: boolean) => void) => void): void {
  featureMap.forEach((analyzerFeatureName: FeatureName, originFeatureName: string) => {
    listener(originFeatureName, async function (enable: boolean): Promise<void> {
      if (enable) {
        FeatureGateManager.INSTANCE.openFeature(analyzerFeatureName);
        await MainHandler.INSTANCE.openFeature({feature: analyzerFeatureName});
      } else {
        FeatureGateManager.INSTANCE.closeFeature(analyzerFeatureName);
        await MainHandler.INSTANCE.closeFeature({feature: analyzerFeatureName});
      }
    });
  });
}

export async function doInitTreeSitterParser(resourceDir: string, logger?: Logger): Promise<void> {
  if (logger) {
    setClientLogger(logger);
  }
  await TreeSitterParser.INSTANCE.init(resourceDir);
}