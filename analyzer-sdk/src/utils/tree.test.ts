import {beforeAll, describe, it} from "vitest";
import {TreeSitterParser} from "../core/parse/treesitter/parser";
import {Logger, setEnableLocalLogging} from "./logger";
import {textToTreeString} from "./tree";
import {LanguageIdEnum} from "../core/parse/share";

describe("test_textToTreeString", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
    setEnableLocalLogging(true);
  });

  it("case1_go", async () => {
    const docText = `
`;

    Logger.info(textToTreeString(docText, LanguageIdEnum.Go, true));
  });

  it("case2_ts", async () => {
    const docText = `
`;

    Logger.info(textToTreeString(docText, LanguageIdEnum.Typescript, true));
  });
});