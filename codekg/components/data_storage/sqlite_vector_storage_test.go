package data_storage

import (
	"database/sql"
	"fmt"
	vec "ide/ckg/codekg/components/data_storage/sqlite_vec"
	"log"
	"math/rand"
	"testing"
	"time"
)

// Global var for assignment in the benchmark to avoid compiler optimizations.
var globalRes []sql.Row

func BenchmarkCollection_Query_500000(b *testing.B) {
	benchmarkCollection_Query(b)
}

// n is number of documents in the collection
func benchmarkCollection_Query(b *testing.B) {
	vec.Auto()

	// Seed to make deterministic
	r := rand.New(rand.NewSource(42))

	// Create collection
	startLoadTime := time.Now()
	db, _ := sql.Open("sqlite3", "/Users/<USER>/Desktop/Projects/vs_playground/sqlite/test.db")
	defer db.Close()
	db.SetMaxOpenConns(1)
	fmt.Printf("Load Time: %v\n", time.Since(startLoadTime))

	// Query
	d := 1024 // dimensions, same as text-embedding-3-small
	// Random query vector
	qv := make([]float32, d)
	for j := 0; j < d; j++ {
		qv[j] = r.Float32()
	}
	query, err := vec.SerializeFloat32(qv)
	if err != nil {
		log.Fatal(err)
	}

	b.ResetTimer()

	rate := 1
	interval := 1 * time.Second / time.Duration(rate)     // Interval between writes
	newInterval := 10 * time.Second / time.Duration(rate) // Interval between writes
	for i := 0; i < 100000; i++ {
		start := time.Now() // Record the start time

		rows, err := db.Query(`
			SELECT rowid, distance
			FROM vec_items
			WHERE embedding MATCH ?
			ORDER BY distance
			LIMIT 50
		`, query)
		if err != nil {
			log.Fatal(err)
		}
		if err = rows.Err(); err != nil {
			log.Fatal(err)
		}
		for rows.Next() {
			var rowid int64
			var distance float64
			err = rows.Scan(&rowid, &distance)
			if err != nil {
				log.Fatal(err)
			}
			//fmt.Printf("rowid=%d, distance=%f\n", rowid, distance)
		}

		elapsed := time.Since(start)
		if sleepTime := interval - elapsed; sleepTime > 0 {
			time.Sleep(sleepTime)
		}
		if i > 20 {
			// 转为静态
			interval = newInterval
		}
	}
}

func TestInsert_500000(t *testing.T) {
	vec.Auto()
	db, _ := sql.Open("sqlite3", "test.db")
	defer db.Close()

	_, err := db.Exec("CREATE VIRTUAL TABLE IF NOT EXISTS vec_items USING vec0(embedding float[1024])")
	if err != nil {
		log.Fatal(err)
	}

	// Number of writes per second
	rate := 150
	interval := time.Second / time.Duration(rate) // Interval between writes
	for i := 0; i < 500000; i++ {
		start := time.Now() // Record the start time
		ve := newVec(float32(i), 1024)
		v, err := vec.SerializeFloat32(ve)
		if err != nil {
			panic("??")
		}
		_, err = db.Exec("INSERT INTO vec_items(embedding) VALUES (?)", v)
		if err != nil {
			log.Fatal(err)
		}
		// Calculate the elapsed time and sleep for the remaining interval
		elapsed := time.Since(start)
		if sleepTime := interval - elapsed; sleepTime > 0 {
			time.Sleep(sleepTime)
		}
	}
}

func newVec(elem float32, dim int) []float32 {
	var vec []float32
	for i := 0; i < dim; i++ {
		vec = append(vec, rand.Float32())
	}
	return vec
}
