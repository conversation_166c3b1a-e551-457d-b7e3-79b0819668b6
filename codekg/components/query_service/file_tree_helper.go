package query_service

import (
	"context"
	"math"
	"os"
	"path/filepath"
	"strings"

	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/components/ignore_service"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/components/validator"
	"ide/ckg/codekg/model"
)

type TreeNode struct {
	Type        int
	Name        string
	GetChildren func() ([]TreeNode, error)
}

type FileTreeNode struct {
	Type        string
	Level       *int
	Value       string
	IsLast      []bool
	GetChildren func() ([]TreeNode, error)
}

// 使用ASCII字符定义
const (
	verticalLine = "|   "
	emptySpace   = "    "
	lastItem     = "`-- "
	regularItem  = "|-- "
)

func countLength(t []FileTreeNode) int {
	sum := 0
	for _, node := range t {
		sum += len(node.Value)
	}
	return sum + int(math.Max(0, float64(len(t)-1)))
}

func flatFileTree(ctx context.Context, level int, files []TreeNode, maxLength int, isLast []bool) []FileTreeNode {
	result := make([]FileTreeNode, 0)

	remainingLength := maxLength
	for i, file := range files {
		isLastItem := i == len(files)-1

		// 计算前缀填充
		padding := ""
		for j := 0; j < level; j++ {
			if isLast[j] {
				padding += emptySpace
			} else {
				padding += verticalLine
			}
		}

		// 添加当前级别的连接符
		prefix := padding
		if level > 0 {
			if isLastItem {
				prefix = padding[:len(padding)-4] + lastItem
			} else {
				prefix = padding[:len(padding)-4] + regularItem
			}
		}

		content := prefix + file.Name
		if file.Type == 2 {
			content += "/" // 为目录添加/
		}
		if len(content) > remainingLength {
			result = append(result, FileTreeNode{Type: "text", Value: padding + "..."})
			break
		}

		// 创建新的isLast切片用于子节点
		newIsLast := append(append([]bool{}, isLast...), isLastItem)

		if file.Type == 2 {
			levelCopy := level
			result = append(result, FileTreeNode{
				Type:        "dir",
				Level:       &levelCopy,
				Value:       content,
				IsLast:      newIsLast,
				GetChildren: file.GetChildren,
			})
		} else {
			result = append(result, FileTreeNode{Type: "text", Value: content})
		}

		remainingLength -= len(content)
		if !isLastItem {
			remainingLength -= 1
		}
	}

	return result
}

func printFileTree(ctx context.Context, t []TreeNode, maxLength int) (string, error) {
	allNodes := make([]FileTreeNode, 0)

	currentNodes := flatFileTree(ctx, 0, t, maxLength, []bool{})
	remainingLength := maxLength - countLength(currentNodes)

	for _, node := range currentNodes {
		if node.Type == "text" {
			allNodes = append(allNodes, node)
		} else if node.Type == "dir" {
			allNodes = append(allNodes, FileTreeNode{Type: "text", Value: node.Value})
			if node.GetChildren != nil {
				children, _ := node.GetChildren()
				childNodes := flatFileTree(ctx, *node.Level+1, children, remainingLength-1, node.IsLast)
				childNodesLength := countLength(childNodes)
				if childNodesLength < remainingLength {
					allNodes = append(allNodes, childNodes...)
					remainingLength -= childNodesLength
				} else {
					// 计算省略号前的填充
					padding := ""
					for j := 0; j <= *node.Level; j++ {
						if j < len(node.IsLast) && node.IsLast[j] {
							padding += emptySpace
						} else {
							padding += verticalLine
						}
					}
					allNodes = append(allNodes, FileTreeNode{Type: "text", Value: padding + "..."})
				}
			}
		}
	}

	allValues := make([]string, 0)
	for _, node := range allNodes {
		allValues = append(allValues, node.Value)
	}
	return strings.Join(allValues, "\n"), nil
}

func readAndProcessDirectory(ctx context.Context, projectId model.URI, directory string, ignoreService ignore_service.IgnoreService, fs file_system.FileSystem) ([]TreeNode, error) {
	directoryContents, err := os.ReadDir(directory)
	if err != nil {
		logs.CtxError(ctx, "ReadDir failed err is %s", err)
		return nil, err
	}

	var entries []TreeNode

	// 实现文件和目录的处理
	for _, entry := range directoryContents {
		tmpEntry := entry
		fullPath := filepath.Join(directory, tmpEntry.Name())
		valid, err := validator.IsValidForKnowledgeGraph(ctx, projectId, fullPath, 0, ignoreService, fs)
		if err != nil {
			continue
		}

		if valid {
			if tmpEntry.IsDir() {
				entries = append(entries, TreeNode{
					Type: 2,
					Name: tmpEntry.Name(),
					GetChildren: func() ([]TreeNode, error) {
						children, err := readAndProcessDirectory(ctx, projectId, filepath.Join(directory, tmpEntry.Name()), ignoreService, fs)
						if err != nil {
							logs.CtxError(ctx, "ReadAndProcessDirectory failed err is %s", err)
							return nil, err
						}
						return children, err
					},
				})
			} else {
				entries = append(entries, TreeNode{
					Type: 1,
					Name: tmpEntry.Name(),
				})
			}
		}
	}

	return entries, nil
}
