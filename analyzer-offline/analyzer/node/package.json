{"name": "@byted-ckg/analyzer-server", "version": "1.0.0", "bin": "dist/index.js", "main": "dist/index.js", "exports": {".": {"types": "./dist/src/index.d.ts", "default": "./dist/src/index.js"}}, "pkg": {"targets": ["linux-x64", "macos-x64", "macos-arm64", "win-x64"], "assets": ["./dist/wasms/**/*", "./dist/analyzer-worker.js", "./dist/tree-sitter.wasm"], "outputPath": "bin"}, "scripts": {"build": "pnpm run clean && pnpm run esbuild  && pnpm run copy:resources", "clean": "tsc -b --clean && rm -rf ./dist", "esbuild": "tsc -noEmit && node esbuild.mjs", "copy:resources": "cp -r ./node_modules/@byted-ckg/analyzer-sdk/dist/resources/ dist/ && pnpm run copy:wasm && pnpm run copy:analyzer-worker", "copy:wasm": "cp ./node_modules/@byted-ckg/analyzer-sdk/dist/src/tree-sitter.wasm dist/", "copy:analyzer-worker": "cp ./node_modules/@byted-ckg/analyzer-sdk/dist/resources/analyzer-worker.js dist"}, "license": "ISC", "dependencies": {"@byted-ckg/analyzer-sdk": "0.4.9", "@byted-ckg/analyzer-server": "link:", "express": "^4.21.2", "tslib": "^2.6.2", "vscode-languageserver-textdocument": "^1.0.12", "vscode-languageserver-types": "^3.17.5", "winston": "^3.14.2"}, "devDependencies": {"@types/express": "^5.0.0", "@types/node": "^20.14.15", "@types/node-fetch": "^2.6.12", "ts-node": "^10.9.2", "typescript": "^5.6.2"}}