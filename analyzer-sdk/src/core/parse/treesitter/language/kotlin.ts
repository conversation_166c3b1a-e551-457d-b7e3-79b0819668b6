import {SyntaxNode} from "web-tree-sitter";
import {Range} from "vscode-languageserver-textdocument";
import {<PERSON><PERSON>itter<PERSON>and<PERSON>} from "./common";
import {LanguageIdEnum} from "../../share";

export class KotlinTreeSitterHandler extends TreeSitterHandler {
  static INSTANCE = new KotlinTreeSitterHandler();

  languageId = LanguageIdEnum.Kotlin;

  funcTypes = ["function_declaration", "lambda_literal"];
  typeTypes = ["class_declaration", "interface_declaration", "enum_declaration", "module_declaration", "record_declaration", "object_declaration"]; // TODO: check
  fileTypes = ["source_file"];
  varTypes = [];

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
    super();
  }

  getParentNodeForKeywordSearch(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, [
      ...this.funcTypes,
      ...this.typeTypes,
      ...this.fileTypes,
    ]);
  }

  getFuncParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.funcTypes);
  }

  getTypeParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.typeTypes);
  }

  getMethodInRange(rootNode: SyntaxNode, range: Range, startIndex: number, endIndex: number, completelyIncluded: boolean): SyntaxNode[] {
    return this.getElementInRangeWithTypeList(rootNode, range, startIndex, endIndex, completelyIncluded, this.funcTypes);
  }

  getImportInfo(rootNode: SyntaxNode): any {
    // TODO
    return null;
  }
}