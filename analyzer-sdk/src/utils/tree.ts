import {TreeSitterParser} from "../core/parse/treesitter/parser";
import type {SyntaxNode} from "web-tree-sitter";

export function textToTreeString(text: string, languageId: string, showHasError = false): string {
  const tree = TreeSitterParser.INSTANCE.parse(languageId, text);
  if (!tree) {
    return "[PARSE-FAILED]";
  }

  return treeNodeToString(tree.rootNode, showHasError);
}

export function treeNodeToString(node: SyntaxNode, showHasError = false): string {
  const result = [];

  treeNodeToStringHelper(node, 0, result, showHasError);

  return `\n${result.join("\n")}\n`;
}

function treeNodeToStringHelper(node: SyntaxNode, deep: number, result: string[], showHasError = false): void {
  if (node.type.toString().trim() !== "") {
    const prefix = `${deep} ${" ".repeat(deep * 2)}`;
    if (showHasError) {
      if (node.childCount === 0 && node.isNamed) {
        result.push(`${prefix}${node.type} [${node.startIndex}-${node.endIndex}] (${node.hasError}) -- ${node.text}`);
      } else {
        result.push(`${prefix}${node.type} [${node.startIndex}-${node.endIndex}] (${node.hasError})`);
      }
    } else {
      if (node.childCount === 0 && node.isNamed) {
        result.push(`${prefix}${node.type} [${node.startIndex}-${node.endIndex}] -- ${node.text}`);
      } else {
        result.push(`${prefix}${node.type} [${node.startIndex}-${node.endIndex}]`);
      }
    }

    for (const child of node.children) {
      treeNodeToStringHelper(child, deep + 1, result, showHasError);
    }
  }
}

export function isNodeWithType(node: SyntaxNode, types: string[]): boolean {
  return types.includes(node.type);
}

export function getRootNode(node: SyntaxNode): SyntaxNode {
  let curNode = node;
  while (curNode.parent) {
    curNode = curNode.parent;
  }
  return curNode;
}

export function traverseNode(node: SyntaxNode, consumer: (node: SyntaxNode) => void): void {
  consumer(node);

  for (const child of node.children) {
    traverseNode(child, consumer);
  }
}