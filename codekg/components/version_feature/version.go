package version_feature

import (
	"github.com/hashicorp/go-version"
	"ide/ckg/clients/ts"
)

var (
	// CKG_VERSION 用于与 SQLite 中的版本号比较。按照二者中较小的版本选择特性。
	CKG_VERSION, _ = version.NewVersion(ts.MustNewPackage().GetVersionCode())
	// DEFAULT_VERSION 用于当 SQLite 中 project_id 表中 ckg_version 字段为 NULL 时的默认值。
	DEFAULT_VERSION, _ = version.NewVersion("0.0.1")

	VersionLocalEmbedding, _       = version.NewVersion("0.0.11")
	VersionLocalEmbeddingCompat, _ = version.NewVersion("0.0.1")
	//VersionHashFolder, _           = version.NewVersion("0.0.24")
	//VersionHashFolderCompat, _     = version.NewVersion("0.0.1")
)
