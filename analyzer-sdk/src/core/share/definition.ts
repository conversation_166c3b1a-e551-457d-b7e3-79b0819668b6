import {TextDocument} from "vscode-languageserver-textdocument";
import {Logger} from "../../utils/logger";
import {SyntaxNode, Tree} from "web-tree-sitter";
import {TreeSitterDispatcher} from "../parse/treesitter/dispatcher";
import {TreeSitterFunctionalityEnum} from "../parse/treesitter/share";
import {positionToPoint} from "../../utils/common";
import {DocumentUri, Position, Range} from "vscode-languageserver-types";
import {DefNodeInfo, DefNodeKindEnum, LanguageIdEnum, UsageNodeInfo, VarDeclNodeInfo} from "../parse/share";
import {FileSymbolDef, ResolvedTypeInfo, SymbolDefInfo, SymbolDefKind} from "./common";
import {TreeSitterParser} from "../parse/treesitter/parser";
import {FeatureGateManager, FeatureName, getConfig} from "../common/config";
import {isInTimeoutInterval} from "../../utils/control";
import {FunctionalityExecutor} from "../parse/functionality/executor";
import {FunctionalityEnum} from "../parse/functionality/share";

interface AnalysisCache {
  nodeIdCache: Map<number, DefNodeInfo>;
  typeFullStringCache: Map<string, DefNodeInfo>;
}

export enum GetSymbolDefSource {
  Direct = "direct",
  Complete = "complete",
  Incremental = "incremental",
  Partial = "partial",
}

export async function getFileSymbolDef(
  source: GetSymbolDefSource,
  result: FileSymbolDef,
  document: TextDocument,
  tree: Tree,
  ranges: Range[],
  definitionHandler: (defInfo: ResolvedTypeInfo) => string,
): Promise<number> {
  if (!isLanguageSupported(document.languageId)) {
    return 0;
  }

  Logger.info(`getFileSymbolDef: start processing for '${document.uri}' ${ranges.map(range => `(${range.start.line},${range.start.character},${range.end.line},${range.end.character})`).join(',')} [${source}]`);
  const start = new Date().getTime();

  if (isInTimeoutInterval()) {
    Logger.info(`getFileSymbolDef: skip processing for '${document.uri}' ${ranges.map(range => `(${range.start.line},${range.start.character},${range.end.line},${range.end.character})`).join(',')} [${source}]`);
    return -1;
  }

  const analysisCache = {
    nodeIdCache: new Map<number, DefNodeInfo>(),
    typeFullStringCache: new Map<string, DefNodeInfo>(),
  };

  let invokeNameNodesCount = 0;
  let processedInvokeNameNodesCount = 0;
  let typeNameNodesCount = 0;
  let processedTypeNameNodesCount = 0;
  let declVarNameNodesCount = 0;
  let processedDeclVarNameNodesCount = 0;
  let usageVarNameNodesCount = 0;
  let processedUsageVarNameNodesCount = 0;

  const toAnalyzeInvokeNameNodes: { node: SyntaxNode, errorNodesRange: { start: number, end: number }[] }[] = [];
  const toAnalyzeTypeNameNodes: { node: SyntaxNode, errorNodesRange: { start: number, end: number }[] }[] = [];
  const toAnalyzeDeclVarNameNodes: { node: SyntaxNode, errorNodesRange: { start: number, end: number }[] }[] = [];
  const toAnalyzeUsageVarNameNodes: { node: SyntaxNode, errorNodesRange: { start: number, end: number }[] }[] = [];

  const importInfo: any = TreeSitterDispatcher.INSTANCE.dispatch(
    document.languageId,
    TreeSitterFunctionalityEnum.GetImportInfo,
    tree.rootNode,
  );

  for (const range of ranges) {
    const startPosition = range.start;
    const endPosition = range.end;

    const errorNodesRange = getErrorNodesRange(document, tree.rootNode, startPosition, endPosition);

    const invokeNameNodes: SyntaxNode[] = TreeSitterDispatcher.INSTANCE.dispatch(
      document.languageId,
      TreeSitterFunctionalityEnum.GetInvokeInFile,
      tree.rootNode,
      positionToPoint(startPosition),
      positionToPoint(endPosition),
    );

    invokeNameNodesCount += invokeNameNodes.length;
    for (const invokeNameNode of invokeNameNodes) {
      if (result.indexDefInfo.slice(invokeNameNode.startIndex, invokeNameNode.endIndex).filter(item => !item || item.kind !== SymbolDefKind.Invoke).length > 0) {
        toAnalyzeInvokeNameNodes.push({node: invokeNameNode, errorNodesRange});
      }
    }

    const typeNameNodes: SyntaxNode[] = TreeSitterDispatcher.INSTANCE.dispatch(
      document.languageId,
      TreeSitterFunctionalityEnum.GetTypeInFile,
      tree.rootNode,
      positionToPoint(startPosition),
      positionToPoint(endPosition),
    );

    const notProcessTypeNameNodes: SyntaxNode[] = TreeSitterDispatcher.INSTANCE.dispatch(
      document.languageId,
      TreeSitterFunctionalityEnum.GetNotProcessTypeNode,
      tree.rootNode,
      typeNameNodes,
    );

    typeNameNodesCount += typeNameNodes.length;
    for (const typeNameNode of typeNameNodes) {
      if (result.indexDefInfo.slice(typeNameNode.startIndex, typeNameNode.endIndex).filter(item => !item || item.kind !== SymbolDefKind.Type).length > 0) {
        if (notProcessTypeNameNodes.includes(typeNameNode)) {
          updateResultAsNull(result, typeNameNode);
        } else {
          toAnalyzeTypeNameNodes.push({node: typeNameNode, errorNodesRange});
        }
      }
    }

    const declVarNameNodes = TreeSitterDispatcher.INSTANCE.dispatch(
      document.languageId,
      TreeSitterFunctionalityEnum.GetDeclVariableInFile,
      tree.rootNode,
      positionToPoint(startPosition),
      positionToPoint(endPosition),
    );

    declVarNameNodesCount += declVarNameNodes.length;
    for (const declVarNameNode of declVarNameNodes) {
      if (result.indexDefInfo.slice(declVarNameNode.startIndex, declVarNameNode.endIndex).filter(item => !item || item.kind !== SymbolDefKind.DeclVar).length > 0) {
        toAnalyzeDeclVarNameNodes.push({node: declVarNameNode, errorNodesRange});
      }
    }

    const usageVarNameNodes: SyntaxNode[] = TreeSitterDispatcher.INSTANCE.dispatch(
      document.languageId,
      TreeSitterFunctionalityEnum.GetUsageVariableInFile,
      tree.rootNode,
      positionToPoint(startPosition),
      positionToPoint(endPosition),
    );

    usageVarNameNodesCount += usageVarNameNodes.length;
    for (const usageVarNameNode of usageVarNameNodes) {
      if (result.indexDefInfo.slice(usageVarNameNode.startIndex, usageVarNameNode.endIndex).filter(item => !item || item.kind !== SymbolDefKind.UsageVar).length > 0) {
        toAnalyzeUsageVarNameNodes.push({node: usageVarNameNode, errorNodesRange});
      }
    }
  }

  const toAnalyzeSymbolCount = toAnalyzeInvokeNameNodes.length + toAnalyzeTypeNameNodes.length + toAnalyzeDeclVarNameNodes.length + toAnalyzeUsageVarNameNodes.length;
  const toAnalyzerMaxSymbolCount = getConfig("analysis.definition.toAnalyzerMaxSymbolCount");

  if (toAnalyzeSymbolCount <= toAnalyzerMaxSymbolCount) {
    for (const {node, errorNodesRange} of toAnalyzeInvokeNameNodes) {
      processedInvokeNameNodesCount++;
      await generateInvokeSymbolDefInfo(result, analysisCache, document, node, definitionHandler, errorNodesRange);
    }
    for (const {node, errorNodesRange} of toAnalyzeTypeNameNodes) {
      processedTypeNameNodesCount++;
      await generateTypeSymbolDefInfo(source, result, analysisCache, document, node, importInfo, definitionHandler, errorNodesRange);
    }
    for (const {node, errorNodesRange} of toAnalyzeDeclVarNameNodes) {
      processedDeclVarNameNodesCount++;
      await generateDeclVariableSymbolDefInfo(result, analysisCache, document, node, importInfo, definitionHandler, errorNodesRange);
    }
    for (const {node, errorNodesRange} of toAnalyzeUsageVarNameNodes) {
      processedUsageVarNameNodesCount++;
      await generateUsageVariableSymbolDefInfo(result, analysisCache, document, node, importInfo, definitionHandler, errorNodesRange);
    }
  } else {
    Logger.warn(`getFileSymbolDef: skip processing for '${document.uri}' ${ranges.map(range => `(${range.start.line},${range.start.character},${range.end.line},${range.end.character})`).join(',')} [${source}] (toAnalyzeCount: ${toAnalyzeSymbolCount}, maxCount: ${toAnalyzerMaxSymbolCount})`);
  }

  const end = new Date().getTime();
  Logger.info(`getFileSymbolDef: cost: ${end - start}ms for '${document.uri}' (${processedInvokeNameNodesCount}/${invokeNameNodesCount} ${processedTypeNameNodesCount}/${typeNameNodesCount} ${processedDeclVarNameNodesCount}/${declVarNameNodesCount} ${processedUsageVarNameNodesCount}/${usageVarNameNodesCount}) [${source}]`);

  return processedInvokeNameNodesCount + processedTypeNameNodesCount + processedDeclVarNameNodesCount + processedUsageVarNameNodesCount;
}

export function getErrorNodesRange(document: TextDocument, node: SyntaxNode, startPosition: Position, endPosition: Position): {
  start: number,
  end: number
}[] {
  const errorNodes: SyntaxNode[] = [];

  const queryStr = `
(ERROR) @error
`;

  const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(document.languageId, queryStr, node, positionToPoint(startPosition), positionToPoint(endPosition));

  for (const match of matchResult) {
    const nameNode = match.matches.get("error");

    if (nameNode.length === 1) {
      errorNodes.push(nameNode[0]);
    }
  }

  const startIndex = document.offsetAt(startPosition);
  const endIndex = document.offsetAt(endPosition);

  return mergeIntervals(errorNodes.map(node => {
    return {
      start: node.startIndex < startIndex ? startIndex : node.startIndex,
      end: node.endIndex > endIndex ? endIndex : node.endIndex,
    };
  }));
}

export function mergeIntervals(intervals: { start: number, end: number }[]): { start: number, end: number }[] {
  const result: { start: number, end: number }[] = [];

  if (!intervals || intervals.length === 0) {
    return [];
  }

  intervals.sort((a, b) => a[0] - b[0]);

  let currentStart = intervals[0].start;
  let currentEnd = intervals[0].end;

  for (let i = 1; i < intervals.length; i++) {
    const {start, end} = intervals[i];
    if (start <= currentEnd) {
      currentEnd = Math.max(currentEnd, end);
    } else {
      result.push({start: currentStart, end: currentEnd});
      [currentStart, currentEnd] = [start, end];
    }
  }

  result.push({start: currentStart, end: currentEnd});

  return result;
}

export function isInErrorNodesRange(errorNodesRange: { start: number, end: number }[], index: number): boolean {
  if (errorNodesRange.length === 0) {
    return false;
  }

  for (const range of errorNodesRange) {
    if (index >= range.start && index < range.end) {
      return true;
    }
  }

  return false;
}

export async function generateInvokeSymbolDefInfo(result: FileSymbolDef, analysisCache: AnalysisCache, document: TextDocument, node: SyntaxNode, definitionHandler: (defInfo: ResolvedTypeInfo) => string, errorNodesRange: {
  start: number,
  end: number
}[]): Promise<void> {
  if (!analysisCache.nodeIdCache.has(node.id)) {
    const defNodeInfos: DefNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
      FunctionalityEnum.GetInvokeDefinition,
      document.languageId,
      document.uri,
      node,
      null,
    );

    let definitionKey: string = null;
    if (defNodeInfos && defNodeInfos.length === 1) {
      analysisCache.nodeIdCache.set(node.id, defNodeInfos[0]);

      const definitionInfo: ResolvedTypeInfo = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GenResolvedTypeInfoFromDefNode, defNodeInfos[0]);
      definitionKey = definitionHandler(definitionInfo);
    }

    const symbolDef = {
      name: node.text,
      kind: SymbolDefKind.Invoke,
      definitionKey,
    };

    updateResult(result, symbolDef, node, errorNodesRange);
  }
}

export async function generateTypeSymbolDefInfo(source: GetSymbolDefSource, result: FileSymbolDef, analysisCache: AnalysisCache, document: TextDocument, node: SyntaxNode, importInfo: any, definitionHandler: (defInfo: ResolvedTypeInfo) => string, errorNodesRange: {
  start: number,
  end: number
}[]): Promise<void> {
  const typeFullString: string = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetTypeFullString, node);

  let defNodeInfo: DefNodeInfo = null;
  if (analysisCache.typeFullStringCache.has(typeFullString)) {
    defNodeInfo = analysisCache.typeFullStringCache.get(typeFullString);
  } else {
    const defNodeInfos: DefNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
      FunctionalityEnum.GetTypeDefinition,
      document.languageId,
      document.uri,
      node,
      await FunctionalityExecutor.INSTANCE.execute(FunctionalityEnum.GetTypeDefinitionPreFilter, document.languageId, importInfo),
      null,
    );

    if (defNodeInfos) {
      const filteredVarTypeDefNodeInfos = defNodeInfos.filter(item => {
        return item.kind !== DefNodeKindEnum.Function;
      });

      if (filteredVarTypeDefNodeInfos.length === 1) {
        analysisCache.typeFullStringCache.set(typeFullString, defNodeInfos[0]);
        defNodeInfo = defNodeInfos[0];
      }
    }
  }

  if (!analysisCache.nodeIdCache.has(node.id)) {
    let definitionKey: string = null;
    if (defNodeInfo) {
      analysisCache.nodeIdCache.set(node.id, defNodeInfo);

      const definitionInfo: ResolvedTypeInfo = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GenResolvedTypeInfoFromDefNode, defNodeInfo);
      definitionKey = definitionHandler(definitionInfo);

      if (source === GetSymbolDefSource.Incremental) {
        const varParentNode = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetVarParentNodeForNode, node);
        if (varParentNode) {
          const varNameNode = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetVarNameNodeFromVarDeclNode, varParentNode);
          if (varNameNode) {
            const declVarSymbolDef = {
              name: varNameNode.text,
              kind: SymbolDefKind.DeclVar,
              definitionKey,
            };

            for (let i = varNameNode.startIndex; i < varNameNode.endIndex; i++) {
              result.indexDefInfo[i] = declVarSymbolDef;
            }

            await updateUsageVarDefInfo(result, document, varNameNode, definitionKey, errorNodesRange);
          }
        }
      }
    }

    const symbolDef = {
      name: node.text,
      kind: SymbolDefKind.Type,
      definitionKey,
    };

    updateResult(result, symbolDef, node, errorNodesRange);
  }
}

export async function generateDeclVariableSymbolDefInfo(
  result: FileSymbolDef,
  analysisCache: AnalysisCache,
  document: TextDocument,
  node: SyntaxNode,
  importInfo: any,
  definitionHandler: (defInfo: ResolvedTypeInfo) => string,
  errorNodesRange: { start: number, end: number }[],
): Promise<void> {
  const declNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(
    document.languageId,
    TreeSitterFunctionalityEnum.GetVarParentNodeForNode,
    node,
  );

  let definitionKey: string = null;
  if (declNode) {
    const varResolvedTypeInfo: ResolvedTypeInfo = await getVariableSymbolDefInfo(result, analysisCache, document.languageId, document.uri, declNode, node, importInfo, definitionHandler, errorNodesRange);
    if (varResolvedTypeInfo) {
      definitionKey = definitionHandler(varResolvedTypeInfo);
    }
  }

  const symbolDef = {
    name: node.text,
    kind: SymbolDefKind.DeclVar,
    definitionKey,
  };

  updateResult(result, symbolDef, node, errorNodesRange);

  await updateUsageVarDefInfo(result, document, node, definitionKey, errorNodesRange);
}

export async function generateUsageVariableSymbolDefInfo(
  result: FileSymbolDef,
  analysisCache: AnalysisCache,
  document: TextDocument,
  node: SyntaxNode,
  importInfo: any,
  definitionHandler: (defInfo: ResolvedTypeInfo) => string,
  errorNodesRange: { start: number, end: number }[],
): Promise<void> {
  if (!analysisCache.nodeIdCache.has(node.id)) {
    const varDeclNodeInfos: VarDeclNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
      FunctionalityEnum.GetVarDeclaration,
      document.languageId,
      document.uri,
      node,
      null,
    );

    let definitionKey: string = null;
    if (varDeclNodeInfos) {
      if (varDeclNodeInfos.length === 1 && varDeclNodeInfos[0].kind === DefNodeKindEnum.VarDecl) {
        const varResolvedTypeInfo = await getVariableSymbolDefInfo(result, analysisCache, document.languageId, varDeclNodeInfos[0].uri, varDeclNodeInfos[0].declNode, node, importInfo, definitionHandler, errorNodesRange);

        if (varResolvedTypeInfo) {
          definitionKey = definitionHandler(varResolvedTypeInfo);
        }
      } else {
        const filteredVarTypeDefNodeInfos = varDeclNodeInfos.filter(item => {
          return item.kind !== DefNodeKindEnum.Function;
        });

        if (filteredVarTypeDefNodeInfos.length === 1) {
          const defNodeInfo = {
            languageId: filteredVarTypeDefNodeInfos[0].languageId,
            uri: filteredVarTypeDefNodeInfos[0].uri,
            kind: filteredVarTypeDefNodeInfos[0].kind,
            name: filteredVarTypeDefNodeInfos[0].name,
            defNode: filteredVarTypeDefNodeInfos[0].declNode,
            subDefNodeInfo: null,
          };
          const typeResolvedTypeInfo: ResolvedTypeInfo = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GenResolvedTypeInfoFromDefNode, defNodeInfo);

          if (typeResolvedTypeInfo) {
            definitionKey = definitionHandler(typeResolvedTypeInfo);
          }
        }
      }
    }

    const symbolDef = {
      name: node.text,
      kind: SymbolDefKind.UsageVar,
      definitionKey,
    };

    updateResult(result, symbolDef, node, errorNodesRange);
  }
}

async function updateUsageVarDefInfo(result: FileSymbolDef, document: TextDocument, node: SyntaxNode, definitionKey: string, errorNodesRange: {
  start: number,
  end: number
}[]): Promise<void> {
  const varUsageNodeInfos: UsageNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
    FunctionalityEnum.GetUsageInUri,
    document.languageId,
    document.uri,
    node,
    document.uri,
  );

  const usageVarSymbolDef = {
    name: node.text,
    kind: SymbolDefKind.UsageVar,
    definitionKey,
  };

  for (const varUsageNodeInfo of varUsageNodeInfos ?? []) {
    if (varUsageNodeInfo.uri === document.uri) {
      for (let i = varUsageNodeInfo.usageNode.startIndex; i < varUsageNodeInfo.usageNode.endIndex; i++) {
        if (!isInErrorNodesRange(errorNodesRange, i)) {
          result.indexDefInfo[i] = usageVarSymbolDef;
        } else {
          result.indexDefInfo[i] = null;
        }
      }
    }
  }
}

async function getVariableSymbolDefInfo(
  result: FileSymbolDef,
  analysisCache: AnalysisCache, languageId: string,
  uri: DocumentUri,
  declNode: SyntaxNode,
  nameNode: SyntaxNode,
  importInfo: any,
  definitionHandler: (defInfo: ResolvedTypeInfo) => string,
  errorNodesRange: { start: number, end: number }[],
): Promise<ResolvedTypeInfo> {
  let varResolvedTypeInfo: ResolvedTypeInfo = null;

  const varTypeNameNode: {
    node: SyntaxNode,
    typeString: string,
    index: number
  } = TreeSitterDispatcher.INSTANCE.dispatch(
    languageId,
    TreeSitterFunctionalityEnum.GetVarTypeNameNode,
    declNode,
    nameNode.text,
  );

  if (varTypeNameNode.node) {
    if (varTypeNameNode.index < 0) {
      const varTypeDefNodeInfo = await getTypeDefNodeInfoHelper(result, analysisCache, languageId, uri, varTypeNameNode.node, importInfo, definitionHandler, errorNodesRange);
      if (varTypeDefNodeInfo) {
        varResolvedTypeInfo = TreeSitterDispatcher.INSTANCE.dispatch(languageId, TreeSitterFunctionalityEnum.GenResolvedTypeInfoFromDefNode, varTypeDefNodeInfo);
      }
    } else {
      let varInvokeDefNodeInfo: DefNodeInfo = null;
      if (analysisCache.nodeIdCache.has(varTypeNameNode.node.id)) {
        varInvokeDefNodeInfo = analysisCache.nodeIdCache.get(varTypeNameNode.node.id);
      } else {
        const varInvokeDefNodeInfos: DefNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
          FunctionalityEnum.GetInvokeDefinition,
          languageId,
          uri,
          varTypeNameNode.node,
          null,
        );

        if (varInvokeDefNodeInfos && varInvokeDefNodeInfos.length === 1) {
          analysisCache.nodeIdCache.set(varTypeNameNode.node.id, varInvokeDefNodeInfos[0]);

          const definitionInfo: ResolvedTypeInfo = TreeSitterDispatcher.INSTANCE.dispatch(languageId, TreeSitterFunctionalityEnum.GenResolvedTypeInfoFromDefNode, varInvokeDefNodeInfos[0]);
          const definitionKey = definitionHandler(definitionInfo);

          const symbolDef = {
            name: varTypeNameNode.node.text,
            kind: SymbolDefKind.Invoke,
            definitionKey,
          };

          updateResult(result, symbolDef, varTypeNameNode.node, errorNodesRange);

          varInvokeDefNodeInfo = varInvokeDefNodeInfos[0];
        }
      }

      if (varInvokeDefNodeInfo) {
        const varInvokeReturnTypeNodes: SyntaxNode[] = TreeSitterDispatcher.INSTANCE.dispatch(
          languageId,
          TreeSitterFunctionalityEnum.GetReturnTypeNodes,
          varInvokeDefNodeInfo.defNode,
        );

        if (varInvokeReturnTypeNodes.length > varTypeNameNode.index && varInvokeReturnTypeNodes[varTypeNameNode.index]) {
          const varInvokeReturnTypeDefNodeInfo = await getTypeDefNodeInfoHelper(null, analysisCache, languageId, varInvokeDefNodeInfo.uri, varInvokeReturnTypeNodes[varTypeNameNode.index], importInfo, null, null);
          if (varInvokeReturnTypeDefNodeInfo) {
            varResolvedTypeInfo = TreeSitterDispatcher.INSTANCE.dispatch(languageId, TreeSitterFunctionalityEnum.GenResolvedTypeInfoFromDefNode, varInvokeReturnTypeDefNodeInfo);
          }
        }
      }
    }
  } else if (varTypeNameNode.typeString) {
    varResolvedTypeInfo = TreeSitterDispatcher.INSTANCE.dispatch(languageId, TreeSitterFunctionalityEnum.GenResolvedTypeInfoFromDefNode, {
      languageId,
      uri: `<primitive>`,
      kind: DefNodeKindEnum.Primitive,
      name: varTypeNameNode.typeString,
      nameIndex: -1,
      declNode: null,
      subDefNodeInfo: null,
    });
  }

  return varResolvedTypeInfo;
}

async function getTypeDefNodeInfoHelper(
  result: FileSymbolDef,
  analysisCache: AnalysisCache,
  languageId: string,
  uri: DocumentUri,
  node: SyntaxNode,
  importInfo: any,
  definitionHandler: (defInfo: ResolvedTypeInfo) => string,
  errorNodesRange: { start: number, end: number }[],
): Promise<DefNodeInfo> {
  const subTypeNodes: SyntaxNode[] = TreeSitterDispatcher.INSTANCE.dispatch(languageId, TreeSitterFunctionalityEnum.GetSubTypeNodes, node);
  if (subTypeNodes.length > 0) {
    const subTypeDefNodeInfos: DefNodeInfo[] = [];

    for (const subTypeNode of subTypeNodes.filter(item => item)) {
      const subTypeDefNodeInfo: DefNodeInfo = await getTypeDefNodeInfoHelper(result, analysisCache, languageId, uri, subTypeNode, importInfo, definitionHandler, errorNodesRange);
      if (subTypeDefNodeInfo) {
        subTypeDefNodeInfos.push(subTypeDefNodeInfo);
      }
    }

    return {
      languageId,
      uri,
      kind: DefNodeKindEnum.Complex,
      name: node.text,
      defNode: node,
      subDefNodeInfo: subTypeDefNodeInfos.length === 0 ? null : subTypeDefNodeInfos,
    };
  } else {
    let varTypeDefNodeInfo: DefNodeInfo = null;
    if (analysisCache.nodeIdCache.has(node.id)) {
      varTypeDefNodeInfo = analysisCache.nodeIdCache.get(node.id);
    } else {
      const varTypeDefNodeInfos: DefNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
        FunctionalityEnum.GetTypeDefinition,
        languageId,
        uri,
        node,
        await FunctionalityExecutor.INSTANCE.execute(FunctionalityEnum.GetTypeDefinitionPreFilter, languageId, importInfo),
        null,
      );

      if (varTypeDefNodeInfos) {

        const filteredVarTypeDefNodeInfos = varTypeDefNodeInfos.filter(item => {
          return item.kind !== DefNodeKindEnum.Function;
        });

        if (filteredVarTypeDefNodeInfos.length === 1) {
          analysisCache.nodeIdCache.set(node.id, filteredVarTypeDefNodeInfos[0]);

          if (result && definitionHandler && errorNodesRange) {
            const definitionInfo: ResolvedTypeInfo = TreeSitterDispatcher.INSTANCE.dispatch(languageId, TreeSitterFunctionalityEnum.GenResolvedTypeInfoFromDefNode, filteredVarTypeDefNodeInfos[0]);
            const definitionKey = definitionHandler(definitionInfo);

            const symbolDef = {
              name: node.text,
              kind: SymbolDefKind.Type,
              definitionKey,
            };

            updateResult(result, symbolDef, node, errorNodesRange);
          }

          varTypeDefNodeInfo = filteredVarTypeDefNodeInfos[0];
        }
      }
    }

    return varTypeDefNodeInfo;
  }
}

function updateResult(
  result: FileSymbolDef,
  symbolDef: SymbolDefInfo,
  node: SyntaxNode,
  errorNodesRange: { start: number, end: number }[],
): void {
  for (let i = node.startIndex; i < node.endIndex && i < result.indexDefInfo.length; i++) {
    if (symbolDef.definitionKey === null && isInErrorNodesRange(errorNodesRange, i)) {
      result.indexDefInfo[i] = null;
    } else {
      result.indexDefInfo[i] = symbolDef;
    }
  }
}

function updateResultAsNull(
  result: FileSymbolDef,
  node: SyntaxNode,
): void {
  for (let i = node.startIndex; i < node.endIndex && i < result.indexDefInfo.length; i++) {
    result.indexDefInfo[i] = null;
  }
}

export function isLanguageSupported(languageId: string): boolean {
  const supportedLanguages: string[] = [
    LanguageIdEnum.Go.toString(),
    LanguageIdEnum.Typescript.toString(),
    LanguageIdEnum.Tsx.toString(),
    LanguageIdEnum.TypescriptReact.toString(),
    LanguageIdEnum.Java.toString(),
  ];

  const enabledLanguages: string[] = [];
  if (FeatureGateManager.INSTANCE.isFeatureOpen(FeatureName.ENABLE_GO_DEFINITION_ANALYSIS)) {
    enabledLanguages.push(LanguageIdEnum.Go.toString());
  }
  if (FeatureGateManager.INSTANCE.isFeatureOpen(FeatureName.ENABLE_TS_DEFINITION_ANALYSIS)) {
    enabledLanguages.push(LanguageIdEnum.Typescript.toString());
    enabledLanguages.push(LanguageIdEnum.Tsx.toString());
    enabledLanguages.push(LanguageIdEnum.TypescriptReact.toString());
  }
  if (FeatureGateManager.INSTANCE.isFeatureOpen(FeatureName.ENABLE_JAVA_DEFINITION_ANALYSIS)) {
    enabledLanguages.push(LanguageIdEnum.Java.toString());
  }

  return supportedLanguages.includes(languageId) && enabledLanguages.map(item => item.toString()).includes(languageId);
}