import path from "path";
import {Worker} from 'worker_threads';
import {Location} from "vscode-languageserver-types";
import {MessagePort, MethodType} from "./share";
import {<PERSON>Handler} from "./base";
import {LSPParser} from "../core/parse/lsp/parser";
import {PSIParser} from "../core/parse/psi/parser";
import {reportEvent} from "../utils/event";
import {Logger} from "../utils/logger";
import {CompletionContextResult} from "../core/feat/completion/context/share";
import type {SyntaxCheckResult} from "../core/feat/completion/post/syntaxCheck";
import type {SyntaxFixResult} from "../core/feat/completion/post/syntaxFix";
import {FeatureName} from "../core/common/config";

export class MainHandler extends BaseHandler {
  static INSTANCE: MainHandler = new MainHandler();

  private worker: Worker;

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
    super();
  }

  init_(workerJsDir: string, workspaceFolderPath: string, psiInitialized: boolean, lspInitialized: boolean, eventConsumerInitialized: boolean): void {
    this.worker = new Worker(path.resolve(workerJsDir, 'analyzer-worker.js'), {
      workerData: {
        workspaceFolderPath,
        psiInitialized,
        lspInitialized,
        eventConsumerInitialized,
      },
    });

    super.init();
  }

  override port(): MessagePort {
    return this.worker;
  }

  override getMethodHandlers(): Map<MethodType, (args: any) => Promise<any>> {
    const result: Map<MethodType, (args: any) => Promise<any>> = new Map();

    result.set(MethodType.ToMain_LspExecute, lspExecuteFunc);
    result.set(MethodType.ToMain_PsiExecute, psiExecuteFunc);
    result.set(MethodType.ToMain_Logging, loggingFunc);
    result.set(MethodType.ToMain_ReportEvent, reportEventFunc);

    return result;
  }

  async initAnalyzer(params: any): Promise<void> {
    return this.sendToPort(MethodType.ToWorker_InitAnalyzer, params);
  }

  async setConfigById(params: any): Promise<void> {
    return this.sendToPort(MethodType.ToWorker_SetConfigById, params);
  }

  async fileActiveListener(params: any): Promise<void> {
    return this.sendToPort(MethodType.ToWorker_FileActiveListener, params);
  }

  async fileEditListener(params: any): Promise<void> {
    return this.sendToPort(MethodType.ToWorker_FileEditListener, params);
  }

  async editorSelectionListener(params: any): Promise<void> {
    return this.sendToPort(MethodType.ToWorker_EditorSelectionListener, params);
  }

  async extractCompletionContext(params: any): Promise<CompletionContextResult[]> {
    return this.sendToPort(MethodType.ToWorker_ExtractCompletionContext, params);
  }

  async completionSyntaxCheck(params: any): Promise<SyntaxCheckResult> {
    return this.sendToPort(MethodType.ToWorker_CompletionSyntaxCheck, params);
  }

  async completionSyntaxFix(params: any): Promise<SyntaxFixResult> {
    return this.sendToPort(MethodType.ToWorker_CompletionSyntaxFix, params);
  }

  async extractUnitTestContext(params: any): Promise<string> {
    return this.sendToPort(MethodType.ToWorker_ExtractUnitTestContext, params);
  }

  async getDefinitionForRange(params: any): Promise<string> {
    return this.sendToPort(MethodType.ToWorker_GetDefinitionForRange, params);
  }

  async openFeature(params: any): Promise<void> {
    return this.sendToPort(MethodType.ToWorker_OpenFeature, params);
  }

  async closeFeature(params: any): Promise<void> {
    return this.sendToPort(MethodType.ToWorker_CloseFeature, params);
  }

  async getOpenedFeature(params: any): Promise<FeatureName[]> {
    return this.sendToPort(MethodType.ToWorker_GetOpenedFeature, params);
  }

  async cacheFeature(params: any): Promise<void> {
    return this.sendToPort(MethodType.ToWorker_CacheFeature, params);
  }

  async restoreFeature(params: any): Promise<void> {
    return this.sendToPort(MethodType.ToWorker_RestoreFeature, params);
  }

  async updateLastTimeoutTimestamp(params: any): Promise<void> {
    return this.sendToPort(MethodType.ToWorker_UpdateLastTimeoutTimestamp, params);
  }

  async getLastTimeoutTimestamp(params: any): Promise<number> {
    return this.sendToPort(MethodType.ToWorker_GetLastTimeoutTimestamp, params);
  }
}

async function lspExecuteFunc(params: any): Promise<Location[]> {
  return LSPParser.INSTANCE.execute(params.command, params.languageId, params.uri, params.position);
}

async function psiExecuteFunc(params: any): Promise<Location[]> {
  return PSIParser.INSTANCE.execute(params.command, params.languageId, params.uri, params.position, params.args);
}

async function loggingFunc(params: any): Promise<void> {
  switch (params.level) {
    case "info":
      Logger.info(params.msg);
      break;
    case "warn":
      Logger.warn(params.msg);
      break;
    case "error":
      Logger.error(params.msg);
      break;
  }
}

async function reportEventFunc(params: any): Promise<void> {
  reportEvent(params.languageId, params.category, params.process, params.name, params.value).then();
}