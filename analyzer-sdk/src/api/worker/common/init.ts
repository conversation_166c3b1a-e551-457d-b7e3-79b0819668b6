import {Logger} from "../../../utils/logger";
import {doInitAnalyzer, doInitTreeSitterParser, InitOptions} from "../../../core/common/init";

export async function initAnalyzer(initOptions: InitOptions): Promise<void> {
  try {
    await doInitAnalyzer(initOptions);
  } catch (e) {
    Logger.error(`initAnalyzer: exception: ${e}`);
    Logger.error(e.stack);
  }
}

export async function initTreeSitterParser(resourceDir: string, logger?: Logger): Promise<void> {
  try {
    await doInitTreeSitterParser(resourceDir, logger);
  } catch (e) {
    Logger.error(`initTreeSitterParser: exception: ${e}`);
    Logger.error(e.stack);
  }
}