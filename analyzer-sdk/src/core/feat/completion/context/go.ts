import {TextDocument} from "vscode-languageserver-textdocument";
import {Position} from "vscode-languageserver-types";
import {getActiveFileCachedTreeAndDocument} from "../../../cache/astCache";
import {SyntaxNode} from "web-tree-sitter";
import {TreeSitterDispatcher} from "../../../parse/treesitter/dispatcher";
import {TreeSitterFunctionalityEnum} from "../../../parse/treesitter/share";
import {queryDefinitionForPosition, queryDefinitionForRange} from "../../../async/definition/query";
import {pointToPosition} from "../../../../utils/common";
import {
  CompletionContextInfo,
  CompletionContextResult,
  ContextItem,
  postProcessingAndSort,
  PriorityEnum,
} from "./share";
import {ResolvedTypeInfo} from "../../../share/common";
import {DefNodeKindEnum} from "../../../parse/share";
import {
  EVENT_CATEGORY_CONTEXT_EXTRACTION, EVENT_NAME_FAILED,
  EVENT_VALUE_NO_CACHE,
  reportEvent,
} from "../../../../utils/event";
import {Logger} from "../../../../utils/logger";

const EVENT_PROCESS_COMPLETION_GO = "completion_go";

export async function doExtractCompletionContextForGo(document: TextDocument, position: Position): Promise<CompletionContextResult[]> {
  const cachedTree = getActiveFileCachedTreeAndDocument(document)?.tree;
  if (!cachedTree) {
    reportEvent(document.languageId, EVENT_CATEGORY_CONTEXT_EXTRACTION, EVENT_PROCESS_COMPLETION_GO, EVENT_NAME_FAILED, EVENT_VALUE_NO_CACHE).then();
    Logger.error(`doExtractCompletionContextForGo: no cached tree found for '${document.uri}'`);
    return [];
  }

  const resultContexts: CompletionContextInfo[] = [];

  const currentNode = cachedTree.rootNode.descendantForPosition({row: position.line, column: position.character});

  resultContexts.push(...getFunctionCallContext(document, position, currentNode));
  resultContexts.push(...getStructInitContext(document, position, currentNode));
  resultContexts.push(...getBranchContext(document, position, currentNode));
  resultContexts.push(...getInlineContext(document, position));
  resultContexts.push(...getScopeFunctionContext(document, position, currentNode));
  resultContexts.push(...getAdjacentContext(document, position, currentNode));

  return postProcessingAndSort(resultContexts);
}

function getFunctionCallContext(document: TextDocument, position: Position, curNode: SyntaxNode): CompletionContextInfo[] {
  const callExpressionNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetParentNodeWithTypeListForNode, curNode, ["call_expression"]);
  if (!callExpressionNode) {
    return [];
  }

  let funcIdentifierNode: SyntaxNode = null;
  if (callExpressionNode.childForFieldName("function")?.type === "identifier") {
    funcIdentifierNode = callExpressionNode.childForFieldName("function");
  } else if (callExpressionNode.childForFieldName("function")?.type === "selector_expression") {
    funcIdentifierNode = callExpressionNode.childForFieldName("function").childForFieldName("field");
  }

  if (!funcIdentifierNode) {
    return [];
  }

  const definitionQueryResult = queryDefinitionForPosition(document, pointToPosition(funcIdentifierNode.startPosition));
  if (!definitionQueryResult || !definitionQueryResult.typeInfo) {
    return [];
  }

  return [{
    priority: PriorityEnum.P0,
    distanceFromPosition: Math.abs(document.offsetAt(position) - funcIdentifierNode.startIndex),
    symbolName: definitionQueryResult.symbolName,
    definitionKey: definitionQueryResult.definitionKey,
    contextInfo: handleResolvedTypeInfoContent(definitionQueryResult.typeInfo),
  }];
}

function getStructInitContext(document: TextDocument, position: Position, curNode: SyntaxNode): CompletionContextInfo[] {
  const compositeLiteralNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetParentNodeWithTypeListForNode, curNode, ["composite_literal"]);
  if (!compositeLiteralNode) {
    return [];
  }

  let typeIdentifierNode: SyntaxNode = null;
  if (compositeLiteralNode.childForFieldName("type")?.type === "type_identifier") {
    typeIdentifierNode = compositeLiteralNode.childForFieldName("type");
  } else if (compositeLiteralNode.childForFieldName("type")?.type === "qualified_type") {
    typeIdentifierNode = compositeLiteralNode.childForFieldName("type").childForFieldName("name");
  }

  if (!typeIdentifierNode) {
    return [];
  }

  const definitionQueryResult = queryDefinitionForPosition(document, pointToPosition(typeIdentifierNode.startPosition));
  if (!definitionQueryResult || !definitionQueryResult.typeInfo) {
    return [];
  }

  return [{
    priority: PriorityEnum.P0,
    distanceFromPosition: Math.abs(document.offsetAt(position) - typeIdentifierNode.startIndex),
    symbolName: definitionQueryResult.symbolName,
    definitionKey: definitionQueryResult.definitionKey,
    contextInfo: handleResolvedTypeInfoContent(definitionQueryResult.typeInfo),
  }];
}

function getBranchContext(document: TextDocument, position: Position, curNode: SyntaxNode): CompletionContextInfo[] {
  const branchIndexRanges = getBranchIndexRanges(curNode);

  const result: CompletionContextInfo[] = [];

  branchIndexRanges.forEach(range => {
    queryDefinitionForRange(document, range.startIndex, range.endIndex).forEach((value, key) => {
      result.push({
        priority: PriorityEnum.P1,
        distanceFromPosition: Math.abs(document.offsetAt(position) - key),
        symbolName: value.symbolName,
        definitionKey: value.definitionKey,
        contextInfo: handleResolvedTypeInfoContent(value.typeInfo),
      });
    });
  });

  return result;
}

function getBranchIndexRanges(curNode: SyntaxNode): { startIndex: number, endIndex: number }[] {
  const result: { startIndex: number, endIndex: number }[] = [];

  let iterNode = curNode;
  while (iterNode) {
    if (iterNode.type === "expression_switch_statement") {
      const valueNode = iterNode.childForFieldName("value");
      if (valueNode) {
        result.push({
          startIndex: valueNode.startIndex,
          endIndex: valueNode.endIndex,
        });
      }
    } else if (iterNode.type === "for_statement") {
      const conditionNode = iterNode.namedChild(0);
      if (conditionNode) {
        result.push({
          startIndex: conditionNode.startIndex,
          endIndex: conditionNode.endIndex,
        });
      }
    } else if (iterNode.type === "if_statement") {
      const conditionNode = iterNode.childForFieldName("condition");
      if (conditionNode) {
        result.push({
          startIndex: conditionNode.startIndex,
          endIndex: conditionNode.endIndex,
        });
      }
    }

    iterNode = iterNode.parent;
  }

  return result;
}

function getInlineContext(document: TextDocument, position: Position): CompletionContextInfo[] {
  const result: CompletionContextInfo[] = [];

  queryDefinitionForRange(
    document,
    document.offsetAt({line: position.line, character: 0}),
    document.offsetAt({line: position.line + 1, character: 0}),
  ).forEach((value, key) => {
    result.push({
      priority: PriorityEnum.P1,
      distanceFromPosition: Math.abs(document.offsetAt(position) - key),
      symbolName: value.symbolName,
      definitionKey: value.definitionKey,
      contextInfo: handleResolvedTypeInfoContent(value.typeInfo),
    });
  });

  return result;
}

function getScopeFunctionContext(document: TextDocument, position: Position, curNode: SyntaxNode): CompletionContextInfo[] {
  const funcDeclNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetFuncParentNodeForNode, curNode);
  if (!funcDeclNode) {
    return [];
  }

  const funcSignatureString = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetFuncSignatureString, funcDeclNode);
  if (!funcSignatureString) {
    return [];
  }

  const result: CompletionContextInfo[] = [];

  queryDefinitionForRange(
    document,
    funcDeclNode.startIndex,
    funcDeclNode.startIndex + funcSignatureString.length,
  ).forEach((value, key) => {
    result.push({
      priority: PriorityEnum.P2,
      distanceFromPosition: Math.abs(document.offsetAt(position) - key),
      symbolName: value.symbolName,
      definitionKey: value.definitionKey,
      contextInfo: handleResolvedTypeInfoContent(value.typeInfo),
    });
  });

  return result;
}

function getAdjacentContext(document: TextDocument, position: Position, curNode: SyntaxNode): CompletionContextInfo[] {
  const funcDeclNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetFuncParentNodeForNode, curNode);
  let minLine = 0;
  if (funcDeclNode) {
    minLine = funcDeclNode.startPosition.row;
  }

  const result: CompletionContextInfo[] = [];

  const p1AvailableLines = 2;
  let p1LineCount = 0;
  let p1i = 1;
  while (true) {
    const lineRange = {
      start: {line: position.line - p1i, character: 0},
      end: {line: position.line - p1i + 1, character: 0},
    };
    if (document.getText(lineRange).trim().length > 0) {
      p1LineCount++;
      if (p1LineCount === p1AvailableLines) {
        break;
      }
    }
    if (position.line - p1i < minLine) {
      break;
    }
    p1i++;
  }

  queryDefinitionForRange(
    document,
    document.offsetAt({line: position.line - p1i, character: 0}),
    document.offsetAt({line: position.line, character: 0}),
  ).forEach((value, key) => {
    result.push({
      priority: PriorityEnum.P1,
      distanceFromPosition: Math.abs(document.offsetAt(position) - key),
      symbolName: value.symbolName,
      definitionKey: value.definitionKey,
      contextInfo: handleResolvedTypeInfoContent(value.typeInfo),
    });
  });

  const p3AvailableLines = 3;
  let p3LineCount = 0;
  let p3i = p1i + 1;
  while (true) {
    const lineRange = {
      start: {line: position.line - p3i, character: 0},
      end: {line: position.line - p3i + 1, character: 0},
    };
    if (document.getText(lineRange).trim().length > 0) {
      p3LineCount++;
      if (p3LineCount === p3AvailableLines) {
        break;
      }
    }
    if (position.line - p3i < minLine) {
      break;
    }
    p3i++;
  }

  queryDefinitionForRange(
    document,
    document.offsetAt({line: position.line - p3i, character: 0}),
    document.offsetAt({line: position.line - p1i, character: 0}),
  ).forEach((value, key) => {
    result.push({
      priority: PriorityEnum.P3,
      distanceFromPosition: Math.abs(document.offsetAt(position) - key),
      symbolName: value.symbolName,
      definitionKey: value.definitionKey,
      contextInfo: handleResolvedTypeInfoContent(value.typeInfo),
    });
  });

  return result;
}

function handleResolvedTypeInfoContent(typeInfo: ResolvedTypeInfo): ContextItem {
  if (typeInfo.path.endsWith("builtin/builtin.go")) {
    return null;
  }

  const result = {
    path: typeInfo.path,
    startIndex: typeInfo.startIndex,
    code: "",
    subInfo: [],
  };

  if (typeInfo.category === DefNodeKindEnum.Function) {
    result.code = typeInfo.optionalContent[ResolvedTypeInfo.OPTIONAL_CONTENT_FUNC_SIGNATURE] ?? "";
  } else if (typeInfo.category === DefNodeKindEnum.Struct) {
    const structContent: string[] = [];
    structContent.push(typeInfo.content);
    if (!typeInfo.path.includes("_gen/")) {
      structContent.push(typeInfo.optionalContent[ResolvedTypeInfo.OPTIONAL_CONTENT_METHODS_IN_CLASS] ?? "");
    }
    result.code = structContent.join("\n");
  } else if (typeInfo.category === DefNodeKindEnum.Complex) {
    result.code = typeInfo.content;
    result.subInfo.push(...typeInfo.subTypeInfo?.map(item => handleResolvedTypeInfoContent(item)).filter(item => item !== null) ?? []);
  } else if ([DefNodeKindEnum.Alias.toString(), DefNodeKindEnum.Primitive.toString(), DefNodeKindEnum.Unknown.toString()].includes(typeInfo.category)) {
    result.code = "";
  } else {
    result.code = typeInfo.content;
  }

  return result.code === "" ? null : result;
}