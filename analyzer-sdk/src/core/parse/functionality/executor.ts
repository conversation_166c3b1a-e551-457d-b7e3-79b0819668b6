import {defaultParserMap, FunctionalityEnum, isAsyncExecuteMap} from "./share";
import {LanguageIdEnum} from "../share";
import {asyncReflectCall, reflectCall} from "../../../utils/common";
import {GoFunctionalityExecutor} from "./impl/go";
import {CppFunctionalityExecutor} from "./impl/cpp";
import {JavascriptFunctionalityExecutor} from "./impl/javascript";
import {TypescriptFunctionalityExecutor} from "./impl/typescript";
import {PythonFunctionalityExecutor} from "./impl/python";
import {JavaFunctionalityExecutor} from "./impl/java";
import {Logger} from "../../../utils/logger";

export class FunctionalityExecutor {
  static INSTANCE = new FunctionalityExecutor();

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
  }

  async execute(functionality: FunctionalityEnum, languageId: string, ...args: any[]): Promise<any> {
    let callFunc = null;
    if (isAsyncExecuteMap.has(functionality)) {
      if (isAsyncExecuteMap.get(functionality)) {
        callFunc = asyncReflectCall;
      } else {
        callFunc = reflectCall;
      }
    }

    const parses = defaultParserMap.get(functionality);

    if (callFunc && parses) {
      switch (languageId) {
        case LanguageIdEnum.Go:
          return callFunc(GoFunctionalityExecutor.INSTANCE, functionality, ...[parses, ...args]);
        case LanguageIdEnum.Cpp:
          return callFunc(CppFunctionalityExecutor.INSTANCE, functionality, ...[parses, ...args]);
        case LanguageIdEnum.Javascript:
        case LanguageIdEnum.Jsx:
        case LanguageIdEnum.JavascriptReact:
          return callFunc(JavascriptFunctionalityExecutor.INSTANCE, functionality, ...[parses, ...args]);
        case LanguageIdEnum.Typescript:
          return callFunc(TypescriptFunctionalityExecutor.TYPESCRIPT_INSTANCE, functionality, ...[parses, ...args]);
        case LanguageIdEnum.Tsx:
          return callFunc(TypescriptFunctionalityExecutor.TSX_INSTANCE, functionality, ...[parses, ...args]);
        case LanguageIdEnum.TypescriptReact:
          return callFunc(TypescriptFunctionalityExecutor.TYPESCRIPT_REACT_INSTANCE, functionality, ...[parses, ...args]);
        case LanguageIdEnum.Python:
          return callFunc(PythonFunctionalityExecutor.INSTANCE, functionality, ...[parses, ...args]);
        case LanguageIdEnum.Java:
          return callFunc(JavaFunctionalityExecutor.INSTANCE, functionality, ...[parses, ...args]);
        default:
          Logger.warn(`FunctionalityExecutor.execute: ${languageId} is not supported`);
          return undefined;
      }
    }

    return undefined;
  }
}