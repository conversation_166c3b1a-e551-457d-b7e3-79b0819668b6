package html

import (
	"context"
	fcmodel "ide/ckg/codekg/components/format_converter/model"
	"ide/ckg/codekg/model"
)

type htmlConverterImpl struct {
	supportedTypes map[fcmodel.FormatType]bool
}

func newHTMLConverter() *htmlConverterImpl {
	return &htmlConverterImpl{
		supportedTypes: map[fcmodel.FormatType]bool{fcmodel.FormatTypeHTML: false},
	}
}

var defaultConverter *htmlConverterImpl

func GetDefaultConverter() fcmodel.FormatConverter {
	if defaultConverter == nil {
		defaultConverter = newHTMLConverter()
	}
	return defaultConverter
}

func (c *htmlConverterImpl) ConvertWithContent(ctx context.Context, content string) (string, error) {
	return "", fcmodel.ErrUnsupportedFormat
}

func (c *htmlConverterImpl) ConvertWithURI(ctx context.Context, uri model.URI) (string, error) {
	return "", fcmodel.ErrUnsupportedFormat
}

func (c *htmlConverterImpl) SupportedTypes() map[fcmodel.FormatType]bool {
	return c.supportedTypes
}
