package model

// from kiwis/api/idl/kitex_gen/knowledgebase/segment.go

import (
	"github.com/go-enry/go-enry/v2"
)

type LanguageType string

const (
	LanguageGo LanguageType = "Go"

	LanguagePython LanguageType = "Python"

	LanguageKotlin LanguageType = "Kotlin"

	LanguageJava LanguageType = "Java"

	LanguageJavaScript LanguageType = "JavaScript"

	LanguageTypeScript LanguageType = "TypeScript"

	LanguageTSX LanguageType = "TSX"

	LanguageCXX LanguageType = "C++"

	LanguageCSharp LanguageType = "C#"

	LanguageC LanguageType = "C"

	LanguageRuby LanguageType = "Ruby"

	LanguageRust LanguageType = "Rust"

	LanguageLua LanguageType = "Lua"

	LanguageOCaml LanguageType = "OCaml"

	LanguagePHP LanguageType = "PHP"

	LanguageScala LanguageType = "Scala"

	LanguageSwift LanguageType = "Swift"

	LanguageObjC LanguageType = "Objective-C"

	LanguageHTML LanguageType = "HTML"

	LanguageJSON LanguageType = "JSON"

	LanguageMarkdown LanguageType = "Markdown"

	LanguageText LanguageType = "Text"
)

// DetectLanguage detects the programming language of a file
func DetectLanguage(filePath string, content []byte) LanguageType {
	return LanguageType(enry.GetLanguage(filePath, content))
}
