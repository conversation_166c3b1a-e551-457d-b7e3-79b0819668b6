import {beforeAll, describe, it, expect} from "vitest";
import {LanguageIdEnum} from "../../parse/share";
import {createDocument} from "../../../utils/common";
import {TreeSitterParser} from "../../parse/treesitter/parser";
import {getCodeStructNodeForKeywords} from "../../../api/direct/general";

describe("test_keyword_search", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_get_keywords_search_class", async () => {
    const docText = `from ..utils import DummyObject, requires_backends


class TensorFlowBenchmarkArguments(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TensorFlowBenchmark(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFForcedBOSTokenLogitsProcessor(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFForcedEOSTokenLogitsProcessor(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFForceTokensLogitsProcessor(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFGenerationMixin(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLogitsProcessor(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])
`;

    const textDocument = createDocument(docText, LanguageIdEnum.Python, "file:///test.py");
    const result = getCodeStructNodeForKeywords(textDocument, [{
      text: "_backends",
      range: {
        start: {
          line: 39,
          character: 4
        },
        end: {
          line: 39,
          character: 13
        }
      }
    }])

    expect(result).toEqual([{
      keyword: {
        text: "_backends",
        range: {
          start: {
            line: 39,
            character: 4
          },
          end: {
            line: 39,
            character: 13
          }
        }
      },
      codeStruct: {
        text: 'class TFGenerationMixin(metaclass=DummyObject):\n' +
          '    _backends = ["tf"]\n' +
          '\n' +
          '    def __init__(self, *args, **kwargs):\n' +
          '        requires_backends(self, ["tf"])',
        range: {start: {line: 38, character: 0}, end: {line: 42, character: 39}}
      }
    }]);
  });

  it("case2_get_keywords_search_whole_file", async () => {
    const docText = `from ..utils import DummyObject, requires_backends


class TensorFlowBenchmarkArguments(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TensorFlowBenchmark(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFForcedBOSTokenLogitsProcessor(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])
`;

    const textDocument = createDocument(docText, LanguageIdEnum.Python, "file:///test.py");
    const result = getCodeStructNodeForKeywords(textDocument, [{
      text: "key1",
      range: {start: {line: 15, character: 6}, end: {line: 15, character: 34}}
    }])

    expect(result).toEqual([{
      keyword: {text: 'key1', range: {start: {line: 15, character: 6}, end: {line: 15, character: 34}}},
      codeStruct: {
        text: 'from ..utils import DummyObject, requires_backends\n' +
          '\n' +
          '\n' +
          'class TensorFlowBenchmarkArguments(metaclass=DummyObject):\n' +
          '    _backends = ["tf"]\n' +
          '\n' +
          '    def __init__(self, *args, **kwargs):\n' +
          '        requires_backends(self, ["tf"])\n' +
          '\n' +
          '\n' +
          'class TensorFlowBenchmark(metaclass=DummyObject):\n' +
          '    _backends = ["tf"]\n' +
          '\n' +
          '    def __init__(self, *args, **kwargs):\n' +
          '        requires_backends(self, ["tf"])\n' +
          '\n' +
          '\n' +
          'class TFForcedBOSTokenLogitsProcessor(metaclass=DummyObject):\n' +
          '    _backends = ["tf"]\n' +
          '\n' +
          '    def __init__(self, *args, **kwargs):\n' +
          '        requires_backends(self, ["tf"])\n',
        range: {start: {line: 0, character: 0}, end: {line: 22, character: 0}}
      }
    }]);
  });


  it("case3_get_keywords_search_function", async () => {
    const docText = `
def load_image(image: Union[str, "PIL.Image.Image"], timeout: Optional[float] = None) -> "PIL.Image.Image":
    requires_backends(load_image, ["vision"])
    if isinstance(image, str):
        if image.startswith("http://") or image.startswith("https://"):
            # We need to actually check for a real protocol, otherwise it's impossible to use a local file
            # like http_huggingface_co.png
            image = PIL.Image.open(BytesIO(requests.get(image, timeout=timeout).content))
        elif os.path.isfile(image):
            image = PIL.Image.open(image)
        else:
            if image.startswith("data:image/"):
                image = image.split(",")[1]

            # Try to load as base64
            try:
                b64 = base64.decodebytes(image.encode())
                image = PIL.Image.open(BytesIO(b64))
            except Exception as e:
                raise ValueError(
                    f"Incorrect image source. Must be a valid URL starting with \`http://\` or \`https://\`, a valid path to an image file, or a base64 encoded string. Got {image}. Failed with {e}"
                )
    elif isinstance(image, PIL.Image.Image):
        image = image
    else:
        raise TypeError(
            "Incorrect format used for image. Should be an url linking to an image, a base64 string, a local path, or a PIL image."
        )
    image = PIL.ImageOps.exif_transpose(image)
    image = image.convert("RGB")
    return image


def validate_preprocess_arguments(
    do_rescale: Optional[bool] = None,
    rescale_factor: Optional[float] = None,
    do_normalize: Optional[bool] = None,
    image_mean: Optional[Union[float, List[float]]] = None,
    image_std: Optional[Union[float, List[float]]] = None,
    do_pad: Optional[bool] = None,
    size_divisibility: Optional[int] = None,
    do_center_crop: Optional[bool] = None,
    crop_size: Optional[Dict[str, int]] = None,
    do_resize: Optional[bool] = None,
    size: Optional[Dict[str, int]] = None,
    resample: Optional["PILImageResampling"] = None,
):
    """
    Checks validity of typically used arguments in an \`ImageProcessor\` \`preprocess\` method.
    Raises \`ValueError\` if arguments incompatibility is caught.
    Many incompatibilities are model-specific. \`do_pad\` sometimes needs \`size_divisor\`,
    sometimes \`size_divisibility\`, and sometimes \`size\`. New models and processors added should follow
    existing arguments when possible.

    """
    if do_rescale and rescale_factor is None:
        raise ValueError("\`rescale_factor\` must be specified if \`do_rescale\` is \`True\`.")

    if do_pad and size_divisibility is None:
        # Here, size_divisor might be passed as the value of size
        raise ValueError(
            "Depending on the model, \`size_divisibility\`, \`size_divisor\`, \`pad_size\` or \`size\` must be specified if \`do_pad\` is \`True\`."
        )

    if do_normalize and (image_mean is None or image_std is None):
        raise ValueError("\`image_mean\` and \`image_std\` must both be specified if \`do_normalize\` is \`True\`.")

    if do_center_crop and crop_size is None:
        raise ValueError("\`crop_size\` must be specified if \`do_center_crop\` is \`True\`.")

    if do_resize and (size is None or resample is None):
        raise ValueError("\`size\` and \`resample\` must be specified if \`do_resize\` is \`True\`.")


# In the future we can add a TF implementation here when we have TF models.
class ImageFeatureExtractionMixin:
    """
    Mixin that contain utilities for preparing image features.
    """

    def _ensure_format_supported(self, image):
        if not isinstance(image, (PIL.Image.Image, np.ndarray)) and not is_torch_tensor(image):
            raise ValueError(
                f"Got type {type(image)} which is not supported, only \`PIL.Image.Image\`, \`np.array\` and "
                "\`torch.Tensor\` are."
            )
`;

    const textDocument = createDocument(docText, LanguageIdEnum.Python, "file:///test.py");
    const result = getCodeStructNodeForKeywords(textDocument, [{
      text: "image",
      range: {start: {line: 81, character: 26}, end: {line: 81, character: 31}}
    }])

    expect(result).toEqual([{
      keyword: {
        text: "image",
        range: {start: {line: 81, character: 26}, end: {line: 81, character: 31}}
      },
      codeStruct: {
        text: 'def _ensure_format_supported(self, image):\n' +
          '        if not isinstance(image, (PIL.Image.Image, np.ndarray)) and not is_torch_tensor(image):\n' +
          '            raise ValueError(\n' +
          '                f"Got type {type(image)} which is not supported, only `PIL.Image.Image`, `np.array` and "\n' +
          '                "`torch.Tensor` are."\n' +
          '            )',
        range: {start: {line: 80, character: 4}, end: {line: 85, character: 13}}
      }
    }]);
  });
});