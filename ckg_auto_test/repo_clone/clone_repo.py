import subprocess
import os
import shutil

def clone_repo(repo_url, destination_dir, commit_id):
    try:
        # 删除目标目录（如果存在且非空）
        if os.path.exists(destination_dir):
            shutil.rmtree(destination_dir)
        os.makedirs(destination_dir)

        # 克隆仓库
        subprocess.run(['git', 'clone', repo_url, destination_dir], check=True, timeout=30)

        # 检出特定的 commit
        subprocess.run(['git', 'checkout', commit_id], cwd=destination_dir, check=True, timeout=30)

        print(f"Repository cloned and checked out to commit {commit_id}")
        return True
    except subprocess.TimeoutExpired:
        print("clone repo timed out")
        return False
    except subprocess.CalledProcessError as e:
        print(f"clone repo error: {e}")
        return False
    except OSError as e:
        print(f"Failed to create directory {destination_dir}: {e}")
        return False