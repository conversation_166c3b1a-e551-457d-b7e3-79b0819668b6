import {findDescendantNodeForRange, LanguageIdEnum, SyntaxNodeWithUri, UsageNodeInfo} from "../share";
import {DocumentUri, Location, Range} from "vscode-languageserver-types";
import type {SyntaxNode} from "web-tree-sitter";
import {PSIParser} from "./parser";
import {pointToPosition, positionToPoint} from "../../../utils/common";
import {getTreeAndDocumentFromUri} from "../../cache/astCache";
import {Logger} from "../../../utils/logger";

export class PsiExecutor {
  static async getDefinitionNode(languageId: string, uri: DocumentUri, node: SyntaxNode, resultFilter: (location: Location) => boolean): Promise<SyntaxNodeWithUri[]> {
    return this.psiLocationOperation(languageId, "jetbrains.getDefinition", uri, node, [], resultFilter);
  }

  static async getUsage(languageId: string, uri: DocumentUri, node: SyntaxNode, resultFilter: (location: Location) => boolean): Promise<UsageNodeInfo[]> {
    const psiResult = await this.psiLocationOperation(languageId, "jetbrains.getUsage", uri, node, [], resultFilter);
    return psiResult.map(item => {
      return {
        languageId,
        uri: item.uri,
        usageNode: item.node,
      };
    });
  }

  static async getUsageInUri(languageId: string, uri: DocumentUri, node: SyntaxNode, scopeUri: DocumentUri): Promise<UsageNodeInfo[]> {
    const psiResult = await this.psiLocationOperation(languageId, "jetbrains.getUsage", uri, node, [scopeUri], null);
    return psiResult.map(item => {
      return {
        languageId,
        uri: item.uri,
        usageNode: item.node,
      };
    });
  }

  private static async psiLocationOperation(languageId: string, command: string, uri: DocumentUri, node: SyntaxNode, args: any[], resultFilter: (location: Location) => boolean): Promise<SyntaxNodeWithUri[]> {
    const result: SyntaxNodeWithUri[] = [];

    const psiResult: Location[] = await PSIParser.INSTANCE.executeInMainThread(command, languageId, uri, pointToPosition(node.startPosition), args);
    for (const location of psiResult ?? []) {
      if (location.uri === uri && location.range.start.line === node.startPosition.row && location.range.start.character === node.startPosition.column) {
        continue;
      }

      if (!resultFilter || resultFilter(location)) {
        const {tree, document} = getTreeAndDocumentFromUri(location.uri, languageId);

        let range = location.range;
        if (languageId === LanguageIdEnum.Java) {
          range = this.adjustRangeForJava(tree.rootNode, location.range);
        }

        if (tree && document) {
          result.push({
            uri: location.uri,
            node: findDescendantNodeForRange(tree.rootNode, document, range),
          });
        }
      }
    }

    return result;
  }

  // private
  static adjustRangeForJava(rootNode: SyntaxNode, range: Range): Range {
    const positionNode = rootNode.namedDescendantForPosition(positionToPoint(range.start));
    if (positionNode.type === "block_comment" || positionNode.type === "line_comment") {
      return this.adjustRangeForJava(rootNode, {start: pointToPosition(positionNode.nextNamedSibling.startPosition), end: range.end});
    }

    return range;
  }
}