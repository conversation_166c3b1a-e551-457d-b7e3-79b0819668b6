import {beforeAll, describe, expect, it} from "vitest";
import {TreeSitterParser} from "../parser";
import {LanguageIdEnum} from "../../share";
import {JavascriptTreeSitterHandler} from "./javascript";

describe("test_getImportFileOrModule", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
const path = require('path');
const {path1} = require('path');
let path = require('path');
import * as fs from 'fs';
import { readFile, writeFile } from 'fs';
const specificExport = require('./myModule').specificExport;
const logger = require('./logger').child({ component: 'environment' });
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = JavascriptTreeSitterHandler.JAVASCRIPT_INSTANCE.getImportFileOrModule(tree.rootNode);

    expect(result).toEqual([
      "import * as fs from 'fs';",
      "import { readFile, writeFile } from 'fs';",
      "const path = require('path');",
      "const {path1} = require('path');",
      "let path = require('path');",
      "const specificExport = require('./myModule').specificExport;",
      "const logger = require('./logger').child({ component: 'environment' });",
    ]);
  });
});

describe("test_getFuncSignatureString", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_function_declaration", () => {
    const docText = `
function myFunction(a, b) {
    return a + b;
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = JavascriptTreeSitterHandler.JAVASCRIPT_INSTANCE.getFuncSignatureString(tree.rootNode.namedChild(0));

    expect(result).toEqual("function myFunction(a, b) ");
  });

  it("case2_method_definition", () => {
    const docText = `
class MyClass {
    myMethod(m, n) {
        return m - n;
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = JavascriptTreeSitterHandler.JAVASCRIPT_INSTANCE.getFuncSignatureString(tree.rootNode.namedChild(0).childForFieldName("body").namedChild(0));

    expect(result).toEqual("myMethod(m, n) ");
  });

  it("case3_generator_function_declaration", () => {
    const docText = `
function* myGeneratorFunction() {
    yield 'a';
    yield 'b';
    yield 'c';
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = JavascriptTreeSitterHandler.JAVASCRIPT_INSTANCE.getFuncSignatureString(tree.rootNode.namedChild(0));

    expect(result).toEqual("function* myGeneratorFunction() ");
  });
})