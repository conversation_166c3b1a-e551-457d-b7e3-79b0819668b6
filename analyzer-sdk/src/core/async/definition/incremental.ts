import {FileEditInfo} from "../../../utils/common";
import {getCachedFileSymbolDef} from "../../cache/symbolDefCache";
import {FileSymbolDef} from "../../share/common";
import {
  addDefinitionAnalysisTask, definition<PERSON><PERSON><PERSON>,
  EVENT_PROCESS_FILE_EDIT, hasIncrementalAnalysisTask,
} from "./share";
import {SyntaxNode, Tree} from "web-tree-sitter";
import {getFileSymbolDef, GetSymbolDefSource, isLanguageSupported} from "../../share/definition";
import {getWorkspaceFolderPath} from "../../../utils/path";
import {
  EVENT_CATEGORY_TYPE_ANALYSIS,
  EVENT_NAME_FAILED,
  EVENT_VALUE_NO_CACHE,
  reportEvent,
} from "../../../utils/event";
import {Logger} from "../../../utils/logger";
import {TextDocument} from "vscode-languageserver-textdocument";

const EVENT_NAME_CLEAR_CACHED_SYMBOL = "clear_cached_symbol";
const EVENT_NAME_MULTI_CHANGE_INDEX_LENGTH_MISMATCH = "multi_change_index_length_mismatch";

const EVENT_VALUE_INDEX_LENGTH_MISMATCH = "index_length_mismatch";
const EVENT_VALUE_DOCUMENT_LENGTH_MISMATCH = "document_length_mismatch";

export async function incrementalOndemandTypeAnalysis(editInfo: FileEditInfo, tree: Tree, documentBeforeEdit: TextDocument): Promise<void> {
  if (!isLanguageSupported(editInfo.document.languageId)) {
    return;
  }

  if (!editInfo.document.uri.startsWith(getWorkspaceFolderPath())) {
    return;
  }

  if (!tree) {
    Logger.error(`incrementalOndemandTypeAnalysis: no tree found for '${editInfo.document.uri}'`);
    reportEvent(editInfo.document.languageId, EVENT_CATEGORY_TYPE_ANALYSIS, EVENT_PROCESS_FILE_EDIT, EVENT_NAME_FAILED, EVENT_VALUE_NO_CACHE).then();
    return;
  }

  if (editInfo.contentChanges.length === 0) {
    return;
  }

  if (documentBeforeEdit) {
    analyzeIndexAndTask(editInfo, tree, documentBeforeEdit);
  } else {
    Logger.warn(`incrementalOndemandTypeAnalysis: clear cached symbol - no documentBeforeEdit`);
    reportEvent(editInfo.document.languageId, EVENT_CATEGORY_TYPE_ANALYSIS, EVENT_PROCESS_FILE_EDIT, EVENT_NAME_CLEAR_CACHED_SYMBOL).then();

    const taskFunc = async (): Promise<FileSymbolDef> => {
      const result = new FileSymbolDef(editInfo.document);
      result.indexDefInfo = new Array(editInfo.document.getText().length).fill(null);
      return result;
    };
    addDefinitionAnalysisTask({
      id: Date.now(),
      source: GetSymbolDefSource.Incremental,
      document: editInfo.document,
      taskFunc,
    });
  }
}

export function analyzeIndexAndTask(editInfo: FileEditInfo, tree: Tree, documentBeforeEdit: TextDocument): void {
  const oldLength = documentBeforeEdit.getText().length;
  const newLength = editInfo.document.getText().length;
  const diff = calcCountOfDiffCharacters(editInfo);

  let taskFunc: () => Promise<FileSymbolDef>;

  const id = Date.now();

  if (oldLength + diff === newLength) {
    const changesInIndex = editInfo.contentChanges.map(change => {
      return {
        oldIndex: documentBeforeEdit.offsetAt(change.position),
        removeLength: change.removeLength,
        text: change.text,
        newIndex: null,
      };
    });
    const sortedChangesInIndex = changesInIndex.sort((a, b) => a.oldIndex - b.oldIndex);

    let indexOffset = 0;
    for (const change of sortedChangesInIndex) {
      change.newIndex = change.oldIndex + indexOffset;
      indexOffset += (change.text.length - change.removeLength);
    }

    taskFunc = async (): Promise<FileSymbolDef> => {
      const cachedSymbolDef = getCachedFileSymbolDef(editInfo.document, false);
      if (cachedSymbolDef) {
        if (cachedSymbolDef.indexDefInfo.length === documentBeforeEdit.getText().length) {

          const result = new FileSymbolDef(editInfo.document);

          const indexRanges: {
            oldStartIndex: number,
            adjustedNewStartIndex: number,
            startAdjusted: number,
            adjustedNewEndIndex: number,
            endAdjusted: number,
            removeLength: number
          }[] = [];

          for (let i = 0; i < changesInIndex.length; i++) {
            const item = changesInIndex[i];

            let minAdjustedNewStartIndex: number;
            if (i === 0) {
              minAdjustedNewStartIndex = 0;
            } else {
              minAdjustedNewStartIndex = indexRanges[i - 1].adjustedNewEndIndex;
            }
            const adjustedNewStartIndex = Math.max(getAdjustedStartIndex(tree.rootNode, editInfo.document, item.newIndex), minAdjustedNewStartIndex);

            let maxAdjustedNewEndIndex: number;
            if (i === changesInIndex.length - 1) {
              maxAdjustedNewEndIndex = editInfo.document.getText().length;
            } else {
              maxAdjustedNewEndIndex = changesInIndex[i + 1].newIndex;
            }
            const adjustedNewEndIndex = Math.min(getAdjustedEndIndex(tree.rootNode, editInfo.document, item.newIndex + item.text.length), maxAdjustedNewEndIndex);

            indexRanges.push({
              oldStartIndex: item.oldIndex,
              adjustedNewStartIndex,
              startAdjusted: item.newIndex - adjustedNewStartIndex,
              adjustedNewEndIndex,
              endAdjusted: adjustedNewEndIndex - (item.newIndex + item.text.length),
              removeLength: item.removeLength,
            });
          }

          result.indexDefInfo = [];

          for (let i = 0; i < indexRanges.length; i++) {
            const indexRange = indexRanges[i];

            if (i === 0) {
              result.indexDefInfo.push(...cachedSymbolDef.indexDefInfo.slice(0, indexRange.oldStartIndex - indexRange.startAdjusted));
            }

            result.indexDefInfo.push(...new Array(indexRange.adjustedNewEndIndex - indexRange.adjustedNewStartIndex).fill(null));

            let nextStartIndex: number;
            if (i === indexRanges.length - 1) {
              nextStartIndex = cachedSymbolDef.indexDefInfo.length;
            } else {
              nextStartIndex = indexRanges[i + 1].oldStartIndex - indexRanges[i + 1].startAdjusted;
            }
            result.indexDefInfo.push(...cachedSymbolDef.indexDefInfo.slice(indexRange.oldStartIndex + indexRange.removeLength + indexRange.endAdjusted, nextStartIndex));
          }

          if (result.indexDefInfo.length !== editInfo.document.getText().length) {
            Logger.warn(`analyzeIndexAndAndTask: new indexDefInfo length mismatch (get: ${result.indexDefInfo.length}, desired: ${editInfo.document.getText().length})`);
            reportEvent(editInfo.document.languageId, EVENT_CATEGORY_TYPE_ANALYSIS, EVENT_PROCESS_FILE_EDIT, EVENT_NAME_MULTI_CHANGE_INDEX_LENGTH_MISMATCH).then();
          }

          if (!hasIncrementalAnalysisTask()) {
            const mergedRanges = mergeRanges(indexRanges.map(item => {
              return {
                adjustedNewStartIndex: item.adjustedNewStartIndex,
                adjustedNewEndIndex: item.adjustedNewEndIndex,
              };
            }));

            await getFileSymbolDef(GetSymbolDefSource.Incremental, result, editInfo.document, tree, mergedRanges.map(item => {
              return {
                start: editInfo.document.positionAt(item.adjustedNewStartIndex),
                end: editInfo.document.positionAt(item.adjustedNewEndIndex),
              };
            }), definitionHandler);
          }

          return result;
        } else {
          Logger.warn(`analyzeIndexAndAndTask: cached indexDefInfo length mismatch (get: ${cachedSymbolDef.indexDefInfo.length}, desired: ${documentBeforeEdit.getText().length})`);
          reportEvent(editInfo.document.languageId, EVENT_CATEGORY_TYPE_ANALYSIS, EVENT_PROCESS_FILE_EDIT, EVENT_NAME_CLEAR_CACHED_SYMBOL, EVENT_VALUE_INDEX_LENGTH_MISMATCH).then();

          const result = new FileSymbolDef(editInfo.document);
          result.indexDefInfo = new Array(editInfo.document.getText().length).fill(null);
          return result;
        }
      } else {
        Logger.warn(`analyzeIndexAndTask: no cachedSymbolDef found for ${editInfo.document.uri}`);

        const result = new FileSymbolDef(editInfo.document);
        result.indexDefInfo = new Array(editInfo.document.getText().length).fill(null);
        return result;
      }
    };
  } else {
    Logger.warn(`analyzeIndexAndTask: clear cached symbol - document length mismatch (old: ${oldLength}, new: ${newLength}, diff: ${diff})`);
    reportEvent(editInfo.document.languageId, EVENT_CATEGORY_TYPE_ANALYSIS, EVENT_PROCESS_FILE_EDIT, EVENT_NAME_CLEAR_CACHED_SYMBOL, EVENT_VALUE_DOCUMENT_LENGTH_MISMATCH).then();

    taskFunc = async (): Promise<FileSymbolDef> => {
      const result = new FileSymbolDef(editInfo.document);
      result.indexDefInfo = new Array(editInfo.document.getText().length).fill(null);
      return result;
    };
  }

  if (taskFunc) {
    addDefinitionAnalysisTask({
      id,
      source: GetSymbolDefSource.Incremental,
      document: editInfo.document,
      taskFunc,
    });
  }
}

function getAdjustedStartIndex(rootNode: SyntaxNode, document: TextDocument, index: number): number {
  if (index === 0) {
    return index;
  }

  const alphanumericRegex = /^[a-zA-Z0-9]$/;
  if (!alphanumericRegex.test(document.getText()[index - 1])) {
    return index;
  }

  return rootNode.namedDescendantForIndex(index).startIndex;
}


function getAdjustedEndIndex(rootNode: SyntaxNode, document: TextDocument, index: number): number {
  if (index === document.getText().length) {
    return index;
  }

  const alphanumericRegex = /^[a-zA-Z0-9]$/;
  if (!alphanumericRegex.test(document.getText()[index])) {
    return index;
  }

  return rootNode.namedDescendantForIndex(index).endIndex;
}


function calcCountOfDiffCharacters(editInfo: FileEditInfo): number {
  let count = 0;
  for (const change of editInfo.contentChanges) {
    count += change.text.length - change.removeLength;
  }
  return count;
}

export function mergeRanges(ranges: {
  adjustedNewStartIndex: number,
  adjustedNewEndIndex: number,
}[]): { adjustedNewStartIndex: number, adjustedNewEndIndex: number }[] {
  if (ranges.length === 0) {
    return [];
  }

  const result: {
    adjustedNewStartIndex: number,
    adjustedNewEndIndex: number,
  }[] = [];

  let curRange = {
    adjustedNewStartIndex: ranges[0].adjustedNewStartIndex,
    adjustedNewEndIndex: ranges[0].adjustedNewEndIndex,
  };
  for (let i = 1; i < ranges.length; i++) {
    const nextRange = ranges[i];
    if (curRange.adjustedNewEndIndex >= nextRange.adjustedNewStartIndex) {
      curRange.adjustedNewEndIndex = nextRange.adjustedNewEndIndex;
    } else {
      result.push(curRange);
      curRange = {
        adjustedNewStartIndex: nextRange.adjustedNewStartIndex,
        adjustedNewEndIndex: nextRange.adjustedNewEndIndex,
      };
    }
  }
  result.push(curRange);

  return result;
}