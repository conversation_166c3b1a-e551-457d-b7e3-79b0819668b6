import {LRUCache} from 'lru-cache';
import type {TextDocument} from 'vscode-languageserver-textdocument';
import type {Tree} from 'web-tree-sitter';
import {TreeSitterParser} from '../parse/treesitter/parser';
import {Logger} from '../../utils/logger';
import {
  changeInfoToString,
  createDocument, FILE_URI_PREFIX,
  FileEditInfo, positionToPoint,
  readFile,
} from '../../utils/common';
import {
  reportEvent,
  EVENT_CATEGORY_CACHE,
  EVENT_NAME_FAILED,
  EVENT_NAME_IGNORE, EVENT_VALUE_NO_CACHE,
} from '../../utils/event';
import {DocumentUri} from "vscode-languageserver-types";
import {getConfig} from "../common/config";

const EVENT_PROCESS_GET_ACTIVE_FILE_TREE = "get_active_file_tree";
const EVENT_PROCESS_COMPLETE_UPDATE = "complete_update";
const EVENT_PROCESS_INCREMENTAL_UPDATE = "incremental_update";

const EVENT_VALUE_LINE_COUNT_EXCEED = "line_count_exceed";
const EVENT_VALUE_PARSE_ERROR = "parse_error";

export interface TreeAndDocument {
  tree: Tree;
  document: TextDocument;
}

const activeFileTreeLRUCache = new LRUCache<string, TreeAndDocument>({
  max: 30,
  ttl: 60 * 60 * 1000,
  ttlAutopurge: true,
  updateAgeOnGet: true,
});

const extraFileTreeLRUCache = new LRUCache<string, TreeAndDocument>({
  max: 30,
  ttl: 60 * 1000,
  ttlAutopurge: true,
  updateAgeOnGet: true,
});

export function getActiveFileCachedTreeAndDocument(document: TextDocument): { tree: Tree, document: TextDocument } {
  if (!TreeSitterParser.INSTANCE.isLanguageSupported(document.languageId)) {
    Logger.warn(`getActiveFileCachedTree: unsupported language ${document.languageId} of '${document.uri}'`);
    return null;
  }

  if (document.lineCount > getConfig("cache.ast.maxLineCount")) {
    return null;
  }

  const key = document.uri;
  const cache = activeFileTreeLRUCache.get(key);

  if (!cache) {
    Logger.warn(`getActiveFileCachedTree: no cached data found for '${document.uri}'`);
    reportEvent(document.languageId, EVENT_CATEGORY_CACHE, EVENT_PROCESS_GET_ACTIVE_FILE_TREE, EVENT_NAME_FAILED, EVENT_VALUE_NO_CACHE).then();

    return null;
  }

  return cache;
}

export function getCacheFileAstTree(uri: DocumentUri): Tree {
  const activeCache = activeFileTreeLRUCache.get(uri);
  if (activeCache) {
    return activeCache.tree;
  }

  const extraCache = extraFileTreeLRUCache.get(uri);
  if (extraCache) {
    return extraCache.tree;
  }

  return null;
}

export function getCacheFileAstTreeAllowCreate(document: TextDocument): Tree {
  if (!TreeSitterParser.INSTANCE.isLanguageSupported(document.languageId)) {
    Logger.warn(`getCacheFileAstTreeAllowCreate: unsupported language ${document.languageId} of '${document.uri}'`);
    return null;
  }

  if (document.lineCount > getConfig("cache.ast.maxLineCount")) {
    return null;
  }

  const cachedTree = getCacheFileAstTree(document.uri);
  if (cachedTree) {
    return cachedTree;
  }

  const tree = TreeSitterParser.INSTANCE.parse(document.languageId, document.getText());
  extraFileTreeLRUCache.set(document.uri, {tree, document});

  return tree;
}

export function getTreeAndDocumentFromUri(uri: DocumentUri, languageId: string): {
  tree: Tree,
  document: TextDocument
} {
  const cachedTree = getCacheFileAstTree(uri);

  if (cachedTree) {
    return {tree: cachedTree, document: createDocument(cachedTree.rootNode.text, languageId, uri)};
  } else {
    const fileContent = readFile(uri);
    const document = createDocument(fileContent, languageId, uri);
    return {tree: getCacheFileAstTreeAllowCreate(document), document};
  }
}

export async function completeUpdateCachedTree(document: TextDocument): Promise<Tree> {
  if (!TreeSitterParser.INSTANCE.isLanguageSupported(document.languageId)) {
    Logger.warn(`completeUpdateCachedTree: unsupported language ${document.languageId} of '${document.uri}'`);
    return null;
  }

  Logger.info(`completeUpdateCachedTree: languageId: ${document.languageId}, document: '${document.uri}'`);

  if (document.lineCount > getConfig("cache.ast.maxLineCount")) {
    reportEvent(document.languageId, EVENT_CATEGORY_CACHE, EVENT_PROCESS_COMPLETE_UPDATE, EVENT_NAME_IGNORE, EVENT_VALUE_LINE_COUNT_EXCEED).then();
    return null;
  }

  const tree = TreeSitterParser.INSTANCE.parse(document.languageId, document.getText());
  if (tree) {
    activeFileTreeLRUCache.set(document.uri, {tree, document});
    if (extraFileTreeLRUCache.has(document.uri)) {
      extraFileTreeLRUCache.delete(document.uri);
    }
  } else {
    Logger.error(`completeUpdateCachedTree: failed to parse document '${document.uri}'`);
    reportEvent(document.languageId, EVENT_CATEGORY_CACHE, EVENT_PROCESS_COMPLETE_UPDATE, EVENT_NAME_FAILED, EVENT_VALUE_PARSE_ERROR).then();
  }

  return tree;
}

export async function incrementalUpdateCachedTree(editInfo: FileEditInfo): Promise<Tree> {
  if (!TreeSitterParser.INSTANCE.isLanguageSupported(editInfo.document.languageId)) {
    Logger.warn(`incrementalUpdateCachedTree: unsupported language ${editInfo.document.languageId} of '${editInfo.document.uri}'`);
    return null;
  }

  Logger.info(`incrementalUpdateCachedTree: languageId: ${editInfo.document.languageId}, document: '${editInfo.document.uri}', changes: ${changeInfoToString(editInfo.contentChanges)}`);

  if (editInfo.document.lineCount > getConfig("cache.ast.maxLineCount")) {
    reportEvent(editInfo.document.languageId, EVENT_CATEGORY_CACHE, EVENT_PROCESS_INCREMENTAL_UPDATE, EVENT_NAME_IGNORE, EVENT_VALUE_LINE_COUNT_EXCEED).then();
    return null;
  }

  const cachedTree = getActiveFileCachedTreeAndDocument(editInfo.document)?.tree;
  if (!cachedTree) {
    Logger.warn(`incrementalUpdateCachedTree: no cached data found for '${editInfo.document.uri}'`);
    reportEvent(editInfo.document.languageId, EVENT_CATEGORY_CACHE, EVENT_PROCESS_INCREMENTAL_UPDATE, EVENT_NAME_FAILED, EVENT_VALUE_NO_CACHE).then();
    return completeUpdateCachedTree(editInfo.document);
  }

  if (editInfo.contentChanges.length === 0) {
    Logger.info(`incrementalUpdateCachedTree: no content changes for '${editInfo.document.uri}'`);
    return cachedTree;
  }

  if (editInfo.contentChanges.length > 1) {
    // TODO: support multi-change
    return completeUpdateCachedTree(editInfo.document);
  }

  const change = editInfo.contentChanges[0];
  const startIndex = editInfo.document.offsetAt(change.position);
  const oldEndIndex = startIndex + change.removeLength;
  const newEndIndex = startIndex + change.text.length;
  const startPosition = editInfo.document.positionAt(startIndex);
  const oldEndPosition = editInfo.document.positionAt(oldEndIndex);
  const newEndPosition = editInfo.document.positionAt(newEndIndex);
  const startPoint = positionToPoint(startPosition);
  const oldEndPoint = positionToPoint(oldEndPosition);
  const newEndPoint = positionToPoint(newEndPosition);

  cachedTree.edit({
    startIndex,
    oldEndIndex,
    newEndIndex,
    startPosition: startPoint,
    oldEndPosition: oldEndPoint,
    newEndPosition: newEndPoint,
  });

  const newTree = TreeSitterParser.INSTANCE.parse(editInfo.document.languageId, editInfo.document.getText(), cachedTree);
  if (newTree) {
    activeFileTreeLRUCache.set(editInfo.document.uri, {tree: newTree, document: editInfo.document});
  } else {
    Logger.error(`incrementalUpdateCachedTree: failed to parse document '${editInfo.document.uri}'`);
    reportEvent(editInfo.document.languageId, EVENT_CATEGORY_CACHE, EVENT_PROCESS_INCREMENTAL_UPDATE, EVENT_NAME_FAILED, EVENT_VALUE_PARSE_ERROR).then();
  }

  return newTree;
}