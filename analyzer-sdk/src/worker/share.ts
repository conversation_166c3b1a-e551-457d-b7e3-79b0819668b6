export interface MessagePort {
  on(event: string, listener: (message: WorkerMessage) => void): void;
  postMessage(message: WorkerMessage): void;
}

export interface WorkerMessage {
  id: number;
  type: MessageType;
  method: MethodType;
  payload: any
}

export enum MessageType {
  Request = "request",
  Response = "response",
}

export enum MethodType {
  ToWorker_InitAnalyzer = "initAnalyzer",
  ToWorker_SetConfigById = "setConfigById",
  ToWorker_FileActiveListener = "fileActiveListener",
  ToWorker_FileEditListener = "fileEditListener",
  ToWorker_EditorSelectionListener = "editorSelectionListener",
  ToWorker_ExtractCompletionContext = "extractCompletionContext",
  ToWorker_CompletionSyntaxCheck = "completionSyntaxCheck",
  ToWorker_CompletionSyntaxFix = "completionSyntaxFix",
  ToWorker_ExtractUnitTestContext = "extractUnitTestContext",
  ToWorker_GetDefinitionForRange = "getDefinitionForRange",
  ToWorker_OpenFeature = "openFeature",
  ToWorker_CloseFeature = "closeFeature",
  ToWorker_GetOpenedFeature = "getOpenedFeature",
  ToWorker_CacheFeature = "cacheFeature",
  ToWorker_RestoreFeature = "restoreFeature",
  ToWorker_UpdateLastTimeoutTimestamp = "updateLastTimeoutTimestamp",
  ToWorker_GetLastTimeoutTimestamp = "getLastTimeoutTimestamp",

  ToMain_LspExecute = "lspExecute",
  ToMain_PsiExecute = "psiExecute",
  ToMain_Logging = "logging",
  ToMain_ReportEvent = "reportEvent",
}