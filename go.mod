module ide/ckg

go 1.21

require (
	code.byted.org/data/http_mario_collector v0.0.0-20201229032103-e7522d182aa7
	code.byted.org/gopkg/ctxvalues v0.4.0
	code.byted.org/gopkg/lang v0.18.15
	code.byted.org/ide/go-sqlcipher v0.0.0-20241125133610-f151017500b9
	github.com/adrianbrad/queue v1.3.0
	github.com/avast/retry-go v3.0.0+incompatible
	github.com/denormal/go-gitignore v0.0.0-20180930084346-ae8ad1d07817
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/emirpasic/gods v1.18.1
	github.com/go-git/go-git/v5 v5.13.2
	github.com/gogo/protobuf v1.3.2
	github.com/google/uuid v1.6.0
	github.com/hashicorp/go-version v1.2.0
	github.com/hashicorp/golang-lru/v2 v2.0.7
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826
	github.com/philippgille/chromem-go v0.7.0
	github.com/pkg/errors v0.9.1
	github.com/samber/lo v1.39.0
	github.com/stretchr/testify v1.10.0
	gitlab.com/golang-commonmark/markdown v0.0.0-20211110145824-bf3e522c626a
	go.uber.org/atomic v1.9.0
	go.uber.org/mock v0.4.0
	go.uber.org/multierr v1.10.0
	go.uber.org/zap v1.27.0
	golang.org/x/sync v0.10.0
	golang.org/x/time v0.5.0
	google.golang.org/grpc v1.65.0
	google.golang.org/protobuf v1.35.2
	gorm.io/gorm v1.25.7
)

require (
	github.com/go-enry/go-oniguruma v1.2.1 // indirect
	gitlab.com/golang-commonmark/html v0.0.0-20191124015941-a22733972181 // indirect
	gitlab.com/golang-commonmark/linkify v0.0.0-20191026162114-a0c2df6c8f82 // indirect
	gitlab.com/golang-commonmark/mdurl v0.0.0-20191124015652-932350d1cb84 // indirect
	gitlab.com/golang-commonmark/puny v0.0.0-20191124015043-9f83538fa04f // indirect
)

require (
	code.byted.org/dp/mario_common v1.0.4 // indirect
	dario.cat/mergo v1.0.0 // indirect
	github.com/Microsoft/go-winio v0.6.1 // indirect
	github.com/ProtonMail/go-crypto v1.1.5 // indirect
	github.com/bytedance/gopkg v0.0.0-20220118075514-1372042b2bbc
	github.com/cloudflare/circl v1.3.7 // indirect
	github.com/cyphar/filepath-securejoin v0.3.6 // indirect
	github.com/danwakefield/fnmatch v0.0.0-20160403171240-cbb64ac3d964 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/go-enry/go-enry/v2 v2.9.2
	github.com/go-git/gcfg v1.5.1-0.20230307220236-3a3c6141e376 // indirect
	github.com/go-git/go-billy/v5 v5.6.2 // indirect
	github.com/go-resty/resty/v2 v2.5.0
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/jbenet/go-context v0.0.0-20150711004518-d14ea06fba99 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/kevinburke/ssh_config v1.2.0 // indirect
	github.com/pjbgf/sha1cd v0.3.2 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/sergi/go-diff v1.3.2-0.20230802210424-5b0b94c5c0d3 // indirect
	github.com/skeema/knownhosts v1.3.0 // indirect
	github.com/xanzy/ssh-agent v0.3.3 // indirect
	golang.org/x/crypto v0.32.0 // indirect
	golang.org/x/exp v0.0.0-20240719175910-8a7402abbf56 // indirect
	golang.org/x/mod v0.19.0 // indirect
	golang.org/x/net v0.34.0 // indirect
	golang.org/x/sys v0.29.0 // indirect
	golang.org/x/text v0.21.0 // indirect
	golang.org/x/tools v0.23.0 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20241202173237-19429a94021a // indirect
	gopkg.in/warnings.v0 v0.1.2 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
