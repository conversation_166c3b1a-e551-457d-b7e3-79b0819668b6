import {beforeAll, describe, expect, it} from "vitest";
import {TreeSitterParser} from "../../parse/treesitter/parser";
import {
  getConstructorDeclNodes,
  getConstructorDefNodes, getFieldDefNodes,
  getFuncNameNode,
  getFunctionDeclaratorNode,
  getNamespaceInfo, getParameterNameNodes,
  getParamTypeNameNodes,
  getReturnTypeNameNodes,
  getSuperClassNameNode, getUsedFuncNameNodes, getUsedOutsideVariableNameNodes,
  getUsedTypeNameNodes, getVariableDeclNameNodes,
} from "./cpp";
import {LanguageIdEnum} from "../../parse/share";

describe("test_getNamespaceInfo", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_one_namespace", async () => {
    const docText = `
namespace N1 {
    func() {
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getNamespaceInfo(tree.rootNode.descendantForPosition({row: 2, column: 5}));

    expect(result).toEqual("N1");
  });

  it("case2_more_than_one_namespace", async () => {
    const docText = `
namespace N1 {
    namespace N2 {
        namespace N3 {
            func() {
            }
        }
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getNamespaceInfo(tree.rootNode.descendantForPosition({row: 4, column: 15}));

    expect(result).toEqual("N1::N2::N3");
  });
});

describe("test_getFunctionDeclaratorNode", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
int func(int i) {}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getFunctionDeclaratorNode(tree.rootNode.namedChild(0));

    expect(result.text).toEqual("func(int i)");
  });

  it("case2_with_pointer", () => {
    const docText = `
int* func(int i) {}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getFunctionDeclaratorNode(tree.rootNode.namedChild(0));

    expect(result.text).toEqual("func(int i)");
  });

  it("case3_with_dereference", () => {
    const docText = `
C& func(int i) {}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getFunctionDeclaratorNode(tree.rootNode.namedChild(0));

    expect(result.text).toEqual("func(int i)");
  });

  it("case4_inner_decl", () => {
    const docText = `
void ImportMgr::importMaterialsImpl() {
    if (m_cancel.load()) {
        emit importMaterialsCanceled(taskId);
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getFunctionDeclaratorNode(tree.rootNode.namedChild(0));

    expect(result.text).toEqual("ImportMgr::importMaterialsImpl()");
  });
})

describe("test_getFuncNameNode", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
int func(int i) {}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getFuncNameNode(tree.rootNode.namedChild(0));

    expect(result.text).toEqual("func");
  });

  it("case2_with_one_identifier", () => {
    const docText = `
int ID1::func(int i) {}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getFuncNameNode(tree.rootNode.namedChild(0));

    expect(result.text).toEqual("func");
  });

  it("case3_with_more_than_one_identifier", () => {
    const docText = `
int ID1::ID2::ID3::func(int i) {}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getFuncNameNode(tree.rootNode.namedChild(0));

    expect(result.text).toEqual("func");
  });

  it("case4_operator", () => {
    const docText = `
std::ostream& operator<<(std::ostream& out, ITransientExpression const& expr) {
    expr.streamReconstructedExpression(out);
    return out;
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getFuncNameNode(tree.rootNode.namedChild(0));

    expect(result.text).toEqual("operator<<");
  });
})

describe("test_getSuperClassNameNode", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
class A: public B {};
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getSuperClassNameNode(tree.rootNode.namedChild(0));

    expect(result.text).toEqual("B");
  });

  it("case2_with_identifier", () => {
    const docText = `
class A: public ID1::B {};
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getSuperClassNameNode(tree.rootNode.namedChild(0));

    expect(result.text).toEqual("B");
  });
})

describe("test_getReturnNameNode", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
C1 speak() const {}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getReturnTypeNameNodes(tree.rootNode.namedChild(0));

    expect(result.map(item => item?.text ?? null)).toEqual(["C1"]);
  });

  it("case2_with_identifier", () => {
    const docText = `
ID1::ID2::C1 speak() const {}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getReturnTypeNameNodes(tree.rootNode.namedChild(0));

    expect(result.map(item => item?.text ?? null)).toEqual(["C1"]);
  });

  it("case3_template", () => {
    const docText = `
N1::T<N2::X, N3::Y> speak() const {}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getReturnTypeNameNodes(tree.rootNode.namedChild(0));

    expect(result.map(item => item?.text ?? null)).toEqual(["T", "X", "Y"]);
  });
})

describe("test_getParamNameNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
void speak(int p0, C1* p1, ID1::C2 p2) const {}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getParamTypeNameNodes(tree.rootNode.namedChild(0));

    expect(result.map(item => item?.text ?? null)).toEqual(["C1", "C2"]);
  });

  it("case2_template", () => {
    const docText = `
void speak(N1::T<N2::X, N3::Y> p0) const {}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getParamTypeNameNodes(tree.rootNode.namedChild(0));

    expect(result.map(item => item?.text ?? null)).toEqual(["T", "X", "Y"]);
  });
})

describe("test_getUsedTypeNameNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
void func(int p0, C1* p1, ID1::C2 p2) {
  C1* c1 = nullptr;
  ID1::C2 c2 = new C2();
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getUsedTypeNameNodes(tree.rootNode.namedChild(0));

    expect(result.map(item => item?.text ?? null)).toEqual(["C1", "ID1", "C2", "C2"]);
  });

  it("case2_as_namespace", () => {
    const docText = `
void func() {
  int i = C3::func(C4::f1);
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getUsedTypeNameNodes(tree.rootNode.namedChild(0));

    expect(result.map(item => item?.text ?? null)).toEqual(["C3", "C4"]);
  });
})

describe("test_getUsedFuncNameNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
void f() {
  f1();
  a = p.f2(i);
  C::f3();
  C::C::f4(i);
  p.x.f5();
  y.f6().z.f7();
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getUsedFuncNameNodes(tree.rootNode.namedChild(0));

    expect(result.map(item => item?.text ?? null).sort()).toEqual(["f1", "f2", "f3", "f4", "f5", "f6", "f7"]);
  });
})

describe("test_getFieldDefNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_class", () => {
    const docText = `
class MyClass {
public:
    // 公有属性
    int publicVar;

    // 常量公有属性
    const int publicConstVar;

    // 静态公有属性
    static int publicStaticVar;

protected:
    // 保护属性
    int protectedVar;

private:
    // 私有属性
    int privateVar;

    // 私有常量属性
    const int privateConstVar;
};
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getFieldDefNodes(tree.rootNode.namedChild(0));

    const expectedResult = [
      "int publicVar;",
      "const int publicConstVar;",
      "static int publicStaticVar;",
      "int protectedVar;",
      "int privateVar;",
      "const int privateConstVar;",
    ];

    expect(result.map(node => node.text)).toEqual(expectedResult);
  });
});

describe("test_getConstructorDefNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
class Calculator {
private:
    int initial_value;

public:
    Calculator() {}
    
    Calculator(int p0, int p1, int p2);

    Calculator(int p0) {}
    
    Calculator(int p0, int p1);

    int add(int a, int b) {
        return a + b + initial_value;
    }
};
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getConstructorDefNodes(tree.rootNode.namedChild(0));

    const expectedResult = ["Calculator() {}", "Calculator(int p0) {}"];

    expect(result.map(node => node.text)).toEqual(expectedResult);
  });
});

describe("test_getConstructorDeclNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
class Calculator {
private:
    int initial_value;

public:
    Calculator() {}
    
    Calculator(int p0, int p1, int p2);

    Calculator(int p0) {}
    
    Calculator(int p0, int p1);

    int add(int a, int b) {
        return a + b + initial_value;
    }
};
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getConstructorDeclNodes(tree.rootNode.namedChild(0));

    const expectedResult = ["Calculator(int p0, int p1, int p2)", "Calculator(int p0, int p1)"];

    expect(result.map(node => node.text)).toEqual(expectedResult);
  });
});

describe("test_getUsedFuncNameNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_template", () => {
    const docText = `
void f() {
    valueRef = static_cast<C>(m);
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getUsedFuncNameNodes(tree.rootNode.namedChild(0));

    expect(result.map(node => node.text)).toEqual(["static_cast"]);
  });
});

describe("test_getUsedOutsideVariableNameNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
string Car::print(A* a) {
    ::bar::B b = a->getB();
    ::bar::B* bp = a->getBP();
    if ( b ==  target_b ){
        return absl::StrCat(this->brand, this->model, year);
    }
    if ( bp != nullptr) {
        return bp.str();
    }
    f5();
    C c;
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const usedFuncResult = getUsedFuncNameNodes(tree.rootNode.namedChild(0));
    expect(usedFuncResult.map(node => node.text)).toEqual(["getB", "getBP", "StrCat", "str", "f5"]);

    const paramNameResult = getParameterNameNodes(tree.rootNode.namedChild(0));
    expect(paramNameResult.map(node => node.text)).toEqual(["a"]);

    const variableDeclNameResult = getVariableDeclNameNodes(tree.rootNode.namedChild(0));
    expect(variableDeclNameResult.map(node => node.text)).toEqual(["b", "bp", "c"]);

    const result = getUsedOutsideVariableNameNodes(tree.rootNode.namedChild(0));
    expect(result.map(node => node.text)).toEqual(["target_b", "year"]);
  });

  it("case2_lambda_expression", () => {
    const docText = `
static size_t find_first_separator(Catch::StringRef sr) {
    auto is_separator = []( char c ) {
        return c == ' ' || c == ':' || c == '=';
    };
    
    return Catch::StringRef::npos;
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = getUsedOutsideVariableNameNodes(tree.rootNode.namedChild(0));
    expect(result.map(node => node.text)).toEqual(["npos"]);
  });
});