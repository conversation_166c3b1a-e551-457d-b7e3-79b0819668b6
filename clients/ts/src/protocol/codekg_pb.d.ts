// package: protocol
// file: codekg.proto

import * as jspb from "google-protobuf";

export class Empty extends jspb.Message {
  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Empty.AsObject;
  static toObject(includeInstance: boolean, msg: Empty): Empty.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: Empty, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Empty;
  static deserializeBinaryFromReader(message: Empty, reader: jspb.BinaryReader): Empty;
}

export namespace Empty {
  export type AsObject = {
  }
}

export class Error extends jspb.Message {
  getMessage(): string;
  setMessage(value: string): void;

  getStack(): string;
  setStack(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Error.AsObject;
  static toObject(includeInstance: boolean, msg: Error): Error.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: Error, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Error;
  static deserializeBinaryFromReader(message: Error, reader: jspb.BinaryReader): Error;
}

export namespace Error {
  export type AsObject = {
    message: string,
    stack: string,
  }
}

export class RefreshTokenRequest extends jspb.Message {
  getToken(): string;
  setToken(value: string): void;

  getUserId(): string;
  setUserId(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): RefreshTokenRequest.AsObject;
  static toObject(includeInstance: boolean, msg: RefreshTokenRequest): RefreshTokenRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: RefreshTokenRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): RefreshTokenRequest;
  static deserializeBinaryFromReader(message: RefreshTokenRequest, reader: jspb.BinaryReader): RefreshTokenRequest;
}

export namespace RefreshTokenRequest {
  export type AsObject = {
    token: string,
    userId: string,
  }
}

export class RefreshTokenResponse extends jspb.Message {
  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): RefreshTokenResponse.AsObject;
  static toObject(includeInstance: boolean, msg: RefreshTokenResponse): RefreshTokenResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: RefreshTokenResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): RefreshTokenResponse;
  static deserializeBinaryFromReader(message: RefreshTokenResponse, reader: jspb.BinaryReader): RefreshTokenResponse;
}

export namespace RefreshTokenResponse {
  export type AsObject = {
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export class Project extends jspb.Message {
  getProjectId(): string;
  setProjectId(value: string): void;

  getStoragePath(): string;
  setStoragePath(value: string): void;

  getIgnoreFile(): string;
  setIgnoreFile(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Project.AsObject;
  static toObject(includeInstance: boolean, msg: Project): Project.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: Project, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Project;
  static deserializeBinaryFromReader(message: Project, reader: jspb.BinaryReader): Project;
}

export namespace Project {
  export type AsObject = {
    projectId: string,
    storagePath: string,
    ignoreFile: string,
  }
}

export class InitRequest extends jspb.Message {
  clearProjectIdsList(): void;
  getProjectIdsList(): Array<string>;
  setProjectIdsList(value: Array<string>): void;
  addProjectIds(value: string, index?: number): string;

  getIgnoreFileLimit(): boolean;
  setIgnoreFileLimit(value: boolean): void;

  clearProjectsList(): void;
  getProjectsList(): Array<Project>;
  setProjectsList(value: Array<Project>): void;
  addProjects(value?: Project, index?: number): Project;

  getUserId(): string;
  setUserId(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): InitRequest.AsObject;
  static toObject(includeInstance: boolean, msg: InitRequest): InitRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: InitRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): InitRequest;
  static deserializeBinaryFromReader(message: InitRequest, reader: jspb.BinaryReader): InitRequest;
}

export namespace InitRequest {
  export type AsObject = {
    projectIdsList: Array<string>,
    ignoreFileLimit: boolean,
    projectsList: Array<Project.AsObject>,
    userId: string,
  }
}

export class InitResponse extends jspb.Message {
  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): InitResponse.AsObject;
  static toObject(includeInstance: boolean, msg: InitResponse): InitResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: InitResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): InitResponse;
  static deserializeBinaryFromReader(message: InitResponse, reader: jspb.BinaryReader): InitResponse;
}

export namespace InitResponse {
  export type AsObject = {
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export class VirtualProject extends jspb.Message {
  getProjectId(): string;
  setProjectId(value: string): void;

  getUri(): string;
  setUri(value: string): void;

  clearRelativeGlobsToLoadList(): void;
  getRelativeGlobsToLoadList(): Array<string>;
  setRelativeGlobsToLoadList(value: Array<string>): void;
  addRelativeGlobsToLoad(value: string, index?: number): string;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): VirtualProject.AsObject;
  static toObject(includeInstance: boolean, msg: VirtualProject): VirtualProject.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: VirtualProject, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): VirtualProject;
  static deserializeBinaryFromReader(message: VirtualProject, reader: jspb.BinaryReader): VirtualProject;
}

export namespace VirtualProject {
  export type AsObject = {
    projectId: string,
    uri: string,
    relativeGlobsToLoadList: Array<string>,
  }
}

export class InitVirtualProjectsRequest extends jspb.Message {
  clearProjectsList(): void;
  getProjectsList(): Array<VirtualProject>;
  setProjectsList(value: Array<VirtualProject>): void;
  addProjects(value?: VirtualProject, index?: number): VirtualProject;

  getLoadFilesFromFs(): boolean;
  setLoadFilesFromFs(value: boolean): void;

  getUserId(): string;
  setUserId(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): InitVirtualProjectsRequest.AsObject;
  static toObject(includeInstance: boolean, msg: InitVirtualProjectsRequest): InitVirtualProjectsRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: InitVirtualProjectsRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): InitVirtualProjectsRequest;
  static deserializeBinaryFromReader(message: InitVirtualProjectsRequest, reader: jspb.BinaryReader): InitVirtualProjectsRequest;
}

export namespace InitVirtualProjectsRequest {
  export type AsObject = {
    projectsList: Array<VirtualProject.AsObject>,
    loadFilesFromFs: boolean,
    userId: string,
  }
}

export class InitVirtualProjectsResponse extends jspb.Message {
  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): InitVirtualProjectsResponse.AsObject;
  static toObject(includeInstance: boolean, msg: InitVirtualProjectsResponse): InitVirtualProjectsResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: InitVirtualProjectsResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): InitVirtualProjectsResponse;
  static deserializeBinaryFromReader(message: InitVirtualProjectsResponse, reader: jspb.BinaryReader): InitVirtualProjectsResponse;
}

export namespace InitVirtualProjectsResponse {
  export type AsObject = {
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export class Document extends jspb.Message {
  getUri(): string;
  setUri(value: string): void;

  getName(): string;
  setName(value: string): void;

  getContent(): string;
  setContent(value: string): void;

  getProjectId(): string;
  setProjectId(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Document.AsObject;
  static toObject(includeInstance: boolean, msg: Document): Document.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: Document, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Document;
  static deserializeBinaryFromReader(message: Document, reader: jspb.BinaryReader): Document;
}

export namespace Document {
  export type AsObject = {
    uri: string,
    name: string,
    content: string,
    projectId: string,
  }
}

export class DocumentRequest extends jspb.Message {
  clearFilePathsList(): void;
  getFilePathsList(): Array<string>;
  setFilePathsList(value: Array<string>): void;
  addFilePaths(value: string, index?: number): string;

  clearDocumentsList(): void;
  getDocumentsList(): Array<Document>;
  setDocumentsList(value: Array<Document>): void;
  addDocuments(value?: Document, index?: number): Document;

  getUserid(): string;
  setUserid(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): DocumentRequest.AsObject;
  static toObject(includeInstance: boolean, msg: DocumentRequest): DocumentRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: DocumentRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): DocumentRequest;
  static deserializeBinaryFromReader(message: DocumentRequest, reader: jspb.BinaryReader): DocumentRequest;
}

export namespace DocumentRequest {
  export type AsObject = {
    filePathsList: Array<string>,
    documentsList: Array<Document.AsObject>,
    userid: string,
  }
}

export class DocumentResponse extends jspb.Message {
  getErrMsgsMap(): jspb.Map<string, string>;
  clearErrMsgsMap(): void;
  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): DocumentResponse.AsObject;
  static toObject(includeInstance: boolean, msg: DocumentResponse): DocumentResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: DocumentResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): DocumentResponse;
  static deserializeBinaryFromReader(message: DocumentResponse, reader: jspb.BinaryReader): DocumentResponse;
}

export namespace DocumentResponse {
  export type AsObject = {
    errMsgsMap: Array<[string, string]>,
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export class DeleteIndexRequest extends jspb.Message {
  getProjectId(): string;
  setProjectId(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): DeleteIndexRequest.AsObject;
  static toObject(includeInstance: boolean, msg: DeleteIndexRequest): DeleteIndexRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: DeleteIndexRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): DeleteIndexRequest;
  static deserializeBinaryFromReader(message: DeleteIndexRequest, reader: jspb.BinaryReader): DeleteIndexRequest;
}

export namespace DeleteIndexRequest {
  export type AsObject = {
    projectId: string,
  }
}

export class DeleteIndexResponse extends jspb.Message {
  getSucceed(): boolean;
  setSucceed(value: boolean): void;

  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): DeleteIndexResponse.AsObject;
  static toObject(includeInstance: boolean, msg: DeleteIndexResponse): DeleteIndexResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: DeleteIndexResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): DeleteIndexResponse;
  static deserializeBinaryFromReader(message: DeleteIndexResponse, reader: jspb.BinaryReader): DeleteIndexResponse;
}

export namespace DeleteIndexResponse {
  export type AsObject = {
    succeed: boolean,
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export class CurrentEditorInfo extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getProjectId(): string;
  setProjectId(value: string): void;

  getCursorLine(): number;
  setCursorLine(value: number): void;

  hasSelectCodeRange(): boolean;
  clearSelectCodeRange(): void;
  getSelectCodeRange(): Range | undefined;
  setSelectCodeRange(value?: Range): void;

  getSelectCodeContent(): string;
  setSelectCodeContent(value: string): void;

  hasVisibleCodeRange(): boolean;
  clearVisibleCodeRange(): void;
  getVisibleCodeRange(): Range | undefined;
  setVisibleCodeRange(value?: Range): void;

  getVisibleCodeContent(): string;
  setVisibleCodeContent(value: string): void;

  getUserFileContent(): string;
  setUserFileContent(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CurrentEditorInfo.AsObject;
  static toObject(includeInstance: boolean, msg: CurrentEditorInfo): CurrentEditorInfo.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: CurrentEditorInfo, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CurrentEditorInfo;
  static deserializeBinaryFromReader(message: CurrentEditorInfo, reader: jspb.BinaryReader): CurrentEditorInfo;
}

export namespace CurrentEditorInfo {
  export type AsObject = {
    filePath: string,
    projectId: string,
    cursorLine: number,
    selectCodeRange?: Range.AsObject,
    selectCodeContent: string,
    visibleCodeRange?: Range.AsObject,
    visibleCodeContent: string,
    userFileContent: string,
  }
}

export class RetrieveEntityRequest extends jspb.Message {
  clearProjectIdsList(): void;
  getProjectIdsList(): Array<string>;
  setProjectIdsList(value: Array<string>): void;
  addProjectIds(value: string, index?: number): string;

  getUserMessage(): string;
  setUserMessage(value: string): void;

  hasEditorInfo(): boolean;
  clearEditorInfo(): void;
  getEditorInfo(): CurrentEditorInfo | undefined;
  setEditorInfo(value?: CurrentEditorInfo): void;

  clearExpectEntityTypesList(): void;
  getExpectEntityTypesList(): Array<EntityTypeMap[keyof EntityTypeMap]>;
  setExpectEntityTypesList(value: Array<EntityTypeMap[keyof EntityTypeMap]>): void;
  addExpectEntityTypes(value: EntityTypeMap[keyof EntityTypeMap], index?: number): EntityTypeMap[keyof EntityTypeMap];

  getEntityNum(): number;
  setEntityNum(value: number): void;

  getIntent(): string;
  setIntent(value: string): void;

  clearFolderPathsList(): void;
  getFolderPathsList(): Array<string>;
  setFolderPathsList(value: Array<string>): void;
  addFolderPaths(value: string, index?: number): string;

  getUserId(): string;
  setUserId(value: string): void;

  getSessionId(): string;
  setSessionId(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): RetrieveEntityRequest.AsObject;
  static toObject(includeInstance: boolean, msg: RetrieveEntityRequest): RetrieveEntityRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: RetrieveEntityRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): RetrieveEntityRequest;
  static deserializeBinaryFromReader(message: RetrieveEntityRequest, reader: jspb.BinaryReader): RetrieveEntityRequest;
}

export namespace RetrieveEntityRequest {
  export type AsObject = {
    projectIdsList: Array<string>,
    userMessage: string,
    editorInfo?: CurrentEditorInfo.AsObject,
    expectEntityTypesList: Array<EntityTypeMap[keyof EntityTypeMap]>,
    entityNum: number,
    intent: string,
    folderPathsList: Array<string>,
    userId: string,
    sessionId: string,
  }
}

export class Entity extends jspb.Message {
  getProjectId(): string;
  setProjectId(value: string): void;

  getEntityId(): string;
  setEntityId(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Entity.AsObject;
  static toObject(includeInstance: boolean, msg: Entity): Entity.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: Entity, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Entity;
  static deserializeBinaryFromReader(message: Entity, reader: jspb.BinaryReader): Entity;
}

export namespace Entity {
  export type AsObject = {
    projectId: string,
    entityId: string,
  }
}

export class RetrieveEntityResponse extends jspb.Message {
  clearEntitiesByUserMessageList(): void;
  getEntitiesByUserMessageList(): Array<Entity>;
  setEntitiesByUserMessageList(value: Array<Entity>): void;
  addEntitiesByUserMessage(value?: Entity, index?: number): Entity;

  clearQueryEmbeddingModelsList(): void;
  getQueryEmbeddingModelsList(): Array<string>;
  setQueryEmbeddingModelsList(value: Array<string>): void;
  addQueryEmbeddingModels(value: string, index?: number): string;

  clearVectorEmbeddingModelsList(): void;
  getVectorEmbeddingModelsList(): Array<string>;
  setVectorEmbeddingModelsList(value: Array<string>): void;
  addVectorEmbeddingModels(value: string, index?: number): string;

  clearEntityChunkingMethodsList(): void;
  getEntityChunkingMethodsList(): Array<string>;
  setEntityChunkingMethodsList(value: Array<string>): void;
  addEntityChunkingMethods(value: string, index?: number): string;

  getEmptyRecallReason(): string;
  setEmptyRecallReason(value: string): void;

  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): RetrieveEntityResponse.AsObject;
  static toObject(includeInstance: boolean, msg: RetrieveEntityResponse): RetrieveEntityResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: RetrieveEntityResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): RetrieveEntityResponse;
  static deserializeBinaryFromReader(message: RetrieveEntityResponse, reader: jspb.BinaryReader): RetrieveEntityResponse;
}

export namespace RetrieveEntityResponse {
  export type AsObject = {
    entitiesByUserMessageList: Array<Entity.AsObject>,
    queryEmbeddingModelsList: Array<string>,
    vectorEmbeddingModelsList: Array<string>,
    entityChunkingMethodsList: Array<string>,
    emptyRecallReason: string,
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export class RetrieveRelationRequest extends jspb.Message {
  clearEntitiesByUserMessageList(): void;
  getEntitiesByUserMessageList(): Array<Entity>;
  setEntitiesByUserMessageList(value: Array<Entity>): void;
  addEntitiesByUserMessage(value?: Entity, index?: number): Entity;

  hasEditorInfo(): boolean;
  clearEditorInfo(): void;
  getEditorInfo(): CurrentEditorInfo | undefined;
  setEditorInfo(value?: CurrentEditorInfo): void;

  getUserId(): string;
  setUserId(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): RetrieveRelationRequest.AsObject;
  static toObject(includeInstance: boolean, msg: RetrieveRelationRequest): RetrieveRelationRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: RetrieveRelationRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): RetrieveRelationRequest;
  static deserializeBinaryFromReader(message: RetrieveRelationRequest, reader: jspb.BinaryReader): RetrieveRelationRequest;
}

export namespace RetrieveRelationRequest {
  export type AsObject = {
    entitiesByUserMessageList: Array<Entity.AsObject>,
    editorInfo?: CurrentEditorInfo.AsObject,
    userId: string,
  }
}

export class CodeChunkVariable extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getStartLine(): number;
  setStartLine(value: number): void;

  getEndLine(): number;
  setEndLine(value: number): void;

  getContent(): string;
  setContent(value: string): void;

  getProvider(): string;
  setProvider(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CodeChunkVariable.AsObject;
  static toObject(includeInstance: boolean, msg: CodeChunkVariable): CodeChunkVariable.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: CodeChunkVariable, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CodeChunkVariable;
  static deserializeBinaryFromReader(message: CodeChunkVariable, reader: jspb.BinaryReader): CodeChunkVariable;
}

export namespace CodeChunkVariable {
  export type AsObject = {
    filePath: string,
    startLine: number,
    endLine: number,
    content: string,
    provider: string,
  }
}

export class TextVariable extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getStartLine(): number;
  setStartLine(value: number): void;

  getEndLine(): number;
  setEndLine(value: number): void;

  getContent(): string;
  setContent(value: string): void;

  getProvider(): string;
  setProvider(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): TextVariable.AsObject;
  static toObject(includeInstance: boolean, msg: TextVariable): TextVariable.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: TextVariable, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): TextVariable;
  static deserializeBinaryFromReader(message: TextVariable, reader: jspb.BinaryReader): TextVariable;
}

export namespace TextVariable {
  export type AsObject = {
    filePath: string,
    startLine: number,
    endLine: number,
    content: string,
    provider: string,
  }
}

export class UsefulFileInfo extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getContent(): string;
  setContent(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): UsefulFileInfo.AsObject;
  static toObject(includeInstance: boolean, msg: UsefulFileInfo): UsefulFileInfo.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: UsefulFileInfo, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): UsefulFileInfo;
  static deserializeBinaryFromReader(message: UsefulFileInfo, reader: jspb.BinaryReader): UsefulFileInfo;
}

export namespace UsefulFileInfo {
  export type AsObject = {
    filePath: string,
    content: string,
  }
}

export class FolderVariable extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getFolderTree(): string;
  setFolderTree(value: string): void;

  clearUsefulFilesList(): void;
  getUsefulFilesList(): Array<UsefulFileInfo>;
  setUsefulFilesList(value: Array<UsefulFileInfo>): void;
  addUsefulFiles(value?: UsefulFileInfo, index?: number): UsefulFileInfo;

  getProvider(): string;
  setProvider(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): FolderVariable.AsObject;
  static toObject(includeInstance: boolean, msg: FolderVariable): FolderVariable.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: FolderVariable, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): FolderVariable;
  static deserializeBinaryFromReader(message: FolderVariable, reader: jspb.BinaryReader): FolderVariable;
}

export namespace FolderVariable {
  export type AsObject = {
    filePath: string,
    folderTree: string,
    usefulFilesList: Array<UsefulFileInfo.AsObject>,
    provider: string,
  }
}

export class FileVariable extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getContent(): string;
  setContent(value: string): void;

  getComment(): string;
  setComment(value: string): void;

  getProvider(): string;
  setProvider(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): FileVariable.AsObject;
  static toObject(includeInstance: boolean, msg: FileVariable): FileVariable.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: FileVariable, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): FileVariable;
  static deserializeBinaryFromReader(message: FileVariable, reader: jspb.BinaryReader): FileVariable;
}

export namespace FileVariable {
  export type AsObject = {
    filePath: string,
    content: string,
    comment: string,
    provider: string,
  }
}

export class FileTopLevelVariable extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getStartLine(): number;
  setStartLine(value: number): void;

  getEndLine(): number;
  setEndLine(value: number): void;

  getContent(): string;
  setContent(value: string): void;

  getProvider(): string;
  setProvider(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): FileTopLevelVariable.AsObject;
  static toObject(includeInstance: boolean, msg: FileTopLevelVariable): FileTopLevelVariable.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: FileTopLevelVariable, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): FileTopLevelVariable;
  static deserializeBinaryFromReader(message: FileTopLevelVariable, reader: jspb.BinaryReader): FileTopLevelVariable;
}

export namespace FileTopLevelVariable {
  export type AsObject = {
    filePath: string,
    startLine: number,
    endLine: number,
    content: string,
    provider: string,
  }
}

export class Member extends jspb.Message {
  getContent(): string;
  setContent(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Member.AsObject;
  static toObject(includeInstance: boolean, msg: Member): Member.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: Member, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Member;
  static deserializeBinaryFromReader(message: Member, reader: jspb.BinaryReader): Member;
}

export namespace Member {
  export type AsObject = {
    content: string,
  }
}

export class ClassVariable extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getStartLine(): number;
  setStartLine(value: number): void;

  getEndLine(): number;
  setEndLine(value: number): void;

  getName(): string;
  setName(value: string): void;

  getComment(): string;
  setComment(value: string): void;

  getContent(): string;
  setContent(value: string): void;

  clearMembersList(): void;
  getMembersList(): Array<Member>;
  setMembersList(value: Array<Member>): void;
  addMembers(value?: Member, index?: number): Member;

  clearMethodsList(): void;
  getMethodsList(): Array<MethodInfo>;
  setMethodsList(value: Array<MethodInfo>): void;
  addMethods(value?: MethodInfo, index?: number): MethodInfo;

  clearTestFunctionsList(): void;
  getTestFunctionsList(): Array<MethodInfo>;
  setTestFunctionsList(value: Array<MethodInfo>): void;
  addTestFunctions(value?: MethodInfo, index?: number): MethodInfo;

  getProvider(): string;
  setProvider(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ClassVariable.AsObject;
  static toObject(includeInstance: boolean, msg: ClassVariable): ClassVariable.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: ClassVariable, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ClassVariable;
  static deserializeBinaryFromReader(message: ClassVariable, reader: jspb.BinaryReader): ClassVariable;
}

export namespace ClassVariable {
  export type AsObject = {
    filePath: string,
    startLine: number,
    endLine: number,
    name: string,
    comment: string,
    content: string,
    membersList: Array<Member.AsObject>,
    methodsList: Array<MethodInfo.AsObject>,
    testFunctionsList: Array<MethodInfo.AsObject>,
    provider: string,
  }
}

export class ClassTopLevelVariable extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getStartLine(): number;
  setStartLine(value: number): void;

  getEndLine(): number;
  setEndLine(value: number): void;

  getContent(): string;
  setContent(value: string): void;

  getProvider(): string;
  setProvider(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ClassTopLevelVariable.AsObject;
  static toObject(includeInstance: boolean, msg: ClassTopLevelVariable): ClassTopLevelVariable.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: ClassTopLevelVariable, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ClassTopLevelVariable;
  static deserializeBinaryFromReader(message: ClassTopLevelVariable, reader: jspb.BinaryReader): ClassTopLevelVariable;
}

export namespace ClassTopLevelVariable {
  export type AsObject = {
    filePath: string,
    startLine: number,
    endLine: number,
    content: string,
    provider: string,
  }
}

export class MethodInfo extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getStartLine(): number;
  setStartLine(value: number): void;

  getEndLine(): number;
  setEndLine(value: number): void;

  getName(): string;
  setName(value: string): void;

  getSignature(): string;
  setSignature(value: string): void;

  getComment(): string;
  setComment(value: string): void;

  getContent(): string;
  setContent(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): MethodInfo.AsObject;
  static toObject(includeInstance: boolean, msg: MethodInfo): MethodInfo.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: MethodInfo, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): MethodInfo;
  static deserializeBinaryFromReader(message: MethodInfo, reader: jspb.BinaryReader): MethodInfo;
}

export namespace MethodInfo {
  export type AsObject = {
    filePath: string,
    startLine: number,
    endLine: number,
    name: string,
    signature: string,
    comment: string,
    content: string,
  }
}

export class MethodVariable extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getStartLine(): number;
  setStartLine(value: number): void;

  getEndLine(): number;
  setEndLine(value: number): void;

  getName(): string;
  setName(value: string): void;

  getSignature(): string;
  setSignature(value: string): void;

  getComment(): string;
  setComment(value: string): void;

  getContent(): string;
  setContent(value: string): void;

  clearCallersList(): void;
  getCallersList(): Array<MethodInfo>;
  setCallersList(value: Array<MethodInfo>): void;
  addCallers(value?: MethodInfo, index?: number): MethodInfo;

  clearCalleesList(): void;
  getCalleesList(): Array<MethodInfo>;
  setCalleesList(value: Array<MethodInfo>): void;
  addCallees(value?: MethodInfo, index?: number): MethodInfo;

  clearTestFunctionsList(): void;
  getTestFunctionsList(): Array<MethodInfo>;
  setTestFunctionsList(value: Array<MethodInfo>): void;
  addTestFunctions(value?: MethodInfo, index?: number): MethodInfo;

  getProvider(): string;
  setProvider(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): MethodVariable.AsObject;
  static toObject(includeInstance: boolean, msg: MethodVariable): MethodVariable.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: MethodVariable, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): MethodVariable;
  static deserializeBinaryFromReader(message: MethodVariable, reader: jspb.BinaryReader): MethodVariable;
}

export namespace MethodVariable {
  export type AsObject = {
    filePath: string,
    startLine: number,
    endLine: number,
    name: string,
    signature: string,
    comment: string,
    content: string,
    callersList: Array<MethodInfo.AsObject>,
    calleesList: Array<MethodInfo.AsObject>,
    testFunctionsList: Array<MethodInfo.AsObject>,
    provider: string,
  }
}

export class Variable extends jspb.Message {
  hasCodeChunk(): boolean;
  clearCodeChunk(): void;
  getCodeChunk(): CodeChunkVariable | undefined;
  setCodeChunk(value?: CodeChunkVariable): void;

  hasText(): boolean;
  clearText(): void;
  getText(): TextVariable | undefined;
  setText(value?: TextVariable): void;

  hasFolder(): boolean;
  clearFolder(): void;
  getFolder(): FolderVariable | undefined;
  setFolder(value?: FolderVariable): void;

  hasFile(): boolean;
  clearFile(): void;
  getFile(): FileVariable | undefined;
  setFile(value?: FileVariable): void;

  hasFileTopLevel(): boolean;
  clearFileTopLevel(): void;
  getFileTopLevel(): FileTopLevelVariable | undefined;
  setFileTopLevel(value?: FileTopLevelVariable): void;

  hasClass(): boolean;
  clearClass(): void;
  getClass(): ClassVariable | undefined;
  setClass(value?: ClassVariable): void;

  hasClassTopLevel(): boolean;
  clearClassTopLevel(): void;
  getClassTopLevel(): ClassTopLevelVariable | undefined;
  setClassTopLevel(value?: ClassTopLevelVariable): void;

  hasMethod(): boolean;
  clearMethod(): void;
  getMethod(): MethodVariable | undefined;
  setMethod(value?: MethodVariable): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Variable.AsObject;
  static toObject(includeInstance: boolean, msg: Variable): Variable.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: Variable, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Variable;
  static deserializeBinaryFromReader(message: Variable, reader: jspb.BinaryReader): Variable;
}

export namespace Variable {
  export type AsObject = {
    codeChunk?: CodeChunkVariable.AsObject,
    text?: TextVariable.AsObject,
    folder?: FolderVariable.AsObject,
    file?: FileVariable.AsObject,
    fileTopLevel?: FileTopLevelVariable.AsObject,
    pb_class?: ClassVariable.AsObject,
    classTopLevel?: ClassTopLevelVariable.AsObject,
    method?: MethodVariable.AsObject,
  }
}

export class SelectedMethodInfo extends jspb.Message {
  getStartLine(): number;
  setStartLine(value: number): void;

  getEndLine(): number;
  setEndLine(value: number): void;

  getName(): string;
  setName(value: string): void;

  getComment(): string;
  setComment(value: string): void;

  getContent(): string;
  setContent(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): SelectedMethodInfo.AsObject;
  static toObject(includeInstance: boolean, msg: SelectedMethodInfo): SelectedMethodInfo.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: SelectedMethodInfo, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): SelectedMethodInfo;
  static deserializeBinaryFromReader(message: SelectedMethodInfo, reader: jspb.BinaryReader): SelectedMethodInfo;
}

export namespace SelectedMethodInfo {
  export type AsObject = {
    startLine: number,
    endLine: number,
    name: string,
    comment: string,
    content: string,
  }
}

export class SelectedClassInfo extends jspb.Message {
  getStartLine(): number;
  setStartLine(value: number): void;

  getEndLine(): number;
  setEndLine(value: number): void;

  getName(): string;
  setName(value: string): void;

  getComment(): string;
  setComment(value: string): void;

  getContent(): string;
  setContent(value: string): void;

  clearSelectedMethodsList(): void;
  getSelectedMethodsList(): Array<SelectedMethodInfo>;
  setSelectedMethodsList(value: Array<SelectedMethodInfo>): void;
  addSelectedMethods(value?: SelectedMethodInfo, index?: number): SelectedMethodInfo;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): SelectedClassInfo.AsObject;
  static toObject(includeInstance: boolean, msg: SelectedClassInfo): SelectedClassInfo.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: SelectedClassInfo, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): SelectedClassInfo;
  static deserializeBinaryFromReader(message: SelectedClassInfo, reader: jspb.BinaryReader): SelectedClassInfo;
}

export namespace SelectedClassInfo {
  export type AsObject = {
    startLine: number,
    endLine: number,
    name: string,
    comment: string,
    content: string,
    selectedMethodsList: Array<SelectedMethodInfo.AsObject>,
  }
}

export class CurrentEditorEntity extends jspb.Message {
  clearSelectedClassesList(): void;
  getSelectedClassesList(): Array<SelectedClassInfo>;
  setSelectedClassesList(value: Array<SelectedClassInfo>): void;
  addSelectedClasses(value?: SelectedClassInfo, index?: number): SelectedClassInfo;

  clearSelectedMethodsList(): void;
  getSelectedMethodsList(): Array<SelectedMethodInfo>;
  setSelectedMethodsList(value: Array<SelectedMethodInfo>): void;
  addSelectedMethods(value?: SelectedMethodInfo, index?: number): SelectedMethodInfo;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CurrentEditorEntity.AsObject;
  static toObject(includeInstance: boolean, msg: CurrentEditorEntity): CurrentEditorEntity.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: CurrentEditorEntity, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CurrentEditorEntity;
  static deserializeBinaryFromReader(message: CurrentEditorEntity, reader: jspb.BinaryReader): CurrentEditorEntity;
}

export namespace CurrentEditorEntity {
  export type AsObject = {
    selectedClassesList: Array<SelectedClassInfo.AsObject>,
    selectedMethodsList: Array<SelectedMethodInfo.AsObject>,
  }
}

export class Range extends jspb.Message {
  getStartLine(): number;
  setStartLine(value: number): void;

  getEndLine(): number;
  setEndLine(value: number): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Range.AsObject;
  static toObject(includeInstance: boolean, msg: Range): Range.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: Range, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Range;
  static deserializeBinaryFromReader(message: Range, reader: jspb.BinaryReader): Range;
}

export namespace Range {
  export type AsObject = {
    startLine: number,
    endLine: number,
  }
}

export class RefClassInfo extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getStartLine(): string;
  setStartLine(value: string): void;

  getEndLine(): string;
  setEndLine(value: string): void;

  getName(): string;
  setName(value: string): void;

  getContent(): string;
  setContent(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): RefClassInfo.AsObject;
  static toObject(includeInstance: boolean, msg: RefClassInfo): RefClassInfo.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: RefClassInfo, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): RefClassInfo;
  static deserializeBinaryFromReader(message: RefClassInfo, reader: jspb.BinaryReader): RefClassInfo;
}

export namespace RefClassInfo {
  export type AsObject = {
    filePath: string,
    startLine: string,
    endLine: string,
    name: string,
    content: string,
  }
}

export class RefTypeInfo extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getStartLine(): string;
  setStartLine(value: string): void;

  getEndLine(): string;
  setEndLine(value: string): void;

  getName(): string;
  setName(value: string): void;

  getContent(): string;
  setContent(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): RefTypeInfo.AsObject;
  static toObject(includeInstance: boolean, msg: RefTypeInfo): RefTypeInfo.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: RefTypeInfo, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): RefTypeInfo;
  static deserializeBinaryFromReader(message: RefTypeInfo, reader: jspb.BinaryReader): RefTypeInfo;
}

export namespace RefTypeInfo {
  export type AsObject = {
    filePath: string,
    startLine: string,
    endLine: string,
    name: string,
    content: string,
  }
}

export class CurrentEditorVariable extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getContent(): string;
  setContent(value: string): void;

  getCursorLine(): number;
  setCursorLine(value: number): void;

  hasSelectRange(): boolean;
  clearSelectRange(): void;
  getSelectRange(): Range | undefined;
  setSelectRange(value?: Range): void;

  hasVisibleRange(): boolean;
  clearVisibleRange(): void;
  getVisibleRange(): Range | undefined;
  setVisibleRange(value?: Range): void;

  hasEntity(): boolean;
  clearEntity(): void;
  getEntity(): CurrentEditorEntity | undefined;
  setEntity(value?: CurrentEditorEntity): void;

  clearCalleesList(): void;
  getCalleesList(): Array<MethodInfo>;
  setCalleesList(value: Array<MethodInfo>): void;
  addCallees(value?: MethodInfo, index?: number): MethodInfo;

  clearRefClassesList(): void;
  getRefClassesList(): Array<RefClassInfo>;
  setRefClassesList(value: Array<RefClassInfo>): void;
  addRefClasses(value?: RefClassInfo, index?: number): RefClassInfo;

  clearRefTypesList(): void;
  getRefTypesList(): Array<RefTypeInfo>;
  setRefTypesList(value: Array<RefTypeInfo>): void;
  addRefTypes(value?: RefTypeInfo, index?: number): RefTypeInfo;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CurrentEditorVariable.AsObject;
  static toObject(includeInstance: boolean, msg: CurrentEditorVariable): CurrentEditorVariable.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: CurrentEditorVariable, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CurrentEditorVariable;
  static deserializeBinaryFromReader(message: CurrentEditorVariable, reader: jspb.BinaryReader): CurrentEditorVariable;
}

export namespace CurrentEditorVariable {
  export type AsObject = {
    filePath: string,
    content: string,
    cursorLine: number,
    selectRange?: Range.AsObject,
    visibleRange?: Range.AsObject,
    entity?: CurrentEditorEntity.AsObject,
    calleesList: Array<MethodInfo.AsObject>,
    refClassesList: Array<RefClassInfo.AsObject>,
    refTypesList: Array<RefTypeInfo.AsObject>,
  }
}

export class Reference extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getStartLine(): number;
  setStartLine(value: number): void;

  getEndLine(): number;
  setEndLine(value: number): void;

  getUri(): string;
  setUri(value: string): void;

  getName(): string;
  setName(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Reference.AsObject;
  static toObject(includeInstance: boolean, msg: Reference): Reference.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: Reference, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Reference;
  static deserializeBinaryFromReader(message: Reference, reader: jspb.BinaryReader): Reference;
}

export namespace Reference {
  export type AsObject = {
    filePath: string,
    startLine: number,
    endLine: number,
    uri: string,
    name: string,
  }
}

export class RetrieveRelationResponse extends jspb.Message {
  hasCurrentEditor(): boolean;
  clearCurrentEditor(): void;
  getCurrentEditor(): CurrentEditorVariable | undefined;
  setCurrentEditor(value?: CurrentEditorVariable): void;

  clearVariablesList(): void;
  getVariablesList(): Array<Variable>;
  setVariablesList(value: Array<Variable>): void;
  addVariables(value?: Variable, index?: number): Variable;

  clearReferencesList(): void;
  getReferencesList(): Array<Reference>;
  setReferencesList(value: Array<Reference>): void;
  addReferences(value?: Reference, index?: number): Reference;

  getJsonResult(): string;
  setJsonResult(value: string): void;

  getErrMetric(): string;
  setErrMetric(value: string): void;

  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): RetrieveRelationResponse.AsObject;
  static toObject(includeInstance: boolean, msg: RetrieveRelationResponse): RetrieveRelationResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: RetrieveRelationResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): RetrieveRelationResponse;
  static deserializeBinaryFromReader(message: RetrieveRelationResponse, reader: jspb.BinaryReader): RetrieveRelationResponse;
}

export namespace RetrieveRelationResponse {
  export type AsObject = {
    currentEditor?: CurrentEditorVariable.AsObject,
    variablesList: Array<Variable.AsObject>,
    referencesList: Array<Reference.AsObject>,
    jsonResult: string,
    errMetric: string,
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export class GetBuildStatusRequest extends jspb.Message {
  clearProjectIdsList(): void;
  getProjectIdsList(): Array<string>;
  setProjectIdsList(value: Array<string>): void;
  addProjectIds(value: string, index?: number): string;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): GetBuildStatusRequest.AsObject;
  static toObject(includeInstance: boolean, msg: GetBuildStatusRequest): GetBuildStatusRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: GetBuildStatusRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): GetBuildStatusRequest;
  static deserializeBinaryFromReader(message: GetBuildStatusRequest, reader: jspb.BinaryReader): GetBuildStatusRequest;
}

export namespace GetBuildStatusRequest {
  export type AsObject = {
    projectIdsList: Array<string>,
  }
}

export class DocumentBuildStatus extends jspb.Message {
  getStatus(): BuildStatusMap[keyof BuildStatusMap];
  setStatus(value: BuildStatusMap[keyof BuildStatusMap]): void;

  getUri(): string;
  setUri(value: string): void;

  getName(): string;
  setName(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): DocumentBuildStatus.AsObject;
  static toObject(includeInstance: boolean, msg: DocumentBuildStatus): DocumentBuildStatus.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: DocumentBuildStatus, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): DocumentBuildStatus;
  static deserializeBinaryFromReader(message: DocumentBuildStatus, reader: jspb.BinaryReader): DocumentBuildStatus;
}

export namespace DocumentBuildStatus {
  export type AsObject = {
    status: BuildStatusMap[keyof BuildStatusMap],
    uri: string,
    name: string,
  }
}

export class ProjectBuildStatus extends jspb.Message {
  getStatus(): BuildStatusMap[keyof BuildStatusMap];
  setStatus(value: BuildStatusMap[keyof BuildStatusMap]): void;

  getProgress(): number;
  setProgress(value: number): void;

  getSuccessIndexFiles(): number;
  setSuccessIndexFiles(value: number): void;

  getFailedIndexFiles(): number;
  setFailedIndexFiles(value: number): void;

  getTotalIndexFiles(): number;
  setTotalIndexFiles(value: number): void;

  getEmptyProject(): boolean;
  setEmptyProject(value: boolean): void;

  clearDocumentsStatusList(): void;
  getDocumentsStatusList(): Array<DocumentBuildStatus>;
  setDocumentsStatusList(value: Array<DocumentBuildStatus>): void;
  addDocumentsStatus(value?: DocumentBuildStatus, index?: number): DocumentBuildStatus;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ProjectBuildStatus.AsObject;
  static toObject(includeInstance: boolean, msg: ProjectBuildStatus): ProjectBuildStatus.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: ProjectBuildStatus, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ProjectBuildStatus;
  static deserializeBinaryFromReader(message: ProjectBuildStatus, reader: jspb.BinaryReader): ProjectBuildStatus;
}

export namespace ProjectBuildStatus {
  export type AsObject = {
    status: BuildStatusMap[keyof BuildStatusMap],
    progress: number,
    successIndexFiles: number,
    failedIndexFiles: number,
    totalIndexFiles: number,
    emptyProject: boolean,
    documentsStatusList: Array<DocumentBuildStatus.AsObject>,
  }
}

export class GetBuildStatusResponse extends jspb.Message {
  getStatusMap(): jspb.Map<string, ProjectBuildStatus>;
  clearStatusMap(): void;
  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): GetBuildStatusResponse.AsObject;
  static toObject(includeInstance: boolean, msg: GetBuildStatusResponse): GetBuildStatusResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: GetBuildStatusResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): GetBuildStatusResponse;
  static deserializeBinaryFromReader(message: GetBuildStatusResponse, reader: jspb.BinaryReader): GetBuildStatusResponse;
}

export namespace GetBuildStatusResponse {
  export type AsObject = {
    statusMap: Array<[string, ProjectBuildStatus.AsObject]>,
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export class GetDocumentsIndexStatusRequest extends jspb.Message {
  clearProjectIdsList(): void;
  getProjectIdsList(): Array<string>;
  setProjectIdsList(value: Array<string>): void;
  addProjectIds(value: string, index?: number): string;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): GetDocumentsIndexStatusRequest.AsObject;
  static toObject(includeInstance: boolean, msg: GetDocumentsIndexStatusRequest): GetDocumentsIndexStatusRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: GetDocumentsIndexStatusRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): GetDocumentsIndexStatusRequest;
  static deserializeBinaryFromReader(message: GetDocumentsIndexStatusRequest, reader: jspb.BinaryReader): GetDocumentsIndexStatusRequest;
}

export namespace GetDocumentsIndexStatusRequest {
  export type AsObject = {
    projectIdsList: Array<string>,
  }
}

export class ProjectDocumentsIndexStatus extends jspb.Message {
  getStatus(): BuildStatusMap[keyof BuildStatusMap];
  setStatus(value: BuildStatusMap[keyof BuildStatusMap]): void;

  clearDocumentsStatusList(): void;
  getDocumentsStatusList(): Array<DocumentIndexStatus>;
  setDocumentsStatusList(value: Array<DocumentIndexStatus>): void;
  addDocumentsStatus(value?: DocumentIndexStatus, index?: number): DocumentIndexStatus;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ProjectDocumentsIndexStatus.AsObject;
  static toObject(includeInstance: boolean, msg: ProjectDocumentsIndexStatus): ProjectDocumentsIndexStatus.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: ProjectDocumentsIndexStatus, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ProjectDocumentsIndexStatus;
  static deserializeBinaryFromReader(message: ProjectDocumentsIndexStatus, reader: jspb.BinaryReader): ProjectDocumentsIndexStatus;
}

export namespace ProjectDocumentsIndexStatus {
  export type AsObject = {
    status: BuildStatusMap[keyof BuildStatusMap],
    documentsStatusList: Array<DocumentIndexStatus.AsObject>,
  }
}

export class DocumentIndexStatus extends jspb.Message {
  getStatus(): BuildStatusMap[keyof BuildStatusMap];
  setStatus(value: BuildStatusMap[keyof BuildStatusMap]): void;

  getUri(): string;
  setUri(value: string): void;

  getName(): string;
  setName(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): DocumentIndexStatus.AsObject;
  static toObject(includeInstance: boolean, msg: DocumentIndexStatus): DocumentIndexStatus.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: DocumentIndexStatus, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): DocumentIndexStatus;
  static deserializeBinaryFromReader(message: DocumentIndexStatus, reader: jspb.BinaryReader): DocumentIndexStatus;
}

export namespace DocumentIndexStatus {
  export type AsObject = {
    status: BuildStatusMap[keyof BuildStatusMap],
    uri: string,
    name: string,
  }
}

export class GetDocumentsIndexStatusResponse extends jspb.Message {
  getProjectsStatusMap(): jspb.Map<string, ProjectDocumentsIndexStatus>;
  clearProjectsStatusMap(): void;
  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): GetDocumentsIndexStatusResponse.AsObject;
  static toObject(includeInstance: boolean, msg: GetDocumentsIndexStatusResponse): GetDocumentsIndexStatusResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: GetDocumentsIndexStatusResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): GetDocumentsIndexStatusResponse;
  static deserializeBinaryFromReader(message: GetDocumentsIndexStatusResponse, reader: jspb.BinaryReader): GetDocumentsIndexStatusResponse;
}

export namespace GetDocumentsIndexStatusResponse {
  export type AsObject = {
    projectsStatusMap: Array<[string, ProjectDocumentsIndexStatus.AsObject]>,
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export class IsVersionMatchedRequest extends jspb.Message {
  getVersion(): string;
  setVersion(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): IsVersionMatchedRequest.AsObject;
  static toObject(includeInstance: boolean, msg: IsVersionMatchedRequest): IsVersionMatchedRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: IsVersionMatchedRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): IsVersionMatchedRequest;
  static deserializeBinaryFromReader(message: IsVersionMatchedRequest, reader: jspb.BinaryReader): IsVersionMatchedRequest;
}

export namespace IsVersionMatchedRequest {
  export type AsObject = {
    version: string,
  }
}

export class IsVersionMatchedResponse extends jspb.Message {
  getMatched(): boolean;
  setMatched(value: boolean): void;

  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): IsVersionMatchedResponse.AsObject;
  static toObject(includeInstance: boolean, msg: IsVersionMatchedResponse): IsVersionMatchedResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: IsVersionMatchedResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): IsVersionMatchedResponse;
  static deserializeBinaryFromReader(message: IsVersionMatchedResponse, reader: jspb.BinaryReader): IsVersionMatchedResponse;
}

export namespace IsVersionMatchedResponse {
  export type AsObject = {
    matched: boolean,
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export class CancelIndexRequest extends jspb.Message {
  getProjectId(): string;
  setProjectId(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CancelIndexRequest.AsObject;
  static toObject(includeInstance: boolean, msg: CancelIndexRequest): CancelIndexRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: CancelIndexRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CancelIndexRequest;
  static deserializeBinaryFromReader(message: CancelIndexRequest, reader: jspb.BinaryReader): CancelIndexRequest;
}

export namespace CancelIndexRequest {
  export type AsObject = {
    projectId: string,
  }
}

export class CancelIndexResponse extends jspb.Message {
  getSucceed(): boolean;
  setSucceed(value: boolean): void;

  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CancelIndexResponse.AsObject;
  static toObject(includeInstance: boolean, msg: CancelIndexResponse): CancelIndexResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: CancelIndexResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CancelIndexResponse;
  static deserializeBinaryFromReader(message: CancelIndexResponse, reader: jspb.BinaryReader): CancelIndexResponse;
}

export namespace CancelIndexResponse {
  export type AsObject = {
    succeed: boolean,
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export class ImportAnalysisRequest extends jspb.Message {
  getProjectId(): string;
  setProjectId(value: string): void;

  getFile(): string;
  setFile(value: string): void;

  getImportStatement(): string;
  setImportStatement(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ImportAnalysisRequest.AsObject;
  static toObject(includeInstance: boolean, msg: ImportAnalysisRequest): ImportAnalysisRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: ImportAnalysisRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ImportAnalysisRequest;
  static deserializeBinaryFromReader(message: ImportAnalysisRequest, reader: jspb.BinaryReader): ImportAnalysisRequest;
}

export namespace ImportAnalysisRequest {
  export type AsObject = {
    projectId: string,
    file: string,
    importStatement: string,
  }
}

export class FilesImportAnalysisRequest extends jspb.Message {
  getProjectId(): string;
  setProjectId(value: string): void;

  clearRelaFilesList(): void;
  getRelaFilesList(): Array<string>;
  setRelaFilesList(value: Array<string>): void;
  addRelaFiles(value: string, index?: number): string;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): FilesImportAnalysisRequest.AsObject;
  static toObject(includeInstance: boolean, msg: FilesImportAnalysisRequest): FilesImportAnalysisRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: FilesImportAnalysisRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): FilesImportAnalysisRequest;
  static deserializeBinaryFromReader(message: FilesImportAnalysisRequest, reader: jspb.BinaryReader): FilesImportAnalysisRequest;
}

export namespace FilesImportAnalysisRequest {
  export type AsObject = {
    projectId: string,
    relaFilesList: Array<string>,
  }
}

export class ImportAnalysisResponse extends jspb.Message {
  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  hasResult(): boolean;
  clearResult(): void;
  getResult(): ImportAnalysisResult | undefined;
  setResult(value?: ImportAnalysisResult): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ImportAnalysisResponse.AsObject;
  static toObject(includeInstance: boolean, msg: ImportAnalysisResponse): ImportAnalysisResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: ImportAnalysisResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ImportAnalysisResponse;
  static deserializeBinaryFromReader(message: ImportAnalysisResponse, reader: jspb.BinaryReader): ImportAnalysisResponse;
}

export namespace ImportAnalysisResponse {
  export type AsObject = {
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
    result?: ImportAnalysisResult.AsObject,
  }
}

export class ImportAnalysisResult extends jspb.Message {
  getMessage(): string;
  setMessage(value: string): void;

  getData(): string;
  setData(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ImportAnalysisResult.AsObject;
  static toObject(includeInstance: boolean, msg: ImportAnalysisResult): ImportAnalysisResult.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: ImportAnalysisResult, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ImportAnalysisResult;
  static deserializeBinaryFromReader(message: ImportAnalysisResult, reader: jspb.BinaryReader): ImportAnalysisResult;
}

export namespace ImportAnalysisResult {
  export type AsObject = {
    message: string,
    data: string,
  }
}

export class Symbol extends jspb.Message {
  getName(): string;
  setName(value: string): void;

  getFilePath(): string;
  setFilePath(value: string): void;

  getStartLine(): number;
  setStartLine(value: number): void;

  getEndLine(): number;
  setEndLine(value: number): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Symbol.AsObject;
  static toObject(includeInstance: boolean, msg: Symbol): Symbol.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: Symbol, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Symbol;
  static deserializeBinaryFromReader(message: Symbol, reader: jspb.BinaryReader): Symbol;
}

export namespace Symbol {
  export type AsObject = {
    name: string,
    filePath: string,
    startLine: number,
    endLine: number,
  }
}

export class SearchCKGDBRequest extends jspb.Message {
  getProjectId(): string;
  setProjectId(value: string): void;

  getSymbolIdentifier(): string;
  setSymbolIdentifier(value: string): void;

  getSymbolRegex(): string;
  setSymbolRegex(value: string): void;

  clearTypesList(): void;
  getTypesList(): Array<EntityTypeMap[keyof EntityTypeMap]>;
  setTypesList(value: Array<EntityTypeMap[keyof EntityTypeMap]>): void;
  addTypes(value: EntityTypeMap[keyof EntityTypeMap], index?: number): EntityTypeMap[keyof EntityTypeMap];

  getEntityNum(): number;
  setEntityNum(value: number): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): SearchCKGDBRequest.AsObject;
  static toObject(includeInstance: boolean, msg: SearchCKGDBRequest): SearchCKGDBRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: SearchCKGDBRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): SearchCKGDBRequest;
  static deserializeBinaryFromReader(message: SearchCKGDBRequest, reader: jspb.BinaryReader): SearchCKGDBRequest;
}

export namespace SearchCKGDBRequest {
  export type AsObject = {
    projectId: string,
    symbolIdentifier: string,
    symbolRegex: string,
    typesList: Array<EntityTypeMap[keyof EntityTypeMap]>,
    entityNum: number,
  }
}

export class SearchCKGDBResponse extends jspb.Message {
  clearVariablesList(): void;
  getVariablesList(): Array<Variable>;
  setVariablesList(value: Array<Variable>): void;
  addVariables(value?: Variable, index?: number): Variable;

  clearReferencesList(): void;
  getReferencesList(): Array<Reference>;
  setReferencesList(value: Array<Reference>): void;
  addReferences(value?: Reference, index?: number): Reference;

  getJsonResult(): string;
  setJsonResult(value: string): void;

  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): SearchCKGDBResponse.AsObject;
  static toObject(includeInstance: boolean, msg: SearchCKGDBResponse): SearchCKGDBResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: SearchCKGDBResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): SearchCKGDBResponse;
  static deserializeBinaryFromReader(message: SearchCKGDBResponse, reader: jspb.BinaryReader): SearchCKGDBResponse;
}

export namespace SearchCKGDBResponse {
  export type AsObject = {
    variablesList: Array<Variable.AsObject>,
    referencesList: Array<Reference.AsObject>,
    jsonResult: string,
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export class CodeSnippet extends jspb.Message {
  getFilePath(): string;
  setFilePath(value: string): void;

  getStartLine(): number;
  setStartLine(value: number): void;

  getEndLine(): number;
  setEndLine(value: number): void;

  getContent(): string;
  setContent(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CodeSnippet.AsObject;
  static toObject(includeInstance: boolean, msg: CodeSnippet): CodeSnippet.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: CodeSnippet, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CodeSnippet;
  static deserializeBinaryFromReader(message: CodeSnippet, reader: jspb.BinaryReader): CodeSnippet;
}

export namespace CodeSnippet {
  export type AsObject = {
    filePath: string,
    startLine: number,
    endLine: number,
    content: string,
  }
}

export class RetrieveRelevantSnippetRequest extends jspb.Message {
  getProjectId(): string;
  setProjectId(value: string): void;

  getUserQuery(): string;
  setUserQuery(value: string): void;

  getWorkspace(): boolean;
  setWorkspace(value: boolean): void;

  clearSelectedFoldersList(): void;
  getSelectedFoldersList(): Array<string>;
  setSelectedFoldersList(value: Array<string>): void;
  addSelectedFolders(value: string, index?: number): string;

  clearSelectedFilesList(): void;
  getSelectedFilesList(): Array<string>;
  setSelectedFilesList(value: Array<string>): void;
  addSelectedFiles(value: string, index?: number): string;

  clearSelectedCodesList(): void;
  getSelectedCodesList(): Array<CodeSnippet>;
  setSelectedCodesList(value: Array<CodeSnippet>): void;
  addSelectedCodes(value?: CodeSnippet, index?: number): CodeSnippet;

  hasCurrentEditor(): boolean;
  clearCurrentEditor(): void;
  getCurrentEditor(): CurrentEditorInfo | undefined;
  setCurrentEditor(value?: CurrentEditorInfo): void;

  getVersion(): string;
  setVersion(value: string): void;

  getUserId(): string;
  setUserId(value: string): void;

  getSessionId(): string;
  setSessionId(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): RetrieveRelevantSnippetRequest.AsObject;
  static toObject(includeInstance: boolean, msg: RetrieveRelevantSnippetRequest): RetrieveRelevantSnippetRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: RetrieveRelevantSnippetRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): RetrieveRelevantSnippetRequest;
  static deserializeBinaryFromReader(message: RetrieveRelevantSnippetRequest, reader: jspb.BinaryReader): RetrieveRelevantSnippetRequest;
}

export namespace RetrieveRelevantSnippetRequest {
  export type AsObject = {
    projectId: string,
    userQuery: string,
    workspace: boolean,
    selectedFoldersList: Array<string>,
    selectedFilesList: Array<string>,
    selectedCodesList: Array<CodeSnippet.AsObject>,
    currentEditor?: CurrentEditorInfo.AsObject,
    version: string,
    userId: string,
    sessionId: string,
  }
}

export class Snippet extends jspb.Message {
  getProjectId(): string;
  setProjectId(value: string): void;

  getType(): SnippetTypeMap[keyof SnippetTypeMap];
  setType(value: SnippetTypeMap[keyof SnippetTypeMap]): void;

  getFilePath(): string;
  setFilePath(value: string): void;

  getStartLine(): number;
  setStartLine(value: number): void;

  getEndLine(): number;
  setEndLine(value: number): void;

  getContent(): string;
  setContent(value: string): void;

  getRecallType(): RecallTypeMap[keyof RecallTypeMap];
  setRecallType(value: RecallTypeMap[keyof RecallTypeMap]): void;

  getCkgEntityId(): string;
  setCkgEntityId(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Snippet.AsObject;
  static toObject(includeInstance: boolean, msg: Snippet): Snippet.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: Snippet, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Snippet;
  static deserializeBinaryFromReader(message: Snippet, reader: jspb.BinaryReader): Snippet;
}

export namespace Snippet {
  export type AsObject = {
    projectId: string,
    type: SnippetTypeMap[keyof SnippetTypeMap],
    filePath: string,
    startLine: number,
    endLine: number,
    content: string,
    recallType: RecallTypeMap[keyof RecallTypeMap],
    ckgEntityId: string,
  }
}

export class RetrieveRelevantSnippetResponse extends jspb.Message {
  clearSnippetsByWorkspaceList(): void;
  getSnippetsByWorkspaceList(): Array<Snippet>;
  setSnippetsByWorkspaceList(value: Array<Snippet>): void;
  addSnippetsByWorkspace(value?: Snippet, index?: number): Snippet;

  clearSnippetsByFolderList(): void;
  getSnippetsByFolderList(): Array<Snippet>;
  setSnippetsByFolderList(value: Array<Snippet>): void;
  addSnippetsByFolder(value?: Snippet, index?: number): Snippet;

  clearSnippetsByFileList(): void;
  getSnippetsByFileList(): Array<Snippet>;
  setSnippetsByFileList(value: Array<Snippet>): void;
  addSnippetsByFile(value?: Snippet, index?: number): Snippet;

  clearSnippetsByCodeList(): void;
  getSnippetsByCodeList(): Array<Snippet>;
  setSnippetsByCodeList(value: Array<Snippet>): void;
  addSnippetsByCode(value?: Snippet, index?: number): Snippet;

  clearSnippetsByCurrentFileList(): void;
  getSnippetsByCurrentFileList(): Array<Snippet>;
  setSnippetsByCurrentFileList(value: Array<Snippet>): void;
  addSnippetsByCurrentFile(value?: Snippet, index?: number): Snippet;

  clearSnippetsByInteractionList(): void;
  getSnippetsByInteractionList(): Array<Snippet>;
  setSnippetsByInteractionList(value: Array<Snippet>): void;
  addSnippetsByInteraction(value?: Snippet, index?: number): Snippet;

  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): RetrieveRelevantSnippetResponse.AsObject;
  static toObject(includeInstance: boolean, msg: RetrieveRelevantSnippetResponse): RetrieveRelevantSnippetResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: RetrieveRelevantSnippetResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): RetrieveRelevantSnippetResponse;
  static deserializeBinaryFromReader(message: RetrieveRelevantSnippetResponse, reader: jspb.BinaryReader): RetrieveRelevantSnippetResponse;
}

export namespace RetrieveRelevantSnippetResponse {
  export type AsObject = {
    snippetsByWorkspaceList: Array<Snippet.AsObject>,
    snippetsByFolderList: Array<Snippet.AsObject>,
    snippetsByFileList: Array<Snippet.AsObject>,
    snippetsByCodeList: Array<Snippet.AsObject>,
    snippetsByCurrentFileList: Array<Snippet.AsObject>,
    snippetsByInteractionList: Array<Snippet.AsObject>,
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export class RerankSnippetRequest extends jspb.Message {
  clearSnippetsByWorkspaceList(): void;
  getSnippetsByWorkspaceList(): Array<Snippet>;
  setSnippetsByWorkspaceList(value: Array<Snippet>): void;
  addSnippetsByWorkspace(value?: Snippet, index?: number): Snippet;

  clearSnippetsByFolderList(): void;
  getSnippetsByFolderList(): Array<Snippet>;
  setSnippetsByFolderList(value: Array<Snippet>): void;
  addSnippetsByFolder(value?: Snippet, index?: number): Snippet;

  clearSnippetsByFileList(): void;
  getSnippetsByFileList(): Array<Snippet>;
  setSnippetsByFileList(value: Array<Snippet>): void;
  addSnippetsByFile(value?: Snippet, index?: number): Snippet;

  clearSnippetsByCodeList(): void;
  getSnippetsByCodeList(): Array<Snippet>;
  setSnippetsByCodeList(value: Array<Snippet>): void;
  addSnippetsByCode(value?: Snippet, index?: number): Snippet;

  clearSnippetsByCurrentFileList(): void;
  getSnippetsByCurrentFileList(): Array<Snippet>;
  setSnippetsByCurrentFileList(value: Array<Snippet>): void;
  addSnippetsByCurrentFile(value?: Snippet, index?: number): Snippet;

  getToken(): string;
  setToken(value: string): void;

  getSessionId(): string;
  setSessionId(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): RerankSnippetRequest.AsObject;
  static toObject(includeInstance: boolean, msg: RerankSnippetRequest): RerankSnippetRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: RerankSnippetRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): RerankSnippetRequest;
  static deserializeBinaryFromReader(message: RerankSnippetRequest, reader: jspb.BinaryReader): RerankSnippetRequest;
}

export namespace RerankSnippetRequest {
  export type AsObject = {
    snippetsByWorkspaceList: Array<Snippet.AsObject>,
    snippetsByFolderList: Array<Snippet.AsObject>,
    snippetsByFileList: Array<Snippet.AsObject>,
    snippetsByCodeList: Array<Snippet.AsObject>,
    snippetsByCurrentFileList: Array<Snippet.AsObject>,
    token: string,
    sessionId: string,
  }
}

export class SnippetWithScore extends jspb.Message {
  hasSnippet(): boolean;
  clearSnippet(): void;
  getSnippet(): Snippet | undefined;
  setSnippet(value?: Snippet): void;

  getScore(): number;
  setScore(value: number): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): SnippetWithScore.AsObject;
  static toObject(includeInstance: boolean, msg: SnippetWithScore): SnippetWithScore.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: SnippetWithScore, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): SnippetWithScore;
  static deserializeBinaryFromReader(message: SnippetWithScore, reader: jspb.BinaryReader): SnippetWithScore;
}

export namespace SnippetWithScore {
  export type AsObject = {
    snippet?: Snippet.AsObject,
    score: number,
  }
}

export class RerankSnippetResponse extends jspb.Message {
  clearSnippetsList(): void;
  getSnippetsList(): Array<SnippetWithScore>;
  setSnippetsList(value: Array<SnippetWithScore>): void;
  addSnippets(value?: SnippetWithScore, index?: number): SnippetWithScore;

  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): RerankSnippetResponse.AsObject;
  static toObject(includeInstance: boolean, msg: RerankSnippetResponse): RerankSnippetResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: RerankSnippetResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): RerankSnippetResponse;
  static deserializeBinaryFromReader(message: RerankSnippetResponse, reader: jspb.BinaryReader): RerankSnippetResponse;
}

export namespace RerankSnippetResponse {
  export type AsObject = {
    snippetsList: Array<SnippetWithScore.AsObject>,
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export class CursorMoveRequest extends jspb.Message {
  getProjectId(): string;
  setProjectId(value: string): void;

  getFile(): string;
  setFile(value: string): void;

  getLine(): number;
  setLine(value: number): void;

  getUserId(): string;
  setUserId(value: string): void;

  getVersion(): string;
  setVersion(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CursorMoveRequest.AsObject;
  static toObject(includeInstance: boolean, msg: CursorMoveRequest): CursorMoveRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: CursorMoveRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CursorMoveRequest;
  static deserializeBinaryFromReader(message: CursorMoveRequest, reader: jspb.BinaryReader): CursorMoveRequest;
}

export namespace CursorMoveRequest {
  export type AsObject = {
    projectId: string,
    file: string,
    line: number,
    userId: string,
    version: string,
  }
}

export class IsCKGEnabledForNonWorkspaceScenarioRequest extends jspb.Message {
  getUserId(): string;
  setUserId(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): IsCKGEnabledForNonWorkspaceScenarioRequest.AsObject;
  static toObject(includeInstance: boolean, msg: IsCKGEnabledForNonWorkspaceScenarioRequest): IsCKGEnabledForNonWorkspaceScenarioRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: IsCKGEnabledForNonWorkspaceScenarioRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): IsCKGEnabledForNonWorkspaceScenarioRequest;
  static deserializeBinaryFromReader(message: IsCKGEnabledForNonWorkspaceScenarioRequest, reader: jspb.BinaryReader): IsCKGEnabledForNonWorkspaceScenarioRequest;
}

export namespace IsCKGEnabledForNonWorkspaceScenarioRequest {
  export type AsObject = {
    userId: string,
  }
}

export class IsCKGEnabledForNonWorkspaceScenarioResponse extends jspb.Message {
  getIsEnabled(): boolean;
  setIsEnabled(value: boolean): void;

  getVersion(): string;
  setVersion(value: string): void;

  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): IsCKGEnabledForNonWorkspaceScenarioResponse.AsObject;
  static toObject(includeInstance: boolean, msg: IsCKGEnabledForNonWorkspaceScenarioResponse): IsCKGEnabledForNonWorkspaceScenarioResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: IsCKGEnabledForNonWorkspaceScenarioResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): IsCKGEnabledForNonWorkspaceScenarioResponse;
  static deserializeBinaryFromReader(message: IsCKGEnabledForNonWorkspaceScenarioResponse, reader: jspb.BinaryReader): IsCKGEnabledForNonWorkspaceScenarioResponse;
}

export namespace IsCKGEnabledForNonWorkspaceScenarioResponse {
  export type AsObject = {
    isEnabled: boolean,
    version: string,
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export class SetUpRequest extends jspb.Message {
  getHost(): string;
  setHost(value: string): void;

  getRegion(): string;
  setRegion(value: string): void;

  getSourceProduct(): string;
  setSourceProduct(value: string): void;

  getDeviceCpu(): string;
  setDeviceCpu(value: string): void;

  getDeviceId(): string;
  setDeviceId(value: string): void;

  getMachineId(): string;
  setMachineId(value: string): void;

  getDeviceBrand(): string;
  setDeviceBrand(value: string): void;

  getDeviceType(): string;
  setDeviceType(value: string): void;

  getOsVersion(): string;
  setOsVersion(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): SetUpRequest.AsObject;
  static toObject(includeInstance: boolean, msg: SetUpRequest): SetUpRequest.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: SetUpRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): SetUpRequest;
  static deserializeBinaryFromReader(message: SetUpRequest, reader: jspb.BinaryReader): SetUpRequest;
}

export namespace SetUpRequest {
  export type AsObject = {
    host: string,
    region: string,
    sourceProduct: string,
    deviceCpu: string,
    deviceId: string,
    machineId: string,
    deviceBrand: string,
    deviceType: string,
    osVersion: string,
  }
}

export class SetUpResponse extends jspb.Message {
  getCode(): CodeMap[keyof CodeMap];
  setCode(value: CodeMap[keyof CodeMap]): void;

  hasError(): boolean;
  clearError(): void;
  getError(): Error | undefined;
  setError(value?: Error): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): SetUpResponse.AsObject;
  static toObject(includeInstance: boolean, msg: SetUpResponse): SetUpResponse.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: SetUpResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): SetUpResponse;
  static deserializeBinaryFromReader(message: SetUpResponse, reader: jspb.BinaryReader): SetUpResponse;
}

export namespace SetUpResponse {
  export type AsObject = {
    code: CodeMap[keyof CodeMap],
    error?: Error.AsObject,
  }
}

export interface CodeMap {
  SUCCEED: 0;
  UNKNOWN_ERROR: 1;
  PANIC: 100;
  USER_ID_IS_MISSING: 101;
  FILE_LIMIT_EXCEED: 102;
  INVALID_TOKEN: 103;
  NIL_CLIENT: 104;
}

export const Code: CodeMap;

export interface EntityTypeMap {
  FOLDER: 0;
  FILE: 1;
  CLASS: 2;
  METHOD: 3;
  TEXT: 4;
}

export const EntityType: EntityTypeMap;

export interface BuildStatusMap {
  BUILDING: 0;
  FINISHED: 1;
  FAILED: 2;
}

export const BuildStatus: BuildStatusMap;

export interface RecallTypeMap {
  RECALL_TYPE_USER_SPECIFIED: 0;
  RECALL_TYPE_EMBEDDING: 1;
  RECALL_TYPE_NER: 2;
  RECALL_TYPE_RELATION_BY_USER_ACTION_TRACE: 20;
  RECALL_TYPE_RELATION_BY_GIT_RELEVANCE: 30;
}

export const RecallType: RecallTypeMap;

export interface SnippetTypeMap {
  SNIPPET_TYPE_CODE: 0;
  SNIPPET_TYPE_FOLDER_TREE: 1;
  SNIPPET_TYPE_FILE: 2;
}

export const SnippetType: SnippetTypeMap;

