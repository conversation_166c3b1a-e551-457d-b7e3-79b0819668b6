package model

type SourceProduct string

const (
	SourceProductCodeVerse         SourceProduct = "codeverse"
	SourceProductMarscode          SourceProduct = "marscode"
	SourceProductIDE               SourceProduct = "ide"
	SourceProductA0                SourceProduct = "a0"
	SourceProductNativeIDE         SourceProduct = "native_ide"
	SourceProductNativeIDEInternal SourceProduct = "native_ide_internal"
	SourceProductUnknown           SourceProduct = "unknown"
)

func (s SourceProduct) StrictEnvCheck() bool { return false }

func GetSourceProduct(product string) SourceProduct {
	switch product {
	case string(SourceProductCodeVerse):
		return SourceProductCodeVerse
	case string(SourceProductMarscode):
		return SourceProductMarscode
	case string(SourceProductIDE):
		return SourceProductIDE
	case string(SourceProductA0):
		return SourceProductA0
	case string(SourceProductNativeIDE):
		return SourceProductNativeIDE
	case string(SourceProductNativeIDEInternal):
		return SourceProductNativeIDEInternal
	}
	return SourceProductUnknown
}
