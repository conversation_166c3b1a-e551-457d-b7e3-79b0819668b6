package common

import (
	"fmt"
	"strings"
	"sync"

	"ide/ckg/codekg/model"
)

// DefaultRegistry 是默认的分割器注册表实例
var DefaultRegistry = NewRegistry()

// Registry 是分割器注册表的实现
type Registry struct {
	splitters      map[string]Splitter
	batchSplitters map[string]BatchSplitter
	languageMap    map[model.LanguageType][]Splitter
	mu             sync.RWMutex
}

// NewRegistry 创建一个新的分割器注册表
func NewRegistry() *Registry {
	return &Registry{
		splitters:      make(map[string]Splitter),
		batchSplitters: make(map[string]BatchSplitter),
		languageMap:    make(map[model.LanguageType][]Splitter),
	}
}

// RegisterSplitter 注册一个分割器
func (r *Registry) RegisterSplitter(name string, splitter Splitter) {
	r.mu.Lock()
	defer r.mu.Unlock()

	// 注册到名称映射
	r.splitters[name] = splitter

	// 注册到语言映射
	for lang, supported := range splitter.SupportedLanguages() {
		if supported {
			r.languageMap[lang] = append(r.languageMap[lang], splitter)
		}
	}
}

// RegisterBatchSplitter 注册一个批处理分割器
func (r *Registry) RegisterBatchSplitter(name string, splitter BatchSplitter) {
	r.mu.Lock()
	defer r.mu.Unlock()

	// 注册到名称映射
	r.batchSplitters[name] = splitter
}

// RegisterSplitterWithKey 使用SplitterKey注册分割器
func (r *Registry) RegisterSplitterWithKey(key SplitterKey, splitter Splitter) {
	r.RegisterSplitter(key.String(), splitter)
}

// RegisterBatchSplitterWithKey 使用SplitterKey注册批处理分割器
func (r *Registry) RegisterBatchSplitterWithKey(key SplitterKey, splitter BatchSplitter) {
	r.RegisterBatchSplitter(key.String(), splitter)
}

// GetSplitter 获取指定名称的分割器
func (r *Registry) GetSplitter(name string) (Splitter, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	splitter, ok := r.splitters[name]
	return splitter, ok
}

// GetBatchSplitter 获取指定名称的批处理分割器
func (r *Registry) GetBatchSplitter(name string) (BatchSplitter, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	splitter, ok := r.batchSplitters[name]
	return splitter, ok
}

// GetSplitterWithKey 使用SplitterKey获取分割器
func (r *Registry) GetSplitterWithKey(key SplitterKey) (Splitter, bool) {
	return r.GetSplitter(key.String())
}

// GetBatchSplitterWithKey 使用SplitterKey获取批处理分割器
func (r *Registry) GetBatchSplitterWithKey(key SplitterKey) (BatchSplitter, bool) {
	return r.GetBatchSplitter(key.String())
}

// GetSplitterForLanguage 获取支持指定语言的第一个分割器
func (r *Registry) GetSplitterForLanguage(language model.LanguageType) (Splitter, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	splitters, ok := r.languageMap[language]
	if !ok || len(splitters) == 0 {
		return nil, false
	}

	return splitters[0], true
}

// RegisteredSplitters 返回所有注册的分割器
func (r *Registry) RegisteredSplitters() map[string]Splitter {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// 创建副本以避免并发修改
	result := make(map[string]Splitter, len(r.splitters))
	for name, splitter := range r.splitters {
		result[name] = splitter
	}

	return result
}

// RegisteredBatchSplitters 返回所有注册的批处理分割器
func (r *Registry) RegisteredBatchSplitters() map[string]BatchSplitter {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// 创建副本以避免并发修改
	result := make(map[string]BatchSplitter, len(r.batchSplitters))
	for name, splitter := range r.batchSplitters {
		result[name] = splitter
	}

	return result
}

// FindSplitters 根据前缀查找分割器
func (r *Registry) FindSplitters(prefix string) map[string]Splitter {
	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(map[string]Splitter)
	for name, splitter := range r.splitters {
		if strings.HasPrefix(name, prefix) {
			result[name] = splitter
		}
	}

	return result
}

// FindBatchSplitters 根据前缀查找批处理分割器
func (r *Registry) FindBatchSplitters(prefix string) map[string]BatchSplitter {
	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(map[string]BatchSplitter)
	for name, splitter := range r.batchSplitters {
		if strings.HasPrefix(name, prefix) {
			result[name] = splitter
		}
	}

	return result
}

// Factory 是分割器工厂的实现
type Factory struct {
	registry      SplitterRegistry
	creators      map[string]func(options map[string]interface{}) (Splitter, error)
	batchCreators map[string]func(options map[string]interface{}) (BatchSplitter, error)
}

// NewFactory 创建一个新的分割器工厂
func NewFactory(registry SplitterRegistry) *Factory {
	return &Factory{
		registry:      registry,
		creators:      make(map[string]func(options map[string]interface{}) (Splitter, error)),
		batchCreators: make(map[string]func(options map[string]interface{}) (BatchSplitter, error)),
	}
}

// RegisterCreator 注册一个分割器创建函数
func (f *Factory) RegisterCreator(splitterType string, creator func(options map[string]interface{}) (Splitter, error)) {
	f.creators[splitterType] = creator
}

// RegisterBatchCreator 注册一个批处理分割器创建函数
func (f *Factory) RegisterBatchCreator(splitterType string, creator func(options map[string]interface{}) (BatchSplitter, error)) {
	f.batchCreators[splitterType] = creator
}

// CreateSplitter 根据指定的类型和选项创建分割器
func (f *Factory) CreateSplitter(splitterType string, options map[string]interface{}) (Splitter, error) {
	// 检查是否已经注册了对应类型的创建函数
	creator, ok := f.creators[splitterType]
	if !ok {
		return nil, fmt.Errorf("unknown splitter type: %s", splitterType)
	}

	// 使用创建函数创建分割器
	return creator(options)
}

// CreateBatchSplitter 根据指定的类型和选项创建批处理分割器
func (f *Factory) CreateBatchSplitter(splitterType string, options map[string]interface{}) (BatchSplitter, error) {
	// 检查是否已经注册了对应类型的创建函数
	creator, ok := f.batchCreators[splitterType]
	if !ok {
		return nil, fmt.Errorf("unknown batch splitter type: %s", splitterType)
	}

	// 使用创建函数创建分割器
	return creator(options)
}

// CreateSplitterFromOption 从SplitterOption创建分割器
func (f *Factory) CreateSplitterFromOption(opt *SplitterOption) (Splitter, error) {
	key := NewSplitterKey(opt)
	keyStr := key.String()

	// 尝试使用完整键查找
	if creator, ok := f.creators[keyStr]; ok {
		// 将选项转换为map
		options := make(map[string]interface{})
		options["ChunkSize"] = opt.ChunkSize
		options["ChunkOverlap"] = opt.ChunkOverlap
		options["AsPlainText"] = opt.AsPlainText

		if opt.Advanced != nil {
			for k, v := range opt.Advanced {
				options[k] = v
			}
		}

		return creator(options)
	}

	// 构建特定的键，根据分割器类型
	var specificKey string
	if key.SplitterType == "local" {
		if key.ContentType == "text" {
			if key.LocalSubtype == "langchain_text" && key.LangchainType != "" {
				// 对于langchain分割器，使用特定的键格式
				specificKey = fmt.Sprintf("local.text.langchain.%s", key.LangchainType)
			} else if key.LocalSubtype == "basic_text" {
				// 对于基本文本分割器
				specificKey = "local.text.basic"
			}
		} else if key.ContentType == "code" {
			// 对于代码分割器
			specificKey = "local.code"
		}
	} else if key.SplitterType == "kb" {
		// 对于知识库分割器
		specificKey = "kb"
	}

	// 如果找到了特定的键，尝试使用它
	if specificKey != "" && specificKey != keyStr {
		if creator, ok := f.creators[specificKey]; ok {
			options := make(map[string]interface{})
			options["ChunkSize"] = opt.ChunkSize
			options["ChunkOverlap"] = opt.ChunkOverlap
			options["AsPlainText"] = opt.AsPlainText

			// 对于token分割器，添加TokenModel选项
			if strings.Contains(specificKey, "token") {
				if tokenModel, ok := opt.Advanced["TokenModel"].(string); ok {
					options["TokenModel"] = tokenModel
				}
			}

			if opt.Advanced != nil {
				for k, v := range opt.Advanced {
					options[k] = v
				}
			}

			return creator(options)
		}
	}

	// 如果找不到完整键，尝试逐级回退
	// 例如，如果找不到 "local.text.langchain.recursive"，尝试 "local.text.langchain"，然后 "local.text" 等
	parts := strings.Split(keyStr, ".")
	for i := len(parts) - 1; i > 0; i-- {
		partialKey := strings.Join(parts[:i], ".")
		if creator, ok := f.creators[partialKey]; ok {
			options := make(map[string]interface{})
			options["ChunkSize"] = opt.ChunkSize
			options["ChunkOverlap"] = opt.ChunkOverlap
			options["AsPlainText"] = opt.AsPlainText
			options["SplitterKey"] = key // 传递完整键，以便创建函数可以知道具体需要哪种类型

			if opt.Advanced != nil {
				for k, v := range opt.Advanced {
					options[k] = v
				}
			}

			return creator(options)
		}
	}

	return nil, fmt.Errorf("no creator found for splitter type: %s", keyStr)
}

// CreateBatchSplitterFromOption 从SplitterOption创建批处理分割器
func (f *Factory) CreateBatchSplitterFromOption(opt *SplitterOption) (BatchSplitter, error) {
	key := NewSplitterKey(opt)

	// 尝试使用完整键查找
	if creator, ok := f.batchCreators[key.String()]; ok {
		// 将选项转换为map
		options := make(map[string]interface{})
		options["ChunkSize"] = opt.ChunkSize
		options["ChunkOverlap"] = opt.ChunkOverlap
		options["AsPlainText"] = opt.AsPlainText

		if opt.Advanced != nil {
			for k, v := range opt.Advanced {
				options[k] = v
			}
		}

		return creator(options)
	}

	// 如果找不到批处理创建器，尝试创建单文件分割器并适配
	splitter, err := f.CreateSplitterFromOption(opt)
	if err != nil {
		return nil, err
	}

	// 使用适配器将单文件分割器转换为批处理分割器
	return &SplitterAdapter{Splitter: splitter}, nil
}
