package data_storage

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"sort"
	"strings"
	"sync"
	"time"

	"ide/ckg/codekg/components/logs"

	"code.byted.org/gopkg/lang/maths"
	sqlite3 "code.byted.org/ide/go-sqlcipher"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/atomic"
	"gorm.io/gorm"
	gormLog "gorm.io/gorm/logger"

	"ide/ckg/codekg/components/data_storage/cipher"
	"ide/ckg/codekg/components/data_storage/consts"
	"ide/ckg/codekg/components/data_storage/user_storage"
	bizErr "ide/ckg/codekg/components/error"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/protocol"
	"ide/ckg/codekg/util"
)

type Storage interface {
	Delete() error
	IsDeleted() bool
	IsExistsOnDisk() bool
	GetStoragePath() string
	GetChunkingMethod() string
	GetVersion() consts.StorageVersion
	GetConn() *gorm.DB
	GetEntityCount(ctx context.Context, conn *gorm.DB) int
	GetRelationCount(ctx context.Context, conn *gorm.DB) int
	InsertEntities(ctx context.Context, conn *gorm.DB, uriStatus *model.URIStatus, entities []*model.Entity) error
	InsertRelations(ctx context.Context, conn *gorm.DB, uriStatus *model.URIStatus, relations []*model.Relation) error
	UpsertUriMeta(ctx context.Context, conn *gorm.DB, uriStatus *model.URIStatus) (*StorageURIMeta, error)
	InsertEntityAlias(ctx context.Context, conn *gorm.DB, uriStatus *model.URIStatus, alias []*model.AliasEntity) error
	DeleteEntitiesFromURIMeta(ctx context.Context, conn *gorm.DB, uriMeta *StorageURIMeta) error
	DeleteRelationsFromURIMeta(ctx context.Context, conn *gorm.DB, uriMeta *StorageURIMeta) error
	DeleteURIMetaFromURI(ctx context.Context, conn *gorm.DB, uriStatus *model.URIStatus) error
	DeleteAliasFromURIMeta(ctx context.Context, conn *gorm.DB, uriMeta *StorageURIMeta) error
	DeleteVectorToEntityFromURIMeta(ctx context.Context, conn *gorm.DB, uriMeta *StorageURIMeta) error
	GetURIMetaFromURIWithoutContent(ctx context.Context, conn *gorm.DB, uriStatus *model.URIStatus) (*StorageURIMeta, error)

	GetURIMetaCount(ctx context.Context, conn *gorm.DB) int
	GetFolderEntityCount(ctx context.Context, conn *gorm.DB) int
	GetAllURIMetaWithoutContent(ctx context.Context, conn *gorm.DB) ([]StorageURIMeta, error)
	GetAllURIDocumentIndexStatus(ctx context.Context, conn *gorm.DB) ([]DocumentIndexStatus, error)
	LoadAllUriMetaFromExistingStorage(ctx context.Context, existingStorage Storage, batchSize int) (uriToIndexIds []int, err error)
	GetAllURIMetaIDs(ctx context.Context, conn *gorm.DB) ([]int, error)
	GetURIMetaByIDs(ctx context.Context, conn *gorm.DB, ids []int) ([]*StorageURIMeta, error)
	GetURIMetaByURI(ctx context.Context, conn *gorm.DB, uri string) (*StorageURIMeta, error)
	GetAllIndexingUriIDs(ctx context.Context, conn *gorm.DB) ([]int, error)

	GetProjectID() model.URI
	SearchEntityByQueryContainsName(ctx context.Context, conn *gorm.DB, query string, maxSize int) ([]*model.Entity, error)
	SearchEntityByQueryContainsAlias(ctx context.Context, conn *gorm.DB, query string, maxSize int) ([]*model.Entity, error)
	GetEntityByEntityID(ctx context.Context, conn *gorm.DB, entityID string) (*model.Entity, error)
	GetEntitiesByEntityIDs(ctx context.Context, conn *gorm.DB, entityIDs []string) ([]*model.Entity, error)
	GetAllEntityByURI(ctx context.Context, conn *gorm.DB, uriID int) ([]*model.Entity, error)
	GetClassAndMethodByURI(ctx context.Context, conn *gorm.DB, uri string) ([]*model.Entity, error)
	GetBelongClassAndFileByMethodEntityID(ctx context.Context, conn *gorm.DB, entityID string) (string, string, error)
	GetBelongFileAndMethodMemberByClassEntityID(ctx context.Context, conn *gorm.DB, entity *model.Entity) (string, []string, error)
	SearchEntityByNameAndTypes(ctx context.Context, conn *gorm.DB, name string, types []model.EntityType, limit int) ([]*model.Entity, error)
	GetAllAliasEqualKeyWord(ctx context.Context, conn *gorm.DB, keyWord string) ([]StorageEntityAlias, error)
	SearchCalleeEntity(ctx context.Context, conn *gorm.DB, entity *model.Entity, selectRange *protocol.Range) ([]*model.Entity, error)
	SearchCallerEntity(ctx context.Context, conn *gorm.DB, entity *model.Entity) ([]CallerEntityResult, error)
	SearchClassMember(ctx context.Context, conn *gorm.DB, entity *model.Entity) ([]*model.Entity, error)
	SearchClassMethod(ctx context.Context, conn *gorm.DB, entity *model.Entity) ([]*model.Entity, error)
	SearchMethodClass(ctx context.Context, conn *gorm.DB, entity *model.Entity) ([]*model.Entity, error)
	GetEntityNotesByEntityID(ctx context.Context, conn *gorm.DB, entityID string) (*model.EntityNotes, error)
	InsertEntityNotes(ctx context.Context, conn *gorm.DB, entityNotes *model.EntityNotes) error
	RemoveEntityNotes(ctx context.Context, conn *gorm.DB, entityID string) error
	GetClassTopLevelEntity(ctx context.Context, conn *gorm.DB, entityID string) (*model.Entity, error)
	GetClassMethodEntities(ctx context.Context, conn *gorm.DB, entityID string) ([]*model.Entity, error)
	UpdateEntityEmbeddingStatus(ctx context.Context, conn *gorm.DB, uri string, hasEmbedding int) error
	UpdateURIMetaStatus(ctx context.Context, conn *gorm.DB, uri string, status int) error
	UpdateURIMetaContent(ctx context.Context, conn *gorm.DB, uri string, content string) error
	InsertVectorToEntity(ctx context.Context, conn *gorm.DB, relation []*model.VectorToEntity) error
	DeleteVectorToEntityFromEntity(ctx context.Context, conn *gorm.DB, entityID string) error
	DeleteVectorToEntityFromEntities(ctx context.Context, conn *gorm.DB, entityIDs []string) error
	SearchEntitiesByURIMeta(ctx context.Context, conn *gorm.DB, uriMeta *StorageURIMeta) ([]StorageEntity, error)
	SearchVectorIDsByEntityIDs(ctx context.Context, conn *gorm.DB, entityIDs []string) ([]string, error)
	SearchVectorToEntityByVector(ctx context.Context, conn *gorm.DB, vectorIDs []string) ([]model.VectorToEntity, error)
	GetEndEntitiesInRelationByStartIDAndType(ctx context.Context, conn *gorm.DB, startID string, relationType model.EntityRelationType) ([]*model.Entity, error)
	GetStartEntitiesInRelationByEndIDAndType(ctx context.Context, conn *gorm.DB, endID string, relationType model.EntityRelationType) ([]*model.Entity, error)
	SearchEntitiesByRegexAndTypes(ctx context.Context, conn *gorm.DB, regex string, entityTypes []model.EntityType, limit int) ([]*model.Entity, error)
}

// Create 的 forceNew 表示强制创建新 db
func Create(ctx context.Context, es *user_storage.UserStorage, storagePath model.URI, projectID model.URI, projectURI model.URI, projectType model.ProjectType, chunking string, storageVersion consts.StorageVersion, forceNew bool) (Storage, error) {
	var (
		entityDB *gorm.DB
		err      error
	)

	dbFilePath := ""
	if len(storagePath) == 0 {
		entityDB, err = gorm.Open(cipher.Open("file::memory:"), &gorm.Config{})
	} else {
		dbFilePath, err = es.GetStorageFromUserStorage(ctx, projectID)
		if forceNew || err != nil || len(dbFilePath) == 0 {
			projectName := filepath.Base(projectID)
			sum := sha256.Sum256([]byte(projectID))
			randStr := util.GetRandStr(6)
			dbFilePath = filepath.Join(storagePath, fmt.Sprintf("%s_%x_%s_codekg.db", projectName, sum[:7], randStr))
			logs.CtxInfo(ctx, "[Create] entity db not exist for project %s, new db path is %s", projectID, dbFilePath)
		}
		// 创建的 db file path 会在 Init 流程完全结束后写入
		entityDB, err = gorm.Open(cipher.Open(fmt.Sprintf("%s?_pragma_key=%s", dbFilePath, consts.PragmaKey)), &gorm.Config{
			Logger:          gormLog.Default.LogMode(gormLog.Silent),
			CreateBatchSize: 500,
		})
		sqliteError := new(sqlite3.Error)
		if err != nil && errors.As(err, sqliteError) && sqliteError.Code == sqlite3.ErrNotADB {
			entityDB, err = gorm.Open(cipher.Open(dbFilePath), &gorm.Config{
				Logger:          gormLog.Default.LogMode(gormLog.Silent),
				CreateBatchSize: 500,
			})
		}
	}
	if err != nil {
		return nil, errors.WithMessagef(err, "[Create] storagePath is %s, projectID is %s", storagePath, projectID)
	}

	db, err := entityDB.DB()
	if err != nil {
		return nil, errors.WithMessagef(err, "[Create] DB is invalid")
	}
	db.SetMaxOpenConns(1)

	err = entityDB.AutoMigrate(
		&StorageEntity{}, &StorageURIMeta{}, &StorageRelation{},
		&StorageEntityAlias{}, &StorageEntityNotes{}, &StorageVectorToEntity{},
	)
	if err != nil {
		return nil, errors.WithMessagef(err, "[Create] AutoMigrate failed")
	}

	// 只需要存 projectPath，为空表示不用判断 IsSubdirectory
	projectPath := util.GetFilePathFromURI(projectURI, projectType == model.ProjectTypeVirtual)

	return &EntityStorage{
		db:          entityDB,
		projectID:   projectID,
		projectPath: projectPath,
		dbFilePath:  dbFilePath,
		isDeleted:   atomic.NewBool(false),
		chunking:    chunking,
		version:     storageVersion,
	}, nil
}

type EntityStorage struct {
	db          *gorm.DB
	projectID   string
	projectPath model.URI
	dbFilePath  string

	isDeleted *atomic.Bool
	chunking  string
	version   consts.StorageVersion
}

func (es *EntityStorage) Delete() error {
	es.isDeleted.Store(true)

	sqlDB, err := es.db.DB()
	if err != nil {
		return err
	}

	err = sqlDB.Close()
	if err != nil {
		return err
	}

	// remove db files
	_ = os.Remove(es.dbFilePath)

	return nil
}

func (es *EntityStorage) IsDeleted() bool {
	return es.isDeleted.Load()
}

func (es *EntityStorage) IsExistsOnDisk() bool {
	_, err := file_system.RealFS.Stat(es.dbFilePath)
	return err == nil
}

func (es *EntityStorage) GetProjectID() string {
	return es.projectID
}

func (es *EntityStorage) GetStoragePath() string            { return es.dbFilePath }
func (es *EntityStorage) GetChunkingMethod() string         { return es.chunking }
func (es *EntityStorage) GetVersion() consts.StorageVersion { return es.version }

func (es *EntityStorage) GetConn() *gorm.DB {
	return es.db
}

func (es *EntityStorage) dbEntityToEntity(ctx context.Context, conn *gorm.DB, dbEntity StorageEntity) (*model.Entity, error) {
	var attributes map[string]interface{}
	err := json.Unmarshal([]byte(dbEntity.Attributes), &attributes)
	if err != nil {
		return nil, errors.WithMessage(bizErr.ErrUnmarshal, "error unmarshal attributes in dbEntityToEntity")
	}

	var uriValue StorageURIMeta
	result := conn.WithContext(ctx).WithContext(ctx).First(&uriValue, "id = ?", dbEntity.UriID)
	if result.Error != nil {
		return nil, errors.WithMessage(bizErr.ErrFindUriMeta, "error find uri meta info in dbEntityToEntity")
	}

	entity := model.CreateEntity(dbEntity.EntityID, dbEntity.Name, uriValue.Uri, dbEntity.Type, attributes)
	return entity, nil
}

func (es *EntityStorage) GetBelongFileAndMethodMemberByClassEntityID(ctx context.Context, conn *gorm.DB, entity *model.Entity) (string, []string, error) {
	var relations []StorageRelation
	if err := conn.WithContext(ctx).WithContext(ctx).Where("startID = ?", entity.ID).Find(&relations).Error; err != nil {
		return "", []string{}, errors.WithMessagef(err, "err find relations by entityID")
	}
	belongFile := ""
	methodMembers := make([]string, 0)
	for _, relation := range relations {
		if relation.Type == model.ClassToFile {
			belongFile = relation.EndName
		}
	}
	result, err := es.SearchClassMethod(ctx, conn, entity)
	if err != nil {
		return "", []string{}, err
	}
	for _, item := range result {
		methodMembers = append(methodMembers, item.ID)
	}
	return belongFile, methodMembers, nil
}

func (es *EntityStorage) GetBelongClassAndFileByMethodEntityID(ctx context.Context, conn *gorm.DB, entityID string) (string, string, error) {
	var relations []StorageRelation
	if err := conn.WithContext(ctx).WithContext(ctx).Where("startID = ?", entityID).Find(&relations).Error; err != nil {
		return "", "", errors.WithMessagef(err, "err find relations by entityID")
	}

	belongClass := ""
	uriID := relations[0].UriID
	for _, relation := range relations {
		if relation.Type == model.MethodToClass {
			belongClass = relation.EndID
			break
		}
	}

	var uriValue StorageURIMeta
	if err := conn.WithContext(ctx).First(&uriValue, "id = ?", uriID).Error; err != nil {
		return "", "", errors.WithMessagef(err, "err find uriMeta by UriID")
	}
	belongFile, err := filepath.Rel(es.projectID, uriValue.Uri)
	if err != nil {
		return "", "", errors.WithMessagef(err, "Rel err")
	}
	return belongClass, belongFile, nil
}

func (es *EntityStorage) GetClassAndMethodByURI(ctx context.Context, conn *gorm.DB, uri string) ([]*model.Entity, error) {
	var uriValue StorageURIMeta
	if err := conn.WithContext(ctx).First(&uriValue, "uri = ?", uri).Error; err != nil {
		return nil, errors.WithMessagef(err, "error find uri meta from uri")
	}
	result, err := es.GetAllEntityByURI(ctx, conn, uriValue.ID)
	if err != nil {
		logs.CtxError(ctx, "GetAllEntityByURI err is %v", err)
		return nil, err
	}
	result = lo.Filter(result, func(item *model.Entity, index int) bool {
		if item.Type != model.Method && item.Type != model.Clazz {
			return false
		}
		return true
	})

	return result, nil
}

func (es *EntityStorage) GetAllEntityByURI(ctx context.Context, conn *gorm.DB, uriID int) ([]*model.Entity, error) {
	var entities []StorageEntity
	if err := conn.WithContext(ctx).Where("uriID = ?", uriID).Find(&entities).Error; err != nil {
		return nil, errors.WithMessagef(err, "err find entities by uri")
	}

	var resultEntities []*model.Entity
	for _, item := range entities {
		entity, err := es.dbEntityToEntity(ctx, conn, item)
		if err != nil {
			return nil, errors.WithMessage(err, "error convert db entity to model in GetAllEntityByURI")
		}
		resultEntities = append(resultEntities, entity)
	}

	return resultEntities, nil
}

func (es *EntityStorage) GetAllAliasEqualKeyWord(ctx context.Context, conn *gorm.DB, keyWord string) ([]StorageEntityAlias, error) {
	var entities []StorageEntityAlias

	result := conn.WithContext(ctx).Where("alias = ?", keyWord).Find(&entities)
	if result.Error != nil {
		return nil, result.Error
	}

	return entities, nil
}

func (es *EntityStorage) SearchCallerEntity(ctx context.Context, conn *gorm.DB, entity *model.Entity) ([]CallerEntityResult, error) {
	var relations []StorageRelation
	result := conn.WithContext(ctx).Where("`type` = ? AND endName = ?", model.CallerToCallee, entity.Name).Find(&relations)
	if result.Error != nil {
		return nil, result.Error
	}

	groupedRelations := make(map[string][]CallerToCalleeAttribute)
	for _, item := range relations {
		_, ok := groupedRelations[item.StartID]
		if !ok {
			arr := make([]CallerToCalleeAttribute, 0)
			groupedRelations[item.StartID] = arr
		}
		var attributes map[string]interface{}
		err := json.Unmarshal([]byte(item.Attributes), &attributes)
		if err != nil {
			return nil, errors.WithMessage(bizErr.ErrUnmarshal, "error unmarshal attributes in SearchCallerEntity")
		}

		startLine := attributes[model.AttributeLabelStartLine]
		endLine := attributes[model.AttributeLabelEndLine]
		if startLine == nil || endLine == nil {
			return nil, errors.WithMessage(bizErr.ErrRelationAttributeInvalid, "start line or end line is nil")
		}

		groupedRelations[item.StartID] = append(groupedRelations[item.StartID], CallerToCalleeAttribute{
			StartLine: int32(startLine.(float64)),
			EndLine:   int32(endLine.(float64)),
		})
	}

	count := 0
	var results = make([]CallerEntityResult, 0)
	for callerID, callsites := range groupedRelations {
		if count > 10 {
			break
		}

		dbEntity, err := es.GetEntityByEntityID(ctx, conn, callerID)
		if err != nil {
			logs.CtxWarn(ctx, "GetEntityByEntityID failed, err is %v", err)
			continue
		}

		count += 1
		candidateRanges := lo.Filter(callsites, func(item CallerToCalleeAttribute, index int) bool {
			return dbEntity.GetStartLine() <= item.StartLine && item.EndLine <= dbEntity.GetEndLine()
		})
		if len(candidateRanges) > 0 {
			ranges := mergeRelationAttributes(candidateRanges)
			results = append(results, CallerEntityResult{
				Entity: dbEntity,
				Ranges: ranges,
			})
		}
	}

	return results, nil
}

func mergeRelationAttributes(origin []CallerToCalleeAttribute) []CallerToCalleeAttribute {
	sort.Slice(origin, func(i, j int) bool {
		return origin[i].StartLine > origin[j].StartLine
	})
	merged := make([]CallerToCalleeAttribute, 0)
	merged = append(merged, origin[0])
	for index := range origin {
		lastMerged := merged[len(merged)-1]
		current := origin[index]
		if current.StartLine <= lastMerged.EndLine {
			lastMerged.EndLine = maths.MaxInt32(lastMerged.EndLine, current.EndLine)
		} else {
			merged = append(merged, current)
		}
	}

	return merged
}

func getRelationStartLine(relation *StorageRelation) int32 {
	var attributes map[string]interface{}
	err := json.Unmarshal([]byte(relation.Attributes), &attributes)
	if err != nil {
		return -1
	}

	return int32(util.DefaultAny[float64](attributes[model.AttributeLabelStartLine]))
}

func getRelationEndLine(relation *StorageRelation) int32 {
	var attributes map[string]interface{}
	err := json.Unmarshal([]byte(relation.Attributes), &attributes)
	if err != nil {
		return -1
	}

	return int32(util.DefaultAny[float64](attributes[model.AttributeLabelEndLine]))
}

func (es *EntityStorage) SearchCalleeEntity(ctx context.Context, conn *gorm.DB, entity *model.Entity, selectRange *protocol.Range) ([]*model.Entity, error) {
	var uriValue StorageURIMeta
	if err := conn.WithContext(ctx).First(&uriValue, "uri = ?", entity.URI).Error; err != nil {
		return nil, errors.WithMessage(err, "error find uri meta")
	}

	var relations []*StorageRelation
	if err := conn.WithContext(ctx).
		Where("`type` = ? AND uriID = ? AND startID = ?", model.CallerToCallee, uriValue.ID, entity.ID).
		Find(&relations).Error; err != nil {
		return nil, errors.WithMessage(err, "error find call relation")
	}

	if selectRange != nil {
		relations = lo.Filter(relations, func(item *StorageRelation, _ int) bool {
			callStartLine := getRelationStartLine(item)
			callEndLine := getRelationEndLine(item)

			selectFull := selectRange.StartLine <= callStartLine && callEndLine <= selectRange.EndLine
			coverStart := selectRange.StartLine <= callStartLine && callStartLine <= selectRange.EndLine
			coverEnd := selectRange.StartLine <= callEndLine && callEndLine <= selectRange.EndLine
			selectPartial := callStartLine <= selectRange.StartLine && selectRange.EndLine <= callEndLine
			return selectFull || coverStart || coverEnd || selectPartial
		})
	}

	dedupe := make(map[string]struct{})
	dedupedRelations := make([]*StorageRelation, 0)
	for _, relation := range relations {
		id := relation.EndID
		if id == "" {
			id = relation.EndName
		}
		if _, ok := dedupe[id]; !ok {
			dedupedRelations = append(dedupedRelations, relation)
			dedupe[id] = struct{}{}
		}
	}

	count := 0
	result := make(map[string]*model.Entity)
	for _, relation := range dedupedRelations {
		if count > 10 {
			break
		}

		count += 1
		if relation.EndID != "" {
			callee, err := es.GetEntityByEntityID(ctx, conn, relation.EndID)
			if err != nil {
				logs.CtxWarn(ctx, "GetEntityByEntityID failed, err is %v", err)
				continue
			}

			if callee.Type == model.Method {
				result[callee.ID] = callee
			}
		} else {
			var callees []StorageEntity
			if err := conn.WithContext(ctx).Where("name != ? AND type = ?", "", model.Method).
				Where("name = ? OR entityID = ?", relation.EndName, relation.EndName).Find(&callees).Error; err != nil {
				return nil, errors.WithMessage(err, "error search entity by name and type")
			}

			if len(callees) >= 3 {
				logs.CtxInfo(ctx, "Callee is too much for %s, ignored", relation.EndName)
				continue
			}

			for _, item := range callees {
				callee, err := es.dbEntityToEntity(ctx, conn, item)
				if err != nil {
					return nil, errors.WithMessage(err, "error get entity by entity id")
				}
				result[callee.ID] = callee
			}
		}
	}

	return lo.Values(result), nil
}

func (es *EntityStorage) SearchClassMember(ctx context.Context, conn *gorm.DB, entity *model.Entity) ([]*model.Entity, error) {
	var results []StorageRelation
	if err := conn.WithContext(ctx).Where("type = ? AND endID = ?", model.ClassTopLevelToClass, entity.ID).Find(&results).Error; err != nil {
		return nil, errors.WithMessage(err, "error search class member")
	}

	entities := make([]*model.Entity, 0)
	for _, item := range results {
		classMember, err := es.GetEntityByEntityID(ctx, conn, item.StartID)
		if err != nil {
			return nil, errors.WithMessage(err, "error get entity by entity id")
		}
		entities = append(entities, classMember)
	}

	return entities, nil
}

func (es *EntityStorage) GetEntityCount(ctx context.Context, conn *gorm.DB) int {
	var count int64
	conn.WithContext(ctx).Model(&StorageEntity{}).Count(&count)
	return int(count)
}

func (es *EntityStorage) GetRelationCount(ctx context.Context, conn *gorm.DB) int {
	var count int64
	conn.WithContext(ctx).Model(&StorageRelation{}).Count(&count)
	return int(count)
}

func (es *EntityStorage) SearchClassMethod(ctx context.Context, conn *gorm.DB, entity *model.Entity) ([]*model.Entity, error) {
	var results []StorageRelation
	if err := conn.WithContext(ctx).Where("type = ? AND endID = ?", model.MethodToClass, entity.ID).Find(&results).Error; err != nil {
		return nil, errors.WithMessage(err, "error search class method")
	}

	entities := make([]*model.Entity, 0)
	for _, item := range results {
		method, err := es.GetEntityByEntityID(ctx, conn, item.StartID)
		if err != nil {
			return nil, errors.WithMessage(err, "error get entity by entity id")
		}

		if method.Type == model.Method {
			entities = append(entities, method)
		}
	}

	return entities, nil
}

func (es *EntityStorage) SearchMethodClass(ctx context.Context, conn *gorm.DB, entity *model.Entity) ([]*model.Entity, error) {
	var results []StorageRelation
	if err := conn.WithContext(ctx).Where("type = ? AND startID = ?", model.MethodToClass, entity.ID).Find(&results).Error; err != nil {
		return nil, errors.WithMessage(err, "error search method class")
	}

	entities := make([]*model.Entity, 0)
	for _, item := range results {
		method, err := es.GetEntityByEntityID(ctx, conn, item.EndID)
		if err != nil {
			return nil, errors.WithMessage(err, "error get entity by entity id")
		}

		if method.Type == model.Clazz {
			entities = append(entities, method)
		}
	}

	return entities, nil
}

func (es *EntityStorage) SearchEntityByNameAndTypes(ctx context.Context, conn *gorm.DB, name string, entityTypes []model.EntityType, limit int) ([]*model.Entity, error) {
	var entities []StorageEntity
	query := conn.WithContext(ctx).Where("name = ? OR entityID = ?", name, name)
	if len(entityTypes) != 0 {
		query = query.Where("type in ?", entityTypes)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&entities).Error; err != nil {
		return nil, errors.WithMessage(err, "error search entity by name and type")
	}

	result := make([]*model.Entity, 0)
	for _, item := range entities {
		entity, err := es.dbEntityToEntity(ctx, conn, item)
		if err != nil {
			return nil, errors.WithMessage(err, "error get entity by entity id")
		}
		result = append(result, entity)
	}

	return result, nil
}

func (es *EntityStorage) SearchEntityByQueryContainsName(ctx context.Context, conn *gorm.DB, query string, maxSize int) ([]*model.Entity, error) {
	var results []StorageEntity
	if err := conn.WithContext(ctx).Where("name != '' AND (type = ? OR type = ?) AND instr(?, name) > 0",
		model.Clazz,
		model.Method,
		query).Limit(5).Find(&results).Error; err != nil {
		return nil, errors.WithMessage(err, "error search entity by name in db")
	}

	sort.Slice(results, func(i, j int) bool {
		return len(results[j].Name) > len(results[i].Name)
	})

	var entityResult = make([]*model.Entity, 0)
	for _, storageEntity := range results {
		if len(entityResult) >= maxSize {
			break
		}
		entity, err := es.dbEntityToEntity(ctx, conn, storageEntity)
		if err != nil {
			return nil, errors.WithMessagef(err, "error convert db entity to entity")
		}
		entityResult = append(entityResult, entity)
	}

	return entityResult, nil
}

func (es *EntityStorage) GetEntityByEntityID(ctx context.Context, conn *gorm.DB, entityID string) (*model.Entity, error) {
	var storageEntity StorageEntity
	if err := conn.WithContext(ctx).Where("entityID = ?", entityID).First(&storageEntity).Error; err != nil {
		return nil, errors.WithMessagef(err, "error get entity by entity id %s", entityID)
	}

	entity, err := es.dbEntityToEntity(ctx, conn, storageEntity)
	if err != nil {
		return nil, errors.WithMessagef(err, "error convert db entity to entity")
	}

	return entity, nil
}

func (es *EntityStorage) GetEntitiesByEntityIDs(ctx context.Context, conn *gorm.DB, entityIDs []string) ([]*model.Entity, error) {
	var storageEntities []StorageEntity
	if err := conn.WithContext(ctx).Where("entityID IN (?)", entityIDs).Find(&storageEntities).Error; err != nil {
		return nil, errors.WithMessagef(err, "error get entity by entity ids %v", entityIDs)
	}
	var entities []*model.Entity
	for _, e := range storageEntities {
		entity, err := es.dbEntityToEntity(ctx, conn, e)
		if err != nil {
			logs.CtxWarn(ctx, "[GetEntitiesByEntityIDs] StorageEntity cannot convert to entity, err is %v", err)
			continue
		}
		entities = append(entities, entity)
	}
	return entities, nil
}

func (es *EntityStorage) SearchEntityByQueryContainsAlias(ctx context.Context, conn *gorm.DB, query string, maxSize int) ([]*model.Entity, error) {
	results := make([]StorageEntityAlias, 0)
	if err := conn.WithContext(ctx).Where("instr(?, alias) > 0", query).Limit(5).Find(&results).Error; err != nil {
		return nil, errors.WithMessage(err, "error search entity by alias")
	}
	sort.Slice(results, func(i, j int) bool {
		return len(results[i].Alias) > len(results[j].Alias)
	})

	selectedEntityIDs := make(map[string]bool)
	selectedAlias := make(map[string]bool)
	for _, aliasEntity := range results {
		if len(selectedEntityIDs) >= maxSize {
			break
		}
		if selectedEntityIDs[aliasEntity.EntityID] {
			selectedAlias[aliasEntity.Alias] = true
		}
		var aliasIsContained = false
		for selectedAlia := range selectedAlias {
			if strings.Contains(selectedAlia, aliasEntity.Alias) {
				aliasIsContained = true
				break
			}
		}
		if !aliasIsContained {
			selectedEntityIDs[aliasEntity.EntityID] = true
			selectedAlias[aliasEntity.Alias] = true
		}
	}

	resultEntity := make([]*model.Entity, 0)
	for selectedEntityID := range selectedEntityIDs {
		result, err := es.GetEntityByEntityID(ctx, conn, selectedEntityID)
		if err != nil {
			logs.CtxWarn(ctx, "GetEntityByEntityID err is %v", err)
			continue
		}
		resultEntity = append(resultEntity, result)
	}

	return resultEntity, nil
}

func (es *EntityStorage) GetURIMetaFromURIWithoutContent(ctx context.Context, conn *gorm.DB, uriStatus *model.URIStatus) (*StorageURIMeta, error) {
	var uriValue StorageURIMeta
	if err := conn.WithContext(ctx).Select("id", "uri", "contentHash", "hasEmbedding", "status", "createTime", "uriCanonical", "name").First(&uriValue, "uri = ?", uriStatus.AbsPath).Error; err != nil {
		return nil, errors.WithMessagef(err, "error find uri meta from uri %s", uriStatus.AbsPath)
	}

	return &uriValue, nil
}

func (es *EntityStorage) DeleteEntitiesFromURIMeta(ctx context.Context, conn *gorm.DB, uriMeta *StorageURIMeta) error {
	if err := conn.WithContext(ctx).Where("uriID = ?", uriMeta.ID).Delete(&StorageEntity{}).Error; err != nil {
		return errors.WithMessagef(err, "error delete entities from uri meta")
	}

	return nil
}

func (es *EntityStorage) DeleteRelationsFromURIMeta(ctx context.Context, conn *gorm.DB, uriMeta *StorageURIMeta) error {
	if err := conn.WithContext(ctx).Where("uriID = ?", uriMeta.ID).Delete(&StorageRelation{}).Error; err != nil {
		return errors.WithMessagef(err, "error delete relations from uri meta")
	}

	return nil
}

func (es *EntityStorage) DeleteURIMetaFromURI(ctx context.Context, conn *gorm.DB, uriStatus *model.URIStatus) error {
	if err := conn.WithContext(ctx).Where("uri = ?", uriStatus.AbsPath).Delete(&StorageURIMeta{}).Error; err != nil {
		return errors.WithMessagef(err, "error delete uri meta from uri")
	}

	return nil
}

func (es *EntityStorage) DeleteAliasFromURIMeta(ctx context.Context, conn *gorm.DB, uriMeta *StorageURIMeta) error {
	if err := conn.WithContext(ctx).Where("uriID = ?", uriMeta.ID).Delete(&StorageEntityAlias{}).Error; err != nil {
		return errors.WithMessagef(err, "error delete alias from uri meta")
	}

	return nil
}

func (es *EntityStorage) DeleteVectorToEntityFromURIMeta(ctx context.Context, conn *gorm.DB, uriMeta *StorageURIMeta) error {
	// 被 insertStorage 调用，无需开启事务
	// 用于删除 uri 对应的所有 entity 对应的 vectors。根据 uriID 在 entity 找到所有 entityID，然后查 vector_to_entity
	var entities []StorageEntity
	if err := conn.WithContext(ctx).Find(&entities, "uriID = ?", uriMeta.ID).Error; err != nil {
		return errors.WithMessagef(err, "error find entities")
	}
	entityIDs := lo.Map(entities, func(item StorageEntity, _ int) string { return item.EntityID })
	if err := conn.WithContext(ctx).Where("entityID IN (?)", entityIDs).Delete(&StorageVectorToEntity{}).Error; err != nil {
		return errors.WithMessagef(err, "error delete vector to entities via entityIDs: %v", entityIDs)
	}
	return nil
}

func (es *EntityStorage) InsertEntityAlias(ctx context.Context, conn *gorm.DB, uriStatus *model.URIStatus, alias []*model.AliasEntity) error {
	if es.projectPath != "" && !util.IsSubdirectory(uriStatus.AbsPath, es.projectPath) {
		return errors.WithMessagef(bizErr.ErrUriNotMatchProject, "error insert entity alias")
	}

	var uriValue StorageURIMeta
	if err := conn.WithContext(ctx).First(&uriValue, "uri = ?", uriStatus.AbsPath).Error; err != nil {
		return errors.WithMessagef(err, "error find uri meta")
	}

	if len(alias) == 0 {
		return nil
	}

	if err := conn.WithContext(ctx).Create(lo.Map(alias, func(item *model.AliasEntity, _ int) *StorageEntityAlias {
		return &StorageEntityAlias{
			UriID:    uriValue.ID,
			EntityID: item.EntityID,
			Alias:    item.Alias,
		}
	})).Error; err != nil {
		return errors.WithMessagef(err, "error create alias")
	}

	return nil
}

func (es *EntityStorage) UpsertUriMeta(ctx context.Context, conn *gorm.DB, uriStatus *model.URIStatus) (*StorageURIMeta, error) {
	if es.projectPath != "" && !util.IsSubdirectory(uriStatus.AbsPath, es.projectPath) {
		return nil, errors.WithMessagef(bizErr.ErrUriNotMatchProject, "error insert uri meta")
	}

	now := time.Now()
	status := StorageURIMeta{
		Uri:          uriStatus.AbsPath,
		ContentHash:  uriStatus.ContentHash,
		HasEmbedding: 0, // UpsertUriMeta 可能会修改 content hash，将该位置 0，后需重新计算 embedding
		Name:         lo.Ternary(uriStatus.Name == "", file_system.RealFS.Base(uriStatus.AbsPath), uriStatus.Name),
		UriCanonical: lo.Ternary(uriStatus.UriCanonical == "", util.GetCanonicalURIFromFilePath(uriStatus.AbsPath, false), uriStatus.UriCanonical),
		Status:       int(model.BuildStatusBuilding),
		CreateTime:   &now,
		Content:      uriStatus.Content,
	}

	if err := conn.WithContext(ctx).Where("uri = ?", uriStatus.AbsPath).Assign(status).FirstOrCreate(&status).Error; err != nil {
		return nil, errors.WithMessagef(err, "failed to upsert uri meta for %s", uriStatus.AbsPath)
	}

	return &status, nil
}

func (es *EntityStorage) GetURIMetaCount(ctx context.Context, conn *gorm.DB) int {
	var count int64
	conn.WithContext(ctx).Model(&StorageURIMeta{}).Count(&count)
	return int(count)
}

func (es *EntityStorage) upsertUriMetaFromExistingUriMeta(ctx context.Context, uriMeta *StorageURIMeta) error {
	var status StorageURIMeta
	if err := es.GetConn().WithContext(ctx).Where("uri = ?", uriMeta.Uri).Assign(uriMeta).FirstOrCreate(&status).Error; err != nil {
		return err
	}

	return nil
}

func (es *EntityStorage) GetFolderEntityCount(ctx context.Context, conn *gorm.DB) int {
	var count int64
	conn.WithContext(ctx).Model(&StorageEntity{}).Where("type = ?", model.Folder).Count(&count)
	return int(count)
}

func (es *EntityStorage) GetAllURIMetaWithoutContent(ctx context.Context, conn *gorm.DB) ([]StorageURIMeta, error) {
	var result []StorageURIMeta
	if err := conn.WithContext(ctx).Find(&result).Error; err != nil {
		return nil, errors.WithMessagef(err, "error get all uri meta")
	}

	return result, nil
}

func (es *EntityStorage) InsertEntities(ctx context.Context, conn *gorm.DB, uriStatus *model.URIStatus, entities []*model.Entity) error {
	if es.projectPath != "" && !util.IsSubdirectory(uriStatus.AbsPath, es.projectPath) {
		return errors.WithMessagef(bizErr.ErrUriNotMatchProject, "error insert entities")
	}

	var uriValue StorageURIMeta
	if err := conn.WithContext(ctx).First(&uriValue, "uri = ?", uriStatus.AbsPath).Error; err != nil {
		return errors.WithMessagef(err, "error find uri meta")
	}

	var insertValue = make([]StorageEntity, 0)
	for _, entity := range entities {
		attributesJSON, err := json.Marshal(entity.GetAttributes())
		if err != nil {
			return errors.WithMessagef(err, "error marshal attributes")
		}
		insertValue = append(insertValue, StorageEntity{
			EntityID:   entity.ID,
			UriID:      uriValue.ID,
			Name:       entity.Name,
			Type:       entity.Type,
			Attributes: string(attributesJSON),
		})
	}

	if len(insertValue) == 0 {
		return nil
	}

	if err := conn.WithContext(ctx).Create(&insertValue).Error; err != nil {
		return errors.WithMessagef(err, "error create entities")
	}

	return nil
}

func (es *EntityStorage) InsertRelations(ctx context.Context, conn *gorm.DB, uriStatus *model.URIStatus, relations []*model.Relation) error {
	if es.projectPath != "" && !util.IsSubdirectory(uriStatus.AbsPath, es.projectPath) {
		return errors.WithMessagef(bizErr.ErrUriNotMatchProject, "error insert entities")
	}

	var uriValue StorageURIMeta
	if err := conn.WithContext(ctx).First(&uriValue, "uri = ?", uriStatus.AbsPath).Error; err != nil {
		return errors.WithMessagef(err, "error get uri meta")
	}

	var relationValue = make([]StorageRelation, 0)
	for _, relation := range relations {
		attributesJSON, err := json.Marshal(relation.Attributes)
		if err != nil {
			return errors.WithMessagef(err, "error marshal attributes")
		}

		relationValue = append(relationValue, StorageRelation{
			UriID:      uriValue.ID,
			StartID:    relation.StartID,
			EndName:    relation.EndName,
			EndID:      relation.EndID,
			Attributes: string(attributesJSON),
			Type:       relation.Type,
			StartName:  relation.StartName,
		})
	}

	if len(relationValue) == 0 {
		return nil
	}

	if err := conn.WithContext(ctx).Create(&relationValue).Error; err != nil {
		return errors.WithMessagef(err, "error create relations")
	}

	return nil
}

// UpdateEntityEmbeddingStatus is used to update hasEmbedding field, when CKG obtains embeddings from KB.
// Around invoking this function, the embedding (vector) should be inserted into vector db.
func (es *EntityStorage) UpdateEntityEmbeddingStatus(ctx context.Context, conn *gorm.DB, uri string, hasEmbedding int) error {
	err := conn.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&StorageURIMeta{}).Where("uri = ?", uri).Update("hasEmbedding", hasEmbedding).Error; err != nil {
			return errors.WithMessagef(err, "error update uri embedding status")
		}
		return nil
	})
	return err
}

func (es *EntityStorage) UpdateURIMetaStatus(ctx context.Context, conn *gorm.DB, uri string, status int) error {
	if err := conn.WithContext(ctx).Model(&StorageURIMeta{}).Where("uri = ?", uri).Update("status", status).Error; err != nil {
		return errors.WithMessagef(err, "error update uri status")
	}
	return nil
}

func (es *EntityStorage) UpdateURIMetaContent(ctx context.Context, conn *gorm.DB, uri string, content string) error {
	if err := conn.WithContext(ctx).Model(&StorageURIMeta{}).Where("uri = ?", uri).Update("content", content).Error; err != nil {
		return errors.WithMessagef(err, "error update uri content")
	}
	return nil
}

// InsertVectorToEntity is used to insert StorageVectorToEntity when CKG receiving a vector from KB.
func (es *EntityStorage) InsertVectorToEntity(ctx context.Context, conn *gorm.DB, relations []*model.VectorToEntity) error {
	if len(relations) == 0 {
		return nil
	}
	insertValue := lo.Map(relations, func(r *model.VectorToEntity, _ int) *StorageVectorToEntity {
		return &StorageVectorToEntity{StorageVectorID: r.VectorID, StorageEntityID: r.EntityID}
	})
	if err := conn.WithContext(ctx).Create(&insertValue).Error; err != nil {
		return errors.WithMessagef(err, "error create vector to entity relation")
	}
	return nil
}

func (es *EntityStorage) SearchEntitiesByURIMeta(ctx context.Context, conn *gorm.DB, uriMeta *StorageURIMeta) ([]StorageEntity, error) {
	var entities []StorageEntity
	if err := conn.WithContext(ctx).Find(&entities, "uriID = ?", uriMeta.ID).Error; err != nil {
		return []StorageEntity{}, errors.WithMessagef(err, "error find entities")
	}
	return entities, nil
}

func (es *EntityStorage) SearchVectorIDsByEntityIDs(ctx context.Context, conn *gorm.DB, entityIDs []string) ([]string, error) {
	var res []StorageVectorToEntity
	if err := conn.WithContext(ctx).Where("entityID IN (?)", entityIDs).Find(&res).Error; err != nil {
		return []string{}, errors.WithMessagef(err, "error find vector to entities")
	}
	return lo.Map(res, func(item StorageVectorToEntity, _ int) string { return item.StorageVectorID }), nil
}

func (es *EntityStorage) SearchVectorToEntityByVector(ctx context.Context, conn *gorm.DB, vectorIDs []string) ([]model.VectorToEntity, error) {
	var res []StorageVectorToEntity
	if err := conn.WithContext(ctx).Where("vectorID IN (?)", vectorIDs).Find(&res).Error; err != nil {
		return []model.VectorToEntity{}, errors.WithMessagef(err, "error find vector ids from vector ids")
	}
	return lo.Map(res, func(item StorageVectorToEntity, _ int) model.VectorToEntity {
		return model.VectorToEntity{VectorID: item.StorageVectorID, EntityID: item.StorageEntityID}
	}), nil
}

// DeleteVectorToEntityFromEntity is used to delete StorageVectorToEntity when deleting an entity.
// After invoking this function, the vectors in vector db should be also removed.
func (es *EntityStorage) DeleteVectorToEntityFromEntity(ctx context.Context, conn *gorm.DB, entityID string) error {
	if err := conn.WithContext(ctx).Where("entityID = ?", entityID).Delete(&StorageVectorToEntity{}).Error; err != nil {
		return errors.WithMessagef(err, "error delete vector to entity relation")
	}
	return nil
}

func (es *EntityStorage) DeleteVectorToEntityFromEntities(ctx context.Context, conn *gorm.DB, entityIDs []string) error {
	if err := conn.WithContext(ctx).Where("entityID IN (?)", entityIDs).Delete(&StorageVectorToEntity{}).Error; err != nil {
		return errors.WithMessagef(err, "error delete vector to entity relation")
	}
	return nil
}

func (es *EntityStorage) GetEntityNotesByEntityID(ctx context.Context, conn *gorm.DB, entityID string) (*model.EntityNotes, error) {
	var storageEntityNotes StorageEntityNotes
	if err := conn.WithContext(ctx).Where("entityID = ?", entityID).First(&storageEntityNotes).Error; err != nil {
		return nil, errors.WithMessagef(err, "failed to get entity notes by entity id")
	}

	entityNotes, err := es.dbEntityNotesToEntityNotes(storageEntityNotes)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to convert StorageEntityNotes to EntityNotes")
	}

	return entityNotes, nil
}

func (es *EntityStorage) dbEntityNotesToEntityNotes(dbEntityNotes StorageEntityNotes) (*model.EntityNotes, error) {
	var elementNotes map[string]interface{}
	err := json.Unmarshal([]byte(dbEntityNotes.ElementNotes), &elementNotes)
	if err != nil {
		return nil, errors.WithMessage(bizErr.ErrUnmarshal, "error unmarshal elementNotes in dbEntityNotesToEntityNotes")
	}

	return model.CreateEntityNotes(dbEntityNotes.EntityID, dbEntityNotes.EntityHash, dbEntityNotes.Summary, elementNotes), nil
}

func (es *EntityStorage) InsertEntityNotes(ctx context.Context, conn *gorm.DB, entityNotes *model.EntityNotes) error {
	_ = es.RemoveEntityNotes(ctx, conn, entityNotes.ID)

	elementNotesJSON, err := json.Marshal(entityNotes.ElementNotes)
	if err != nil {
		return errors.WithMessagef(err, "error marshal elementNotes")
	}

	if err := conn.WithContext(ctx).Create(&StorageEntityNotes{
		EntityID:     entityNotes.ID,
		EntityHash:   entityNotes.Hash,
		Summary:      entityNotes.Summary,
		ElementNotes: string(elementNotesJSON),
	}).Error; err != nil {
		return errors.WithMessagef(err, "error create entity notes")
	}

	return nil
}

func (es *EntityStorage) RemoveEntityNotes(ctx context.Context, conn *gorm.DB, entityID string) error {
	if err := conn.WithContext(ctx).Where("entityID = ?", entityID).Delete(&StorageEntityNotes{}).Error; err != nil {
		return errors.WithMessagef(err, "error remove entity notes")
	}

	return nil
}

func (es *EntityStorage) GetClassTopLevelEntity(ctx context.Context, conn *gorm.DB, entityID string) (*model.Entity, error) {
	var relation *StorageRelation
	if err := conn.WithContext(ctx).Where("type = ? AND endID = ? ", model.ClassTopLevelToClass, entityID).First(relation).Error; err != nil {
		return nil, errors.WithMessagef(err, "error get classTopLevelToClass relation")
	}

	if relation == nil {
		return nil, nil
	}

	entity, err := es.GetEntityByEntityID(ctx, conn, relation.StartID)
	if err != nil {
		return nil, errors.WithMessagef(err, "error get classTopLevel entity")
	}

	return entity, nil
}

func (es *EntityStorage) GetClassMethodEntities(ctx context.Context, conn *gorm.DB, entityID string) ([]*model.Entity, error) {
	var relations []StorageRelation
	if err := conn.WithContext(ctx).Where("type = ? AND endID = ?", model.MethodToClass, entityID).Find(&relations).Error; err != nil {
		return nil, errors.WithMessagef(err, "error get methodToClass relations")
	}

	var result = make([]*model.Entity, 0)
	for _, relation := range relations {
		entity, err := es.GetEntityByEntityID(ctx, conn, relation.StartID)
		if err != nil {
			logs.CtxError(ctx, "failed to GetEntityByEntityID: %s", err.Error())
			continue
		}
		result = append(result, entity)
	}

	return result, nil
}

func (es *EntityStorage) GetEndEntitiesInRelationByStartIDAndType(ctx context.Context, conn *gorm.DB, startID string, relationType model.EntityRelationType) ([]*model.Entity, error) {
	var relations []StorageRelation
	if err := conn.WithContext(ctx).Where("type = ? AND startID = ?", relationType, startID).Find(&relations).Error; err != nil {
		return nil, errors.WithMessagef(err, "error get relations by startID and type")
	}

	var result = make([]*model.Entity, 0)
	for _, relation := range relations {
		entity, err := es.GetEntityByEntityID(ctx, conn, relation.EndID)

		if err != nil && relationType == model.FileToFile && !strings.HasPrefix(relation.EndID, "F.") {

			entity, err = es.GetEntityByEntityID(ctx, conn, fmt.Sprintf("F.%s", relation.EndID))

			if err != nil {
				logs.CtxError(ctx, "failed to GetEntityByEntityID: %s", err.Error())
				logs.CtxError(ctx, "error relations: [%s] %s -> %s", relation.ID, relation.StartID, relation.EndID)
				continue
			}
		}
		result = append(result, entity)
	}

	return result, nil
}

func (es *EntityStorage) GetStartEntitiesInRelationByEndIDAndType(ctx context.Context, conn *gorm.DB, endID string, relationType model.EntityRelationType) ([]*model.Entity, error) {
	var relations []StorageRelation
	if err := conn.WithContext(ctx).Where("type = ? AND endID = ?", relationType, endID).Find(&relations).Error; err != nil {
		return nil, errors.WithMessagef(err, "error get relations by endID and type")
	}

	var result = make([]*model.Entity, 0)
	for _, relation := range relations {
		entity, err := es.GetEntityByEntityID(ctx, conn, relation.StartID)
		if err != nil {
			logs.CtxError(ctx, "failed to GetEntityByEntityID: %s", err.Error())
			logs.CtxError(ctx, "error relations: [%s] %s -> %s", relation.ID, relation.StartID, relation.EndID)

			continue
		}
		result = append(result, entity)
	}

	return result, nil
}

func (es *EntityStorage) SearchEntitiesByRegexAndTypes(ctx context.Context, conn *gorm.DB, regex string, entityTypes []model.EntityType, limit int) ([]*model.Entity, error) {
	// SQLite支持正则吗？好像不支持，先把所有name取出来再依次正则匹配
	re, err := regexp.Compile(regex)
	if err != nil {
		return nil, errors.WithMessagef(err, "invalid regex %s", regex)
	}
	var entities []StorageEntity
	query := conn.WithContext(ctx)
	if len(entityTypes) != 0 {
		query = query.Where("type in ?", entityTypes)
	}

	if err := query.Find(&entities).Error; err != nil {
		return nil, errors.WithMessage(err, "error search entities")
	}

	maxProcs := runtime.GOMAXPROCS(0)
	chunkSize := (len(entities) + maxProcs - 1) / maxProcs
	chunks := lo.Chunk(entities, chunkSize)
	var result []*model.Entity
	var mu sync.Mutex
	var wg sync.WaitGroup
	for _, chunk := range chunks {
		wg.Add(1)
		go func(chunk []StorageEntity) {
			defer wg.Done()
			for _, entity := range chunk {
				if re.MatchString(entity.EntityID) || re.MatchString(entity.Name) {
					entity := &model.Entity{
						ID:   entity.EntityID,
						Name: entity.Name, // 当前场景只需保留实体名与名称
					}
					mu.Lock()
					result = append(result, entity)
					if limit > 0 && len(result) >= limit {
						mu.Unlock()
						return
					}
					mu.Unlock()
				}
			}
		}(chunk)
	}
	wg.Wait()

	return result, nil
}

func (es *EntityStorage) GetAllURIDocumentIndexStatus(ctx context.Context, conn *gorm.DB) ([]DocumentIndexStatus, error) {
	logs.CtxInfo(ctx, "[GetAllURIDocumentIndexStatus] Starting to get all URI document index status for project: %s", es.projectID)

	query := conn.WithContext(ctx)
	// 移除 uri_canonical 列，因为它可能不存在
	query = query.Select("id", "uri", "name", "status", "uriCanonical")

	uriMetas := make([]StorageURIMeta, 0)
	if err := query.Find(&uriMetas).Error; err != nil {
		logs.CtxError(ctx, "[GetAllURIDocumentIndexStatus] Error finding URI metas: %v", err)
		return nil, errors.WithMessagef(err, "error get all uri document index status")
	}

	logs.CtxInfo(ctx, "[GetAllURIDocumentIndexStatus] Found %d URI metas for project: %s", len(uriMetas), es.projectID)

	// 打印前几个 URI meta 的详细信息，以便调试
	for i, uriMeta := range uriMetas {
		if i < 5 { // 只打印前 5 个
			logs.CtxInfo(ctx, "[GetAllURIDocumentIndexStatus] URI meta %d: ID=%d, URI=%s, Name=%s, Status=%d",
				i, uriMeta.ID, uriMeta.Uri, uriMeta.Name, uriMeta.Status)
		}
	}

	results := lo.Map(uriMetas, func(item StorageURIMeta, _ int) DocumentIndexStatus {
		uriCanonical := item.UriCanonical
		if uriCanonical == "" {
			uriCanonical = util.GetCanonicalURIFromURIOrPath(item.Uri, true)
		}

		return DocumentIndexStatus{
			Uri:    uriCanonical,
			Name:   item.Name,
			Status: model.BuildStatus(item.Status),
		}
	})

	logs.CtxInfo(ctx, "[GetAllURIDocumentIndexStatus] Mapped %d URI metas to document index statuses for project: %s", len(results), es.projectID)

	return results, nil
}

func (es *EntityStorage) LoadAllUriMetaFromExistingStorage(ctx context.Context, existingStorage Storage,
	batchSize int) (uriToIndexIds []int, err error) {
	uriToIndexIds = make([]int, 0)
	// 1. 获取所有ID
	ids, err := existingStorage.GetAllURIMetaIDs(ctx, existingStorage.GetConn())
	if err != nil {
		err = errors.WithMessagef(err, "LoadAllUriMetaFromExistingStorage error get all uri meta ids")
		return
	}

	if len(ids) == 0 {
		logs.CtxInfo(ctx, "LoadAllUriMetaFromExistingStorage no uri meta in existing storage")
		return
	}

	// 2. 分批处理ID
	var totalProcessed int = 0

	for i := 0; i < len(ids); i += batchSize {
		end := i + batchSize
		if end > len(ids) {
			end = len(ids)
		}

		batchIDs := ids[i:end]

		// 3. 获取这批ID对应的完整数据
		var uriMetas []*StorageURIMeta
		uriMetas, err = existingStorage.GetURIMetaByIDs(ctx, existingStorage.GetConn(), batchIDs)
		if err != nil {
			err = errors.WithMessagef(err, "LoadAllUriMetaFromExistingStorage error get uri meta by ids")
			return
		}
		// 重置标志位
		for _, uriMeta := range uriMetas {
			uriMeta.Status = int(model.BuildStatusBuilding)
			uriMeta.HasEmbedding = 0
		}

		// 4. 使用事务批量插入到目标存储
		err = es.db.Transaction(func(tx *gorm.DB) error {
			for _, uriMeta := range uriMetas {
				if err := es.upsertUriMetaFromExistingUriMeta(ctx, uriMeta); err != nil {
					return err
				}
			}
			return nil
		})

		if err != nil {
			err = errors.WithMessagef(err, "LoadAllUriMetaFromExistingStorage error batch insert uri meta")
			return uriToIndexIds, err
		}

		for _, uriMeta := range uriMetas {
			uriToIndexIds = append(uriToIndexIds, uriMeta.ID)
		}

		totalProcessed += len(uriMetas)
		logs.CtxInfo(ctx, "LoadAllUriMetaFromExistingStorage processed %d/%d uri meta", totalProcessed, len(ids))

		// 检查上下文是否取消
		select {
		case <-ctx.Done():
			err = ctx.Err()
			return uriToIndexIds, err
		default:
		}
	}

	logs.CtxInfo(ctx, "LoadAllUriMetaFromExistingStorage success migrate %d uri meta", totalProcessed)
	return
}

func (es *EntityStorage) GetAllURIMetaIDs(ctx context.Context, conn *gorm.DB) ([]int, error) {
	var ids []int

	err := conn.WithContext(ctx).Model(&StorageURIMeta{}).Order("id ASC").Pluck("id", &ids).Error

	if err != nil {
		return nil, errors.WithMessagef(err, "LoadAllUriMetaFromExistingStorage error get all uri meta ids")
	}

	return ids, nil
}

func (es *EntityStorage) GetURIMetaByIDs(ctx context.Context, conn *gorm.DB, ids []int) ([]*StorageURIMeta, error) {
	var uriMetas []*StorageURIMeta
	if len(ids) == 0 {
		return uriMetas, nil
	}

	err := conn.WithContext(ctx).Where("id IN ?", ids).Find(&uriMetas).Error

	if err != nil {
		return nil, errors.WithMessagef(err, "LoadAllUriMetaFromExistingStorage error get uri meta by ids")
	}

	return uriMetas, nil
}

func (es *EntityStorage) GetURIMetaByURI(ctx context.Context, conn *gorm.DB, uri string) (*StorageURIMeta, error) {
	var uriMeta StorageURIMeta
	err := conn.WithContext(ctx).Where("uri = ?", uri).First(&uriMeta).Error
	if err != nil {
		return nil, errors.WithMessagef(err, "error get uri meta by uri")
	}

	return &uriMeta, nil
}

func (es *EntityStorage) GetAllIndexingUriIDs(ctx context.Context, conn *gorm.DB) ([]int, error) {
	var ids []int
	err := conn.WithContext(ctx).Model(&StorageURIMeta{}).Where("status = ?", model.BuildStatusBuilding).Pluck("id", &ids).Error
	if err != nil {
		return nil, errors.WithMessagef(err, "error get all indexing uri ids")
	}

	return ids, nil
}
