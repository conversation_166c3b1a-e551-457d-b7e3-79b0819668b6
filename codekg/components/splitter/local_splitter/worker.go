package local_splitter

import (
	"context"
	"runtime/debug"
	"sync"
	"time"

	kb "ide/ckg/codekg/components/knowledgebase"
	lsmodel "ide/ckg/codekg/components/splitter/local_splitter/model"
	"ide/ckg/codekg/model"

	"errors"

	"ide/ckg/codekg/components/logs"
)

// 全局 worker 池实例
var (
	globalWorkerPool *lsmodel.LocalSplitterWorkerPool
	poolOnce         sync.Once
)

// GetGlobalWorkerPool 返回全局共享的 worker 池实例
func GetGlobalWorkerPool() *lsmodel.LocalSplitterWorkerPool {
	poolOnce.Do(func() {
		// TODO(ldx): 使用 4 个 worker，改为可配置
		numWorkers := 4
		globalWorkerPool = newLocalSplitterWorkerPool(numWorkers)
	})
	return globalWorkerPool
}

// newLocalSplitterWorkerPool 创建一个新的 worker 池来并行处理文件分割任务
func newLocalSplitterWorkerPool(workers int) *lsmodel.LocalSplitterWorkerPool {
	ctx, cancel := context.WithCancel(context.Background())
	pool := &lsmodel.LocalSplitterWorkerPool{
		Workers:     workers,
		TaskQueue:   make(chan *lsmodel.LocalSplitTask, workers*10), // 增加队列容量
		ResultQueue: make(chan *lsmodel.LocalSplitResult, workers*10),
		Ctx:         ctx,
		Cancel:      cancel,
	}

	startWorkerPool(pool)
	return pool
}

// startWorkerPool 启动 worker 池中的所有 worker
func startWorkerPool(pool *lsmodel.LocalSplitterWorkerPool) {
	for i := 0; i < pool.Workers; i++ {
		pool.Wg.Add(1)
		go workerFunc(pool, i)
	}
	logs.Info("Started splitter worker pool with %d workers", pool.Workers)
}

// workerFunc 是每个 worker 线程执行的函数
func workerFunc(pool *lsmodel.LocalSplitterWorkerPool, id int) {
	defer pool.Wg.Done()
	defer func() {
		if r := recover(); r != nil {
			logs.Error("Splitter worker %d panicked: %v\n%s", id, r, debug.Stack())
			// 重新启动 worker
			pool.Wg.Add(1)
			go workerFunc(pool, id)
		}
	}()

	splitter := getDefaultLocalSplitter()
	logs.Debug("Splitter worker %d started", id)

	processedCount := 0
	errorCount := 0

	for {
		select {
		case <-pool.Ctx.Done():
			logs.Info("Splitter worker %d shutting down, processed %d files (%d errors)",
				id, processedCount, errorCount)
			return
		case task, ok := <-pool.TaskQueue:
			if !ok {
				logs.Info("Splitter worker %d task queue closed, processed %d files (%d errors)",
					id, processedCount, errorCount)
				return
			}

			// 检查任务上下文是否已取消
			select {
			case <-task.Ctx.Done():
				logs.Debug("Skipping cancelled task for file %s", task.File.Path)
				continue
			default:
				// 继续处理
			}

			// 检查文件路径和内容
			if task.File == nil {
				logs.Error("Worker %d: Received nil file", id)
				continue
			}

			if task.File.Path == "" {
				logs.Error("Worker %d: Received file with empty path", id)
				continue
			}

			if len(task.File.Content) == 0 {
				logs.Warn("Worker %d: File %s has empty content", id, task.File.Path)
				// 继续处理，因为有些文件可能确实是空的
			}

			// 获取适合该语言的分割器
			languageSplitter, err := splitter.getSplitterForLanguage(task.Language)

			result := &lsmodel.LocalSplitResult{
				Path: task.File.Path,
			}

			if err != nil {
				result.Err = err
				errorCount++
				logs.Error("Worker %d: Failed to get splitter for %s (language: %s): %v",
					id, task.File.Path, task.Language, err)
			} else {
				// 执行分割
				startTime := time.Now()

				// 添加详细的错误处理
				splitResult, splitErr := func() (*kb.SplitFileResult_, error) {
					defer func() {
						if r := recover(); r != nil {
							logs.Error("Worker %d: Panic while splitting file %s: %v\n%s",
								id, task.File.Path, r, debug.Stack())
						}
					}()
					return languageSplitter.SplitFile(task.Ctx, task.File)
				}()

				duration := time.Since(startTime)

				result.Result = splitResult
				result.Err = splitErr

				if splitErr != nil {
					errorCount++
					logs.Error("Worker %d: Failed to split file %s: %v (took %v)",
						id, task.File.Path, splitErr, duration)
				} else if splitResult == nil {
					errorCount++
					result.Err = errors.New("split result is nil")
					logs.Error("Worker %d: Split result is nil for file %s (took %v)",
						id, task.File.Path, duration)
				} else {
					// 检查分割结果
					if splitResult.SegmentsWithEmbedding == nil || len(splitResult.SegmentsWithEmbedding) == 0 {
						logs.Warn("Worker %d: No segments generated for file %s (took %v)",
							id, task.File.Path, duration)
					}

					processedCount++
					if processedCount%50 == 0 {
						logs.Info("Worker %d: Processed %d files so far", id, processedCount)
					}
					logs.Debug("Worker %d: Split file %s successfully with %d segments (took %v)",
						id, task.File.Path, len(splitResult.SegmentsWithEmbedding), duration)
				}
			}

			// 发送结果
			select {
			case pool.ResultQueue <- result:
				// 成功发送结果
			case <-task.Ctx.Done():
				// 任务已取消，丢弃结果
				logs.Debug("Task cancelled while sending result for %s", task.File.Path)
			case <-pool.Ctx.Done():
				// worker 池已关闭，丢弃结果
				logs.Debug("Worker pool closed while sending result for %s", task.File.Path)
				return
			}
		}
	}
}

// submitTask 向 worker 池提交一个任务
func submitTask(pool *lsmodel.LocalSplitterWorkerPool, task *lsmodel.LocalSplitTask) {
	pool.TaskQueue <- task
}

// getResult 从 worker 池获取一个结果
func getResult(pool *lsmodel.LocalSplitterWorkerPool) (*lsmodel.LocalSplitResult, bool) {
	result, ok := <-pool.ResultQueue
	return result, ok
}

// closeWorkerPool 关闭 worker 池
func closeWorkerPool(pool *lsmodel.LocalSplitterWorkerPool) {
	pool.Cancel()
	close(pool.TaskQueue)
	pool.Wg.Wait()
	close(pool.ResultQueue)
}

// splitWithWorkerPool 使用 worker 池并行处理文件分割
// 现在使用全局 worker 池而不是每次创建新的
func splitWithWorkerPool(ctx context.Context, files []*kb.SplitFile, allSplitAsText bool) (*kb.SplitFilesResponse, error) {
	startTime := time.Now()
	pool := GetGlobalWorkerPool()

	// 检查输入文件
	if len(files) == 0 {
		logs.Warn("No files to split")
		return &kb.SplitFilesResponse{
			Results:     make(map[string]*kb.SplitFileResult_),
			FailedFiles: make(map[string]string),
		}, nil
	}

	logs.Info("Starting to split %d files", len(files))

	// 提交所有任务
	validFileCount := 0
	skippedFiles := 0
	for _, file := range files {
		if file == nil {
			skippedFiles++
			continue
		}

		if file.Path == "" {
			logs.Warn("Skipping file with empty path")
			skippedFiles++
			continue
		}

		if len(file.Content) == 0 {
			logs.Warn("File %s has empty content", file.Path)
			// 继续处理，因为有些文件可能确实是空的
		}

		validFileCount++

		language := model.LanguageText
		if !allSplitAsText {
			language = model.DetectLanguage(file.Path, []byte(file.Content))
			logs.Debug("Detected language for %s: %s", file.Path, language)
		}

		// 创建任务，使用主上下文
		submitTask(pool, &lsmodel.LocalSplitTask{
			File:           file,
			Language:       language,
			AllSplitAsText: allSplitAsText,
			Ctx:            ctx, // 使用传入的上下文
		})
	}

	if validFileCount == 0 {
		logs.Warn("No valid files to split after filtering %d files", len(files))
		return &kb.SplitFilesResponse{
			Results:     make(map[string]*kb.SplitFileResult_),
			FailedFiles: make(map[string]string),
		}, nil
	}

	logs.Info("Submitted %d files for splitting (skipped %d invalid files)", validFileCount, skippedFiles)

	// 收集结果
	results := make(map[string]*kb.SplitFileResult_)
	failedResults := make(map[string]string)

	// 设置进度报告间隔
	progressInterval := validFileCount / 10
	if progressInterval < 100 {
		progressInterval = 100
	}
	if progressInterval > 1000 {
		progressInterval = 1000
	}

	// 设置超时，避免永久等待
	// TODO(ldx): 超时时间可配置
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Duration(validFileCount)*time.Second*30) // 每个文件平均5秒的超时
	defer cancel()

	for i := 0; i < validFileCount; i++ {
		select {
		case result, ok := <-pool.ResultQueue:
			if !ok {
				logs.Warn("Result queue closed unexpectedly after receiving %d/%d results", i, validFileCount)
				break
			}
			if result.Err != nil {
				failedResults[result.Path] = result.Err.Error()
				logs.Debug("File %s split failed: %v", result.Path, result.Err)
			} else if result.Result == nil {
				failedResults[result.Path] = "split result is nil"
				logs.Debug("File %s split returned nil result", result.Path)
			} else {
				results[result.Path] = result.Result
				if i > 0 && i%progressInterval == 0 {
					elapsedTime := time.Since(startTime)
					estimatedTotal := float64(elapsedTime) * float64(validFileCount) / float64(i)
					remainingTime := time.Duration(estimatedTotal) - elapsedTime
					logs.Info("Split progress: %d/%d files processed (%.1f%%), elapsed: %v, estimated remaining: %v",
						i, validFileCount, float64(i)/float64(validFileCount)*100, elapsedTime.Round(time.Second), remainingTime.Round(time.Second))
				}
			}
		case <-timeoutCtx.Done():
			if timeoutCtx.Err() == context.DeadlineExceeded {
				// 超时
				logs.Error("Timeout after receiving %d/%d results", i, validFileCount)
			} else {
				// 上下文取消
				logs.Warn("Context cancelled after receiving %d/%d results", i, validFileCount)
			}
			return &kb.SplitFilesResponse{
				Results:     results,
				FailedFiles: failedResults,
			}, timeoutCtx.Err()
		}
	}

	totalTime := time.Since(startTime)
	successCount := len(results)
	failureCount := len(failedResults)

	logs.Info("Split completed: %d files processed in %v (%.2f files/sec), %d succeeded, %d failed",
		validFileCount, totalTime, float64(validFileCount)/totalTime.Seconds(), successCount, failureCount)

	if failureCount > 0 && failureCount <= 10 {
		logs.Info("Failed files: %v", getMapKeys(failedResults))
	} else if failureCount > 10 {
		logs.Info("First 10 failed files: %v", getMapKeys(failedResults)[:10])
	}

	return &kb.SplitFilesResponse{
		Results:     results,
		FailedFiles: failedResults,
	}, nil
}

// getMapKeys 返回 map 的所有键
func getMapKeys(m map[string]string) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}
