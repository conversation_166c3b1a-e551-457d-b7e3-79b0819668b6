import * as winston from 'winston';
import {isMainThread} from "worker_threads";
import {addWorkerLoggingTask} from "../worker/queue/logging";
import {FeatureGateManager, FeatureName} from "../core/common/config";

const TAG = '[CKG-Analyzer]';

export const localLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ level, message, timestamp }) => {
      return `${timestamp} [${level}]: ${message}`;
    }),
  ),
  transports: [
    new winston.transports.Console(),
  ],
});

export interface Logger {
  info(msg: string): void
  warn(msg: string): void
  error(msg: string): void
}

let clientLogger: Logger = null;
let enableLocalLogging = false;

export function setClientLogger(logger: Logger): void {
  clientLogger = logger;
}

export function setEnableLocalLogging(enable: boolean): void {
  enableLocalLogging = enable;
}

export namespace Logger {
  export function info(msg: string): void {
    if (!FeatureGateManager.INSTANCE.isFeatureOpen(FeatureName.DISABLE_LOG)) {
      if (isMainThread) {
        if (clientLogger) {
          clientLogger.info(`${TAG} ${msg}`);
        } else {
          if (enableLocalLogging) {
            localLogger.info(`${TAG} ${msg}`);
          }
        }
      } else {
        addWorkerLoggingTask({level: "info", msg: `[Worker] ${msg}`});
      }
    }
  }

  export function warn(msg: string): void {
    if (!FeatureGateManager.INSTANCE.isFeatureOpen(FeatureName.DISABLE_LOG)) {
      if (isMainThread) {
        if (clientLogger) {
          clientLogger.warn(`${TAG} ${msg}`);
        } else {
          if (enableLocalLogging) {
            localLogger.warn(`${TAG} ${msg}`);
          }
        }
      } else {
        addWorkerLoggingTask({level: "warn", msg: `[Worker] ${msg}`});
      }
    }
  }

  export function error(msg: string): void {
    if (!FeatureGateManager.INSTANCE.isFeatureOpen(FeatureName.DISABLE_LOG)) {
      if (isMainThread) {
        if (clientLogger) {
          clientLogger.error(`${TAG} ${msg}`);
        } else {
          if (enableLocalLogging) {
            localLogger.error(`${TAG} ${msg}`);
          }
        }
      } else {
        addWorkerLoggingTask({level: "error", msg: `[Worker] ${msg}`});
      }
    }
  }
}