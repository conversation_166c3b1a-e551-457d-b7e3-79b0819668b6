import type {Language, Tree, Input, Query, Point, QueryCapture, SyntaxNode, QueryMatch} from 'web-tree-sitter';
import type ParserT from 'web-tree-sitter';
import {Logger} from '../../../utils/logger';
import {LanguageIdEnum} from "../share";

// eslint-disable-next-line @typescript-eslint/no-var-requires
const Parser = require('web-tree-sitter');

enum WasmLanguageEnum {
  Python = 'python',
  Javascript = 'javascript',
  Typescript = 'typescript',
  TypescriptReact = 'typescriptreact',
  Go = 'go',
  Ruby = 'ruby',
  Java = 'java',
  C = 'c',
  Cpp = 'cpp',
  CSharp = 'c-sharp',
  Kotlin = 'kotlin',
  Dart = 'dart',
  Php = 'php',
  Rust = 'rust',
}

export enum ParserTimeout {
  Default_5000_Ms = 5_000,
  Completion_70_Ms = 70,
}

export const languageIdToWasmLanguageMap = new Map<string, WasmLanguageEnum>([
  [LanguageIdEnum.Python.toString(), WasmLanguageEnum.Python],
  [LanguageIdEnum.Javascript.toString(), WasmLanguageEnum.Javascript],
  [LanguageIdEnum.JavascriptReact.toString(), WasmLanguageEnum.Javascript],
  [LanguageIdEnum.Jsx.toString(), WasmLanguageEnum.Javascript],
  [LanguageIdEnum.Typescript.toString(), WasmLanguageEnum.Typescript],
  [LanguageIdEnum.TypescriptReact.toString(), WasmLanguageEnum.TypescriptReact],
  [LanguageIdEnum.Tsx.toString(), WasmLanguageEnum.TypescriptReact],
  [LanguageIdEnum.Go.toString(), WasmLanguageEnum.Go],
  [LanguageIdEnum.Ruby.toString(), WasmLanguageEnum.Ruby],
  [LanguageIdEnum.Java.toString(), WasmLanguageEnum.Java],
  [LanguageIdEnum.C.toString(), WasmLanguageEnum.C],
  [LanguageIdEnum.Cpp.toString(), WasmLanguageEnum.Cpp],
  [LanguageIdEnum.CSharp.toString(), WasmLanguageEnum.CSharp],
  [LanguageIdEnum.Dart.toString(), WasmLanguageEnum.Dart],
  [LanguageIdEnum.Php.toString(), WasmLanguageEnum.Php],
  [LanguageIdEnum.Kotlin.toString(), WasmLanguageEnum.Kotlin],
  [LanguageIdEnum.Rust.toString(), WasmLanguageEnum.Rust],
]);

function createParserKey(language: WasmLanguageEnum, timeout: ParserTimeout): string {
  return `${language}_${timeout}`;
}

export interface MatchResult {
  pattern: number;
  matches: Map<string, SyntaxNode[]>;
}

export class TreeSitterParser {
  static INSTANCE = new TreeSitterParser();

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
  }

  initialized = false;
  private supportLanguageMap = new Map<WasmLanguageEnum, Language>();
  private resourcesRootPath = '';
  private cachedParsers = new Map<string, ParserT>();

  async init(resourcesRootPath: string): Promise<void> {
    // Currently ignore multiple init calls, despite that the resourceRootPath may be different
    if (this.initialized) {
      return;
    }

    try {
      Logger.info(`TreeSitter.init: start to init tree-sitter instance with resourcesRootPath '${resourcesRootPath}'`);

      this.resourcesRootPath = resourcesRootPath;
      await Parser.init();
      await this.loadLanguage(WasmLanguageEnum.Typescript);
      await this.loadLanguage(WasmLanguageEnum.Javascript);
      await this.loadLanguage(WasmLanguageEnum.Go);
      await this.loadLanguage(WasmLanguageEnum.Python);
      await this.loadLanguage(WasmLanguageEnum.Ruby);
      await this.loadLanguage(WasmLanguageEnum.Java);
      await this.loadLanguage(WasmLanguageEnum.TypescriptReact);
      await this.loadLanguage(WasmLanguageEnum.C);
      await this.loadLanguage(WasmLanguageEnum.Cpp);
      await this.loadLanguage(WasmLanguageEnum.CSharp);
      await this.loadLanguage(WasmLanguageEnum.Kotlin);
      await this.loadLanguage(WasmLanguageEnum.Dart);
      await this.loadLanguage(WasmLanguageEnum.Php);
      await this.loadLanguage(WasmLanguageEnum.Rust);

      this.initialized = true;
      Logger.info(`TreeSitter.init: init done with resourcesRootPath '${resourcesRootPath}'`);
    } catch (error) {
      Logger.error(`TreeSitter.init: failed to init tree-sitter instance: ${error}`);
    }
  }

  private async loadLanguage(language: WasmLanguageEnum): Promise<void> {
    const wasmLanguage = await Parser.Language.load(`${this.resourcesRootPath}/wasms/tree-sitter-${language}.wasm`);
    this.supportLanguageMap.set(language, wasmLanguage);
    Logger.info(`TreeSitter.loadLanguage: load language ${language} done`);
  }

  private getLanguage(languageId: string): Language | undefined {
    const wasmLanguageId = languageIdToWasmLanguageMap.get(languageId);
    if (!wasmLanguageId) {
      return undefined;
    }
    return this.supportLanguageMap.get(wasmLanguageId);
  }

  private getParser(languageId: string, timeout: ParserTimeout): ParserT | undefined {
    const wasmLanguageId = languageIdToWasmLanguageMap.get(languageId);
    if (!wasmLanguageId) {
      return undefined;
    }
    const wasmLanguage = this.supportLanguageMap.get(wasmLanguageId);
    if (!wasmLanguage) {
      return undefined;
    }

    const key = createParserKey(wasmLanguageId, timeout);
    if (!this.cachedParsers.get(key)) {
      const parser = new Parser();
      parser.setLanguage(wasmLanguage);
      parser.setTimeoutMicros(timeout * 1_000);
      this.cachedParsers.set(key, parser);
      Logger.info(`TreeSitter.getParser: parser for languageId ${languageId} is created`);
    }

    return this.cachedParsers.get(key);
  }

  private removeParser(languageId: string, timeout: ParserTimeout): void {
    const wasmLanguageId = languageIdToWasmLanguageMap.get(languageId);
    if (!wasmLanguageId) {
      return;
    }

    const key = createParserKey(wasmLanguageId, timeout);
    if (this.cachedParsers.get(key)) {
      this.cachedParsers.delete(key);
      Logger.info(`TreeSitter.removeParser: parser for languageId ${languageId} is removed`);
    }
  }

  private safeParse(parser: ParserT, text: string | Input, languageId: string, timeout: ParserTimeout, isRemoveParser: boolean, oldTree?: Tree): Tree | undefined {
    try {
      return parser.parse(text, oldTree);
    } catch (e) {
      Logger.error(`safeParse: tree-sitter parse error: ${e}`);
      Logger.error(e.stack);

      if (isRemoveParser && e.message?.includes("memory access out of bounds")) {
        this.removeParser(languageId, timeout);
        const newParser = this.getParser(languageId, timeout);

        return this.safeParse(newParser, text, languageId, timeout, false, oldTree);
      }

      return undefined;
    }
  }

  isLanguageSupported(languageId: string): boolean {
    return this.getLanguage(languageId) !== undefined;
  }

  parse(languageId: string, text: string | Input, oldTree?: Tree, timeout: ParserTimeout = ParserTimeout.Default_5000_Ms): Tree | undefined {
    if (!this.initialized) {
      Logger.error("TreeSitter.parse: the TreeSitter instance is not initialized");
      return undefined;
    }

    const parser = this.getParser(languageId, timeout);
    if (parser === undefined) {
      return undefined;
    }

    return this.safeParse(parser, text, languageId, timeout, true, oldTree);
  }

  query(languageId: string, queryStr: string): Query | undefined {
    if (!this.initialized) {
      Logger.error("TreeSitter.query: the TreeSitter instance is not initialized");
      return undefined;
    }

    const wasmLanguage = this.getLanguage(languageId);
    if (wasmLanguage === undefined) {
      Logger.error(`TreeSitter.query: languageId ${languageId} is not supported`);
      return undefined;
    }

    return wasmLanguage.query(queryStr);
  }

  queryAndMatchForText(languageId: string, queryStr: string, targetText: string, timeout: ParserTimeout = ParserTimeout.Default_5000_Ms): QueryMatch[] {
    const queryResult = TreeSitterParser.INSTANCE.query(languageId, queryStr);
    if (queryResult === undefined) {
      return [];
    }

    const parseResult = TreeSitterParser.INSTANCE.parse(languageId, targetText, undefined, timeout);
    if (parseResult === undefined) {
      return [];
    }

    return queryResult.matches(parseResult.rootNode);
  }

  queryAndMatchForNode(languageId: string, queryStr: string, targetNode: SyntaxNode, startPosition?: Point, endPosition?: Point): QueryMatch[] {
    const queryResult = TreeSitterParser.INSTANCE.query(languageId, queryStr);
    if (queryResult === undefined) {
      return [];
    }

    if (startPosition && endPosition) {
      return queryResult.matches(targetNode, {startPosition, endPosition});
    } else {
      return queryResult.matches(targetNode);
    }
  }

  queryAndCaptureForText(languageId: string, queryStr: string, targetText: string, timeout: ParserTimeout = ParserTimeout.Default_5000_Ms): QueryCapture[] {
    const queryResult = TreeSitterParser.INSTANCE.query(languageId, queryStr);
    if (queryResult === undefined) {
      return [];
    }

    const parseResult = TreeSitterParser.INSTANCE.parse(languageId, targetText, undefined, timeout);
    if (parseResult === undefined) {
      return [];
    }

    return queryResult.captures(parseResult.rootNode);
  }

  queryAndCaptureForNode(languageId: string, queryStr: string, targetNode: SyntaxNode, startPosition?: Point, endPosition?: Point): QueryCapture[] {
    const queryResult = TreeSitterParser.INSTANCE.query(languageId, queryStr);
    if (queryResult === undefined) {
      return [];
    }

    if (startPosition && endPosition) {
      return queryResult.captures(targetNode, {startPosition, endPosition});
    } else {
      return queryResult.captures(targetNode);
    }
  }

  queryAndHandleMatchForNode(languageId: string, queryStr: string, node: SyntaxNode, startPosition?: Point, endPosition?: Point): MatchResult[] {
    const result: MatchResult[] = [];

    const matches = TreeSitterParser.INSTANCE.queryAndMatchForNode(languageId, queryStr, node, startPosition, endPosition);
    for (const match of matches) {
      const matchResult = {
        pattern: match.pattern,
        matches: new Map<string, SyntaxNode[]>(),
      };

      for (const capture of match.captures) {
        if (!matchResult.matches.has(capture.name)) {
          matchResult.matches.set(capture.name, []);
        }
        matchResult.matches.get(capture.name).push(capture.node);
      }

      result.push(matchResult);
    }

    return result;
  }
}