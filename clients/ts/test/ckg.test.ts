// import { getCkgDirByCurrentProject } from "utils/ckg";
import { describe, expect, it } from '@jest/globals';
import { CKG, CKGOptions } from '../src/ckg';
import { CKGProcessStatus, SourceProductType } from '../src/process/process';
import { AccessBeforeInitError, AccessDeadProcessError } from '../src/errors/errors';

function sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
}
describe('CKG', () => {
    // let ckg: CKG; 
    let options: CKGOptions = {
        // knowledgebase_id: 'ckg_v2_codeverse_test_wgq',
        // host: 'https://copilot.byted.org/',
        // app_id: 'a4c6c500-6846-45b6-94f6-1b231eb53742',
        storagePath: '/Users/<USER>/tmp/storage_test',
        binaryRootDir: '/Users/<USER>/tmp/binary',
        userId: "jiagengli",
        sourceProduct: SourceProductType.Codeverse,
        // ide_version: '1.92.2',
        // version_code: '2002104',
        // region: 'cn',
        // extension_version: '2.2.104',
        // limit_cpu: 1,
        // source_product: 'codeverse'
    }
    it('should start a process and kill it', async () => {

        let ckg = await CKG.getInstance();
        expect(ckg).toBeInstanceOf(CKG);
        // 先起process
        try {
            await CKG.init(options);
        } catch (err) {
            console.log(err);
        }
        expect((await ckg.getProcessStatus()).toString()).toBe(CKGProcessStatus.RUNNING)
        // expect(await ckg.test_process_exist()).toBe(true);

        const resp = await ckg.indexWorkspace([]);
        await sleep(3000);

    }, 150000);

    it('test singleton', async () => {
        let ckg = await CKG.getInstance();
        const resp = await ckg.indexWorkspace([]);
        expect(await ckg.killProcess()).toBe(true);
        expect((await ckg.getProcessStatus()).toString()).toBe(CKGProcessStatus.NOTEXIST)
    }, 5000);

    it('test access before init', async () => {
        let ckg = await CKG.getInstance();
        try {
            const resp = await ckg.indexWorkspace([]);
        } catch (e) {
            expect(e).toBeInstanceOf(AccessDeadProcessError);
        }
        
    })



});
