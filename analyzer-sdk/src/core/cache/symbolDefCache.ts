import {LRUCache} from "lru-cache";
import {Logger} from "../../utils/logger";
import type {TextDocument} from "vscode-languageserver-textdocument";
import {FileSymbolDef} from "../share/common";
import {EVENT_CATEGORY_CACHE, EVENT_NAME_FAILED, EVENT_VALUE_NO_CACHE, reportEvent} from "../../utils/event";

const fileSymbolDefLRUCache = new LRUCache<string, FileSymbolDef>({
  max: 30,
  ttl: 60 * 60 * 1000,
  ttlAutopurge: true,
  updateAgeOnGet: true,
});

const EVENT_PROCESS_GET_FILE_SYMBOL_DEF = "get_file_symbol_def";

export function clearCachedFileSymbolDef(): void {
  fileSymbolDefLRUCache.clear();
}

export function hasFileSymbolDefCached(document: TextDocument): boolean {
  return fileSymbolDefLRUCache.has(document.uri);
}

export function getCachedFileSymbolDef(document: TextDocument, allowMiss: boolean): FileSymbolDef {
  const key = document.uri;
  const cache = fileSymbolDefLRUCache.get(key);

  if (!cache) {
    if (!allowMiss) {
      Logger.warn(`getCachedFileSymbolDef: no cached data found for '${document.uri}'`);
      reportEvent(document.languageId, EVENT_CATEGORY_CACHE, EVENT_PROCESS_GET_FILE_SYMBOL_DEF, EVENT_NAME_FAILED, EVENT_VALUE_NO_CACHE).then();
    }
    return null;
  }

  return cache;
}

export function getAllCachedFileSymbolDef(): FileSymbolDef[] {
  const result: FileSymbolDef[] = [];

  fileSymbolDefLRUCache.forEach(value => {
    result.push(value);
  });

  return result;
}

export function updateFileSymbolDefCache(document: TextDocument, fileSymbolDef: FileSymbolDef): void {
  const key = document.uri;
  fileSymbolDefLRUCache.set(key, fileSymbolDef);
}