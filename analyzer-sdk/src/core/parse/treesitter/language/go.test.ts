import {beforeAll, describe, expect, it} from "vitest";
import {TreeSitterParser} from "../parser";
import {GoTreeSitterHandler} from "./go";
import {LanguageIdEnum} from "../../share";
import {SyntaxNode} from "web-tree-sitter";
import {createDocument} from "../../../../utils/common";

describe("test_getVarTypeNameNode", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_var_spec", async () => {
    const docText = `
func m1(t *p.T) {
  var a int
  var b p.T1
  var c *p.T2
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);

    const queryString = `
(var_spec) @var_spec
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Go, queryString, tree.rootNode);

    const varDeclNode: SyntaxNode[] = []
    for (const match of matchResult) {
      const declNode = match.matches.get("var_spec");

      if (declNode.length === 1) {
        varDeclNode.push(declNode[0]);
      }
    }

    const varNameMap = new Map<number, string[]>();
    varNameMap.set(0, ["a"]);
    varNameMap.set(1, ["b"]);
    varNameMap.set(2, ["c"]);
    const result = varDeclNode.map((node: SyntaxNode, index: number) => {
      return varNameMap.get(index).map((varName) => {
        return GoTreeSitterHandler.INSTANCE.getVarTypeNameNode(node, varName)?.node.text;
      });
    }).flat();

    expect(result).toEqual(["int", "T1", "T2"]);
  });

  it("case2_short_var_declaration", async () => {
    const docText = `
func m1(t *p.T) {
  a, b := T1{}, T2{}
  c, d := invoke()
  e, f := t.m1(f())
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);

    const queryString = `
(short_var_declaration) @short_var_declaration
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Go, queryString, tree.rootNode);

    const varDeclNode: SyntaxNode[] = []
    for (const match of matchResult) {
      const declNode = match.matches.get("short_var_declaration");

      if (declNode.length === 1) {
        varDeclNode.push(declNode[0]);
      }
    }

    const varNameMap = new Map<number, string[]>();
    varNameMap.set(0, ["a", "b"]);
    varNameMap.set(1, ["c", "d"]);
    varNameMap.set(2, ["e", "f"]);
    const result = varDeclNode.map((node: SyntaxNode, index: number) => {
      return varNameMap.get(index).map((varName) => {
        const result = GoTreeSitterHandler.INSTANCE.getVarTypeNameNode(node, varName);
        return `${result?.node?.text ?? ""}@${result?.index ?? ""}`;
      });
    }).flat();

    expect(result).toEqual(["T1@-1", "T2@-1", "invoke@0", "invoke@1", "m1@0", "m1@1"]);
  });
});

describe("test_getDeclVariableInFile", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
func m1(t *p.T) {
  a, b := T1{}, T2{}
  c, d := invoke()
  e, f := t.m1(f())
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);

    const result = GoTreeSitterHandler.INSTANCE.getDeclVariableInFile(tree.rootNode);

    expect(result.map(node => node.text)).toEqual(["t", "a", "b", "c", "d", "e", "f"]);
  });
});

describe("test_getUsageVariableInFile", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
func m1(t *p.T) {
  a, b := T1{}, T2{}
  c, d := invoke()
  e, f := t.m1(f())
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);

    const result = GoTreeSitterHandler.INSTANCE.getUsageVariableInFile(tree.rootNode);

    expect(result.map(node => node.text)).toEqual(["t"]);
  });
});

describe("test_getMethodInRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_completely_included", async () => {
    const docText = `
func (p *T) m1() {
  var a int
  var b int
}

func (p *T) m2() {
  var a int
  var b int
}

func f1(t *p.T) {
  var a int
  
  var v2 = func(t *p.T) {
    var a int
    var b int
  }
  
  var b int
}

var v1 = func(t *p.T) {
  var a int
  var b int
}
`;

    const document = createDocument(docText, LanguageIdEnum.Go, "file:///test.go");
    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);

    const range1 = {
      start: {
        line: 3,
        character: 3,
      },
      end: {
        line: 16,
        character: 5,
      },
    };

    const result1 = GoTreeSitterHandler.INSTANCE.getMethodInRange(tree.rootNode, range1, document.offsetAt(range1.start), document.offsetAt(range1.end), true);

    expect(result1.map(node => node.text)).toEqual([
      `
func (p *T) m2() {
  var a int
  var b int
}
`,
    ].map(item => item.trim()));

    const range2 = {
      start: {
        line: 3,
        character: 3,
      },
      end: {
        line: 19,
        character: 5,
      },
    };

    const result2 = GoTreeSitterHandler.INSTANCE.getMethodInRange(tree.rootNode, range2, document.offsetAt(range2.start), document.offsetAt(range2.end), true);

    expect(result2.map(node => node.text)).toEqual([
      `
func (p *T) m2() {
  var a int
  var b int
}
`,
      `
func(t *p.T) {
    var a int
    var b int
  }
`,
    ].map(item => item.trim()));
  });

  it("case2_not_completely_included", async () => {
    const docText = `
func (p *T) m1() {
  var a int
  var b int
}

func (p *T) m2() {
  var a int
  var b int
}

func f1(t *p.T) {
  var a int
  
  var v2 = func(t *p.T) {
    var a int
    var b int
  }
  
  var b int
}

var v1 = func(t *p.T) {
  var a int
  var b int
}
`;

    const document = createDocument(docText, LanguageIdEnum.Go, "file:///test.go");
    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);

    const range = {
      start: {
        line: 3,
        character: 3,
      },
      end: {
        line: 16,
        character: 5,
      },
    };

    const result = GoTreeSitterHandler.INSTANCE.getMethodInRange(tree.rootNode, range, document.offsetAt(range.start), document.offsetAt(range.end), false);

    expect(result.map(node => node.text)).toEqual([
      `
func (p *T) m1() {
  var a int
  var b int
}
`,
      `
func (p *T) m2() {
  var a int
  var b int
}
`,
      `
func f1(t *p.T) {
  var a int
  
  var v2 = func(t *p.T) {
    var a int
    var b int
  }
  
  var b int
}
`,
    ].map(item => item.trim()));
  });
});

describe("test_getInvokeInFile", () => {
  it("case1_normal", async () => {
    const docText = `
func m1(t *p.T) {
  f()
  t.m1();
  t.f1.m2();
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);

    const result = GoTreeSitterHandler.INSTANCE.getInvokeInFile(tree.rootNode);

    expect(result.map(node => node.text)).toEqual(["f", "m1", "m2"]);
  });
});

describe("test_getImportInfo", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
import "context1"
import "io/fs1"
import s1 "ide/ckg/codekg/components/data_storage1"
import (
  "context2"
  "io/fs2"
  s2 "ide/ckg/codekg/components/data_storage2"
)
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);

    const result = GoTreeSitterHandler.INSTANCE.getImportInfo(tree.rootNode);

    const expectedResult = new Map<string, string>();
    expectedResult.set("context1", "context1");
    expectedResult.set("fs1", "io/fs1");
    expectedResult.set("s1", "ide/ckg/codekg/components/data_storage1");
    expectedResult.set("context2", "context2");
    expectedResult.set("fs2", "io/fs2");
    expectedResult.set("s2", "ide/ckg/codekg/components/data_storage2");

    expect(result).toEqual(expectedResult);
  });
});