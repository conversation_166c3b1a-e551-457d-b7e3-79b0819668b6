import {TextDocument} from "vscode-languageserver-textdocument";
import {Range} from "vscode-languageserver-types";
import {TreeSitterDispatcher} from "../../parse/treesitter/dispatcher";
import {TreeSitterFunctionalityEnum} from "../../parse/treesitter/share";
import {TreeSitterParser} from "../../parse/treesitter/parser";
import {SyntaxNode} from "web-tree-sitter";
import {pointToPosition} from "../../../utils/common";
import {TreeSitterTypeCategoryEnum} from "../../parse/treesitter/language/common";

export function doGetDefinitionNodeRange(document: TextDocument, range: Range): Range {
  const tree = TreeSitterParser.INSTANCE.parse(document.languageId, document.getText());

  const funcNodeTypes = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetTreeSitterNodeTypes, TreeSitterTypeCategoryEnum.FUNCTION);
  const typeNodeTypes = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetTreeSitterNodeTypes, TreeSitterTypeCategoryEnum.TYPE);

  const targetParentNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetParentNodeWithTypeListForRange, tree.rootNode, range, [...funcNodeTypes, ...typeNodeTypes]);

  return {start: pointToPosition(targetParentNode.startPosition), end: pointToPosition(targetParentNode.endPosition)};
}