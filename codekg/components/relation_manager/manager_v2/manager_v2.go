package manager_v2

import (
	"context"
	"encoding/json"
	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/env"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/relation_manager/git_graph"
	"ide/ckg/codekg/components/relation_manager/interaction_graph"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/protocol"
	"ide/ckg/codekg/util"
	"os"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-git/go-git/v5"
	lru "github.com/hashicorp/golang-lru/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"ide/ckg/codekg/components/logs"
)

const (
	// git 相关配置
	GitMaxNodes              = 50
	GitMaxCommit             = 300
	GitMaxFileCountPerCommit = 100
	GitMinFileCountPerCommit = 3
	GitCurrentUserOnly       = false
	GitMinFileChangeLine     = 3
	GitMaxFileChangeLine     = 1000
	GitMaxGraphNodeCount     = 300
	GitMaxGraphEdgeCount     = 50000

	MinNodes                        = 3
	MaxLRUFileCacheSize             = 100
	MaxSizeOfRecentInteractionNodes = 100
	NumberOfRecentFiles             = 5
	SurroundLines                   = 10
	NotCKGEntityPrefix              = "nockg"
)

type fileEntityCache struct {
	uriMeta  *data_storage.StorageURIMeta
	entities []*model.Entity
}

type Manager struct {
	project             string
	gitGraph            *git_graph.GitGraph
	getStorage          func() data_storage.Storage
	getEmbeddingStorage func() data_storage.EmbeddingStorage
	config              *ckg_config.Config

	recentOpenFileCache   *lru.Cache[string, *fileEntityCache]                               // 维护最近打开的文件文件
	repo                  *git.Repository                                                    // 维护 Git 构建的关系
	interactionGraph      *interaction_graph.InteractionGraph                                // 维护用户行为构建出来的图结构
	recentInteractionList *interaction_graph.AccessQueue[*interaction_graph.InteractionNode] // 维护用户行为构建出来的时间序列关系
}

func NewManager(project string, userID string, config *ckg_config.Config, getStorage func() data_storage.Storage, getEmbeddingStorage func() data_storage.EmbeddingStorage) *Manager {
	var (
		gitGraph *git_graph.GitGraph
		err      error
	)

	repo, err := git.PlainOpen(project)
	if err != nil {
		logs.Warn("failed to open git repo, err: %v", err)
	} else {
		gitGraph, err = git_graph.NewGitGraph(project)
		if err != nil {
			logs.Warn("failed to open git graph, err: %v", err)
		} else {
			// 异步初始化
			util.SafeGo(context.Background(), func() {
				err = gitGraph.BuildGitGraph(context.Background(), userID, &git_graph.BuildGitGraphOption{
					CommitLimit:       GitMaxCommit,
					MaxCommitSize:     GitMaxFileCountPerCommit,
					MinCommitSize:     GitMinFileCountPerCommit,
					CurrentUserOnly:   GitCurrentUserOnly,
					MinChangeSize:     GitMinFileChangeLine,
					MaxChangeSize:     GitMaxFileChangeLine,
					MaxGraphNodeCount: GitMaxGraphNodeCount,
					MaxGraphEdgeCount: GitMaxGraphEdgeCount,
				})
				if err != nil {
					logs.Warn("failed to build git graph, err: %v", err)
				}
			})
		}
	}

	interactionGraph := interaction_graph.NewInteractionGraph()
	recentInteractionList := interaction_graph.NewAccessQueue[*interaction_graph.InteractionNode](
		MaxSizeOfRecentInteractionNodes,
		func(n *interaction_graph.InteractionNode) string {
			return n.ID
		},
	)

	recentOpenFileCache, _ := lru.New[string, *fileEntityCache](MaxLRUFileCacheSize)

	return &Manager{
		project:               project,
		getStorage:            getStorage,
		getEmbeddingStorage:   getEmbeddingStorage,
		config:                config,
		repo:                  repo,
		interactionGraph:      interactionGraph,
		recentInteractionList: recentInteractionList,
		recentOpenFileCache:   recentOpenFileCache,
		gitGraph:              gitGraph,
	}
}

func (m *Manager) deduplicateEntities(entities []*model.Entity) []*model.Entity {
	seen := make(map[string]bool)
	result := make([]*model.Entity, 0)

	for _, entity := range entities {
		if !seen[entity.ID] {
			seen[entity.ID] = true
			result = append(result, entity)
		}
	}
	return result
}

func (m *Manager) filterCurrentFileEntities(entities []*model.Entity, currentEntity *model.Entity) []*model.Entity {
	if currentEntity == nil {
		return entities
	}

	result := make([]*model.Entity, 0)
	for _, entity := range entities {
		if entity.URI != currentEntity.URI {
			result = append(result, entity)
		}
	}
	return result
}

func (m *Manager) deduplicateNodes(nodes []*interaction_graph.InteractionNode) []*interaction_graph.InteractionNode {
	seen := make(map[string]bool)
	result := make([]*interaction_graph.InteractionNode, 0)

	for _, node := range nodes {
		if !seen[node.ID] {
			seen[node.ID] = true
			result = append(result, node)
		}
	}
	return result
}

func (m *Manager) filterCurrentFileNodes(nodes []*interaction_graph.InteractionNode, curURI string) []*interaction_graph.InteractionNode {
	if curURI == "" {
		return nodes
	}
	result := make([]*interaction_graph.InteractionNode, 0)
	for _, node := range nodes {
		if node.Entity.URI != curURI {
			result = append(result, node)
		}
	}
	return result
}

func (m *Manager) convertNodesToSnippets(ctx context.Context, nodes []*interaction_graph.InteractionNode) []*protocol.Snippet {
	snippets := make([]*protocol.Snippet, 0)
	for _, n := range nodes {
		nodeContent := n.GetNodeContent(ctx)
		if nodeContent == "" {
			continue
		}

		recallType := protocol.RecallType_recall_type_relation_by_user_action_trace
		if n.GetSourceType() == "git" {
			recallType = protocol.RecallType_recall_type_relation_by_git_relevance
		}

		snippets = append(snippets, &protocol.Snippet{
			ProjectId:   m.project,
			Type:        protocol.SnippetType_snippet_type_code,
			FilePath:    n.Entity.URI,
			StartLine:   n.Entity.GetStartLine(),
			EndLine:     n.Entity.GetEndLine(),
			Content:     nodeContent,
			RecallType:  recallType,
			CkgEntityId: n.ID,
		})
	}

	// 根据行号排序
	sort.Slice(snippets, func(i, j int) bool {
		if snippets[i].FilePath != snippets[j].FilePath {
			return snippets[i].FilePath < snippets[j].FilePath
		}
		return snippets[i].StartLine < snippets[j].StartLine
	})
	return snippets
}

func (m *Manager) getCKGEntitiesFromFile(ctx context.Context, filePath string) []*model.Entity {
	storage := m.getStorage()
	if storage == nil {
		logs.CtxInfo(ctx, "getCKGEntitiesFromFile: storage is not available")
		return nil
	}

	filePath = util.GetURIFromFilePathForCursorMove(filePath)
	logs.CtxInfo(ctx, "getCKGEntitiesFromFile GetURIMetaFromURIWithoutContent, filePath: %v", filePath)
	uriMeta, err := storage.GetURIMetaFromURIWithoutContent(ctx, storage.GetConn(), &model.URIStatus{
		AbsPath: filePath,
	})
	if err != nil {
		logs.CtxInfo(ctx, "getCKGEntitiesFromFile: failed to get uri meta: %v", err)
		return nil
	}

	// 检查缓存
	cacheValue, ok := m.recentOpenFileCache.Get(filePath)
	if ok && cacheValue.uriMeta.ContentHash == uriMeta.ContentHash {
		return cacheValue.entities
	}
	// hash 值不相同，需要重新获取，先删除
	if ok {
		m.recentOpenFileCache.Remove(filePath)
	}

	dbEntities, err := storage.SearchEntitiesByURIMeta(ctx, storage.GetConn(), uriMeta)
	if err != nil {
		logs.CtxInfo(ctx, "getCKGEntitiesFromFile: failed to search entities by uri meta: %v", err)
		return nil
	} else if len(dbEntities) == 0 {
		logs.CtxInfo(ctx, "getCKGEntitiesFromFile: no entities found for %s in CKG", filePath)
		return nil
	}

	entities := make([]*model.Entity, 0)
	for _, dbEntity := range dbEntities {
		// 只保留这些实体
		if dbEntity.Type != model.Clazz && dbEntity.Type != model.Method && dbEntity.Type != model.CodeChunk && dbEntity.Type != model.Text {
			continue
		}
		var attributes map[string]interface{}
		err = json.Unmarshal([]byte(dbEntity.Attributes), &attributes)
		if err != nil {
			continue
		}

		entities = append(entities, model.CreateEntity(dbEntity.EntityID, dbEntity.Name, uriMeta.Uri, dbEntity.Type, attributes))
	}

	// 更新缓存
	if len(entities) > 0 {
		m.recentOpenFileCache.ContainsOrAdd(filePath, &fileEntityCache{
			uriMeta:  uriMeta,
			entities: entities,
		})
	} else {
		logs.CtxInfo(ctx, "getCKGEntitiesFromFile: no entities found for %s in CKG", filePath)
	}

	return entities
}

/** 获取文件对应的实体，优先从CKG获取，如果CKG没有实体或出错(e.g. 仓库外的文件)，则从本地获取 **/
func (m *Manager) getEntitiesFromFile(ctx context.Context, filePath string, cursorLine int32) []*model.Entity {
	var entities []*model.Entity

	cacheValue, ok := m.recentOpenFileCache.Get(filePath)
	if ok {
		// 检查下是不是包括当前行的实体，如果没有包括，需要重新走获取的流程
		cursorExists := false
		for _, entity := range cacheValue.entities {
			if entity.GetStartLine() <= cursorLine && cursorLine <= entity.GetEndLine() {
				cursorExists = true
				break
			}
		}
		if cursorExists {
			return cacheValue.entities
		}
	}

	// 尝试从CKG获取实体
	entities = m.getCKGEntitiesFromFile(ctx, filePath)

	// CKG 中没有，尝试从本地创建一个
	if len(entities) == 0 {
		// 如果没有实体，尝试创建一个非CKG实体
		nonCKGEntity := m.getCreateNotCKGEntity(ctx, filePath, cursorLine)
		return []*model.Entity{nonCKGEntity}
	}

	return entities
}

func (m *Manager) getCreateNotCKGEntityFromFile(ctx context.Context, filePath string) []*model.Entity {
	entities := make([]*model.Entity, 0)

	content, err := os.ReadFile(filePath)
	if err != nil {
		logs.CtxInfo(ctx, "failed to read file %s, err: %v", filePath, err)
		return entities
	}
	totalLines := len(strings.Split(string(content), "\n"))
	totalLines = min(totalLines, 1000) // 最多取1000行，防止实体太多了

	startLine := 0
	if totalLines >= SurroundLines {
		startLine = SurroundLines
	}

	for i := startLine; i < totalLines; i += SurroundLines * 2 {
		entities = append(entities, m.getCreateNotCKGEntity(ctx, filePath, int32(i)))
	}

	return entities
}

// 创建一个非CKG实体
func (m *Manager) getCreateNotCKGEntity(ctx context.Context, filePath string, cursorLine int32) *model.Entity {
	content, err := os.ReadFile(filePath)
	if err != nil {
		logs.CtxInfo(ctx, "failed to read file %s, err: %v", filePath, err)
		return nil
	}

	nonCKGEntityID := NotCKGEntityPrefix + "_" + filePath + "_" + strconv.Itoa(int(cursorLine))
	startLine := max(0, cursorLine-SurroundLines)
	endLine := min(cursorLine+SurroundLines, int32(len(strings.Split(string(content), "\n"))))
	lines := strings.Split(string(content), "\n")
	selectedContent := strings.Join(lines[startLine:endLine], "\n")
	nonCKGEntity := model.CreateEntity(
		nonCKGEntityID,
		filePath,
		filePath,
		model.CodeChunk,
		map[string]interface{}{
			model.AttributeLabelContent:   selectedContent,
			model.AttributeLabelStartLine: float64(startLine),
			model.AttributeLabelEndLine:   float64(endLine),
		},
	)

	//logs.CtxInfo(ctx, "create not CKG entity: %+v", nonCKGEntity)

	// 更新历史打开文件的缓存
	notCKGEntityCache, _ := m.recentOpenFileCache.Get(filePath)
	if notCKGEntityCache != nil {
		notCKGEntityCache.entities = append(notCKGEntityCache.entities, nonCKGEntity)
		m.recentOpenFileCache.ContainsOrAdd(filePath, notCKGEntityCache)
	} else {
		m.recentOpenFileCache.ContainsOrAdd(filePath, &fileEntityCache{
			uriMeta:  nil, // 不需要hash值
			entities: []*model.Entity{nonCKGEntity},
		})
	}

	return nonCKGEntity
}

func (m *Manager) getEntitiesFromFileLine(ctx context.Context, filePath string, cursorLine int32) ([]*model.Entity, error) {
	entities := m.getEntitiesFromFile(ctx, filePath, cursorLine)
	if len(entities) == 0 {
		return nil, errors.New("no entities found")
	}

	hitEntities := lo.Filter(entities, func(item *model.Entity, _ int) bool {
		return item.GetStartLine() <= cursorLine && cursorLine <= item.GetEndLine()
	})

	if len(hitEntities) == 0 {
		return nil, errors.New("no entities found")
	}
	return hitEntities, nil
}

func (m *Manager) getNodeFromFileLine(ctx context.Context, filePath string, cursorLine int32) (*interaction_graph.InteractionNode, error) {
	entities, err := m.getEntitiesFromFileLine(ctx, filePath, cursorLine)
	if err != nil {
		return nil, err
	}

	sort.Slice(entities, func(i, j int) bool {
		return entities[i].GetEndLine()-entities[i].GetStartLine() < entities[j].GetEndLine()-entities[j].GetStartLine()
	})
	currentEntity := entities[0]

	if m.interactionGraph.HasNode(currentEntity.ID) {
		return m.interactionGraph.GetNode(currentEntity.ID), nil
	} else {
		return interaction_graph.NewInteractionNode(currentEntity), nil
	}
}

func (m *Manager) getNodeFromRequest(ctx context.Context, request *protocol.RetrieveRelevantSnippetRequest) (currentNode *interaction_graph.InteractionNode, err error) {
	var currentEntity *model.Entity

	// 如果没有 currentEditor，就不能获取实体
	if request.CurrentEditor == nil {
		return nil, errors.New("get node from request: current editor is nil")
	}

	// 获取当前实体失败
	currentEntities := m.getEntitiesFromFile(ctx, request.CurrentEditor.FilePath, request.CurrentEditor.CursorLine)
	if len(currentEntities) == 0 {
		return nil, errors.New("get node from request: current entities is empty")
	}

	// 选中代码是用户指定的行为，优先级最高。查找选中代码对应的节点作为当前节点
	if request.CurrentEditor != nil && request.CurrentEditor.SelectCodeRange != nil {
		currentEntity = getBestMatchEntity(ctx, currentEntities,
			request.CurrentEditor.SelectCodeRange.StartLine,
			request.CurrentEditor.SelectCodeRange.EndLine)
	} else if request.CurrentEditor != nil && request.CurrentEditor.VisibleCodeRange != nil {
		// TODO: 优先选择当前可见区域内最近访问过的节点
		recentNodes := m.recentInteractionList.Get()
		for _, recentNode := range recentNodes {
			if recentNode.Entity.URI == request.CurrentEditor.FilePath {
				currentEntity = recentNode.Entity
				break
			}
		}
		// 如果没有，选择最佳匹配节点
		if currentEntity == nil {
			currentEntity = getBestMatchEntity(ctx, currentEntities,
				request.CurrentEditor.VisibleCodeRange.StartLine,
				request.CurrentEditor.VisibleCodeRange.EndLine)
		}
	}

	if currentEntity != nil && m.interactionGraph.HasNode(currentEntity.ID) {
		return m.interactionGraph.GetNode(currentEntity.ID), nil
	} else {
		return nil, errors.New("no valid code range provided")
	}
}

func (m *Manager) retrieveNodesFromRecentOpenFiles(ctx context.Context, topK int) ([]*interaction_graph.InteractionNode, error) {
	// 获取最近访问过的 TopK 的文件名
	recentNodes := m.recentInteractionList.Get()
	recentFiles := make(map[string]bool)
	retrievedEntities := make([]*model.Entity, 0)
	for _, n := range recentNodes {
		if len(recentFiles) >= topK {
			break
		}
		if !recentFiles[n.Entity.URI] {
			recentFiles[n.Entity.URI] = true
		}
	}
	if len(recentFiles) == 0 {
		logs.CtxInfo(ctx, "retrieve from open files: no recent files found")
		return nil, nil
	}

	// 获取这些文件中包含的实体
	for filePath := range recentFiles {
		cache, ok := m.recentOpenFileCache.Peek(filePath)
		if !ok {
			logs.CtxInfo(ctx, "retrieve from open files: no entity found at file: %v", filePath)
			continue
		}
		retrievedEntities = append(retrievedEntities, cache.entities...)
	}

	// 将实体转换为节点
	recentFileNodes := lo.Map(retrievedEntities, func(entity *model.Entity, _ int) *interaction_graph.InteractionNode {
		if m.interactionGraph.HasNode(entity.ID) {
			return m.interactionGraph.GetNode(entity.ID)
		}
		return interaction_graph.NewInteractionNode(entity)
	})
	if len(recentFileNodes) == 0 {
		logs.CtxInfo(ctx, "retrieve from open files: result is empty")
	}

	return recentFileNodes, nil
}

func (m *Manager) retrieveNodesFromInteractionGraph(ctx context.Context, request *protocol.RetrieveRelevantSnippetRequest) (*interaction_graph.InteractionNode, []*interaction_graph.InteractionNode, error) {
	curNode, err := m.getNodeFromRequest(ctx, request)
	if err != nil {
		return nil, nil, err
	}

	if curNode == nil {
		return nil, nil, nil
	}

	connectedNodes := m.interactionGraph.GetConnectedNodes(curNode)
	if len(connectedNodes) == 0 {
		// convert to interaction node
		logs.CtxInfo(ctx, "retrieve from interaction graph: no connected nodes found")
		return nil, nil, nil
	}
	return curNode, connectedNodes, nil
}

func (m *Manager) retrieveFilesFromGitGraph(ctx context.Context, curNode *interaction_graph.InteractionNode, curURI string, topK int) ([]*interaction_graph.InteractionNode, error) {
	var (
		gitFiles []string
		err      error
	)

	numberOfGitFiles := 5
	// 如果当前节点是文件，召回 Git 上和这个相关联的文件，
	if curURI != "" {
		gitFiles, err = m.gitGraph.GetRelatedFiles(ctx, curURI, numberOfGitFiles)
	}
	// 如果仍然没有文件，召回 Git 上最近改动的文件
	if len(gitFiles) == 0 {
		gitFiles, err = m.gitGraph.GetHotGitFiles(ctx, numberOfGitFiles)
		if err != nil {
			return nil, err
		}
	}

	if len(gitFiles) == 0 {
		return nil, errors.New("retrieve from git graph: no files found")
	}

	// 根据文件构造节点，先查看 CKG，再使用非 CKG 实体
	tempNodes := make([]*interaction_graph.InteractionNode, 0)
	for _, gitFile := range gitFiles {
		entities := m.getCKGEntitiesFromFile(ctx, gitFile)
		for _, entity := range entities {
			node := interaction_graph.NewInteractionNode(entity)
			node.SetSourceType("git")
			tempNodes = append(tempNodes, node)
		}
	}
	if len(tempNodes) == 0 {
		for _, gitFile := range gitFiles {
			entities := m.getCreateNotCKGEntityFromFile(ctx, gitFile)
			for _, entity := range entities {
				node := interaction_graph.NewInteractionNode(entity)
				node.SetSourceType("git")
				tempNodes = append(tempNodes, node)
			}
		}
	}

	if topK > 0 && len(tempNodes) > topK {
		// 随机选择 topK 个节点
		tempNodes = lo.Shuffle(tempNodes)
		tempNodes = tempNodes[:topK]
	}

	logs.CtxInfo(ctx, "retrieval from git graph success, get %v nodes", len(tempNodes))
	return tempNodes, nil
}

func (m *Manager) HandleCursorMove(ctx context.Context, cursorMoveFilePath string, cursorMoveLine int32) error {
	// 获取当前实体以及对应的节点
	currentNode, err := m.getNodeFromFileLine(ctx, cursorMoveFilePath, cursorMoveLine)
	if err != nil || currentNode == nil {
		logs.CtxInfo(ctx, "handle cursor move v2: no node found in file path: %v, line: %v, err is %v", cursorMoveFilePath, cursorMoveLine, err)
		return nil
	}
	timestamp := time.Now().Unix()

	// 获取最近的一个节点
	recentNodes := m.recentInteractionList.PeekRecent(1)
	if len(recentNodes) == 0 { // 没有最近的节点，则创建一个新的节点
		m.recentInteractionList.Add(currentNode)
		currentNode.View(timestamp)
		m.interactionGraph.InsertNode(ctx, currentNode)
		logs.CtxInfo(ctx, "[HandleCursorMove] Recent interaction list is empty, add node %v", currentNode)
		return nil
	}
	recentNode := recentNodes[0]

	// 更新交互图
	if err := m.interactionGraph.HandleCursorMove(ctx, currentNode, recentNode, timestamp); err != nil {
		return err
	}
	m.recentInteractionList.Add(currentNode)

	logs.CtxInfo(ctx, "[HandleCursorMove] update interaction graph success")
	return nil
}

func (m *Manager) HandleDocumentChange(ctx context.Context, entity *model.Entity, timestamp int64) error {
	// TODO 处理 DocumentChange(Edit), 其中 CursorMove 事件同时包括 Edit 和 View，需要和 DocumentChange 结合考虑
	////当前节点
	//currentNode, _ := m.interactionGraph.getOrCreateNode(entity, timestamp)
	//// 最近访问的节点
	//if len(recentNodes) == 0 {
	//	return errors.New("no recent node found")
	//}
	//recentNode := recentNodes[0]
	//recentNodes := m.recentInteractionList.PeekRecent(1)
	//return m.interactionGraph.handleDocumentChange(ctx, entity, recentNode, timestamp)
	return nil
}

func calculateEmbeddingForNotCKGEntity(ctx context.Context, cli knowledgebase.Client, node *interaction_graph.InteractionNode, userId string, datastoreName string, config *ckg_config.Config) {
	var embeddingResp *knowledgebase.EmbeddingResponse
	var err error

	nodeContent := node.GetNodeContent(ctx)
	if nodeContent == "" {
		logs.CtxInfo(ctx, "calculateEmbeddingForNotCKGEntity: node content is empty")
		return
	}
	if config.IsIndexFeatureEnabled(ctx, userId, ckg_config.UseV2SplitEmbeddingAPI) {
		embeddingResp, err = cli.EmbeddingV2(ctx, env.GetToken(userId), ckg_metrics.EventNameCKGInteraction, &knowledgebase.EmbeddingRequestV2{
			EmbeddingModel:    config.GetEmbeddingModelBaseline(ctx, userId),
			EmbeddingContents: []string{nodeContent},
			OfflineCluster:    false,
		})
	} else {
		embeddingResp, err = cli.Embedding(ctx, env.GetToken(userId), ckg_metrics.EventNameCKGInteraction, &knowledgebase.EmbeddingRequest{
			DatastoreName:     datastoreName,
			EmbeddingContents: []string{nodeContent},
			OfflineCluster:    false,
		})
	}
	if err != nil {
		logs.CtxInfo(ctx, "calculateEmbeddingForNotCKGEntity: failed to get embedding, err: %v", err)
		return
	} else if embeddingResp == nil {
		logs.CtxInfo(ctx, "calculateEmbeddingForNotCKGEntity: embeddingResp is nil")
		return
	}
	node.Embedding = knowledgebase.ConvertKBEmbeddingToEmbedding(embeddingResp.Embeddings[0])
}

func (m *Manager) RetrieveRelevantContextFromInteraction(ctx context.Context, cli knowledgebase.Client, processedQuery *model.ProcessedQuery,
	request *protocol.RetrieveRelevantSnippetRequest, tccConfig *model.CodeKGTCCConfig, recallNum int) ([]*protocol.Snippet, error) {
	reportEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGInteraction, request.UserId, "", string(env.GetSourceProduct()))
	defer reportEvent.Report(ctx, false)

	startTime := time.Now()
	reportEvent.AddTeaParam(ckg_metrics.TeaParamsUserInteractionRetrievalVersion, request.Version)
	reportEvent.AddTeaParam(ckg_metrics.TeaParamUserInteractionHasCurrentEditor, request.CurrentEditor != nil)
	reportEvent.AddTeaParam(ckg_metrics.TeaParamUserInteractionHasGitRepo, m.gitGraph != nil)

	// 上报用户此时的历史数据
	if m.repo != nil {
		remotes, err := m.repo.Remotes()
		if err != nil {
			logs.CtxError(ctx, "failed to get remote, err: %v", err)
		} else if len(remotes) > 0 {
			var commit string
			head, err := m.repo.Head()
			if err == nil {
				commit = head.Hash().String()
			}
			event := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGUserTrace, request.UserId, "", string(env.GetSourceProduct()))
			event.AddParam(ckg_metrics.ParamUserTraceData, map[string]interface{}{
				"request":            request,
				"git_remotes":        remotes[0].Config().URLs,
				"commit":             commit,
				"recent_interaction": m.recentInteractionList,
				"interaction_graph":  m.interactionGraph,
			})
			event.ReportSync(ctx, true)
		}
	}

	var curNode *interaction_graph.InteractionNode
	nodeChan := make(chan []*interaction_graph.InteractionNode, 2)
	wg := sync.WaitGroup{}
	wg.Add(2)

	// 1. 交互图中获取和当前实体相关的节点
	util.SafeGo(ctx, func() {
		defer wg.Done()
		currentNode, retrievedNodes, err := m.retrieveNodesFromInteractionGraph(ctx, request)
		if err != nil || currentNode == nil {
			logs.CtxInfo(ctx, "retrieveNodesFromInteractionGraph error: %v", err)
			return
		}
		nodeChan <- retrievedNodes
		curNode = currentNode
	})

	// 2. 最近访问文件包括的所有节点。
	util.SafeGo(ctx, func() {
		defer wg.Done()
		retrievedNodes, err := m.retrieveNodesFromRecentOpenFiles(ctx, NumberOfRecentFiles)
		if err != nil {
			logs.CtxInfo(ctx, "retrieveNodesFromRecentInteractionList error: %v", err)
			return
		}
		nodeChan <- retrievedNodes
	})

	wg.Wait()
	close(nodeChan)

	allNodes := make([]*interaction_graph.InteractionNode, 0)
	for nodes := range nodeChan {
		allNodes = append(allNodes, nodes...)
	}

	// 去重过滤
	uniqueNodes := m.deduplicateNodes(allNodes)
	curURI := func() string {
		if curNode != nil && curNode.Entity != nil {
			return curNode.Entity.URI
		}
		if request.CurrentEditor != nil {
			return request.CurrentEditor.FilePath
		}
		return "" // 或返回默认值
	}()
	filteredNodes := m.filterCurrentFileNodes(uniqueNodes, curURI)

	// 如果过滤后没有找到备选实体，尝试使用 Git 中的信息作为兜底
	if len(filteredNodes) < MinNodes && m.gitGraph != nil {
		logs.CtxInfo(ctx, "try git graph as fallback")
		gitNodes, err := m.retrieveFilesFromGitGraph(ctx, curNode, curURI, GitMaxNodes)
		if err != nil {
			logs.CtxInfo(ctx, "retrieve from git graph: error %v", err)
		} else {
			filteredNodes = append(filteredNodes, gitNodes...)
		}
	}

	if len(filteredNodes) == 0 {
		logs.CtxInfo(ctx, "retrieve from interaction graph and git graph: no nodes found")
		return make([]*protocol.Snippet, 0), nil
	}

	// 如果有非备选的CKG实体，在这里统一计算一下 Embedding （不在创建的时候计算）
	notCKGNodes := lo.Filter(filteredNodes, func(node *interaction_graph.InteractionNode, _ int) bool {
		return node.IsCKG == false
	})
	for _, node := range notCKGNodes {
		if node.Embedding != nil {
			continue
		}
		calculateEmbeddingForNotCKGEntity(ctx, cli, node, request.UserId, tccConfig.DatastoreName, m.config)
	}

	// 计算权重并排序
	rankOptions := interaction_graph.NodeRankOptions{
		Now:              time.Now().Unix(),
		OriginalQuery:    processedQuery.OriginalQuery,
		Embedding:        processedQuery.Embedding,
		Keywords:         processedQuery.Keywords,
		EmbeddingStorage: m.getEmbeddingStorage(),
		GitGraph:         m.gitGraph,
	}
	processedNodes, err := m.interactionGraph.RankNodes(ctx, curNode, filteredNodes, rankOptions)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to rank nodes")
	}

	// 获取时重新从数据库中查询，直到查到 topK 个 entity，每次查到了都更新一下图结构。
	storage := m.getStorage()
	finalNodes := make([]*interaction_graph.InteractionNode, 0, recallNum)
	for _, n := range processedNodes {
		if len(finalNodes) >= recallNum {
			break
		}
		if !n.IsCKG { // 如果不是CKG实体，直接加入结果
			finalNodes = append(finalNodes, n)
			continue
		}
		if storage == nil {
			continue
		}
		newEntity, err := storage.GetEntityByEntityID(ctx, storage.GetConn(), n.Entity.ID)
		if err != nil || newEntity == nil {
			logs.CtxInfo(ctx, "failed to get entity %s: %v, delete this node", n.Entity.ID, err)
			err := m.interactionGraph.DeleteNode(n.ID)
			if err != nil {
				logs.CtxInfo(ctx, "failed to delete node, %s", err)
			}
			continue
		}

		if newEntity.ID != n.Entity.ID { // 预期之外的行为
			logs.CtxInfo(ctx, "update nodes error, new entity ID != previous entity ID")
			continue
		}

		n.Entity = newEntity // 指向新的 entity
		finalNodes = append(finalNodes, n)
	}

	defer func() {
		reportEvent.AddTeaParam(ckg_metrics.TeaParamUserInteractionGitRepoNum, len(finalNodes))
		reportEvent.AddTeaParam(ckg_metrics.TeaParamUserInteractionRetrievalTime, time.Since(startTime).Milliseconds())
	}()

	return m.convertNodesToSnippets(ctx, finalNodes), nil
}

func calculateIntersectRatio(startLine1, endLine1, startLine2, endLine2 int32) float64 {
	if startLine1 > endLine1 || startLine2 > endLine2 {
		return 0.0
	}

	// intersect 区域
	intersectStart := max(startLine1, startLine2)
	intersectEnd := min(endLine1, endLine2)
	if intersectStart > intersectEnd {
		return 0.0
	}
	intersectLines := intersectEnd - intersectStart + 1

	// union 区域
	unionStart := min(startLine1, startLine2)
	unionEnd := max(endLine1, endLine2)
	union := unionEnd - unionStart + 1

	return float64(intersectLines) / float64(union)
}

func getBestMatchEntity(ctx context.Context, entities []*model.Entity, startLine int32, endLine int32) *model.Entity {
	if len(entities) == 0 {
		return nil
	} else if startLine > endLine {
		logs.CtxTrace(ctx, "[getBestMatchEntity] startLine > endLine, startLine: %d, endLine: %d", startLine, endLine)
		return entities[0]
	}

	var bestMatch *model.Entity
	var minimumRange int32
	var maxIntersectRatio float64

	for i, entity := range entities {
		entityStart, entityEnd := entity.GetStartLine(), entity.GetEndLine()
		entityRange := entityEnd - entityStart + 1
		ratio := calculateIntersectRatio(startLine, endLine, entityStart, entityEnd)

		if i == 0 {
			bestMatch = entity
			minimumRange = entityRange
			maxIntersectRatio = ratio
			continue
		}

		if ratio > maxIntersectRatio || (ratio == maxIntersectRatio && entityRange < minimumRange) {
			maxIntersectRatio = ratio
			bestMatch = entity
			minimumRange = entityRange
		}
	}

	return bestMatch
}
