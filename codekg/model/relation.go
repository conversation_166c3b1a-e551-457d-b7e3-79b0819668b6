package model

type EntityRelationType string

const (
	FileToFile           EntityRelationType = "fileToFile"           // 文件到文件（文件夹）
	ClassToFile          EntityRelationType = "classToFile"          // 类到文件关系
	ClassTopLevelToClass EntityRelationType = "classTopLevelToClass" // 类成员变量代码块到类的关系
	FileTopLevelToFile   EntityRelationType = "fileTopLevelToFile"   // 全局变量代码块到类的关系
	MethodToClass        EntityRelationType = "methodToClass"        // 成员函数到类的关系
	MethodToFile         EntityRelationType = "methodToFile"         // 全局函数到类的关系
	CallerToCallee       EntityRelationType = "callerToCallee"       // 调用关系
)

type Relation struct {
	URI        URI
	Type       EntityRelationType
	StartID    string
	StartName  string
	EndID      string
	EndName    string
	Attributes map[string]interface{}
}

func CreateRelation(startName string, startID string, uri URI,
	entityRelationType EntityRelationType, endID string, endName string, attributes map[string]interface{}) *Relation {
	return &Relation{
		URI:        uri,
		Type:       entityRelationType,
		StartID:    startID,
		StartName:  startName,
		EndID:      endID,
		EndName:    endName,
		Attributes: attributes,
	}
}
