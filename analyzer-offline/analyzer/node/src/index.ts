import {startServer} from "./connect";
import {localLogger} from "./log";

function parseArgs() {
  const args = process.argv.slice(2);
  const result: { [key: string]: string | boolean } = {};

  for (let i = 0; i < args.length; i++) {
    if (args[i].startsWith('-')) {
      const key = args[i].replace(/^-+/, '');

      if (i + 1 < args.length && !args[i + 1].startsWith('-')) {
        result[key] = args[i + 1];
        i++;
      } else {
        result[key] = true;
      }
    }
  }

  return result;
}

const args = parseArgs();

if (!args.port || typeof args.port !== 'string') {
  localLogger.error(`port is not specified`);
  process.exit(1);
}

startServer(Number(args.port));