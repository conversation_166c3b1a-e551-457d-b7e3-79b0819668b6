package data_storage

import (
	"context"
	"crypto/sha256"
	"database/sql"
	"fmt"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/atomic"
	"gorm.io/gorm"
	"ide/ckg/codekg/components/data_storage/consts"
	vec "ide/ckg/codekg/components/data_storage/sqlite_vec"
	"ide/ckg/codekg/components/data_storage/user_storage"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/util"
	"path/filepath"
	"strconv"
)

const (
	// 暂时 embeddingId 还是 text，之后看 int 会不会更快
	DBSchemaV2 = `CREATE VIRTUAL TABLE IF NOT EXISTS vec_items_v2 USING vec0(
   	embedding float[%d] distance_metric=cosine,
	embeddingId integer PRIMARY KEY,
    entity_type text,
	rel_folder_path text,
    ext text,
)`
)

type SQLiteVecStorageV2 struct {
	// 只是复用 V1 版本 SQLiteVecStorage 的一些 struct 成员，打开的 db schema 不同
	old *SQLiteVecStorage
}

// CreateSQLiteVecStorageV2 创建 v2 embedding db，该 schema 不同之处在于不再有 entityId，而是 embeddingId
// 注意：仍然保持了 “实体类型召回” 以及 “#Folder” 的能力
func CreateSQLiteVecStorageV2(ctx context.Context, es *user_storage.UserStorage, storagePath model.URI, projectID model.URI,
	embeddingModel string, t consts.EmbeddingStorageType, forceNew bool) (*SQLiteVecStorageV2, error) {
	var (
		db  *sql.DB
		err error
	)
	if t != consts.EmbeddingStorage_SQLiteVec {
		return nil, errors.Errorf("[CreateSQLiteVecStorageV2] invalid embedding storage type: %s", t)
	}

	vec.Auto()
	dbFilePath := ""
	if len(storagePath) == 0 {
		db, err = sql.Open(SQLITE, "file::memory:")
	} else {
		dbFilePath, err = es.GetEmbeddingStorageFromUserStorage(ctx, projectID, t)
		dbFileInfo, err1 := file_system.RealFS.Stat(dbFilePath) // sqlite 在 Open 时不会因 dbFilePath 是 folder 而报错，需要手动检查。
		if forceNew || err != nil || err1 != nil || dbFileInfo.IsDir() || len(dbFilePath) == 0 {
			projectName := filepath.Base(projectID)
			sum := sha256.Sum256([]byte(projectID))
			randStr := util.GetRandStr(6)
			dbFilePath = filepath.Join(storagePath, fmt.Sprintf("%s_%x_%s_embedding_vec.db", projectName, sum[:7], randStr))
			logs.CtxInfo(ctx, "[CreateSQLiteVecStorageV2] embedding db not exist for project %s, new db path is %s", projectID, dbFilePath)
		}
		// 创建的 db file path 会在 Init 流程完全结束后写入
		db, err = sql.Open(SQLITE, dbFilePath)
	}
	if err != nil {
		logs.CtxError(ctx, "[CreateSQLiteVecStorageV2] open db failed", err)
		return nil, errors.WithMessagef(err, "[CreateSQLiteVecStorageV2] open db failed")
	}
	db.SetMaxOpenConns(1)

	// 初始化向量数据库表
	var dim int
	var ok bool
	if dim, ok = dimMap[embeddingModel]; !ok {
		dim = 1024
	}
	_, err = db.Exec(fmt.Sprintf(DBSchemaV2, dim))
	if err != nil {
		logs.CtxError(ctx, "[CreateSQLiteVecStorageV2] create vec table failed", err)
		return nil, errors.WithMessagef(err, "[CreateSQLiteVecStorageV2] create vec table failed")
	}
	old := &SQLiteVecStorage{
		db:             db,
		projectID:      projectID,
		isDeleted:      atomic.NewBool(false),
		dbFilePath:     dbFilePath,
		embeddingModel: embeddingModel,
	}
	return &SQLiteVecStorageV2{old: old}, nil
}

func (svs2 *SQLiteVecStorageV2) AddEmbeddingDocument(ctx context.Context, doc *model.EmbeddingDocument, option AddEmbeddingDocumentOptions) error {
	panic("No need to implement temporarily")
}

func (svs2 *SQLiteVecStorageV2) AddEmbeddingDocuments(ctx context.Context, docs []*model.EmbeddingDocument, option AddEmbeddingDocumentOptions) (err error) {
	const InsertStmt = "INSERT INTO vec_items_v2 (embedding, embeddingId, entity_type, rel_folder_path, ext) VALUES (?, ?, ?, ?, ?)"
	tx, err := svs2.old.db.BeginTx(ctx, &sql.TxOptions{ReadOnly: false, Isolation: sql.LevelSerializable})
	if err != nil {
		logs.CtxError(ctx, "[AddEmbeddingDocuments] v2 begin tx failed", err)
		return errors.WithMessagef(err, "[AddEmbeddingDocuments] v2 begin tx failed")
	}
	defer func() {
		if p := recover(); p != nil {
			_ = tx.Rollback()
		} else if err != nil {
			_ = tx.Rollback()
		} else {
			if err := tx.Commit(); err != nil {
				logs.CtxError(ctx, "[AddEmbeddingDocuments] v2 commit tx failed", err)
			}
		}
	}()
	for _, doc := range docs {
		var folderRelPath, ext, entityType string
		var ok bool
		if folderRelPath, ok = doc.Metadata[string(model.EMK_RelPath)].(string); !ok {
			logs.CtxError(ctx, "[AddEmbeddingDocuments] v2 folder rel path is nil")
			return errors.Errorf("folder rel path is nil. metadata: %v", doc.Metadata)
		}
		if ext, ok = doc.Metadata[string(model.EMK_Ext)].(string); !ok {
			logs.CtxError(ctx, "[AddEmbeddingDocuments] v2 file ext is nil")
			return errors.Errorf("file ext is nil. metadata: %v", doc.Metadata)
		}
		if entityType, ok = doc.Metadata[string(model.EMK_EntityType)].(string); !ok {
			logs.CtxError(ctx, "[AddEmbeddingDocument] v2 entity type is nil")
			return errors.Errorf("entity type is nil. metadata: %v", doc.Metadata)
		}
		q, err := vec.SerializeFloat32(doc.Embedding.Embedding)
		if err != nil {
			logs.CtxError(ctx, "[AddEmbeddingDocuments] v2 serialize float32 failed", err)
			return errors.WithMessagef(err, "[AddEmbeddingDocuments] v2 serialize float32 failed")
		}
		id, err := strconv.Atoi(doc.ID)
		if err != nil {
			logs.CtxError(ctx, "[AddEmbeddingDocuments] v2 atoi failed: %s, err: %+v", doc.ID, err)
			return errors.WithMessagef(err, "[AddEmbeddingDocuments] v2 atoi failed: %s", doc.ID)
		}
		_, err = tx.Exec(InsertStmt, q, id, entityType, folderRelPath, ext)
		if err != nil {
			logs.CtxError(ctx, "[AddEmbeddingDocument] v2 insert vec items failed", err)
			return errors.WithMessagef(err, "[AddEmbeddingDocument] v2 insert vec items failed")
		}
	}
	return nil
}

func (svs2 *SQLiteVecStorageV2) GetById(ctx context.Context, id, collectionName string) (*model.EmbeddingDocument, error) {
	panic("No need to implement temporarily")
}

func (svs2 *SQLiteVecStorageV2) QueryByEmbedding(ctx context.Context, query *model.Embedding, nResults int, option EmbeddingQueryOptions) ([]*model.EmbeddingQueryResult, error) {
	const InitialQueryStmt = "SELECT embeddingId, entity_type, rel_folder_path, ext, distance, embedding FROM vec_items_v2 WHERE embedding match ? "
	queryStmt := InitialQueryStmt
	// 处理 metadata
	if option.WhereMetadata != nil {
		if relFolderPaths, ok := option.WhereMetadata[string(model.EMK_RelPath)].([]string); ok && len(relFolderPaths) > 0 {
			// 将 relFolderPaths 连接起来，方便 raw SQL 拼接
			concatFolderPaths := lo.Reduce(relFolderPaths, func(acc string, relFolderPath string, i int) string {
				if i == 0 {
					return fmt.Sprintf("'%s'", relFolderPath)
				}
				return fmt.Sprintf("%s, '%s'", acc, relFolderPath)
			}, "")
			queryStmt += fmt.Sprintf(" AND rel_folder_path IN (%s)", concatFolderPaths)
		}
		if ext, ok := option.WhereMetadata[string(model.EMK_Ext)].(string); ok {
			queryStmt += fmt.Sprintf(" AND ext = '%s'", ext)
		}
		if entityTypes, ok := option.WhereMetadata[string(model.EMK_EntityType)].([]model.EntityType); ok && len(entityTypes) > 0 {
			// 将 expectTypes 连接起来，方便 raw SQL 拼接
			concatExpectTypes := lo.Reduce(entityTypes, func(acc string, expectType model.EntityType, i int) string {
				if i == 0 {
					return fmt.Sprintf("'%s'", expectType)
				}
				return fmt.Sprintf("%s, '%s'", acc, expectType)
			}, "")
			queryStmt += fmt.Sprintf(" AND entity_type IN (%s)", concatExpectTypes)
		}
		if embeddingIds, ok := option.WhereMetadata[string(model.EMK_EmbeddingID)].([]string); ok && len(embeddingIds) > 0 {
			concatEmbeddingIds := lo.Reduce(embeddingIds, func(acc string, embeddingId string, i int) string {
				if i == 0 {
					return fmt.Sprintf("'%s'", embeddingId)
				}
				return fmt.Sprintf("%s, '%s'", acc, embeddingId)
			}, "")
			queryStmt += fmt.Sprintf(" AND embeddingId IN (%s)", concatEmbeddingIds)
		}
	}
	q, err := vec.SerializeFloat32(query.Embedding)
	if err != nil {
		logs.CtxError(ctx, "[QueryByEmbedding] v2 serialize float32 failed", err)
		return nil, errors.WithMessagef(err, "[QueryByEmbedding] v2 serialize float32 failed")
	}
	queryStmt += " ORDER BY distance LIMIT ?"
	// 执行向量查询
	rows, err := svs2.old.db.Query(queryStmt, q, nResults)
	if err != nil {
		logs.CtxError(ctx, "[QueryByEmbedding] v2 query vec items failed", err)
		return nil, errors.WithMessagef(err, "[QueryByEmbedding] v2 query vec items failed")
	}
	results := make([]*model.EmbeddingQueryResult, 0)
	for rows.Next() {
		var embeddingId int
		var relFolderPath, ext string
		var entityType model.EntityType
		var distance float32
		var embedding []uint8
		err := rows.Scan(&embeddingId, &entityType, &relFolderPath, &ext, &distance, &embedding)
		if err != nil {
			logs.CtxError(ctx, "[QueryByEmbedding] v2 scan vec items failed", err)
			return nil, errors.WithMessagef(err, "[QueryByEmbedding] v2 scan vec items failed")
		}
		embeddingFloat32, err := vec.DeserializeFloat32(embedding)
		if err != nil {
			logs.CtxError(ctx, "[QueryByEmbedding] v2 deserialize float32 failed", err)
			return nil, errors.WithMessagef(err, "[QueryByEmbedding] v2 deserialize float32 failed")
		}
		res := ConvertSQLiteVecV2ResultToEmbeddingQueryResult(strconv.Itoa(embeddingId), entityType, relFolderPath, ext, 1-distance, embeddingFloat32)
		results = append(results, res)
	}

	results = lo.UniqBy(results, func(res *model.EmbeddingQueryResult) string { return res.ID })
	return results, nil
}

func (svs2 *SQLiteVecStorageV2) EmbeddingDocumentCount(ctx context.Context) int {
	rows, err := svs2.old.db.Query("SELECT COUNT(*) from vec_items_v2")
	if err != nil {
		logs.CtxError(ctx, "[EmbeddingDocumentCount] v2 get vec items rowids failed", err)
		return 0
	}
	var cnt int
	for rows.Next() {
		err := rows.Scan(&cnt)
		if err != nil {
			logs.CtxError(ctx, "[EmbeddingDocumentCount] v2 scan vec items rowids failed", err)
			return 0
		}
	}
	return cnt
}

func (svs2 *SQLiteVecStorageV2) DeleteDocumentsByIds(ctx context.Context, option DeleteDocumentOptions, ids ...string) error {
	idsAny := lo.FilterMap(ids, func(id string, _ int) (any, bool) {
		idInt, err := strconv.Atoi(id)
		if err != nil {
			return -1, false
		}
		return idInt, true
	})
	concatQuestionMarks := lo.Reduce(idsAny, func(acc string, _ any, i int) string {
		if i == 0 {
			return "?"
		}
		return fmt.Sprintf("%s, ?", acc)
	}, "")
	deleteStmt := fmt.Sprintf("DELETE FROM vec_items_v2 WHERE embeddingId IN (%s)", concatQuestionMarks)
	if _, err := svs2.old.db.Exec(deleteStmt, idsAny...); err != nil {
		logs.CtxError(ctx, "[DeleteDocumentsByEntity] v2 delete vec items failed, err: %v", err)
		return errors.WithMessagef(err, "[DeleteDocumentsByEntity] v2 delete vec items failed")
	}
	return nil
}

func (svs2 *SQLiteVecStorageV2) DeleteDocumentsByMetadata(ctx context.Context, whereMetadata map[string]any, option DeleteDocumentOptions) error {
	panic("No need to implement temporarily")
}

func (svs2 *SQLiteVecStorageV2) DeleteDocumentsByEntity(ctx context.Context, entities []StorageEntity, storage Storage, tx *gorm.DB, option DeleteDocumentOptions) error {
	panic("No need to implement temporarily")
}

func (svs2 *SQLiteVecStorageV2) Delete() error             { return svs2.old.Delete() }
func (svs2 *SQLiteVecStorageV2) IsDeleted() bool           { return svs2.old.IsDeleted() }
func (svs2 *SQLiteVecStorageV2) IsExistsOnDisk() bool      { return svs2.old.IsExistsOnDisk() }
func (svs2 *SQLiteVecStorageV2) GetStoragePath() string    { return svs2.old.GetStoragePath() }
func (svs2 *SQLiteVecStorageV2) GetType() string           { return svs2.old.GetType() }
func (svs2 *SQLiteVecStorageV2) GetEmbeddingModel() string { return svs2.old.GetEmbeddingModel() }
func (svs2 *SQLiteVecStorageV2) GetVersion() consts.StorageVersion {
	return consts.StorageVersionV2
}

func ConvertSQLiteVecV2ResultToEmbeddingQueryResult(embeddingId string, entityType model.EntityType, relFolderPath, ext string, similarity float32, embedding []float32) *model.EmbeddingQueryResult {
	metadata := make(map[string]any)
	metadata[string(model.EMK_RelPath)] = relFolderPath
	metadata[string(model.EMK_Ext)] = ext
	metadata[string(model.EMK_EntityType)] = entityType
	return &model.EmbeddingQueryResult{
		EmbeddingDocument: model.EmbeddingDocument{
			ID:        embeddingId,
			Content:   "",
			Embedding: &model.Embedding{Embedding: embedding},
			Metadata:  metadata,
		},
		Score: similarity,
	}
}
