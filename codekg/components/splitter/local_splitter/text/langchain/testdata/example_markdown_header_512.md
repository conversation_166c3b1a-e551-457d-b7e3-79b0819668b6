# Contributing to langchaingo
First off, thanks for taking the time to contribute! ❤️
All types of contributions are encouraged and valued. See the [Table of Contents](#table-of-contents) for different ways
to help and details about how this project handles them. Please make sure to read the relevant section before making
your contribution. It will make it a lot easier for us maintainers and smooth out the experience for all involved. The
community looks forward to your contributions. 🎉

---

# Contributing to langchaingo
> And if you like the project, but just don’t have time to contribute, that’s fine. There are other easy ways to support
> the project and show your appreciation, which we would also be very happy about:
> - Star the project
> - Tweet about it
> - Refer this project in your project’s readme
> - Mention the project at local meetups and tell your friends/colleagues

---

## Table of Contents
- [Code of Conduct](#code-of-conduct)
- [I Have a Question](#i-have-a-question)

---

## Table of Contents
- [I Want To Contribute](#i-want-to-contribute)
  - [Reporting Bugs](#reporting-bugs)
    - [Before Submitting a Bug Report](#before-submitting-a-bug-report)
    - [How Do I Submit a Good Bug Report?](#how-do-i-submit-a-good-bug-report)
  - [Suggesting Enhancements](#suggesting-enhancements)
    - [Before Submitting an Enhancement](#before-submitting-an-enhancement)
    - [How Do I Submit a Good Enhancement Suggestion?](#how-do-i-submit-a-good-enhancement-suggestion)

---

## Table of Contents
  - [Your First Code Contribution](#your-first-code-contribution)
    - [Make Changes](#make-changes)
      - [Make changes in the UI](#make-changes-in-the-ui)
      - [Make changes locally](#make-changes-locally)
    - [Commit your update](#commit-your-update)
    - [Pull Request](#pull-request)
    - [Your PR is merged!](#your-pr-is-merged)

---

## Code of Conduct
This project and everyone participating in it is governed by the
[langchaingo Code of Conduct](/CODE_OF_CONDUCT.md).
By participating, you are expected to uphold this code. Please report unacceptable behavior
to <<EMAIL>>.

---

## I Have a Question
> If you want to ask a question, we assume that you have read the
> available [Documentation](https://pkg.go.dev/github.com/tmc/langchaingo).

---

## I Have a Question
Before you ask a question, it is best to search for existing [Issues](https://github.com/tmc/langchaingo/issues) that
might help you. In case you have found a suitable issue and still need clarification, you can write your question in
this issue. It is also advisable to search the internet for answers first.
If you then still feel the need to ask a question and need clarification, we recommend the following:
- Open an [Issue](https://github.com/tmc/langchaingo/issues/new).

---

## I Have a Question
- Provide as much context as you can about what you’re running into.
- Provide project and platform versions (nodejs, npm, etc), depending on what seems relevant.
We will then take care of the issue as soon as possible.

---

## I Want To Contribute
> ### Legal Notice
> When contributing to this project, you must agree that you have authored 100% of the content, that you have the
> necessary rights to the content and that the content you contribute may be provided under the project license.

---

### Reporting Bugs

---

#### Before Submitting a Bug Report
A good bug report shouldn't leave others needing to chase you up for more information. Therefore, we ask you to
investigate carefully, collect information and describe the issue in detail in your report. Please complete the
following steps in advance to help us fix any potential bug as fast as possible.
- Make sure that you are using the latest version.

---

#### Before Submitting a Bug Report
- Determine if your bug is really a bug and not an error on your side e.g. using incompatible environment
components/versions (Make sure that you have read the [documentation](https://pkg.go.dev/github.com/tmc/langchaingo).
If you are looking for support, you might want to check [this section](#i-have-a-question)).

---

#### Before Submitting a Bug Report
- To see if other users have experienced (and potentially already solved) the same issue you are having, check if there
is not already a bug report existing for your bug or error in
the [bug tracker](https://github.com/tmc/langchaingo/issues?q=label%3Abug).
- Also make sure to search the internet (including Stack Overflow) to see if users outside of the GitHub community have
discussed the issue.

---

#### Before Submitting a Bug Report
- Collect information about the bug:
  - Stack trace (Traceback)
  - OS, Platform and Version (Windows, Linux, macOS, x86, ARM)
  - Version of the interpreter, compiler, SDK, runtime environment, package manager, depending on what seems relevant.
  - Possibly your input and the output
  - Can you reliably reproduce the issue? And can you also reproduce it with older versions?

---

#### How Do I Submit a Good Bug Report?
> You must never report security related issues, vulnerabilities or bugs including sensitive information to the issue
> tracker, or elsewhere in public. Instead sensitive bugs must be sent by email to [<EMAIL>](mailto:<EMAIL>).
> <!– You may add a PGP key to allow the messages to be sent encrypted as well. –>

---

#### How Do I Submit a Good Bug Report?
We use GitHub issues to track bugs and errors. If you run into an issue with the project:
- Open an [Issue](https://github.com/tmc/langchaingo/issues/new). (Since we can’t be sure at this point whether it is a
bug or not, we ask you not to talk about a bug yet and not to label the issue.)
- Explain the behavior you would expect and the actual behavior.

---

#### How Do I Submit a Good Bug Report?
- Please provide as much context as possible and describe the *reproduction steps* that someone else can follow to
recreate the issue on their own. This usually includes your code. For good bug reports you should isolate the problem
and create a reduced test case.
- Provide the information you collected in the previous section.
Once it's filed:
- The project team will label the issue accordingly.

---

#### How Do I Submit a Good Bug Report?
- A team member will try to reproduce the issue with your provided steps. If there are no reproduction steps or no
obvious way to reproduce the issue, the team will ask you for those steps and mark the issue as `needs-repro`. Bugs
with the `needs-repro` tag will not be addressed until they are reproduced.

---

#### How Do I Submit a Good Bug Report?
- If the team is able to reproduce the issue, it will be marked `needs-fix`, as well as possibly other tags (such
as `critical`), and the issue will be left to be [implemented by someone](#your-first-code-contribution).
<!-- You might want to create an issue template for bugs and errors that can be used as a guide and that defines the structure of the information to be included. If you do so, reference it here in the description. -->

---

### Suggesting Enhancements
This section guides you through submitting an enhancement suggestion for langchaingo, **including completely new
features and minor improvements to existing functionality**. Following these guidelines will help maintainers and the
community to understand your suggestion and find related suggestions.

---

#### Before Submitting an Enhancement
- Make sure that you are using the latest version.
- Read the [documentation](https://pkg.go.dev/github.com/tmc/langchaingo) carefully and find out if the functionality is
already covered, maybe by an individual configuration.
- Perform a [search](https://github.com/tmc/langchaingo/issues) to see if the enhancement has already been suggested. If
it has, add a comment to the existing issue instead of opening a new one.

---

#### Before Submitting an Enhancement
- Find out whether your idea fits with the scope and aims of the project. It’s up to you to make a strong case to
convince the project’s developers of the merits of this feature. Keep in mind that we want features that will be
useful to the majority of our users and not just a small subset. If you’re just targeting a minority of users,
consider writing an add-on/plugin library.

---

#### How Do I Submit a Good Enhancement Suggestion?
Enhancement suggestions are tracked as [GitHub issues](https://github.com/tmc/langchaingo/issues).
- Use a **clear and descriptive title** for the issue to identify the suggestion.
- Provide a **step-by-step description of the suggested enhancement** in as many details as possible.
- **Describe the current behavior** and **explain which behavior you expected to see instead** and why. At this point
you can also tell which alternatives do not work for you.

---

#### How Do I Submit a Good Enhancement Suggestion?
- You may want to **include screenshots and animated GIFs** which help you demonstrate the steps or point out the part
which the suggestion is related to. You can use [this tool](https://www.cockos.com/licecap/) to record GIFs on macOS
and Windows, and [this tool](https://github.com/colinkeenan/silentcast)
or [this tool](https://github.com/GNOME/byzanz) on
Linux. <!– this should only be included if the project has a GUI –>

---

#### How Do I Submit a Good Enhancement Suggestion?
- **Explain why this enhancement would be useful** to most langchaingo users. You may also want to point out the other
projects that solved it better and which could serve as inspiration.
- We strive to conceptually align with the Python and TypeScript versions of Langchain. Please link/reference the
associated concepts in those codebases when introducing a new concept.

---

#### How Do I Submit a Good Enhancement Suggestion?
<!-- You might want to create an issue template for enhancement suggestions that can be used as a guide and that defines the structure of the information to be included. If you do so, reference it here in the description. -->

---

### Your First Code Contribution

---

#### Make Changes

---

##### Make changes in the UI
Click **Make a contribution** at the bottom of any docs page to make small changes such as a typo, sentence fix, or a
broken link. This takes you to the `.md` file where you can make your changes and [create a pull request](#pull-request)
for a review.

---

##### Make changes locally
1. Fork the repository.
- Using GitHub Desktop:
  - [Getting started with GitHub Desktop](https://docs.github.com/en/desktop/installing-and-configuring-github-desktop/getting-started-with-github-desktop)
  will guide you through setting up Desktop.
  - Once Desktop is set up, you can use it
  to [fork the repo](https://docs.github.com/en/desktop/contributing-and-collaborating-using-github-desktop/cloning-and-forking-repositories-from-github-desktop)!

---

##### Make changes locally
- Using the command line:
  - [Fork the repo](https://docs.github.com/en/github/getting-started-with-github/fork-a-repo#fork-an-example-repository)
  so that you can make your changes without affecting the original project until you’re ready to merge them.
1. Install or make sure **Golang** is updated.
2. Create a working branch and start with your changes!

---

#### Commit your update
Commit the changes once you are happy with them. Don't forget to self-review to speed up the review process:zap:.

---

#### Pull Request
When you're finished with the changes, create a pull request, also known as a PR.
- Name your Pull Request title clearly, concisely, and prefixed with the name of primarily affected package you changed
according to [Go Contribute Guideline](https://go.dev/doc/contribute#commit_messages). (such
as `memory: added interfaces` or `util: added helpers`)

---

#### Pull Request
- **We strive to conceptually align with the Python and TypeScript versions of Langchain. Please link/reference the
associated concepts in those codebases when introducing a new concept.**
- Fill the “Ready for review” template so that we can review your PR. This template helps reviewers understand your
changes as well as the purpose of your pull request.

---

#### Pull Request
- Don’t forget
to [link PR to issue](https://docs.github.com/en/issues/tracking-your-work-with-issues/linking-a-pull-request-to-an-issue)
if you are solving one.

---

#### Pull Request
- Enable the checkbox
to [allow maintainer edits](https://docs.github.com/en/github/collaborating-with-issues-and-pull-requests/allowing-changes-to-a-pull-request-branch-created-from-a-fork)
so the branch can be updated for a merge.
Once you submit your PR, a team member will review your proposal. We may ask questions or request additional
information.

---

#### Pull Request
- We may ask for changes to be made before a PR can be merged, either
using [suggested changes](https://docs.github.com/en/github/collaborating-with-issues-and-pull-requests/incorporating-feedback-in-your-pull-request)
or pull request comments. You can apply suggested changes directly through the UI. You can make any other changes in
your fork, then commit them to your branch.

---

#### Pull Request
- As you update your PR and apply changes, mark each conversation
as [resolved](https://docs.github.com/en/github/collaborating-with-issues-and-pull-requests/commenting-on-a-pull-request#resolving-conversations).
- If you run into any merge issues, checkout this [git tutorial](https://github.com/skills/resolve-merge-conflicts) to
help you resolve merge conflicts and other issues.

---

#### Your PR is merged!
Congratulations :tada::tada: The langchaingo team thanks you :sparkles:.
Once your PR is merged, your contributions will be publicly visible on the repository contributors list.
Now that you are part of the community!

---

