import {beforeAll, describe, expect, it} from "vitest";
import {TreeSitterParser} from "../parser";
import {LanguageIdEnum} from "../../share";
import {<PERSON><PERSON>reeSitterHandler} from "./python";

import {Position} from "vscode-languageserver-types";

describe("test_getFuncSignatureString", () => {
    beforeAll(async () => {
        await TreeSitterParser.INSTANCE.init("./resources");
    });

    it("case1_normal", async () => {
        const docText = `
def contains_all_necessary_keys(request_dict, necessary_key_array):
    missing_key_array = []
    try:
        # 校验是否参数合规
        # Example: necessary_key_array = ["name", "appid", "os_type", "git_url", "gitlab_pid", "manager"]
        for necessary_key in necessary_key_array:
            if necessary_key not in request_dict:
                missing_key_array.append(necessary_key)
        return missing_key_array    
    except Exception as e:
        return missing_key_array
`;

        const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);

        const result = PythonTreeSitterHandler.INSTANCE.getFuncSignatureString(tree.rootNode.namedChild(0));

        expect(result).toEqual("def contains_all_necessary_keys(request_dict, necessary_key_array)");
    });
});


describe("test_getImportFileOrModule", () => {
    beforeAll(async () => {
        await TreeSitterParser.INSTANCE.init("./resources");
    });

    it("case1_normal", async () => {
        const docText = `
import unittest 
from utils.common import get_address 
from utils import core 
from ares import LOGGER
def contains_all_necessary_keys(request_dict, necessary_key_array):
    missing_key_array = []
    try:
        # 校验是否参数合规
        # Example: necessary_key_array = ["name", "appid", "os_type", "git_url", "gitlab_pid", "manager"]
        for necessary_key in necessary_key_array:
            if necessary_key not in request_dict:
                missing_key_array.append(necessary_key)
        return missing_key_array    
    except Exception as e:
        return missing_key_array
`;

        const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);

        const result = PythonTreeSitterHandler.INSTANCE.getImportFileOrModule(tree.rootNode);
        expect(result).toEqual([
            "import unittest",
            "from utils.common import get_address",
            "from utils import core",
            "from ares import LOGGER"
        ]);
    });
});

describe("test_var", () =>{
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("constant", async () => {
    const docText = `IS_BOE = bytedenv.is_boe()`;
    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
    const startPosition: Position = {"line": 0, "character": 0};
    const endPosition: Position = {"line": 0, "character": 0};
    const selectRange = {start: startPosition, end: endPosition};
    const result = PythonTreeSitterHandler.INSTANCE.getVarParentNodeForRange(tree.rootNode, selectRange);
    expect(result?.text).toEqual(docText);
  });
});
