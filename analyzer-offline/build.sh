rm -rf output
mkdir output

cd analyzer/node || exit
pnpm install .
pnpm build
pkg .
cd ../..

GOOS=darwin GOARCH=arm64 go build -o output/analyzer-offline-support-macos-arm64
GOOS=darwin GOARCH=amd64 go build -o output/analyzer-offline-support-macos-x64
GOOS=linux GOARCH=arm64 go build -o output/analyzer-offline-support-linux-arm64
GOOS=linux GOARCH=amd64 go build -o output/analyzer-offline-support-linux-x64

mkdir output/bin
cp ./analyzer/node/bin/* output/bin
cp ./languageServer/bin/* output/bin