package manager_v1

import (
	"context"
	"encoding/json"
	"fmt"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/env"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/relation_manager/diff_algo"
	"ide/ckg/codekg/components/relation_manager/git_graph"
	"ide/ckg/codekg/components/relation_manager/interaction_graph"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/protocol"
	"ide/ckg/codekg/util"
	"sort"
	"sync"
	"time"

	"github.com/go-git/go-git/v5"
	lru "github.com/hashicorp/golang-lru/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"ide/ckg/codekg/components/logs"
)

type Manager struct {
	project             string
	getStorage          func() data_storage.Storage
	getEmbeddingStorage func() data_storage.EmbeddingStorage

	repo                  *git.Repository                                                    // 维护 Git 构建的关系
	interactionGraph      *interaction_graph.InteractionGraph                                // 维护用户行为构建出来的图结构
	recentInteractionList *interaction_graph.AccessQueue[*interaction_graph.InteractionNode] // 维护用户行为构建出来的时间序列关系
	recentOpenFileCache   *lru.Cache[string, *fileEntityCache]
	gitGraph              *git_graph.GitGraph
}

const (
	MaxSizeOfRecentInteractionNodes = 100
	NumberOfRecentFiles             = 5
)

type fileEntityCache struct {
	uriMeta  *data_storage.StorageURIMeta
	entities []*model.Entity
}

func NewManager(project string, getStorage func() data_storage.Storage, getEmbeddingStorage func() data_storage.EmbeddingStorage) *Manager {
	repo, err := git.PlainOpen(project)
	if err != nil {
		logs.Warn("failed to open git repo, err: %v", err)
	}

	interactionGraph := interaction_graph.NewInteractionGraph()
	recentInteractionList := interaction_graph.NewAccessQueue[*interaction_graph.InteractionNode](
		MaxSizeOfRecentInteractionNodes,
		func(n *interaction_graph.InteractionNode) string {
			return n.ID
		},
	)

	recentOpenFileCache, _ := lru.New[string, *fileEntityCache](100)

	return &Manager{
		project:               project,
		getStorage:            getStorage,
		getEmbeddingStorage:   getEmbeddingStorage,
		repo:                  repo,
		interactionGraph:      interactionGraph,
		recentInteractionList: recentInteractionList,
		recentOpenFileCache:   recentOpenFileCache,
		gitGraph:              nil,
	}
}

func (m *Manager) deduplicateEntities(entities []*model.Entity) []*model.Entity {
	seen := make(map[string]bool)
	result := make([]*model.Entity, 0)

	for _, entity := range entities {
		if !seen[entity.ID] {
			seen[entity.ID] = true
			result = append(result, entity)
		}
	}
	return result
}

func (m *Manager) filterCurrentFileEntities(entities []*model.Entity, currentEntity *model.Entity) []*model.Entity {
	if currentEntity == nil {
		return entities
	}

	result := make([]*model.Entity, 0)
	for _, entity := range entities {
		if entity.URI != currentEntity.URI {
			result = append(result, entity)
		}
	}
	return result
}

func (m *Manager) deduplicateNodes(nodes []*interaction_graph.InteractionNode) []*interaction_graph.InteractionNode {
	seen := make(map[string]bool)
	result := make([]*interaction_graph.InteractionNode, 0)

	for _, node := range nodes {
		if !seen[node.ID] {
			seen[node.ID] = true
			result = append(result, node)
		}
	}
	return result
}

func (m *Manager) filterCurrentFileNodes(nodes []*interaction_graph.InteractionNode, curURI string) []*interaction_graph.InteractionNode {
	if curURI == "" {
		return nodes
	}
	result := make([]*interaction_graph.InteractionNode, 0)
	for _, node := range nodes {
		if node.Entity.URI != curURI {
			result = append(result, node)
		}
	}
	return result
}

func (m *Manager) convertNodesToSnippets(ctx context.Context, nodes []*interaction_graph.InteractionNode) []*protocol.Snippet {
	snippets := make([]*protocol.Snippet, 0)
	for _, n := range nodes {
		nodeContent := n.GetNodeContent(ctx)
		if nodeContent == "" {
			continue
		}
		snippets = append(snippets, &protocol.Snippet{
			ProjectId:   m.project,
			Type:        protocol.SnippetType_snippet_type_code,
			FilePath:    n.Entity.URI,
			StartLine:   n.Entity.GetStartLine(),
			EndLine:     n.Entity.GetEndLine(),
			Content:     nodeContent,
			RecallType:  protocol.RecallType_recall_type_relation_by_user_action_trace,
			CkgEntityId: n.ID,
		})
	}

	// 根据行号排序
	sort.Slice(snippets, func(i, j int) bool {
		if snippets[i].FilePath != snippets[j].FilePath {
			return snippets[i].FilePath < snippets[j].FilePath
		}
		return snippets[i].StartLine < snippets[j].StartLine
	})
	return snippets
}

func (m *Manager) getEntitiesFromFile(ctx context.Context, filePath string) ([]*model.Entity, error) {
	// TODO：如果没找到，可能遇到了大仓库/长文件/...，需要处理这种情况 切分 & Embedding & 构建实体...
	storage := m.getStorage()
	if storage == nil {
		return []*model.Entity{}, errors.New("no storage found")
	}

	filePath = util.GetURIFromFilePathForCursorMove(filePath)
	logs.CtxInfo(ctx, "getCKGEntitiesFromFile GetURIMetaFromURIWithoutContent, filePath: %v", filePath)

	uriMeta, err := storage.GetURIMetaFromURIWithoutContent(ctx, storage.GetConn(), &model.URIStatus{
		AbsPath: filePath,
	})
	if err != nil {
		return []*model.Entity{}, errors.WithMessage(err, "error AddRecentCursorRelation")
	}

	entities := make([]*model.Entity, 0)
	cacheValue, ok := m.recentOpenFileCache.Get(filePath)
	if ok && cacheValue.uriMeta.ContentHash == uriMeta.ContentHash {
		entities = cacheValue.entities
	} else {
		if ok {
			m.recentOpenFileCache.Remove(filePath)
		}

		dbEntities, err := storage.SearchEntitiesByURIMeta(ctx, storage.GetConn(), uriMeta)
		if err != nil {
			return []*model.Entity{}, errors.WithMessagef(err, "error AddRecentCursorRelation")
		}

		for _, dbEntity := range dbEntities {
			// TODO: 是否要按类型过滤？
			if dbEntity.Type != model.Clazz && dbEntity.Type != model.Method &&
				dbEntity.Type != model.CodeChunk && dbEntity.Type != model.Text {
				continue
			}

			var attributes map[string]interface{}
			err = json.Unmarshal([]byte(dbEntity.Attributes), &attributes)
			if err != nil {
				continue
			}

			entities = append(entities, model.CreateEntity(dbEntity.EntityID, dbEntity.Name, uriMeta.Uri, dbEntity.Type, attributes))
		}
	}

	if len(entities) > 0 {
		m.recentOpenFileCache.ContainsOrAdd(filePath, &fileEntityCache{
			uriMeta:  uriMeta,
			entities: entities,
		})
	}

	return entities, nil
}

func (m *Manager) getEntitiesFromFileLine(ctx context.Context, filePath string, cursorLine int32) ([]*model.Entity, error) {
	entities, err := m.getEntitiesFromFile(ctx, filePath)
	if err != nil {
		return nil, err
	}

	hitEntities := lo.Filter(entities, func(item *model.Entity, _ int) bool {
		return item.GetStartLine() <= cursorLine && cursorLine <= item.GetEndLine()
	})

	return hitEntities, nil
}

func (m *Manager) getNodeFromFileLine(ctx context.Context, filePath string, cursorLine int32) (*interaction_graph.InteractionNode, error) {
	entities, err := m.getEntitiesFromFileLine(ctx, filePath, cursorLine)
	if err != nil || len(entities) == 0 {
		return nil, err
	}

	// TODO: 考虑多个实体的情况，暂时先选出 end-line - start-line 最小的实体
	sort.Slice(entities, func(i, j int) bool {
		return entities[i].GetEndLine()-entities[i].GetStartLine() < entities[j].GetEndLine()-entities[j].GetStartLine()
	})
	currentEntity := entities[0]
	return interaction_graph.NewInteractionNode(currentEntity), nil
}

func (m *Manager) getNodeFromRequest(ctx context.Context, request *protocol.RetrieveRelevantSnippetRequest) (currentNode *interaction_graph.InteractionNode, err error) {
	var currentEntity *model.Entity

	// 如果没有 currentEditor，就不能获取实体
	if request.CurrentEditor == nil {
		return nil, errors.New("get node from request: current editor is nil")
	}

	// 获取当前实体失败
	currentEntities, err := m.getEntitiesFromFile(ctx, request.CurrentEditor.FilePath)
	if err != nil {
		logs.CtxError(ctx, "failed to get entities from file: %v", err)
		return nil, fmt.Errorf("failed to get entities: %w", err)
	} else if len(currentEntities) == 0 {
		return nil, errors.New("current entities is empty")
	}

	// 选中代码是用户指定的行为，优先级最高。查找选中代码对应的节点作为当前节点
	if request.CurrentEditor != nil && request.CurrentEditor.SelectCodeRange != nil {
		currentEntity = getBestMatchEntity(ctx, currentEntities,
			request.CurrentEditor.SelectCodeRange.StartLine,
			request.CurrentEditor.SelectCodeRange.EndLine)
	} else if request.CurrentEditor != nil && request.CurrentEditor.VisibleCodeRange != nil {
		// 优先选择当前可见区域内最近访问过的节点
		recentNodes := m.recentInteractionList.Get()
		for _, recentNode := range recentNodes {
			if recentNode.Entity.URI == request.CurrentEditor.FilePath {
				currentEntity = recentNode.Entity
				break
			}
		}
		// 如果没有，选择最佳匹配节点
		if currentEntity == nil {
			currentEntity = getBestMatchEntity(ctx, currentEntities,
				request.CurrentEditor.VisibleCodeRange.StartLine,
				request.CurrentEditor.VisibleCodeRange.EndLine)
		}
	}

	if currentEntity != nil && m.interactionGraph.HasNode(currentEntity.ID) {
		return m.interactionGraph.GetNode(currentEntity.ID), nil
	} else {
		return nil, errors.New("no valid code range provided")
	}
}

func (m *Manager) retrieveNodesFromRecentInteractionList(ctx context.Context, topK int) ([]*interaction_graph.InteractionNode, error) {
	// 获取 TopK 个最近访问的文件对应的所有实体
	recentNodes := m.recentInteractionList.Get()
	recentFiles := make(map[string]bool)
	retrievedEntities := make([]*model.Entity, 0)
	for _, n := range recentNodes {
		if len(recentFiles) >= topK {
			break
		}
		if !recentFiles[n.Entity.URI] {
			recentFiles[n.Entity.URI] = true
		}
	}
	if len(recentFiles) == 0 {
		return nil, errors.New("no recent files found")
	}

	// 获取这些文件中包含的实体
	for filePath := range recentFiles {
		cache, ok := m.recentOpenFileCache.Peek(filePath)
		if !ok {
			logs.CtxInfo(ctx, "retrieve from open files: no entity found at file: %v", filePath)
			continue
		}
		retrievedEntities = append(retrievedEntities, cache.entities...)
	}

	// 将实体转换为节点
	recentFileNodes := lo.Map(retrievedEntities, func(entity *model.Entity, _ int) *interaction_graph.InteractionNode {
		if m.interactionGraph.HasNode(entity.ID) {
			return m.interactionGraph.GetNode(entity.ID)
		} else {
			return interaction_graph.NewInteractionNode(entity)
		}
	})
	return recentFileNodes, nil
}

func (m *Manager) retrieveNodesFromInteractionGraph(ctx context.Context, request *protocol.RetrieveRelevantSnippetRequest) (*interaction_graph.InteractionNode, []*interaction_graph.InteractionNode, error) {
	curNode, err := m.getNodeFromRequest(ctx, request)
	if err != nil {
		return nil, nil, err
	}
	connectedNodes := m.interactionGraph.GetConnectedNodes(curNode)
	if len(connectedNodes) == 0 {
		return nil, nil, errors.New("no connected nodes found")
	}
	return curNode, connectedNodes, nil
}

func (m *Manager) HandleCursorMove(ctx context.Context, cursorMoveFilePath string, cursorMoveLine int32) error {
	//logs.CtxInfo(ctx, "HandleCursorMove, filePath: %s, line: %d", cursorMoveFilePath, cursorMoveLine)
	// 获取当前实体以及对应的节点
	currentNode, err := m.getNodeFromFileLine(ctx, cursorMoveFilePath, cursorMoveLine)
	if err != nil || currentNode == nil {
		logs.CtxInfo(ctx, "handle cursor move v1: no node found in file path: %v, line: %v, err: %v", cursorMoveFilePath, cursorMoveLine, err)
		return nil
	}
	timestamp := time.Now().Unix()

	// 获取最近的一个节点
	recentNodes := m.recentInteractionList.PeekRecent(1)
	if len(recentNodes) == 0 { // 没有最近的节点，则创建一个新的节点
		m.recentInteractionList.Add(currentNode)
		currentNode.View(timestamp)
		m.interactionGraph.InsertNode(ctx, currentNode)
		logs.CtxInfo(ctx, "[HandleCursorMove] Recent interaction list is empty, add node %v", currentNode)
		return nil
	}
	recentNode := recentNodes[0]

	// 更新交互图
	if err := m.interactionGraph.HandleCursorMove(ctx, currentNode, recentNode, timestamp); err != nil {
		return err
	}
	m.recentInteractionList.Add(currentNode)

	logs.CtxInfo(ctx, "[HandleCursorMove] update interaction graph success")
	return nil
}

func (m *Manager) HandleDocumentChange(ctx context.Context, entity *model.Entity, timestamp int64) error {
	// TODO 处理 DocumentChange(Edit), 其中 CursorMove 事件同时包括 Edit 和 View，需要和 DocumentChange 结合考虑
	////当前节点
	//currentNode, _ := m.interactionGraph.getOrCreateNode(entity, timestamp)
	//// 最近访问的节点
	//if len(recentNodes) == 0 {
	//	return errors.New("no recent node found")
	//}
	//recentNode := recentNodes[0]
	//recentNodes := m.recentInteractionList.PeekRecent(1)
	//return m.interactionGraph.handleDocumentChange(ctx, entity, recentNode, timestamp)
	return nil
}

func (m *Manager) RetrieveRelevantContextFromInteraction(ctx context.Context, cli knowledgebase.Client, processedQuery *model.ProcessedQuery,
	request *protocol.RetrieveRelevantSnippetRequest, tccConfig *model.CodeKGTCCConfig, recallNum int) ([]*protocol.Snippet, error) {
	reportEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGInteraction, request.UserId, "", string(env.GetSourceProduct()))
	defer reportEvent.Report(ctx, false)

	startTime := time.Now()
	reportEvent.AddTeaParam(ckg_metrics.TeaParamsUserInteractionRetrievalVersion, request.Version)
	reportEvent.AddTeaParam(ckg_metrics.TeaParamUserInteractionHasCurrentEditor, request.CurrentEditor != nil)
	reportEvent.AddTeaParam(ckg_metrics.TeaParamUserInteractionHasGitRepo, m.repo != nil)

	// 上报用户此时的历史数据
	if m.repo != nil {
		remotes, err := m.repo.Remotes()
		if err != nil {
			logs.CtxError(ctx, "failed to get remote, err: %v", err)
		} else if len(remotes) > 0 {
			var commit string
			head, err := m.repo.Head()
			if err == nil {
				commit = head.Hash().String()
			}
			event := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGUserTrace, request.UserId, "", string(env.GetSourceProduct()))
			event.AddParam(ckg_metrics.ParamUserTraceData, map[string]interface{}{
				"request":            request,
				"git_remotes":        remotes[0].Config().URLs,
				"commit":             commit,
				"recent_interaction": m.recentInteractionList,
				"interaction_graph":  m.interactionGraph,
			})
			event.ReportSync(ctx, true)
		}
	}

	storage := m.getStorage()
	if storage == nil {
		return nil, errors.New("interaction graph getStorage failed")
	}

	var curNode *interaction_graph.InteractionNode
	nodeChan := make(chan []*interaction_graph.InteractionNode, 2)
	wg := sync.WaitGroup{}
	wg.Add(2)

	// 1. 交互图中获取和当前实体相关的节点
	util.SafeGo(ctx, func() {
		defer wg.Done()
		currentNode, retrievedNodes, err := m.retrieveNodesFromInteractionGraph(ctx, request)
		if err != nil || currentNode == nil {
			logs.CtxError(ctx, "retrieveNodesFromInteractionGraph error: %v", err)
			return
		}
		nodeChan <- retrievedNodes
		curNode = currentNode
	})

	// 2. 最近访问文件包括的所有节点。
	util.SafeGo(ctx, func() {
		defer wg.Done()
		retrievedNodes, err := m.retrieveNodesFromRecentInteractionList(ctx, NumberOfRecentFiles)
		if err != nil {
			logs.CtxError(ctx, "retrieveNodesFromRecentInteractionList error: %v", err)
			return
		}
		nodeChan <- retrievedNodes
	})

	wg.Wait()
	close(nodeChan)

	allNodes := make([]*interaction_graph.InteractionNode, 0)
	for nodes := range nodeChan {
		allNodes = append(allNodes, nodes...)
	}
	if len(allNodes) == 0 {
		return nil, errors.New("no nodes found")
	}

	// 去重过滤
	uniqueNodes := m.deduplicateNodes(allNodes)
	curURI := func() string {
		if curNode != nil && curNode.Entity != nil {
			return curNode.Entity.URI
		}
		if request.CurrentEditor != nil {
			return request.CurrentEditor.FilePath
		}
		return "" // 或返回默认值
	}()
	filteredNodes := m.filterCurrentFileNodes(uniqueNodes, curURI)
	if len(filteredNodes) == 0 {
		return make([]*protocol.Snippet, 0), nil
	}

	// 计算权重并排序
	rankOptions := interaction_graph.NodeRankOptions{
		Now:              time.Now().Unix(),
		OriginalQuery:    processedQuery.OriginalQuery,
		Embedding:        processedQuery.Embedding,
		Keywords:         processedQuery.Keywords,
		EmbeddingStorage: m.getEmbeddingStorage(),
		GitGraph:         nil,
	}
	processedNodes, err := m.interactionGraph.RankNodes(ctx, curNode, filteredNodes, rankOptions)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to rank nodes")
	}

	// 获取时重新从数据库中查询，直到查到 topK 个 entity，每次查到了都更新一下图结构。
	finalNodes := make([]*interaction_graph.InteractionNode, 0, recallNum)
	for _, n := range processedNodes {
		if len(finalNodes) >= recallNum {
			break
		}
		newEntity, err := storage.GetEntityByEntityID(ctx, storage.GetConn(), n.Entity.ID)
		if err != nil || newEntity == nil {
			logs.CtxInfo(ctx, "failed to get entity %s: %v, delete this node", n.Entity.ID, err)
			err := m.interactionGraph.DeleteNode(n.ID)
			if err != nil {
				logs.CtxInfo(ctx, "failed to delete node, %s", err)
			}
			continue
		}

		if newEntity.ID != n.Entity.ID { // 预期之外的行为
			logs.CtxInfo(ctx, "update nodes error, new entity ID != previous entity ID")
			continue
		}

		n.Entity = newEntity // 指向新的 entity
		finalNodes = append(finalNodes, n)
	}

	defer func() {
		reportEvent.AddTeaParam(ckg_metrics.TeaParamUserInteractionGitRepoNum, len(finalNodes))
		reportEvent.AddTeaParam(ckg_metrics.TeaParamUserInteractionRetrievalTime, time.Since(startTime).Milliseconds())
	}()

	return m.convertNodesToSnippets(ctx, finalNodes), nil
}

func computeDiffLinesNumber(a []string, b []string) []int {
	diffLines := make([]int, 0)
	diff := diff_algo.New(a, b)
	diff.Compose()

	lineNumberBeforeLatestDeletion := 0
	for _, s := range diff.Ses() {
		switch s.GetType() {
		case diff_algo.SesAdd:
			diffLines = append(diffLines, s.BIdx)
			lineNumberBeforeLatestDeletion = s.BIdx
		case diff_algo.SesDelete:
			if len(diffLines) > 0 {
				if diffLines[len(diffLines)-1] != lineNumberBeforeLatestDeletion {
					diffLines = append(diffLines, lineNumberBeforeLatestDeletion)
				}
			} else {
				diffLines = append(diffLines, lineNumberBeforeLatestDeletion)
			}
		case diff_algo.SesCommon:
			lineNumberBeforeLatestDeletion = s.BIdx
		}
	}

	return diffLines
}

func calculateIntersectRatio(startLine1, endLine1, startLine2, endLine2 int32) float64 {
	if startLine1 > endLine1 || startLine2 > endLine2 {
		return 0.0
	}

	// intersect 区域
	intersectStart := max(startLine1, startLine2)
	intersectEnd := min(endLine1, endLine2)
	if intersectStart > intersectEnd {
		return 0.0
	}
	intersectLines := intersectEnd - intersectStart + 1

	// union 区域
	unionStart := min(startLine1, startLine2)
	unionEnd := max(endLine1, endLine2)
	union := unionEnd - unionStart + 1

	return float64(intersectLines) / float64(union)
}

func getBestMatchEntity(ctx context.Context, entities []*model.Entity, startLine int32, endLine int32) *model.Entity {
	if len(entities) == 0 {
		return nil
	} else if startLine > endLine {
		logs.CtxTrace(ctx, "[getBestMatchEntity] startLine > endLine, startLine: %d, endLine: %d", startLine, endLine)
		return entities[0]
	}

	var bestMatch *model.Entity
	var minimumRange int32
	var maxIntersectRatio float64

	for i, entity := range entities {
		entityStart, entityEnd := entity.GetStartLine(), entity.GetEndLine()
		entityRange := entityEnd - entityStart + 1
		ratio := calculateIntersectRatio(startLine, endLine, entityStart, entityEnd)

		if i == 0 {
			bestMatch = entity
			minimumRange = entityRange
			maxIntersectRatio = ratio
			continue
		}

		if ratio > maxIntersectRatio || (ratio == maxIntersectRatio && entityRange < minimumRange) {
			maxIntersectRatio = ratio
			bestMatch = entity
			minimumRange = entityRange
		}
	}

	return bestMatch
}
