package splitter

import (
	"context"
	"fmt"
	"sync"

	kb "ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/splitter/common"
	"ide/ckg/codekg/components/splitter/kb_splitter"
	"ide/ckg/codekg/components/splitter/local_splitter"
	"ide/ckg/codekg/components/splitter/local_splitter/text/langchain"
	"ide/ckg/codekg/components/splitter/model"
)

// 使用model包中定义的类型
// 为了向后兼容，保留这些常量，但使用model包中的类型
const (
	// LocalType 使用本地基础markdown分割器
	LocalType = model.SplitterTypeLocal
	// KBType 使用知识库分割器
	KBType = model.SplitterTypeKB
	// LangchainType 使用Langchain分割器 - 这个需要添加到model包中
	LangchainType = "langchain"
)

var (
	registry *common.Registry
	factory  *common.Factory
	once     sync.Once
)

// 初始化注册表和工厂
func initRegistry() {
	once.Do(func() {
		registry = common.NewRegistry()
		factory = common.NewFactory(registry)
		common.RegisterStandardSplitters(registry, factory)
	})
}

// Split 是分割文件的统一入口
// opt 参数指定了使用的分割器类型和选项
// files 是要分割的文件列表
func Split(ctx context.Context, opt *model.SplitOption, caller string, files []*kb.SplitFile) (*kb.SplitFilesResponse, error) {
	// 初始化注册表和工厂（如果尚未初始化）
	initRegistry()

	// 尝试使用注册表和工厂机制
	if opt != nil && opt.SplitterType != "" {
		// 使用新的注册表机制
		return common.SplitWithRegistry(ctx, registry, factory, opt, files)
	}

	// 以下是向后兼容的代码，保留原有逻辑
	// 使用显式的类型选择（新接口）
	if opt != nil && opt.SplitterType != "" {
		switch opt.SplitterType {
		case model.SplitterTypeLocal:
			return splitByContentType(ctx, opt, files)
		case model.SplitterTypeKB:
			if opt.KBOpt != nil {
				return splitWithKB(ctx, opt.KBOpt, caller, files)
			}
			return nil, fmt.Errorf("KB options are required for KB splitter")
		default:
			return nil, fmt.Errorf("unknown splitter type: %s", opt.SplitterType)
		}
	}

	// 兼容旧接口（向后兼容）
	if opt != nil {
		// 检查是否提供了LocalOpt且包含LangchainOpt
		if opt.LocalOpt != nil && opt.LocalOpt.LangchainOpt != nil {
			// 使用Langchain分割
			return splitWithLangchain(ctx, opt.LocalOpt.LangchainOpt, files)
		}

		// Check PreferLocalSplit flag first
		if opt.PreferLocalSplit {
			// Try local splitting first
			result, err := splitLocally(ctx, opt.LocalOpt, files)
			if err == nil {
				return result, nil
			}

			// If local splitting fails and KB options are available, fallback to KB splitting
			if opt.KBOpt != nil {
				return splitWithKB(ctx, opt.KBOpt, caller, files)
			}

			failedFiles := make(map[string]string)
			for _, file := range files {
				failedFiles[file.Path] = err.Error()
			}

			// If no KB options, return the original error
			return &kb.SplitFilesResponse{
				Results:     make(map[string]*kb.SplitFileResult_),
				FailedFiles: failedFiles,
			}, err
		} else if opt.KBOpt != nil {
			// If not PreferLocalSplit and KB options are available, use KB splitting
			return splitWithKB(ctx, opt.KBOpt, caller, files)
		}
	}

	// Default to local splitting if no specific options provided
	if opt.LocalOpt == nil {
		opt.LocalOpt = &model.LocalSplitOption{
			ContentType: model.ContentTypeText,
		}
	}
	return splitLocally(ctx, opt.LocalOpt, files)
}

// splitByContentType 根据内容类型选择合适的分割器
func splitByContentType(ctx context.Context, opt *model.SplitOption, files []*kb.SplitFile) (*kb.SplitFilesResponse, error) {
	if opt.LocalOpt == nil {
		opt.LocalOpt = &model.LocalSplitOption{
			ContentType: model.ContentTypeText,
		}
	}

	switch opt.LocalOpt.ContentType {
	case model.ContentTypeText:
		// 使用文本分割器
		return splitLocalText(ctx, opt, files)
	case model.ContentTypeCode:
		// 使用代码分割器（目前通过local_splitter处理）
		return splitLocally(ctx, opt.LocalOpt, files)
	default:
		return nil, fmt.Errorf("unknown content type: %s", opt.LocalOpt.ContentType)
	}
}

// splitLocalText 根据本地文本分割器子类型选择合适的分割器
func splitLocalText(ctx context.Context, opt *model.SplitOption, files []*kb.SplitFile) (*kb.SplitFilesResponse, error) {
	if opt.LocalOpt == nil {
		opt.LocalOpt = &model.LocalSplitOption{
			LocalSubtype: model.LocalSubtypeBasicText,
		}
	}

	switch opt.LocalOpt.LocalSubtype {
	case model.LocalSubtypeBasicText:
		// 使用基础文本分割器
		return splitLocally(ctx, opt.LocalOpt, files)
	case model.LocalSubtypeLangchainText:
		// 使用Langchain分割器
		return splitWithLangchainByType(ctx, opt, files)
	default:
		return nil, fmt.Errorf("unknown local text splitter subtype: %s", opt.LocalOpt.LocalSubtype)
	}
}

// splitWithLangchainByType 根据指定的Langchain类型分割文件
func splitWithLangchainByType(ctx context.Context, opt *model.SplitOption, files []*kb.SplitFile) (*kb.SplitFilesResponse, error) {
	// 确保LangchainOpt存在
	if opt.LocalOpt == nil || opt.LocalOpt.LangchainOpt == nil {
		// 创建默认的LangchainOpt
		if opt.LocalOpt == nil {
			opt.LocalOpt = &model.LocalSplitOption{}
		}
		opt.LocalOpt.LangchainOpt = &model.LangchainSplitOption{
			SplitterType: model.LangchainTypeRecursive,
			ChunkSize:    4000, // 默认值
			ChunkOverlap: 200,  // 默认值
		}
	}

	// 使用Langchain分割
	return splitWithLangchain(ctx, opt.LocalOpt.LangchainOpt, files)
}

func splitWithKB(ctx context.Context, opt *model.KBSplitOption, caller string, files []*kb.SplitFile) (*kb.SplitFilesResponse, error) {
	return kb_splitter.Split(ctx, *opt, caller, files)
}

func splitLocally(ctx context.Context, opt *model.LocalSplitOption, files []*kb.SplitFile) (*kb.SplitFilesResponse, error) {
	return local_splitter.Split(ctx, opt, files)
}

func splitWithLangchain(ctx context.Context, opt *model.LangchainSplitOption, files []*kb.SplitFile) (*kb.SplitFilesResponse, error) {
	// 使用新的适配函数
	return langchain.SplitFilesWithLangchainOpt(ctx, opt, files)
}
