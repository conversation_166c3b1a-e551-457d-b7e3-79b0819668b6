import {EditorSelectionInfo, pointToPosition} from "../../../utils/common";
import {FileSymbolDef} from "../../share/common";
import {getCachedFileSymbolDef} from "../../cache/symbolDefCache";
import {
  addDefinitionAnalysisTask, definition<PERSON><PERSON><PERSON>,
  EVENT_PROCESS_CURSOR_MOVE,
} from "./share";
import {getConfig} from "../../common/config";
import {getActiveFileCachedTreeAndDocument} from "../../cache/astCache";
import {TreeSitterDispatcher} from "../../parse/treesitter/dispatcher";
import {TreeSitterFunctionalityEnum} from "../../parse/treesitter/share";
import {SyntaxNode} from "web-tree-sitter";
import {getFileSymbolDef, GetSymbolDefSource, isLanguageSupported} from "../../share/definition";
import {getWorkspaceFolderPath} from "../../../utils/path";
import {
  EVENT_CATEGORY_TYPE_ANALYSIS,
  EVENT_NAME_FAILED,
  EVENT_VALUE_NO_CACHE,
  reportEvent,
} from "../../../utils/event";
import {Logger} from "../../../utils/logger";

let debounceRunTimer = null;

export async function partialOndemandTypeAnalysis(editorSelectionInfo: EditorSelectionInfo): Promise<void> {
  if (!isLanguageSupported(editorSelectionInfo.document.languageId)) {
    return;
  }

  if (!editorSelectionInfo.document.uri.startsWith(getWorkspaceFolderPath())) {
    return;
  }

  if (debounceRunTimer) {
    clearTimeout(debounceRunTimer);
  }

  const debounceRunFunc = async (): Promise<void> => {
    const cachedTree = getActiveFileCachedTreeAndDocument(editorSelectionInfo.document)?.tree;
    if (!cachedTree) {
      Logger.error(`partialOndemandTypeAnalysis: no tree found for '${editorSelectionInfo.document.uri}'`);
      reportEvent(editorSelectionInfo.document.languageId, EVENT_CATEGORY_TYPE_ANALYSIS, EVENT_PROCESS_CURSOR_MOVE, EVENT_NAME_FAILED, EVENT_VALUE_NO_CACHE).then();
      return;
    }

    const taskFunc = async (): Promise<FileSymbolDef> => {
      const result = new FileSymbolDef(editorSelectionInfo.document);

      const cachedSymbolDef = getCachedFileSymbolDef(editorSelectionInfo.document, false);
      if (cachedSymbolDef) {
        result.indexDefInfo = [
          ...cachedSymbolDef.indexDefInfo,
        ];
      } else {
        Logger.warn(`partialOndemandTypeAnalysis: no cachedSymbolDef found for ${editorSelectionInfo.document.uri}`);
        result.indexDefInfo = new Array(editorSelectionInfo.document.getText().length).fill(null);
      }

      let analysisStartLine = Math.max(editorSelectionInfo.selectionRange.start.line - getConfig("analysis.definition.partialAnalysisLines"), 0);

      const funcDeclNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(
        editorSelectionInfo.document.languageId,
        TreeSitterFunctionalityEnum.GetFuncParentNodeForRange,
        cachedTree.rootNode,
        {start: editorSelectionInfo.selectionRange.start, end: editorSelectionInfo.selectionRange.end},
      );
      if (funcDeclNode) {
        if (funcDeclNode.startPosition.row >= analysisStartLine) {
          analysisStartLine = funcDeclNode.startPosition.row;
        } else {
          const funcSignatureString = TreeSitterDispatcher.INSTANCE.dispatch(editorSelectionInfo.document.languageId, TreeSitterFunctionalityEnum.GetFuncSignatureString, funcDeclNode);
          if (funcSignatureString) {
            await getFileSymbolDef(GetSymbolDefSource.Partial, result, editorSelectionInfo.document, cachedTree, [{
              start: pointToPosition(funcDeclNode.startPosition),
              end: editorSelectionInfo.document.positionAt(funcDeclNode.startIndex + funcSignatureString.length),
            }], definitionHandler);
          }
        }
      }

      await getFileSymbolDef(GetSymbolDefSource.Partial, result, editorSelectionInfo.document, cachedTree, [{
        start: {line: analysisStartLine, character: 0},
        end: {line: editorSelectionInfo.selectionRange.end.line + 1, character: 0},
      }], definitionHandler);

      return result;
    };

    addDefinitionAnalysisTask({
      id: Date.now(),
      source: GetSymbolDefSource.Partial,
      document: editorSelectionInfo.document,
      taskFunc,
    });
  };

  debounceRunTimer = setTimeout(debounceRunFunc, getConfig("analysis.definition.partialAnalysisDebounce"));
}