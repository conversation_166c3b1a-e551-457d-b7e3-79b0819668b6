package main

import (
	"context"
	"fmt"
	"ide/ckg/codekg/components/relation_manager/git_graph"
	_ "net/http/pprof"
	"os"
	"runtime"
	"time"
)

func main() {
	// 初始化性能统计
	stats := git_graph.NewPerformanceStats()

	// 检查命令行参数
	if len(os.Args) != 2 {
		fmt.Printf("Usage: %s <repository-path>\n", os.Args[0])
		os.Exit(1)
	}

	repoPath := os.Args[1]

	// 创建GitGraph
	g, err := git_graph.NewGitGraph(repoPath)
	CheckIfError(err)

	// 构建GitGraph
	err = g.BuildGitGraph(context.Background(), "test", &git_graph.BuildGitGraphOption{
		CommitLimit:       300,
		MaxCommitSize:     100,
		MinCommitSize:     3,
		CurrentUserOnly:   false,
		MinChangeSize:     3,
		MaxChangeSize:     1000,
		MaxGraphNodeCount: 300,
		MaxGraphEdgeCount: 50000,
	})
	CheckIfError(err)
	stats.PrintStats("After Build")

	// sleep 10s
	time.Sleep(10 * time.Second)

	// 获取热点文件
	start := time.Now()
	files, err := g.GetHotGitFiles(context.Background(), 20)
	fmt.Printf("get hot files time: %v\n", time.Since(start))
	CheckIfError(err)

	// 打印文件列表
	for _, file := range files {
		fmt.Printf("file: %s\n", file)
	}

	runtime.GC()
	stats.PrintStats("After clear repo")

	if len(files) < 10 {
		fmt.Println("Not enough files for testing.")
		return
	}

	relevantFiles, err := g.GetRelatedFiles(context.Background(), files[8], 50)
	CheckIfError(err)
	fmt.Println("\n=============\nrelevant files for files: ", files[8])
	for _, file := range relevantFiles {
		fmt.Printf("file: %s\n", file)
	}

	relevantFiles, err = g.GetRelatedFiles(context.Background(), files[2], 10)
	CheckIfError(err)
	fmt.Println("\n=============\nrelevant files for files: ", files[2])
	for _, file := range relevantFiles {
		fmt.Printf("file: %s\n", file)
	}
	g.PrintGraphSize()
	fmt.Println("Done")
}

func CheckIfError(err error) {
	if err == nil {
		return
	}

	fmt.Printf("\x1b[31;1m%s\x1b[0m\n", fmt.Sprintf("error: %s", err))
	os.Exit(1)
}
