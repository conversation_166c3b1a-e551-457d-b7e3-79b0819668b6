export interface Error {
  message: string;
  stack: string;
}

export interface Range {
  startLine: number;
  endLine: number;
}

export interface Entity {
  projectId: string;
  entityId: string;
}

export interface BuildStatusMap {
  BUILDING: 0;
  FINISHED: 1;
  FAILED: 2;
}

export interface ProjectBuildStatus {
  status: BuildStatusMap[keyof BuildStatusMap];
  progress: number;
  successIndexFiles: number,
  failedIndexFiles: number,
  totalIndexFiles: number,
  emptyProject: boolean,
}

export interface CurrentEditorInfo {
  filePath: string;
  projectId: string;
  cursorLine: number;
  selectCodeRange?: Range;
  selectCodeContent: string;
  visibleCodeRange?: Range;
  visibleCodeContent: string;
  userFileContent: string;
}

export interface Reference {
  filePath: string,
  startLine: number,
  endLine: number,
}

export interface ImportAnalysisResponse {
  code: number;
  error?: Error;
  result?: ImportAnalysisResult;
}

export interface ImportAnalysisResult {
  message: string;
  data: string;
}

export interface FilesImportAnalysisResponse {
  code: number;
  error?: Error;
  result?: ImportAnalysisResult;
}

export enum EntityType {
  Folder = 0,
  File = 1,
  Class = 2,
  Method = 3,
  Text = 4,
}

export interface SymbolInfo {
  name: string,
  filePath: string,
  startLine: number,
  endLine: number,
}

export interface CodeInfo {
  filePath: string,
  startLine: number,
  endLine: number,
  content: string,
}

export interface SnippetTypeMap {
  SNIPPET_TYPE_CODE: 0;
  SNIPPET_TYPE_FOLDER_TREE: 1;
  SNIPPET_TYPE_FILE: 2;
}

export interface RecallTypeMap {
  RECALL_TYPE_USER_SPECIFIED: 0;
  RECALL_TYPE_EMBEDDING: 1;
  RECALL_TYPE_NER: 2;
  RECALL_TYPE_RELATION_BY_USER_ACTION_TRACE: 20;
  RECALL_TYPE_RELATION_BY_GIT_RELEVANCE: 30;
}

export interface Snippet {
  projectId: string,
  type: SnippetTypeMap[keyof SnippetTypeMap],
  filePath: string,
  startLine: number,
  endLine: number,
  content: string,
  recallType: RecallTypeMap[keyof RecallTypeMap],
  ckgEntityId: string,
}

export interface RelevantSnippets {
  snippetsByWorkspace: Snippet[],
  snippetsBySelectedFolders: Snippet[],
  snippetsBySelectedFiles: Snippet[],
  snippetsBySelectedCodes: Snippet[],
  snippetsByCurrentEditor: Snippet[],
  snippetsByInteraction: Snippet[],
}

export interface VirtualProjectInfo {
  projectId: string;
  uri: string;
  relativeGlobsToLoadList: string[];
}

export interface SnippetInfo {
  projectId: string;
  type: SnippetTypeMap[keyof SnippetTypeMap];
  filePath: string;
  startLine: number;
  endLine: number;
  content: string;
  recallType: RecallTypeMap[keyof RecallTypeMap];
  ckgEntityId?: string;
}

export interface SnippetWithScoreInfo {
  snippet: SnippetInfo;
  score: number;
}
