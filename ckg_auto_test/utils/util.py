import requests
import subprocess
import os
from . import const
import glob
import platform
import shutil
import time
import stat
import json


def get_token_id():
    try:
        headers = {'authorization': f'Bearer {const.jwt_token}'}
        resp = requests.get(
            url=const.jwt_token_url,
            headers=headers,
            timeout=10
        )
        resp.raise_for_status()
        token_id = resp.headers.get('X-JWT-Token')
        if not token_id:
            raise ValueError("No X-JWT-Token found in response headers")
        return f"Byte-Cloud-JWT {token_id}"
    except Exception as e:
        print(f"Get JWT Token error: {str(e)}")
        raise


def kill_process_by_port(port):
    pid = None
    # windows下的指令和linux下指令要做区分
    if platform.system() == "Windows":
        command = f"netstat -ano | findstr :{port}"
        try:
            result = subprocess.check_output(command, shell=True).decode()

            if result:
                lines = result.strip().split('\n')
                for line in lines:
                    parts = line.split()
                    if len(parts) == 5 and parts[1].endswith(f":{port}"):
                        pid = parts[4]
                        break
        except subprocess.CalledProcessError:
            print(f"Failed to get port: {port} pid")
            return False
    else:
        command = f"lsof -i :{port} | grep LISTEN | awk '{{print $2}}'"
        pid = subprocess.check_output(command, shell=True).decode().strip()

    if pid:
        try:
            os.kill(int(pid), 9)
            print(f"Process {pid} has been terminated.")
            return True
        except Exception as e:
            print(f"Failed to terminate process {pid}: {e}")
            return False
    else:
        print(f"No process found using port {port}.")
        return False


def is_ckg_db_delete():
    pattern = os.path.join(const.storage_path, '*.db')
    file_list = glob.glob(pattern)
    if len(file_list) != 1:
        return False
    if file_list[0].endswith('env_codekg.db'):
        return True
    return False


def download_binary_file(url, binary_name):
    try:
        if os.path.exists(const.download_binary_path):
            shutil.rmtree(const.download_binary_path)
            os.makedirs(const.download_binary_path)
        os.makedirs(const.download_binary_path, exist_ok=True)
        download_name = os.path.join(const.download_binary_path, binary_name)
        with requests.get(url, stream=True, timeout=15) as response:
            response.raise_for_status()
            with open(download_name, 'wb') as out_file:
                out_file.write(response.content)
                print('download binary success')
                os.chmod(download_name, 0o777)
                return download_name
    except requests.Timeout:
        print('download binary timed out')
        return ''
    except requests.RequestException as e:
        print('download binary failed')
        return ''


def get_download_url(json_data):
    os_type = platform.system()
    arch_type = platform.machine()
    binary_file_name = ''
    if os_type == "Linux" and arch_type == "aarch64":
        binary_file_name = const.binary_linux_arm64
    if os_type == "Linux" and arch_type == "x86_64":
        binary_file_name = const.binary_linux_x64
    if os_type == "Darwin" and arch_type == "arm64":
        binary_file_name = const.binary_darwin_arm64
    if os_type == "Darwin" and arch_type == "x86_64":
        binary_file_name = const.binary_darwin_x64
    if os_type == "Windows" and arch_type == "AMD64":
        binary_file_name = const.binary_win_x64
    return json_data[binary_file_name], binary_file_name


def get_binary_path(download_name):
    return os.path.join(const.download_binary_path, download_name)


def ckg_start(download_name, local_embedding, need_clear_storage_path=True):
    binary_path = get_binary_path(download_name)
    os.chmod(binary_path, 0o777)
    print('clear storage_path')
    if need_clear_storage_path and os.path.exists(const.storage_path):
        shutil.rmtree(const.storage_path)
        os.makedirs(const.storage_path)
    args = [
        '--port', str(const.port),
        '--storage_path', const.storage_path,
        '--region', 'cn',
        '--extension_version', '2.2.119',
        '--source_product', 'codeverse',
        f'--local_embedding={local_embedding}',
        '--embedding_storage_type=sqlite_vec',
        '--host', 'https://copilot.byted.org',
        '--app_id', 'b070098d-978d-4274-ae6b-b76da2570112'
    ]
    subprocess.Popen([binary_path] + args)
    print(f"ckg start success")
    return True


def remove_readonly(func, path, excinfo):
    os.chmod(path, stat.S_IWRITE)
    func(path)


def release_ckg():
    time.sleep(3)
    try:
        if not kill_process_by_port(const.port):
            print('kill ckg failed')
        if os.path.exists(const.repo_path1):
            shutil.rmtree(const.repo_path1, onerror=remove_readonly)
        if os.path.exists(const.repo_path2):
            shutil.rmtree(const.repo_path2, onerror=remove_readonly)
        if os.path.exists(const.storage_path):
            shutil.rmtree(const.storage_path, onerror=remove_readonly)
        if os.path.exists(const.download_binary_path):
            shutil.rmtree(const.download_binary_path, onerror=remove_readonly)
        print('release ckg success')
        return True
    except Exception as e:
        print(f"release ckg error: {str(e)}")
        return False

def get_abs_path(root_path,relative_path):
    return os.path.join(root_path, relative_path)

def get_ckg_version():
    target_code_path = os.environ.get('TARGETCODEPATH')
    if not target_code_path:
        raise ValueError("TARGETCODEPATH environment variable is not set")
    abs_path = get_abs_path(target_code_path, const.relative_path)
    print(f"abs_path: {abs_path}")
    with open(abs_path) as f:
        package_data = json.load(f)
        ckg_version = package_data['version']
        return ckg_version