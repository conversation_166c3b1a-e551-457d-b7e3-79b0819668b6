package tasks

import (
	"ide/ckg/codekg/util"
	"math/rand"
	"strconv"
	"sync"
	"testing"

	"github.com/stretchr/testify/require"
)

type mockTask struct {
	name string
}

func (m *mockTask) do() {}

func (m *mockTask) GetFilePath() string {
	return ""
}

func (m *mockTask) GetProject() string {
	return ""
}

func (m *mockTask) IsRemoved() {

}

func (m *mockTask) ID() string                { return "" }
func (m *mockTask) GetChunkingMethod() string { return "" }
func (m *mockTask) GetEmbeddingModel() string { return "" }

func TestTaskQueue(t *testing.T) {
	ctx := util.NewBackgroundContext("")
	queue := NewTaskQueue()

	queue.Push(ctx, &mockTask{name: "3"}, 3)
	queue.Push(ctx, &mockTask{name: "2"}, 2)
	queue.Push(ctx, &mockTask{name: "5"}, 5)
	queue.Push(ctx, &mockTask{name: "1"}, 1)
	queue.Push(ctx, &mockTask{name: "4"}, 4)

	require.Equal(t, "5", queue.Pop().(*mockTask).name)
	require.Equal(t, "4", queue.Pop().(*mockTask).name)
	require.Equal(t, "3", queue.Pop().(*mockTask).name)
	require.Equal(t, "2", queue.Pop().(*mockTask).name)
	require.Equal(t, "1", queue.Pop().(*mockTask).name)
}

func TestTaskQueueParallelRandom(t *testing.T) {
	ctx := util.NewBackgroundContext("")
	queue := NewTaskQueue()

	num := 5
	wg := sync.WaitGroup{}
	for i := 0; i < num; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for j := 0; j < 100; j++ {
				priority := rand.Intn(10000)
				queue.Push(ctx, &mockTask{
					strconv.Itoa(priority),
				}, priority)
			}
		}()
	}
	wg.Wait()

	prev := queue.Pop().(*mockTask)
	for queue.Len() != 0 {
		curr := queue.Pop().(*mockTask)

		prevP, _ := strconv.Atoi(prev.name)
		currP, _ := strconv.Atoi(curr.name)
		require.True(t, prevP >= currP)

		prev = curr
	}
}
