package data_manager

import (
	"context"
	"errors"
	"time"

	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/relation_manager"
	"ide/ckg/codekg/components/validator"

	"ide/ckg/codekg/components/logs"
)

func (m *manager) InitRelationManager(ctx context.Context, cli knowledgebase.Client, project string, userID string) error {
	// 命中逻辑和 isCKGEnabledForNonWorkspaceScenario 一致
	var userInteractionVersion string
	userInteractionVersion = ""

	// 如果命中 AB 实验，使用 libra 上的字段
	userInteractionVersion, ok := m.config.GetABConfig(ctx, cli, userID).GetUserInteractionVersion()
	if !ok {
		// 否则，看 FeatureGate 是否命中灰度
		isFeatureEnabled := m.config.IsFeatureEnabled(ctx, userID, ckg_config.EnableInteractionGraph)
		if isFeatureEnabled {
			userInteractionVersion = "default"
		} else {
			userInteractionVersion = ""
		}
	}

	r := m.GetRelationManager(ctx, project, userID, userInteractionVersion)
	if r == nil {
		return errors.New("relation manager init failed, manager version: " + userInteractionVersion)
	}

	return nil
}

func (m *manager) GetRelationManager(ctx context.Context, project string, userID string, version string) relation_manager.RelationManager {
	m.relationMu.Lock()
	defer m.relationMu.Unlock()

	if version == "" {
		return nil
	}

	tempManager, ok := m.relations[project]
	if !ok || tempManager == nil {
		m.relations[project] = relation_manager.NewRelationManager(
			project,
			userID,
			m.config,
			func() data_storage.Storage {
				storage, err := m.GetEntityStorage(context.Background(), project)
				if err != nil {
					return nil
				}
				return storage
			},
			func() data_storage.EmbeddingStorage {
				storage, err := m.GetLocalEmbeddingStorage(context.Background(), project)
				if err != nil {
					return nil
				}
				return storage
			},
			version,
		)
	}

	return m.relations[project]
}

func (m *manager) CursorMove(ctx context.Context, project, filePath string, userID string, line int32, version string) error {
	// Debounce: 延迟 500ms 再处理
	time.Sleep(500 * time.Millisecond)

	valid, err := validator.IsValidForCursorMove(ctx, filePath, 0, m.ignoreService, m.fs) // TODO: 在IgnoreServiceV2中实现对等逻辑
	if err != nil || !valid {
		logs.CtxTrace(ctx, "file %s is not valid, ignore file select event, err: %v", filePath, err)
		return nil
	}

	relationManager := m.GetRelationManager(ctx, project, userID, version)
	if relationManager == nil {
		logs.CtxError(ctx, "relationManager is nil, ignore file select event")
		return nil
	}

	err = relationManager.HandleCursorMove(ctx, filePath, line)
	if err != nil {
		logs.CtxInfo(ctx, "handle cursor move err: %v", err)
		return err
	}

	return nil
}
