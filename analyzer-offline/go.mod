module byted-ckg/analyzer-offline

go 1.21

require (
	code.byted.org/gopkg/logs/v2 v2.1.57
	go.lsp.dev/protocol v0.12.0
)

require (
	code.byted.org/aiops/apm_vendor_byted v0.0.22 // indirect
	code.byted.org/aiops/metrics_codec v0.0.18 // indirect
	code.byted.org/aiops/monitoring-common-go v0.0.3 // indirect
	code.byted.org/gopkg/apm_vendor_interface v0.0.2 // indirect
	code.byted.org/gopkg/env v1.6.18 // indirect
	code.byted.org/gopkg/metrics v1.4.25 // indirect
	code.byted.org/gopkg/metrics/v3 v3.1.31 // indirect
	code.byted.org/gopkg/metrics_core v0.0.26 // indirect
	code.byted.org/gopkg/net2 v1.5.0 // indirect
	code.byted.org/log_market/gosdk v0.0.0-20220328031951-809cbf0ba485 // indirect
	code.byted.org/log_market/ttlogagent_gosdk v0.0.6 // indirect
	code.byted.org/log_market/ttlogagent_gosdk/v4 v4.0.51 // indirect
	code.byted.org/middleware/gocaller v0.0.6 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bytedance/gopkg v0.0.0-20210716082555-acbf5a2aa7e2 // indirect
	github.com/caarlos0/env/v6 v6.2.2 // indirect
	github.com/go-kit/log v0.2.1 // indirect
	github.com/go-logfmt/logfmt v0.5.1 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/google/uuid v1.3.0 // indirect
	github.com/klauspost/compress v1.15.9 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/segmentio/asm v1.1.3 // indirect
	github.com/segmentio/encoding v0.3.4 // indirect
	go.lsp.dev/jsonrpc2 v0.10.0 // indirect
	go.lsp.dev/pkg v0.0.0-20210717090340-384b27a52fb2 // indirect
	go.lsp.dev/uri v0.3.0 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/multierr v1.8.0 // indirect
	go.uber.org/zap v1.21.0 // indirect
	golang.org/x/sys v0.0.0-20220319134239-a9b59b0215f8 // indirect
	golang.org/x/time v0.1.0 // indirect
)
