package ckg_metrics

const (
	EventNameCKGBoot           = "ckg_boot"
	EventNameCKGInit           = "ckg_init"
	EventNameCKGInitReq        = "ckg_init_req"
	EventNameCKGRecall         = "ckg_recall"
	EventNameCKGRelevantRecall = "ckg_relevant_recall"
	EVentNameCKGRecallReq      = "ckg_recall_req"
	EventNameCKGSync           = "ckg_sync"
	EventNameCkgRequest        = "ckg_request_client"
	EventNameCKGIndex          = "ckg_index"
	EventNameCKGLocalIndex     = "ckg_local_index"
	EventNameCKGBatchSplit     = "ckg_batch_split"
	EventNameCKGBatchEmbedding = "ckg_batch_embedding"
	EventNameCKGProcess        = "ckg_process"
	EventNameCKGRelation       = "ckg_retrieve_relation"
	EventNameCKGExit           = "ckg_exit"
	EventNameCKGUserTrace      = "ckg_user_trace"
	EventNameCKGInteraction    = "ckg_user_interaction"
	EventNameCKGGitGraph       = "ckg_git_graph"
	EventNameCKGRecover        = "ckg_recover"
	TeaParamCpuCoreNum         = "cpu_core_num"
	TeaParamMemorySize         = "memory_size"
	TeaParamCpuUsePercent      = "cpu_use_percent"
	TeaParamMemoryUsePercent   = "memory_use_percent"
	TeaParamMemoryUse          = "memory_use"
	TeaParamCpyUseCore         = "cpu_use_core"

	// Tea 通用属性
	TeaParamEventID          = "event_id"
	TeaParamParentEventID    = "parent_event_id"
	TeaParamStartTime        = "start_time"
	TeaParamProcessID        = "ckg_process_id"
	TeaParamSourceProduct    = "source_product"
	TeaParamOsType           = "os_type"
	TeaParamAIBackendHost    = "ai_backend_host"
	TeaParamExtensionVersion = "extension_version"
	TeaParamIDEVersion       = "ide_version"
	TeaParamRegion           = "region"
	TeaParamCKGVersion       = "ckg_version"
	TeaParamCKGUserID        = "ckg_user_id"
	TeaParamDuration         = "duration"

	// 多个事件都会用到的属性
	TeaParamIsFailed = "failed"
	TeaParamErrStep  = "error_step"
	TeaCKGCrash      = "ckg_crash"

	// ckg_boot
	TeaParamEnvStorageCreateTime   = "ckg_env_storage_create_time"
	TeaParamParfaitCreateTime      = "ckg_parfait_create_time"
	TeaParamCodeKGServerCreateTime = "ckg_server_create_time"
	TeaParamProcessVersion         = "ckg_process_version"
	TeaParamProcessRecoverTime     = "ckg_process_recover_time"

	// ckg_exit
	TeaParamExitPPID = "ckg_process_exit_ppid"

	// ckg_request_client
	TeaParamApi                 = "api"
	TeaParamHttpCode            = "http_code"
	TeaParamLogID               = "log_id"
	TeaParamLossSend            = "loss_send"
	TeaParamLossRecv            = "loss_recv"
	TeaParamServerTime          = "server_time"
	TeaParamIndexCaller         = "index_caller"
	TeaParamRequestServerApiErr = "request_server_api_err"

	// ckg_init_req
	TeaParamInitProjectNum      = "init_project_num"
	TeaParamInitIgnoreFileLimit = "init_ignore_file_limit"
	// ckg_init
	TeaParamKnowledgebaseID            = "knowledgebase_id"
	TeaParamProject                    = "project"
	TeaParamFileCountLimit             = "file_count_limit"
	TeaParamInitIgnoreServiceCost      = "init_ignore_service_cost"
	TeaParamClearProjectOutdatedNum    = "clear_project_outdated_num"
	TeaParamClearProjectOutdatedTime   = "clear_project_outdated_time"
	TeaParamCollectAllUrisCost         = "collect_all_uris_cost"
	TeaParamProjectPathIsVfs           = "project_path_is_vfs"
	TeaParamFileCountBeforeIgnore      = "total_file_count_before_ignore" // collectAllURIs 中上报，ignore 前的文件数量
	TeaParamFileSkipIndex              = "file_skip_index"
	TeaParamTotalFilesCount            = "total_files_count"
	TeaParamStatErrFileNum             = "stat_err_file_num"
	TeaParamRelErrFileNum              = "rel_err_file_num"
	TeaParamCreateInfoErrFileNum       = "create_info_err_file_num"
	TeaParamCreateInfoErrFolderNum     = "create_info_err_folder_num"
	TeaParamComputeHashCost            = "compute_hash_cost"
	TeaParamIndexType                  = "index_type"
	TeaParamFileIndexCount             = "file_index_count"
	TeaParamFolderIndexCount           = "folder_index_count"
	TeaParamFileLineAvg                = "file_line_avg"
	TeaParamFileLineMax                = "file_line_max"
	TeaParamFileLineSum                = "file_line_sum"
	TeaParamFileSizeAvg                = "file_size_avg"
	TeaParamFileSizeMax                = "file_size_max"
	TeaParamUriMetaCount               = "uri_meta_count"
	TeaParamEntityCount                = "entity_count"
	TeaParamRelationCount              = "relation_count"
	TeaParamNoEntityStorageAtEnd       = "no_entity_storage_at_end"
	TeaParamUnknownEntityStorageErr    = "unknown_entity_storage_err"
	TeaParamNoEmbeddingStorageAtEnd    = "no_embedding_storage_at_end"
	TeaParamUnknownEmbeddingStorageErr = "unknown_embedding_storage_err"
	TeaParamChunkingMethod             = "chunking_method"
	TeaParamEmbeddingModel             = "embedding_model"
	// 远端初始化
	TeaParamSkipped                = "skipped"
	TeaParamToIndexFilesCount      = "to_index_files_count"
	TeaParamToIndexFilesCountDelta = "to_index_files_count_delta"
	// 本地初始化
	TeaParamEmbeddingStorageType        = "embedding_storage_type"
	TeaParamToLocalIndexFilesCount      = "to_local_index_files_count"
	TeaParamToLocalIndexFilesCountDelta = "to_local_index_files_count_delta"

	// ckg_index 远端索引
	TeaParamIndexFailed                       = "index_failed"
	TeaParamRemoteIndexTokenExpired           = "remote_index_token_expired"
	TeaParamRemoteIndexFileNotExisted         = "remote_index_file_not_exist"
	TeaParamIndexSkipped                      = "index_skipped"
	TeaParamIndexRequestSent                  = "index_request_sent"
	TeaParamIndexRequestFailed                = "index_request_failed"
	TeaParamRemoteIndexCurrFileNum            = "remote_index_curr_file_num"
	TeaParamRemoteIndexRequestUnexpected      = "remote_index_request_unexpected"
	TeaParamRemoteIndexEmptyUriData           = "remote_index_empty_uri_data"
	TeaParamRemoteIndexEntityStorageDeleted   = "remote_index_entity_storage_deleted"
	TeaParamRemoteIndexEntityStorageNotOnDisk = "remote_index_entity_storage_not_on_disk"
	TeaParamRemoteIndexUriDataInsertFailed    = "remote_index_uri_data_insert_failed"

	// ckg_local_index 本地索引
	TeaParamLocalIndexIgnore                    = "local_index_ignore"
	TeaParamLocalIndexTokenExpired              = "local_index_token_expired"
	TeaParamLocalIndexFileNotExisted            = "local_index_file_not_exist"
	TeaParamLocalIndexSkipped                   = "local_index_skipped"
	TeaParamLocalIndexSplitSent                 = "local_index_split_sent"
	TeaParamLocalIndexSplitFailed               = "local_index_split_failed"
	TeaParamLocalIndexEmbedSent                 = "local_index_embed_sent"
	TeaParamLocalIndexEmbedFailed               = "local_index_embed_failed"
	TeaParamEmbeddingLenNotEq                   = "embedding_len_not_eq"
	TeaParamEmbeddingFileTotal                  = "embedding_file_total"
	TeaParamEmbeddingContentTotal               = "embedding_content_total"
	TeaParamEmbeddingAllCacheHit                = "embedding_all_cache_hit"
	TeaParamEmbeddingMemCacheHit                = "embedding_mem_cache_hit"
	TeaParamEmbeddingAbaseHit                   = "embedding_abase_hit"
	TeaParamEmbeddingStillNotAll                = "embedding_still_not_all"
	TeaParamLocalIndexEmptyUriData              = "local_index_empty_uri_data"
	TeaParamLocalIndexEntityStorageDeleted      = "local_index_entity_storage_deleted"
	TeaParamLocalIndexEmbeddingStorageDeleted   = "local_index_embedding_storage_deleted"
	TeaParamLocalIndexEntityStorageNotOnDisk    = "local_index_entity_storage_not_on_disk"
	TeaParamLocalIndexEmbeddingStorageNotOnDisk = "local_index_embedding_storage_not_on_disk"
	TeaParamLocalIndexUriDataInsertFailed       = "local_index_uri_data_insert_failed"
	TeaParamLocalIndexUpdateStatusFailed        = "local_index_update_embedding_status_failed"
	TeaParamLocalIndexEmbeddingInsertFailed     = "local_index_embedding_insert_failed"
	TeaParamUseSplitFilesAPIVersion             = "use_split_files_api_version"
	TeaParamUseEmbeddingAPIVersion              = "use_embedding_api_version"

	// ckg_batch_embedding
	TeaParamBatchEmbeddingEmptyEmbeddingData = "batch_embedding_empty_embedding_data"
	TeaParamBatchEmbeddingBatchSize          = "batch_embedding_batch_size"

	// ckg_recall_req
	TeaParamRecallReqProjectNum          = "recall_req_project_num"
	TeaParamRecallReqFolderNum           = "recall_req_folder_num"
	TeaParamRecallReqEntityNum           = "recall_req_entity_num"
	TeaParamRecallReqFinalEntityNum      = "recall_req_final_entity_num"
	TeaParamRecallReqNoHashFolderProject = "recall_req_no_hash_folder_project"
	// rerank
	TeaParamRecallReqEnableRerank       = "recall_req_enable_rerank"
	TeaParamRecallReqRerankCandidateNum = "recall_req_rerank_candidate_num"
	TeaParamRecallReqRerankFailed       = "recall_req_rerank_failed"
	TeaParamRecallReqRerankErrCode      = "recall_req_rerank_err_code"
	TeaParamRecallReqRerankEmpty        = "recall_req_rerank_empty"
	TeaParamRecallReqRerankCost         = "recall_req_rerank_cost"

	// ckg relevance recall
	TeaParamRelevantRecallEvent = "relevance_recall_event"

	// ckg_recall
	TeaParamRecallEvent             = "recall_event" // 用这个表示 workspace 和 folder 吧
	TeaParamEmbeddingRecallType     = "embedding_recall_type"
	TeaParamNoEntityStorage         = "no_entity_storage"
	TeaParamNoLocalEmbeddingStorage = "no_local_embedding_storage"
	// remote embedding recall
	TeaParamEmbeddingRecallNum                       = "embedding_recall_num"
	TeaParamEmbeddingRecallCost                      = "embedding_recall_cost"
	TeaParamEmbeddingRecallQueryErr                  = "embedding_query_err"
	TeaParamEmbeddingRecallErrCode                   = "embedding_recall_err_code"
	TeaParamEmbeddingRecallQuerySegEmpty             = "embedding_query_seg_empty"
	TeaParamEmbeddingRecallQuerySearchResultEmpty    = "embedding_query_search_result_empty"
	TeaParamEmbeddingRecallSelectedErr               = "embedding_selected_err"
	TeaParamEmbeddingRecallSelectedSegEmpty          = "embedding_selected_seg_empty"
	TeaParamEmbeddingRecallSelectedSearchResultEmpty = "embedding_selected_search_result_empty"
	TeaParamEmbeddingEmptySearchResult               = "embedding_empty_search_result"
	// remote embedding (segment -> entity)
	TeaParamQueryFolderErrNum       = "embedding_query_folder_err_num"
	TeaParamSelectedFolderErrNum    = "embedding_selected_folder_err_num"
	TeaParamQueryCodeChunkErrNum    = "embedding_query_code_chunk_err_num"
	TeaParamSelectedCodeChunkErrNum = "embedding_selected_code_chunk_err_num"
	TeaParamQueryTextErrNum         = "embedding_query_text_err_num"
	TeaParamSelectedTextErrNum      = "embedding_selected_text_err_num"
	TeaParamQueryFileErrNum         = "embedding_query_file_err_num"
	TeaParamSelectedFileErrNum      = "embedding_selected_file_err_num"
	TeaParamQueryClassErrNum        = "embedding_query_class_err_num"
	TeaParamSelectedClassErrNum     = "embedding_selected_class_err_num"
	TeaParamQueryMethodErrNum       = "embedding_query_method_err_num"
	TeaParamSelectedMethodErrNum    = "embedding_selected_method_err_num"
	// local embedding recall
	TeaParamLocalEmbeddingRecallNum     = "local_embedding_recall_num"
	TeaParamLocalEmbeddingRecallCost    = "local_embedding_recall_cost"
	TeaParamLocalEmbeddingRecallErrCode = "local_embedding_recall_err_code"
	// local embedding recall internal
	TeaParamIndexProgressWhenRecall         = "index_progress_when_recall"
	TeaParamLocalEmbeddingRecallTag         = "local_embedding_recall_tag"
	TeaParamLocalEmbeddingStorageType       = "local_embedding_storage_type"
	TeaParamNotExistedFolderNum             = "recall_hash_not_existed_folder_num"
	TeaParamRelErrFolderNum                 = "recall_hash_rel_err_folder_num"
	TeaParamLocalEmbeddingWorkspaceErr      = "local_embedding_workspace_err"
	TeaParamLocalEmbeddingWorkspaceEmpty    = "local_embedding_workspace_empty"
	TeaParamLocalEmbeddingFolderErr         = "local_embedding_folder_err"
	TeaParamLocalEmbeddingFolderEmpty       = "local_embedding_folder_empty"
	TeaParamLocalEmbeddingEmptySearchResult = "local_embedding_empty_search_result"
	// local embedding performance
	TeaParamLocalRecallEmbeddingCost          = "local_recall_embedding_cost"
	TeaParamLocalRecallWorkspaceVecSearchCost = "local_recall_workspace_vec_search_cost"
	TeaParamLocalRecallFolderVecSearchCost    = "local_recall_folder_vec_search_cost"
	// alias recall
	TeaParamAliasRecallNum  = "alias_recall_num"
	TeaParamAliasRecallCost = "alias_recall_cost"
	// ner recall
	TeaParamNERRecallNum  = "ner_recall_num"
	TeaParamNERRecallCost = "ner_recall_cost"

	// ckg_sync
	TeaParamSyncTag                 = "sync_tag"
	TeaParamSyncInputNum            = "sync_input_num"
	TeaParamSyncDocNum              = "sync_doc_num"
	TeaParamSyncInputNumAfterIgnore = "sync_input_num_after_ignore"
	TeaParamSyncDocNumAfterIgnore   = "sync_doc_num_after_ignore"

	// retrieve relavnt snippet
	TeaParamEmptyRelevantSnippets                      = "relevant_snippets_result_empty"
	TeaParamRelevantSnippetsEmptyStorage               = "relevant_snippets_empty_storage"
	TeaParamRelevantSnippetsQueryProcessFailed         = "relevant_snippets_query_process_failed"
	TeaParamRelevantSnippetsRetrieveFailed             = "relevant_snippets_retrieve_failed"
	TeaParamRelevantSnippetsWorkspaceSnippetsNum       = "relevant_snippets_workspace_snippets_num"
	TeaParamRelevantSnippetsFolderSnippetsNum          = "relevant_snippets_folder_snippets_num"
	TeaParamRelevantSnippetsFileSnippetsNum            = "relevant_snippets_file_snippets_num"
	TeaParamRelevantSnippetsCodeSnippetsNum            = "relevant_snippets_code_snippets_num"
	TeaParamRelevantSnippetsCurrentEditorSnippetsNum   = "relevant_snippets_current_editor_snippets_num"
	TeaParamRelevantSnippetsUserInteractionSnippetsNum = "relevant_snippets_user_interaction_snippets_num"

	// user interaction contexts
	TeaParamsUserInteractionRetrievalVersion = "user_interaction_version"
	TeaParamUserInteractionHasCurrentEditor  = "user_interaction_has_current_editor"
	TeaParamUserInteractionHasGitRepo        = "user_interaction_has_git_repo"
	TeaParamUserInteractionGitRepoNum        = "user_interaction_recall_num"
	TeaParamUserInteractionRetrievalTime     = "user_interaction_retrieval_time"

	// git graph
	TeaParamGitGraphBuildSuccess      = "git_graph_build_success"
	TeaParamGitGraphCommitCount       = "git_graph_repo_commit_count"
	TeaParamGitGraphIgnoreCommitCount = "git_graph_ignore_commit_count"
	TeaParamGitGraphAnalysisTime      = "git_graph_analysis_time"
	TeaParamGitGraphNodeCount         = "git_graph_node_count"
	TeaParamGitGraphEdgeCount         = "git_graph_edge_count"

	// 本地召回为空 - 归因
	TeaParamShouldRemoteButLocal            = "embedding_recall_should_be_remote"
	TeaParamHashFolderButDBUnsupported      = "hash_folder_but_db_unsupported"
	TeaParamHashFolderButAllFolderEmpty     = "hash_folder_but_all_folder_empty"
	TeaParamEmptyRecallWithEmptyVecDB       = "local_embedding_recall_with_empty_embedding_db"
	TeaParamEmptyRecallSinceAllFolderEntity = "embedding_recall_but_all_folder_entity"
	TeaParamEmptyRecallNonExistedEntityDB   = "embedding_recall_non_existed_entity_db"
	TeaParamEmptyRecallWithEmptyEntityDB    = "embedding_recall_with_empty_entity_db"
	TeaParamEmptyRecallWithNarrowCondition  = "embedding_recall_with_narrow_condition"
	TeaParamEmptyRecallAllFolderNotExisted  = "local_embedding_with_all_not_existed_folder"
	TeaParamEmptyRecallAllFolderRelErr      = "local_embedding_with_all_rel_err_folder"
	TeaParamEmptyRecallIndexStatus          = "embedding_recall_index_status"
	TeaParamIndexProgressWhenEmptyRecall    = "index_progress_when_empty_recall"
	TeaParamEmptyRecallSinceProjectEmpty    = "embedding_recall_with_empty_project"

	// ckg_retrieve_relation
	TeaParamRetrieveRelationEntityNum = "retrieve_relation_entity_num"
	TeaParamVariableNum               = "variable_num"
	TeaParamReferenceNum              = "reference_num"
	TeaParamGetEditorVar              = "get_editor_var"
	TeaParamCodeChunkVarNum           = "code_chunk_var_num"
	TeaParamTextVarNum                = "text_var_num"
	TeaParamFolderVarNum              = "folder_var_num"
	TeaParamFileVarNum                = "file_var_num"
	TeaParamFileTopLevelVarNum        = "file_top_level_var_num"
	TeaParamClassVarNum               = "class_var_num"
	TeaParamClassTopLevelVarNum       = "class_top_level_var_num"
	TeaParamMethodVarNum              = "method_var_num"
	TeaParamCodeChunkRefNum           = "code_chunk_ref_num"
	TeaParamTextRefNum                = "text_ref_num"
	TeaParamFolderRefNum              = "folder_ref_num"
	TeaParamFileRefNum                = "file_ref_num"
	TeaParamFileTopLevelRefNum        = "file_top_level_ref_num"
	TeaParamClassRefNum               = "class_ref_num"
	TeaParamClassTopLevelRefNum       = "class_top_level_ref_num"
	TeaParamMethodRefNum              = "method_ref_num"
	TeaParamTextErrNum                = "text_err_num"
	TeaParamFolderErrNum              = "folder_err_num"
	TeaParamFileErrNum                = "file_err_num"
	TeaParamClassErrNum               = "class_err_num"
	TeaParamMethodErrNum              = "method_err_num"
	TeaParamOriginalVariableNum       = "original_variable_num"
	TeaParamVariableSize              = "variable_size"
	TeaParamVarCuttingNum             = "var_cutting_num"

	// 不用上报于 Tea 的事件
	ParamRerankCandidates                = "rerank_candidates"
	ParamRetrieveEntityRequest           = "retrieve_entity_request"
	ParamQueryRequest                    = "query_request"
	ParamRerankSummary                   = "rerank_summary"
	ParamProcessRecoverProjects          = "ckg_process_params"
	ParamRerankRawResult                 = "rerank_raw_result"
	ParamRecallResultEntities            = "recall_result_entities"
	ParamEmbeddingRecallParams           = "embedding_recall_params"
	ParamEmbeddingRecallRawEntities      = "embedding_recall_final_result_entities"
	ParamLocalEmbeddingRecallParams      = "local_embedding_recall_params"
	ParamLocalEmbeddingRecallRawEntities = "local_embedding_recall_final_result_entities"
	ParamAliasRecallResultEntities       = "alias_recall_result_entities"
	ParamNERRecallRawResult              = "ner_recall_raw_result"
	ParamNERRecallNormalResultEntities   = "ner_recall_normal_result_entities"
	TeaParamNERRecallErrCode             = "ner_recall_err_code"
	ParamNERRecallCurrentResultEntities  = "ner_recall_current_result_entities"
	ParamUserTraceData                   = "user_trace_data"
)
