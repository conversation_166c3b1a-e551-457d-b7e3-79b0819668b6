import type {Tree, Input, QueryCapture, SyntaxNode, Point, QueryMatch} from "web-tree-sitter";
import {ParserTimeout, TreeSitterParser} from "../../core/parse/treesitter/parser";

export function parse(languageId: string, text: string | Input, oldTree?: Tree, timeout: ParserTimeout = ParserTimeout.Default_5000_Ms): Tree | undefined {
  return TreeSitterParser.INSTANCE.parse(languageId, text, oldTree, timeout);
}

export function queryAndMatchForText(languageId: string, queryStr: string, targetText: string, timeout: ParserTimeout = ParserTimeout.Default_5000_Ms): QueryMatch[] {
  return TreeSitterParser.INSTANCE.queryAndMatchForText(languageId, queryStr, targetText, timeout);
}

export function queryAndMatchForNode(languageId: string, queryStr: string, targetNode: SyntaxNode, startPosition?: Point, endPosition?: Point): QueryMatch[] {
  return TreeSitterParser.INSTANCE.queryAndMatchForNode(languageId, queryStr, targetNode, startPosition, endPosition);
}

export function queryAndCaptureForText(languageId: string, queryStr: string, targetText: string, timeout: ParserTimeout = ParserTimeout.Default_5000_Ms): QueryCapture[] {
  return TreeSitterParser.INSTANCE.queryAndCaptureForText(languageId, queryStr, targetText, timeout);
}

export function queryAndCaptureForNode(languageId: string, queryStr: string, targetNode: SyntaxNode, startPosition?: Point, endPosition?: Point): QueryCapture[] {
  return TreeSitterParser.INSTANCE.queryAndCaptureForNode(languageId, queryStr, targetNode, startPosition, endPosition);
}