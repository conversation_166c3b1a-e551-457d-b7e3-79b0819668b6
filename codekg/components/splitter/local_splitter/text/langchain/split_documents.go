package langchain

import (
	"context"
	"errors"
	"fmt"
	"log"
	"strings"

	"ide/ckg/codekg/components/knowledgebase"
	lsmodel "ide/ckg/codekg/components/splitter/local_splitter/model"
	"ide/ckg/codekg/components/splitter/local_splitter/util"
	"ide/ckg/codekg/model"
)

// ErrMismatchMetadatasAndText is returned when the number of texts and metadatas
// given to CreateDocuments does not match. The function will not error if the
// length of the metadatas slice is zero.
var ErrMismatchMetadatasAndText = errors.New("number of texts and metadatas does not match")

// BaseAdapter is a base implementation of the LocalSplitterAdapter interface
type BaseAdapter struct {
	splitter           TextSplitter
	supportedLanguages map[model.LanguageType]bool
}

// GetSplitter returns the underlying TextSplitter
func (a *BaseAdapter) GetSplitter() TextSplitter {
	return a.splitter
}

// GetSupportedLanguages returns the supported languages
func (a *BaseAdapter) GetSupportedLanguages() map[model.LanguageType]bool {
	return a.supportedLanguages
}

// SupportedLanguages returns the supported languages for this splitter
func (a *BaseAdapter) SupportedLanguages() map[model.LanguageType]bool {
	return a.supportedLanguages
}

// SplitFile implements the LocalSplitter interface
func (a *BaseAdapter) SplitFile(ctx context.Context, file *knowledgebase.SplitFile) (*knowledgebase.SplitFileResult_, error) {
	if file == nil {
		return nil, fmt.Errorf("file cannot be nil")
	}

	// 尝试使用带上下文信息的方法
	var textSegments []*knowledgebase.TextSegment

	// 尝试使用SplitText方法
	textSplitter, ok := a.splitter.(TextSplitter)
	if ok {
		// 使用SplitText方法，现在它直接返回带行号信息的TextChunk
		chunks, err := textSplitter.SplitText(file.Content)
		if err != nil {
			return nil, fmt.Errorf("failed to split text: %w", err)
		}

		// 使用带行号信息的结果
		textSegments = make([]*knowledgebase.TextSegment, 0, len(chunks))
		for _, chunk := range chunks {
			// 为每个chunk添加标题
			content := chunk.Content
			if !strings.HasPrefix(content, file.Title) {
				content = fmt.Sprintf("%s\n%s", file.Title, content)
			}

			// 创建TextSegment
			textSegment := &knowledgebase.TextSegment{
				Content:   content,
				StartLine: chunk.StartLine,
				EndLine:   chunk.EndLine,
			}

			textSegments = append(textSegments, textSegment)
		}
	} else {
		// 如果不是TextSplitter接口，使用旧的SplitText方法
		// 这里需要修改lsmodel.LocalSplitter接口的SplitFile方法实现
		// 暂时使用直接调用SplitFile方法
		result, err := a.splitter.(lsmodel.LocalSplitter).SplitFile(ctx, file)
		if err != nil {
			return nil, fmt.Errorf("failed to split file: %w", err)
		}

		return result, nil
	}

	// 生成ID
	generator := util.NewSegmentIDGenerator(file.Path)
	for _, segment := range textSegments {
		segment.ID = generator.GenerateTextSegmentID(segment)
	}

	// 转换结果
	segmentsWithEmbedding := util.ConvertTextSegmentsToSegments(textSegments)

	return &knowledgebase.SplitFileResult_{
		SegmentsWithEmbedding: segmentsWithEmbedding,
	}, nil
}

// NewAdapter creates a new BaseAdapter
func NewAdapter(splitter TextSplitter, supportedLanguages map[model.LanguageType]bool) *BaseAdapter {
	return &BaseAdapter{
		splitter:           splitter,
		supportedLanguages: supportedLanguages,
	}
}

// SplitDocuments splits documents using a textsplitter.
func SplitDocuments(textSplitter TextSplitter, documents []Document) ([]Document, error) {
	texts := make([]string, 0)
	metadatas := make([]map[string]interface{}, 0)
	for _, document := range documents {
		texts = append(texts, document.Content)
		metadatas = append(metadatas, document.Metadata)
	}

	return CreateDocuments(textSplitter, texts, metadatas)
}

// CreateDocuments creates documents from texts and metadatas with a text splitter. If
// the length of the metadatas is zero, the result documents will contain no metadata.
// Otherwise, the numbers of texts and metadatas must match.
func CreateDocuments(textSplitter TextSplitter, texts []string, metadatas []map[string]interface{}) ([]Document, error) {
	if len(metadatas) == 0 {
		metadatas = make([]map[string]interface{}, len(texts))
	}

	if len(texts) != len(metadatas) {
		return nil, ErrMismatchMetadatasAndText
	}

	documents := make([]Document, 0)

	for i := 0; i < len(texts); i++ {
		chunks, err := textSplitter.SplitText(texts[i])
		if err != nil {
			return nil, err
		}

		for _, chunk := range chunks {
			// Copy the document metadata
			curMetadata := make(map[string]interface{}, len(metadatas[i]))
			for key, value := range metadatas[i] {
				curMetadata[key] = value
			}

			documents = append(documents, Document{
				Content:  chunk.Content,
				Metadata: curMetadata,
			})
		}
	}

	return documents, nil
}

// joinDocs comines two documents with the separator used to split them.
func joinDocs(docs []string, separator string) string {
	return strings.TrimSpace(strings.Join(docs, separator))
}

// mergeSplits merges smaller splits into splits that are closer to the chunkSize.
func mergeSplits(splits []string, separator string, chunkSize int, chunkOverlap int, lenFunc func(string) int) []string { //nolint:cyclop
	docs := make([]string, 0)
	currentDoc := make([]string, 0)
	total := 0

	for _, split := range splits {
		totalWithSplit := total + lenFunc(split)
		if len(currentDoc) != 0 {
			totalWithSplit += lenFunc(separator)
		}

		maybePrintWarning(total, chunkSize)
		if totalWithSplit > chunkSize && len(currentDoc) > 0 {
			doc := joinDocs(currentDoc, separator)
			if doc != "" {
				docs = append(docs, doc)
			}

			for shouldPop(chunkOverlap, chunkSize, total, lenFunc(split), lenFunc(separator), len(currentDoc)) {
				total -= lenFunc(currentDoc[0]) //nolint:gosec
				if len(currentDoc) > 1 {
					total -= lenFunc(separator)
				}
				currentDoc = currentDoc[1:] //nolint:gosec
			}
		}

		currentDoc = append(currentDoc, split)
		total += lenFunc(split)
		if len(currentDoc) > 1 {
			total += lenFunc(separator)
		}
	}

	doc := joinDocs(currentDoc, separator)
	if doc != "" {
		docs = append(docs, doc)
	}

	return docs
}

func maybePrintWarning(total, chunkSize int) {
	if total > chunkSize {
		log.Printf(
			"[WARN] created a chunk with size of %v, which is longer then the specified %v\n",
			total,
			chunkSize,
		)
	}
}

// Keep popping if:
//   - the chunk is larger than the chunk overlap
//   - or if there are any chunks and the length is long
func shouldPop(chunkOverlap, chunkSize, total, splitLen, separatorLen, currentDocLen int) bool {
	docsNeededToAddSep := 2
	if currentDocLen < docsNeededToAddSep {
		separatorLen = 0
	}

	return currentDocLen > 0 && (total > chunkOverlap || (total+splitLen+separatorLen > chunkSize && total > 0))
}
