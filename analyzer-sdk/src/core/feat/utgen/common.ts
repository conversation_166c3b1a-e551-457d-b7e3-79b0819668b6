import { LanguageIdEnum } from "../../parse/share";
import { doExtractUnitTestContextForCpp } from "./cpp";
import { doExtractUnitTestContextForJS } from "./javascript";
import { doExtractUnitTestContextForPython } from "./python";
import { TextDocument } from "vscode-languageserver-textdocument";
import { Logger } from "../../../utils/logger";
import { getConfig } from "../../common/config";
import { withTimeout } from "../../../utils/common";

export async function doExtractUnitTestContext(
  document: TextDocument,
  startIndex: number,
  endIndex: number,
): Promise<string> {
  const adjustedIndex = adjustIndex(document, startIndex, endIndex);

  let result = "{}";
  switch (document.languageId) {
    case LanguageIdEnum.Cpp:
      result = await doExtractUnitTestContextForCpp(
        document,
        adjustedIndex.startIndex,
        adjustedIndex.endIndex,
      );
      break;
    case LanguageIdEnum.Javascript:
    case LanguageIdEnum.Jsx:
    case LanguageIdEnum.JavascriptReact:
      result = await doExtractUnitTestContextForJS(
        document,
        adjustedIndex.startIndex,
        adjustedIndex.endIndex,
      );
      break;
    case LanguageIdEnum.Python:
      try {
        result = await withTimeout<string>(
          doExtractUnitTestContextForPython(
            document,
            adjustedIndex.startIndex,
            adjustedIndex.endIndex,
          ),
          getConfig(`analysis.utgen.python.timeoutMs`),
          false,
        );
      } catch (error) {
        Logger.warn(`doExtractUnitTestContext: ${error}`);
      }
      break;
  }

  Logger.info(`doExtractUnitTestContext: result is '${result}'`);
  return result;
}

export function adjustIndex(
  textDocument: TextDocument,
  startIndex: number,
  endIndex: number,
): { startIndex: number; endIndex: number } {
  while ([" ", "\n", "\t"].includes(textDocument.getText()[startIndex]) && startIndex < endIndex) {
    startIndex++;
  }

  while (
    [" ", "\n", "\t"].includes(textDocument.getText()[endIndex - 1]) &&
    startIndex < endIndex
  ) {
    endIndex--;
  }

  return { startIndex, endIndex };
}
