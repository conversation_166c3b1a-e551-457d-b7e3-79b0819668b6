package vec

import "C"
import (
	"bytes"
	"encoding/binary"
)

// #cgo linux CFLAGS: -DSQLITE_CORE
// #cgo linux LDFLAGS: -lm
// #cgo windows CFLAGS: -DSQLITE_CORE
// #cgo windows LDFLAGS: -lm
// #include "sqlite-vec.h"
//
import "C"

// Once called, every future new SQLite3 connection created in this process
// will have the sqlite-vec extension loaded. It will persist until [Cancel] is
// called.
//
// Calls [sqlite3_auto_extension()] under the hood.
//
// [sqlite3_auto_extension()]: https://www.sqlite.org/c3ref/auto_extension.html
func Auto() {
	C.sqlite3_auto_extension((*[0]byte)((C.sqlite3_vec_init)))
}

// "Cancels" any previous calls to [Auto]. Any new SQLite3 connections created
// will not have the sqlite-vec extension loaded.
//
// Calls sqlite3_cancel_auto_extension() under the hood.
func Cancel() {
	C.sqlite3_cancel_auto_extension((*[0]byte)(C.sqlite3_vec_init))
}

// Serializes a float32 list into a vector BLOB that sqlite-vec accepts.
func SerializeFloat32(vector []float32) ([]byte, error) {
	buf := new(bytes.Buffer)
	err := binary.Write(buf, binary.LittleEndian, vector)
	if err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

// DeserializeFloat32 将 []byte 反序列化为 float32 切片
func DeserializeFloat32(data []byte) ([]float32, error) {
	buf := bytes.NewReader(data)
	vector := make([]float32, len(data)/4) // 一个 float32 占 4 个字节
	err := binary.Read(buf, binary.LittleEndian, &vector)
	if err != nil {
		return nil, err
	}
	return vector, nil
}
