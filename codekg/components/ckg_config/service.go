package ckg_config

import (
	"context"
	"encoding/json"
	"fmt"
	"ide/ckg/codekg/components/data_storage/consts"
	"ide/ckg/codekg/components/env"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/components/version_feature"
	"ide/ckg/codekg/model"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"

	"github.com/avast/retry-go"
	"github.com/pkg/errors"
	"golang.org/x/sync/singleflight"

	"github.com/samber/lo"
)

const (
	defaultFileCountLimit           = 10000
	defaultDatastoreName            = "default-latest"
	DefaultFileSizeThreshold        = 1024 * 1024 // 默认处理 1MB 以下的
	DefaultSingleFileLineThreshold  = 5000
	defaultRecallNum                = 25
	defaultForceIndexFileCountLimit = 10000
	defaultIndexFileLineLimit       = 1_500_000 // 1_500_000
	defaultDebounceTime             = 3
	defaultRecentCursorRelationNum  = 5
	DefaultChunkingV1RecallNum      = 25
	DefaultChunkingV2RecallNum      = 100
	defaultTaskSleepFactor          = 1

	// Virtual Project indexing restrictions
	defaultVirtualProjectFileCountLimit         = 1000
	defaultVirtualProjectFileSizeThreshold      = 1024 * 1024 * 10 // 默认处理 10MB 以下的
	defaultVirtualProjectTotalFileSizeLimit     = 1024 * 1024 * 50 // 总文件大小限制 50MB
	defaultVirtualProjectRecallNum              = 25
	defaultVirtualProjectChunkingMethodBaseline = "local.text.langchain.markdown"
	defaultVirtualProjectChunkingSize           = 1500
	defaultVirtualProjectChunkingOverlap        = 100
)

const (
	defaultDatastoreNameBaseline  = "default-latest"
	defaultEmbeddingModelBaseline = "codekg_bge_m3" // 兼容 knowledgebase 现有 embedding model，后续均使用
	defaultChunkingMethodBaseline = "v2"
	ChunkingMethodV1              = "v1"
	ChunkingMethodV2              = "v2"
)

var (
	singleflightGroup singleflight.Group
)

type tccConfigWithExpiration struct {
	expire time.Time
	config *model.CodeKGTCCConfig
}

type abConfigWithExpiration struct {
	expire time.Time
	config *model.CodeKGABConfig
}

type featureGateConfigWithExpiration struct {
	expire time.Time
	config map[string]bool
}

type featureGateConfigWithoutExpiration struct {
	config map[string]bool
}

type Config struct {
	userConfigs                 *sync.Map
	userABConfigs               *sync.Map
	userFeatureGatesConfigs     *sync.Map
	userFeatureGatesConfigsOnce *sync.Map
}

func MustNewConfig() *Config {
	resultConfig := &Config{
		userConfigs:                 new(sync.Map),
		userABConfigs:               new(sync.Map),
		userFeatureGatesConfigs:     new(sync.Map),
		userFeatureGatesConfigsOnce: new(sync.Map),
	}

	return resultConfig
}

func (c *Config) GetTCCConfig(ctx context.Context, cli knowledgebase.Client, userID string) *model.CodeKGTCCConfig {
	if value, ok := c.userConfigs.Load(userID); ok {
		config := value.(*tccConfigWithExpiration)
		if time.Now().Before(config.expire) {
			return config.config
		}
	}

	result, err, _ := singleflightGroup.Do(fmt.Sprintf("fetch_tcc_config_%s", userID), func() (interface{}, error) {
		resp, err := cli.GetCodeKGFeaturesConfig(ctx, env.GetToken(userID))
		if err != nil {
			return nil, err
		} else {
			config := new(model.CodeKGTCCConfig)

			ckgConfig := lo.Filter(resp.Features, func(item *knowledgebase.FeatureConfig, index int) bool {
				if env.GetSourceProduct() == model.SourceProductCodeVerse || env.GetSourceProduct() == model.SourceProductMarscode {
					return item.Name == "ckg_codeverse"
				} else if env.GetSourceProduct() == model.SourceProductNativeIDE {
					return item.Name == "ckg_native_ide"
				}

				return item.Name == "ckg"
			})
			if len(ckgConfig) > 0 {
				err = json.Unmarshal([]byte(ckgConfig[0].Config), config)
				if err != nil {
					return nil, err
				}
			} else {
				defaultConfig := lo.Filter(resp.Features, func(item *knowledgebase.FeatureConfig, index int) bool {
					return item.Name == "ckg"
				})
				if len(defaultConfig) > 0 {
					err = json.Unmarshal([]byte(defaultConfig[0].Config), config)
					if err != nil {
						return nil, err
					}
				} else {
					return nil, errors.New("ckg config not found")
				}
			}

			if config.FileCountLimit == 0 {
				config.FileCountLimit = defaultFileCountLimit
			}
			if config.FileSizeThreshold == 0 {
				config.FileSizeThreshold = DefaultFileSizeThreshold
			}
			if config.SingleFileLineThreshold == 0 {
				config.SingleFileLineThreshold = DefaultSingleFileLineThreshold
			}
			if config.DatastoreName == "" {
				config.DatastoreName = defaultDatastoreName
			}
			if config.DefaultRecallNum == 0 {
				config.DefaultRecallNum = defaultRecallNum
			}
			if config.ForceIndexFileCountLimit == 0 {
				config.ForceIndexFileCountLimit = defaultForceIndexFileCountLimit
			}
			if config.IndexFileLineLimit == 0 {
				config.IndexFileLineLimit = defaultIndexFileLineLimit
			}
			if config.DebounceTime == 0 {
				config.DebounceTime = defaultDebounceTime
			}
			if config.RecentCursorRelationNum == 0 {
				config.RecentCursorRelationNum = defaultRecentCursorRelationNum
			}
			if config.TaskSleepTimeFactor == 0 {
				config.TaskSleepTimeFactor = defaultTaskSleepFactor
			}

			virtualProjectConfig := config.VirtualProjectConfig
			if virtualProjectConfig == nil {
				virtualProjectConfig = &model.VirtualProjectConfig{}
				config.VirtualProjectConfig = virtualProjectConfig
			}
			if virtualProjectConfig.FileCountLimit == 0 {
				virtualProjectConfig.FileCountLimit = defaultVirtualProjectFileCountLimit
			}
			if virtualProjectConfig.FileSizeLimit == 0 {
				virtualProjectConfig.FileSizeLimit = defaultVirtualProjectFileSizeThreshold
			}
			if virtualProjectConfig.TotalFileSizeLimit == 0 {
				virtualProjectConfig.TotalFileSizeLimit = defaultVirtualProjectTotalFileSizeLimit
			}
			if virtualProjectConfig.RecallNum == 0 {
				virtualProjectConfig.RecallNum = defaultVirtualProjectRecallNum
			}
			if virtualProjectConfig.ChunkingMethodBaseline == "" {
				virtualProjectConfig.ChunkingMethodBaseline = defaultVirtualProjectChunkingMethodBaseline
			}
			if virtualProjectConfig.ChunkingSize == 0 {
				virtualProjectConfig.ChunkingSize = defaultVirtualProjectChunkingSize
			}
			if virtualProjectConfig.ChunkingOverlap == 0 {
				virtualProjectConfig.ChunkingOverlap = defaultVirtualProjectChunkingOverlap
			}

			logs.CtxInfo(ctx, "codekg config is fetch for %s, %+v", userID, config)

			c.userConfigs.Store(userID, &tccConfigWithExpiration{
				expire: time.Now().Add(5 * time.Minute),
				config: config,
			})
			return config, nil
		}
	})
	if err != nil {
		logs.CtxWarn(ctx, "failed to get codekg config, err: %v", err)

		virtualProjectConfig := &model.VirtualProjectConfig{
			FileCountLimit:         defaultVirtualProjectFileCountLimit,
			FileSizeLimit:          defaultVirtualProjectFileSizeThreshold,
			TotalFileSizeLimit:     defaultVirtualProjectTotalFileSizeLimit,
			RecallNum:              defaultVirtualProjectRecallNum,
			ChunkingMethodBaseline: defaultVirtualProjectChunkingMethodBaseline,
			ChunkingSize:           defaultVirtualProjectChunkingSize,
			ChunkingOverlap:        defaultVirtualProjectChunkingOverlap,
		}

		return &model.CodeKGTCCConfig{
			FileCountLimit:           defaultFileCountLimit,
			FileSizeThreshold:        DefaultFileSizeThreshold,
			SingleFileLineThreshold:  DefaultSingleFileLineThreshold,
			DatastoreName:            defaultDatastoreName,
			EnableRerank:             false,
			DefaultRecallNum:         defaultRecallNum,
			ForceIndexFileCountLimit: defaultForceIndexFileCountLimit,
			IndexFileLineLimit:       defaultIndexFileLineLimit,
			DebounceTime:             defaultDebounceTime,
			RecentCursorRelationNum:  defaultRecentCursorRelationNum,
			VirtualProjectConfig:     virtualProjectConfig,
		}
	}

	return result.(*model.CodeKGTCCConfig)
}

func (c *Config) GetABConfig(ctx context.Context, cli knowledgebase.Client, userID string) *model.CodeKGABConfig {
	if value, ok := c.userABConfigs.Load(userID); ok {
		config := value.(*abConfigWithExpiration)
		if time.Now().Before(config.expire) {
			return config.config
		}
	}

	result, err, _ := singleflightGroup.Do(fmt.Sprintf("fetch_ab_config_%s", userID), func() (interface{}, error) {
		resp, err := cli.GetCodeKGABConfig(ctx, env.GetToken(userID))
		if err != nil {
			return nil, err
		}

		if resp.ABParams == "" {
			return nil, errors.New("ckg ab config not found")
		}

		abConfig := new(model.CodeKGABConfig)
		err = json.Unmarshal([]byte(resp.ABParams), abConfig)
		if err != nil {
			return nil, err
		}

		logs.CtxInfo(ctx, "codekg ab config is fetch for %s, %+v", userID, abConfig)

		c.userABConfigs.Store(userID, &abConfigWithExpiration{
			expire: time.Now().Add(5 * time.Minute),
			config: abConfig,
		})
		return abConfig, nil
	})
	if err != nil {
		logs.CtxWarn(ctx, "failed to get codekg config, err: %v", err)
		return new(model.CodeKGABConfig)
	}

	return result.(*model.CodeKGABConfig)
}

func (c *Config) IsFeatureEnabled(ctx context.Context, userID, feature string) bool {
	if value, ok := c.userFeatureGatesConfigs.Load(userID); ok {
		config := value.(*featureGateConfigWithExpiration)
		if time.Now().Before(config.expire) {
			return config.config[feature]
		}
	}

	result, err := c.fetchFeatureGatesConfig(ctx, userID)
	if err != nil {
		logs.CtxError(ctx, "failed to get codekg config, err: %v", err)
		// 如果请求失败，尝试使用先前请求到的 feature gate config
		if value, ok := c.userFeatureGatesConfigs.Load(userID); ok {
			config := value.(*featureGateConfigWithExpiration)
			return config.config[feature]
		} else {
			return false
		}
	}
	c.userFeatureGatesConfigs.Store(userID, &featureGateConfigWithExpiration{
		expire: time.Now().Add(5 * time.Minute),
		config: result,
	})
	if _, ok := c.userFeatureGatesConfigsOnce.Load(userID); !ok {
		c.userFeatureGatesConfigsOnce.Store(userID, &featureGateConfigWithoutExpiration{config: result})
	}
	return result[feature]
}

// IsIndexFeatureEnabled 对单个 user 仅请求一次，始终使用 ckg 启动时使用的配置
func (c *Config) IsIndexFeatureEnabled(ctx context.Context, userID, feature string) bool {
	// 如果已有 config，不考虑过期时间
	if value, ok := c.userFeatureGatesConfigsOnce.Load(userID); ok {
		config := value.(*featureGateConfigWithoutExpiration)
		if config != nil && len(config.config) > 0 {
			return config.config[feature]
		}
	}
	result, err := c.fetchFeatureGatesConfig(ctx, userID)
	if err != nil {
		logs.CtxError(ctx, "failed to get codekg config, err: %v", err)
		return false
	}
	c.userFeatureGatesConfigsOnce.Store(userID, &featureGateConfigWithoutExpiration{config: result})
	return result[feature]
}

// GetLocalStrategyOrDefault 尝试获取本地 db 中记录的 "策略"，用于本地召回前确定 query 的 embedding 方式。
// 1. 当本地 db 存在时，返回 db 中的 "策略"
// 2. 当本地 db 不存在时，返回默认值。但该 case 不应触发。
// 注意：不使用 A/B 或 TCC 的 baseline，
func (c *Config) GetLocalStrategyOrDefault(ctx context.Context, userId string, project model.URI) (string, string) {
	envDB, err := env.GetEnvStorage(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[GetLocalStrategyOrDefault] no env db")
		return consts.NoChunkingMethodSinceNoEntityStorage, consts.NoEmbeddingModelSinceNoEmbeddingStorage
	}
	storageProjectId, err := envDB.GetProjectIDByProjectPath(ctx, envDB.GetConn(), project)
	if err != nil {
		logs.CtxWarn(ctx, "[GetLocalStrategyOrDefault] GetProjectIDByProjectPath error: %v", err)
		return consts.NoChunkingMethodSinceNoEntityStorage, consts.NoEmbeddingModelSinceNoEmbeddingStorage
	}
	if c.IsIndexFeatureEnabled(ctx, userId, UseV2SplitEmbeddingAPI) {
		// 如果使用 v2 api，且 model baseline 读出来是 datastore name default，那么 model baseline 自动变成 codekg_bge_m3
		if storageProjectId.EmbeddingModel == defaultDatastoreName {
			storageProjectId.EmbeddingModel = defaultEmbeddingModelBaseline
		}
		return storageProjectId.ChunkingMethod, storageProjectId.EmbeddingModel
	} else {
		// 如果仍使用 v1 api，且 db 中村的 model baseline 是 codekg_bge_m3 了，
		// 说明用过 v2 api 但回退了，此时 model baseline 还是 datastore name
		if storageProjectId.EmbeddingModel == defaultEmbeddingModelBaseline {
			storageProjectId.EmbeddingModel = defaultDatastoreName
		}
		return storageProjectId.ChunkingMethod, storageProjectId.EmbeddingModel
	}
}

// GetTargetStrategy 尝试获得初始化创建 db 时期望对齐的 "策略"
// 1. A/B 实验开启时，本函数尝试获取 A/B 策略，
// 2. A/B 未开启时，使用 TCC 的 baseline。
func (c *Config) GetTargetStrategy(ctx context.Context, cli knowledgebase.Client, userId string) (string, string) {
	chunkingBaseline := c.GetChunkingMethodBaseline(ctx, userId)
	embeddingBaseline := c.GetEmbeddingModelBaseline(ctx, userId)
	abConfig := c.GetABConfig(ctx, cli, userId)
	chunkingMethod, chunkingABEnabled := abConfig.GetChunkingMethod()
	if !chunkingABEnabled {
		chunkingMethod = chunkingBaseline
	}
	embeddingModel, embeddingABEnabled := abConfig.GetEmbeddingModel()
	if !embeddingABEnabled {
		embeddingModel = embeddingBaseline
	}
	return chunkingMethod, embeddingModel
}

func (c *Config) GetVirtualProjectTargetStrategy(ctx context.Context, cli knowledgebase.Client, userId string) (string, string) {
	tccConfig, abConfig := c.GetTCCConfig(ctx, cli, userId), c.GetABConfig(ctx, cli, userId)
	embeddingBaseline := c.GetEmbeddingModelBaseline(ctx, userId)
	chunkingMethod := tccConfig.VirtualProjectConfig.ChunkingMethodBaseline
	embeddingModel, embeddingABEnabled := abConfig.GetEmbeddingModel()
	if !embeddingABEnabled {
		embeddingModel = embeddingBaseline
	}
	return chunkingMethod, embeddingModel
}

func (c *Config) GetChunkingMethodBaseline(ctx context.Context, userId string) string {
	if c.IsIndexFeatureEnabled(ctx, userId, EnableV2ChunkingMethod) {
		return ChunkingMethodV2
	} else {
		return ChunkingMethodV1
	}
}

func (c *Config) GetEmbeddingModelBaseline(ctx context.Context, userId string) string {
	if c.IsIndexFeatureEnabled(ctx, userId, UseV2SplitEmbeddingAPI) {
		logs.CtxInfo(ctx, "[GetEmbeddingModelBaseline] embedding model: %s", defaultEmbeddingModelBaseline)
		return defaultEmbeddingModelBaseline
	} else {
		logs.CtxInfo(ctx, "[GetEmbeddingModelBaseline] datastore name: %s", defaultDatastoreNameBaseline)
		return defaultDatastoreNameBaseline
	}
}

func (c *Config) GetLocalStorageVersion(ctx context.Context, project model.URI) consts.StorageVersion {
	envDB, err := env.GetEnvStorage(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[GetStorageLocalVersion] no env db")
		return consts.NoStorageVersion
	}
	storageProjectId, err := envDB.GetProjectIDByProjectPath(ctx, envDB.GetConn(), project)
	if err != nil {
		logs.CtxWarn(ctx, "[GetStorageLocalVersion] GetProjectIDByProjectPath error: %v", err)
		return consts.NoStorageVersion
	}
	return consts.StorageVersion(storageProjectId.DBVersion)
}

func (c *Config) GetTargetStorageVersion(ctx context.Context, userId string, t consts.EmbeddingStorageType) consts.StorageVersion {
	if t == consts.EmbeddingStorage_SQLiteVec {
		// SQLite-Vec 已全量，正常都走这个分支了
		if c.IsIndexFeatureEnabled(ctx, userId, UseEmbeddingDBV2) {
			// SQLite 向量库使用 embeddingId 为 primary key
			return consts.StorageVersionV2
		} else {
			// SQLite 向量库没有 primary key，使用 entity id 来删除向量（较慢）
			return consts.StorageVersionV1
		}
	} else {
		// Chromem-Go
		return consts.StorageVersionV0
	}
}

func (c *Config) fetchFeatureGatesConfig(ctx context.Context, userID string) (map[string]bool, error) {
	result, err, _ := singleflightGroup.Do(fmt.Sprintf("fetch_feature_gates_config_%s", userID), func() (interface{}, error) {
		cli := resty.New().SetHostURL(lo.Ternary(env.IsMerlin(), "https://iac.byted.org", "https://bytegate.zijieapi.com"))

		type response struct {
			Code    int             `json:"code"`
			Message string          `json:"message"`
			Data    map[string]bool `json:"data"`
		}
		var (
			err  error
			resp *resty.Response
		)
		err = retry.Do(func() error {
			resp, err = cli.R().SetContext(ctx).SetResult(&response{}).SetBody(map[string]interface{}{
				"workspace_name": "CodeKG",
				"user":           userID,
				"custom": map[string]interface{}{
					"ckg_version":        version_feature.CKG_VERSION.String(),
					"ckg_source_product": env.GetSourceProduct(),
					"app_id":             env.GetAppID(),
				},
			}).Post("/api/v1/workspace/feature_gates/values")
			if err != nil {
				return err
			}

			if resp.StatusCode() != 200 {
				return errors.Errorf("failed to get feature gates config, resp: %s", resp.String())
			}

			logId := resp.Header().Get("X-Tt-Logid")
			logs.CtxInfo(ctx, "codekg feature gate config is fetch for %s, %+v, log id: %s", userID, resp.Result().(*response).Data, logId)

			return nil
		},
			retry.Attempts(3),
			retry.DelayType(retry.BackOffDelay),
			retry.Delay(time.Second),
			retry.MaxDelay(time.Second*20))
		if err != nil {
			return nil, err
		}

		return resp.Result().(*response).Data, nil
	})
	if err != nil {
		logs.CtxWarn(ctx, "failed to get codekg feature gate config, err: %v", err)
		return make(map[string]bool), err
	}
	res, ok := result.(map[string]bool)
	if !ok {
		return nil, errors.Errorf("result is not a map string -> bool, but: %v", result)
	}
	return res, nil
}
