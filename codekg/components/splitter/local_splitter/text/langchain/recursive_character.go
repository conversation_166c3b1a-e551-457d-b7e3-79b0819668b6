package langchain

import (
	"strings"

	"ide/ckg/codekg/model"
)

// RecursiveCharacterSplitter is a text splitter that will split texts recursively by different
// characters.
type RecursiveCharacterSplitter struct {
	Separators    []string
	ChunkSize     int
	ChunkOverlap  int
	LenFunc       func(string) int
	KeepSeparator bool
}

// NewRecursiveCharacter creates a new recursive character splitter with default values. By
// default, the separators used are "\n\n", "\n", " " and "". The chunk size is set to 4000
// and chunk overlap is set to 200.
func NewRecursiveCharacter(opts ...Option) RecursiveCharacterSplitter {
	options := DefaultOptions()
	for _, o := range opts {
		o(&options)
	}

	s := RecursiveCharacterSplitter{
		Separators:    options.Separators,
		ChunkSize:     options.ChunkSize,
		ChunkOverlap:  options.ChunkOverlap,
		LenFunc:       options.LenFunc,
		KeepSeparator: options.KeepSeparator,
	}

	return s
}

// SplitText splits a text into multiple chunks with line information.
func (s RecursiveCharacterSplitter) SplitText(text string) ([]TextChunk, error) {
	// 获取分割结果和位置信息
	chunks, positions := s.splitTextWithPositions(text)

	// 计算行索引
	lineIndices := findLineIndices(text)

	// 转换位置信息为行号信息
	chunkLineIndices := make([][2]int, len(chunks))
	for i, chunk := range chunks {
		if chunk == "" {
			continue
		}

		// 获取块的起始和结束位置
		startPos := positions[i]
		endPos := startPos + len(chunk) - 1

		// 获取起始和结束行号
		startLine := getLineNumberFromPosition(lineIndices, startPos)
		endLine := getLineNumberFromPosition(lineIndices, endPos)

		chunkLineIndices[i] = [2]int{startLine, endLine}
	}

	// 使用基于行索引的处理函数
	return processChunksWithLineIndices(chunks, chunkLineIndices)
}

// splitTextWithPositions 分割文本并返回每个块在原文中的位置
func (s RecursiveCharacterSplitter) splitTextWithPositions(text string) ([]string, []int) {
	chunks := make([]string, 0)
	positions := make([]int, 0)

	// 使用第一个分隔符进行初始分割
	separator := ""
	nextSeparators := []string{}

	if len(s.Separators) > 0 {
		separator = s.Separators[len(s.Separators)-1]
		for i, c := range s.Separators {
			if c == "" || strings.Contains(text, c) {
				separator = c
				if i+1 < len(s.Separators) {
					nextSeparators = s.Separators[i+1:]
				}
				break
			}
		}
	}

	// 分割文本
	if separator == "" {
		// 如果没有分隔符，将整个文本作为一个块
		chunks = append(chunks, text)
		positions = append(positions, 0)
		return chunks, positions
	}

	// 分割文本并记录位置
	splits := strings.Split(text, separator)
	currentPos := 0

	goodSplits := make([]string, 0)
	goodPositions := make([]int, 0)

	for _, split := range splits {
		if s.KeepSeparator && currentPos > 0 {
			// 如果保留分隔符，将分隔符添加到分割后的文本前面
			split = separator + split
		}

		splitPos := currentPos
		if !s.KeepSeparator {
			currentPos += len(separator)
		}

		if s.LenFunc(split) < s.ChunkSize {
			// 如果块的大小小于ChunkSize，添加到goodSplits
			goodSplits = append(goodSplits, split)
			goodPositions = append(goodPositions, splitPos)
		} else {
			// 处理之前收集的goodSplits
			if len(goodSplits) > 0 {
				mergedChunks, mergedPositions := s.mergeSplitsWithPositions(goodSplits, goodPositions, separator)
				chunks = append(chunks, mergedChunks...)
				positions = append(positions, mergedPositions...)
				goodSplits = make([]string, 0)
				goodPositions = make([]int, 0)
			}

			// 处理当前大块
			if len(nextSeparators) == 0 {
				// 如果没有下一级分隔符，将整个块添加到结果
				chunks = append(chunks, split)
				positions = append(positions, splitPos)
			} else {
				// 使用下一级分隔符递归分割
				subChunks, subPositions := s.splitWithSeparatorsAndPositions(split, nextSeparators, splitPos)
				chunks = append(chunks, subChunks...)
				positions = append(positions, subPositions...)
			}
		}

		currentPos += len(split)
	}

	// 处理剩余的goodSplits
	if len(goodSplits) > 0 {
		mergedChunks, mergedPositions := s.mergeSplitsWithPositions(goodSplits, goodPositions, separator)
		chunks = append(chunks, mergedChunks...)
		positions = append(positions, mergedPositions...)
	}

	return chunks, positions
}

// mergeSplitsWithPositions 合并分割并跟踪位置
func (s RecursiveCharacterSplitter) mergeSplitsWithPositions(splits []string, positions []int, separator string) ([]string, []int) {
	// 实现与mergeSplits类似的逻辑，但同时跟踪位置
	result := make([]string, 0)
	resultPositions := make([]int, 0)

	current := ""
	currentPosition := 0

	for i, split := range splits {
		if i == 0 {
			current = split
			currentPosition = positions[i]
			continue
		}

		if s.LenFunc(current)+s.LenFunc(separator)+s.LenFunc(split) <= s.ChunkSize {
			// 如果合并后的大小不超过ChunkSize，继续合并
			if separator != "" {
				current += separator
			}
			current += split
		} else {
			// 否则，将当前块添加到结果，并开始新的块
			result = append(result, current)
			resultPositions = append(resultPositions, currentPosition)

			// 处理重叠
			if s.ChunkOverlap > 0 {
				// 实现重叠逻辑
				overlapStart := len(current) - s.ChunkOverlap
				if overlapStart < 0 {
					overlapStart = 0
				}
				current = current[overlapStart:]
				currentPosition += overlapStart
			} else {
				current = ""
			}

			if current == "" {
				current = split
				currentPosition = positions[i]
			} else {
				if separator != "" {
					current += separator
				}
				current += split
			}
		}
	}

	if current != "" {
		result = append(result, current)
		resultPositions = append(resultPositions, currentPosition)
	}

	return result, resultPositions
}

// splitWithSeparatorsAndPositions 使用指定的分隔符分割文本并跟踪位置
func (s RecursiveCharacterSplitter) splitWithSeparatorsAndPositions(text string, separators []string, basePosition int) ([]string, []int) {
	chunks := make([]string, 0)
	positions := make([]int, 0)

	// 使用第一个分隔符进行初始分割
	separator := ""
	nextSeparators := []string{}

	if len(separators) > 0 {
		separator = separators[len(separators)-1]
		for i, c := range separators {
			if c == "" || strings.Contains(text, c) {
				separator = c
				if i+1 < len(separators) {
					nextSeparators = separators[i+1:]
				}
				break
			}
		}
	}

	// 分割文本
	if separator == "" {
		// 如果没有分隔符，将整个文本作为一个块
		chunks = append(chunks, text)
		positions = append(positions, basePosition)
		return chunks, positions
	}

	// 分割文本并记录位置
	splits := strings.Split(text, separator)
	currentPos := basePosition

	goodSplits := make([]string, 0)
	goodPositions := make([]int, 0)

	for _, split := range splits {
		if s.KeepSeparator && currentPos > basePosition {
			// 如果保留分隔符，将分隔符添加到分割后的文本前面
			split = separator + split
		}

		splitPos := currentPos
		if !s.KeepSeparator {
			currentPos += len(separator)
		}

		if s.LenFunc(split) < s.ChunkSize {
			// 如果块的大小小于ChunkSize，添加到goodSplits
			goodSplits = append(goodSplits, split)
			goodPositions = append(goodPositions, splitPos)
		} else {
			// 处理之前收集的goodSplits
			if len(goodSplits) > 0 {
				mergedChunks, mergedPositions := s.mergeSplitsWithPositions(goodSplits, goodPositions, separator)
				chunks = append(chunks, mergedChunks...)
				positions = append(positions, mergedPositions...)
				goodSplits = make([]string, 0)
				goodPositions = make([]int, 0)
			}

			// 处理当前大块
			if len(nextSeparators) == 0 {
				// 如果没有下一级分隔符，将整个块添加到结果
				chunks = append(chunks, split)
				positions = append(positions, splitPos)
			} else {
				// 使用下一级分隔符递归分割
				subChunks, subPositions := s.splitWithSeparatorsAndPositions(split, nextSeparators, splitPos)
				chunks = append(chunks, subChunks...)
				positions = append(positions, subPositions...)
			}
		}

		currentPos += len(split)
	}

	// 处理剩余的goodSplits
	if len(goodSplits) > 0 {
		mergedChunks, mergedPositions := s.mergeSplitsWithPositions(goodSplits, goodPositions, separator)
		chunks = append(chunks, mergedChunks...)
		positions = append(positions, mergedPositions...)
	}

	return chunks, positions
}

// addSeparatorInSplits adds the separator in each of splits.
func (s RecursiveCharacterSplitter) addSeparatorInSplits(splits []string, separator string) []string {
	splitsWithSeparator := make([]string, 0, len(splits))
	for i, s := range splits {
		if i > 0 {
			s = separator + s
		}
		splitsWithSeparator = append(splitsWithSeparator, s)
	}
	return splitsWithSeparator
}

// splitTextWithSeparators is a helper function for recursive splitting
func (s RecursiveCharacterSplitter) splitTextWithSeparators(text string, separators []string) []string {
	chunks, _ := s.splitWithSeparatorsAndPositions(text, separators, 0)
	return chunks
}

// NewRecursiveCharacterSplitterAdapter creates a new recursive character splitter adapter
func NewRecursiveCharacterSplitterAdapter(chunkSize, chunkOverlap int) LocalSplitterAdapter {
	// 创建RecursiveCharacter splitter
	splitter := NewRecursiveCharacter(
		WithChunkSize(chunkSize),
		WithChunkOverlap(chunkOverlap),
		WithSeparators([]string{"\n\n", "\n"}),
		WithKeepSeparator(true),
	)

	// 创建适配器
	return NewAdapter(splitter, map[model.LanguageType]bool{
		model.LanguageText: true,
	})
}
