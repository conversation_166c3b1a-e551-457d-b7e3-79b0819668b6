import {TextDocument} from "vscode-languageserver-textdocument";
import {DocumentUri, Range} from "vscode-languageserver-types";
import {getCacheFileAstTreeAllowCreate} from "../../cache/astCache";
import {TreeSitterParser} from "../../parse/treesitter/parser";
import {Logger} from "../../../utils/logger";
import {SyntaxNode} from "web-tree-sitter";
import {
  DefNodeInfo, inWorkspaceLocationFilter,
  LanguageIdEnum,
  SyntaxNodeWithUri,
  UsageNodeInfo,
  VarDeclNodeInfo,
} from "../../parse/share";
import {TreeSitterDispatcher} from "../../parse/treesitter/dispatcher";
import {TreeSitterFunctionalityEnum} from "../../parse/treesitter/share";
import {LSPParser} from "../../parse/lsp/parser";
import {convertToPlainObject, positionToPoint} from "../../../utils/common";
import * as crypto from 'crypto';
import path from "path";
import {getWorkspaceFolderPath} from "../../../utils/path";
import {PSIParser} from "../../parse/psi/parser";
import {getConfig} from "../../common/config";
import {FunctionalityExecutor} from "../../parse/functionality/executor";
import {FunctionalityEnum} from "../../parse/functionality/share";

class ContextResult {
  errMsg: string;

  fileName: string;
  relativeFilePath: string;
  relativeFilePathWithoutExtension: string;
  imports: string[];
  requires: string[];
  name: string;
  signature: string;
  body: string;

  classInfo: TypeContextInfo;
  usedCodeInfo: (FuncContextInfo | VariableContextInfo)[];
  usageInfo: UsageContextInfo[];

  withError(errMsg: string): ContextResult {
    this.errMsg = errMsg;
    return this;
  }

  withName(name: string): ContextResult {
    this.name = name;
    return this;
  }

  withFileName(fileName: string): ContextResult {
    this.fileName = fileName;
    this.relativeFilePath = getRelativePathFromTargetFuncFileFolder(path.dirname(this.fileName), this.fileName);
    this.relativeFilePathWithoutExtension = this.relativeFilePath.slice(0, this.relativeFilePath.length - path.extname(this.relativeFilePath).length);
    return this;
  }

  withImports(imports: string[]): ContextResult {
    this.imports = imports;
    return this;
  }

  withRequires(requires: string[]): ContextResult {
    this.requires = requires;
    return this;
  }

  withSignature(signature: string): ContextResult {
    this.signature = signature;
    return this;
  }

  withBody(body: string): ContextResult {
    this.body = body;
    return this;
  }

  withClassInfo(classInfo: TypeContextInfo): ContextResult {
    this.classInfo = classInfo;
    return this;
  }

  withUsedCodeInfo(usedCodeInfo: (FuncContextInfo | VariableContextInfo)[]): ContextResult {
    this.usedCodeInfo = this.removeRepeatedInfo(usedCodeInfo) as (FuncContextInfo | VariableContextInfo)[];
    return this;
  }

  withUsageInfo(usageInfo: UsageContextInfo[]): ContextResult {
    this.usageInfo = this.removeRepeatedInfo(usageInfo) as UsageContextInfo[];
    return this;
  }

  toJsonString(): string {
    return JSON.stringify(convertToPlainObject(this), null, 2);
  }

  private removeRepeatedInfo(infos: ContextInfo[]): ContextInfo[] {
    const result: ContextInfo[] = [];

    const md5Map = new Map<string, ContextInfo>();
    for (const info of infos) {
      const md5 = info.getMD5();
      if (!md5Map.has(md5)) {
        md5Map.set(md5, info);
        result.push(info);
      }
    }

    return result;
  }
}

interface ContextInfo {
  getMD5(): string;
}

class TypeContextInfo implements ContextInfo {
  name: string;
  file: string;
  relativeFilePath: string;
  relativeFilePathWithoutExtension: string;
  superClass: string;
  fields: string[];
  constructors: string[];

  constructor(name: string, file: string, targetFuncFile: string) {
    this.name = name;
    this.file = file;
    this.relativeFilePath = getRelativePathFromTargetFuncFileFolder(path.dirname(file), targetFuncFile);
    this.relativeFilePathWithoutExtension = this.relativeFilePath.slice(0, this.relativeFilePath.length - path.extname(this.relativeFilePath).length);
  }

  withSuperClass(superClass: string): TypeContextInfo {
    this.superClass = superClass;
    return this;
  }

  withFields(fields: string[]): TypeContextInfo {
    this.fields = fields;
    return this;
  }

  withConstructors(constructors: string[]): TypeContextInfo {
    this.constructors = constructors;
    return this;
  }

  getMD5(): string {
    const jsonStr = JSON.stringify(convertToPlainObject(this));
    return crypto.createHash('md5').update(jsonStr).digest('hex');
  }
}

class FuncContextInfo implements ContextInfo {
  name: string;
  file: string;
  relativeFilePath: string;
  relativeFilePathWithoutExtension: string;
  body: string;
  classInfo: TypeContextInfo;

  constructor(name: string, file: string, targetFuncFile: string) {
    this.name = name;
    this.file = file;
    this.relativeFilePath = getRelativePathFromTargetFuncFileFolder(path.dirname(file), targetFuncFile);
    this.relativeFilePathWithoutExtension = this.relativeFilePath.slice(0, this.relativeFilePath.length - path.extname(this.relativeFilePath).length);
  }

  withBody(body: string): FuncContextInfo {
    this.body = body;
    return this;
  }

  withClassInfo(classInfo: TypeContextInfo): FuncContextInfo {
    this.classInfo = classInfo;
    return this;
  }

  getMD5(): string {
    const jsonStr = JSON.stringify(convertToPlainObject(this));
    return crypto.createHash('md5').update(jsonStr).digest('hex');
  }
}

class VariableContextInfo implements ContextInfo {
  name: string;
  file: string;
  relativeFilePath: string;
  relativeFilePathWithoutExtension: string;
  code: string;

  constructor(name: string, file: string, targetFuncFile: string) {
    this.name = name;
    this.file = file;
    this.relativeFilePath = getRelativePathFromTargetFuncFileFolder(path.dirname(file), targetFuncFile);
    this.relativeFilePathWithoutExtension = this.relativeFilePath.slice(0, this.relativeFilePath.length - path.extname(this.relativeFilePath).length);
  }

  withCode(code: string): VariableContextInfo {
    this.code = code;
    return this;
  }

  getMD5(): string {
    const jsonStr = JSON.stringify(convertToPlainObject(this));
    return crypto.createHash('md5').update(jsonStr).digest('hex');
  }
}

class UsageContextInfo implements ContextInfo {
  file: string;
  relativeFilePath: string;
  relativeFilePathWithoutExtension: string;
  code: string;

  constructor(file: string, code: string) {
    this.file = file;
    this.relativeFilePath = getRelativePathFromTargetFuncFileFolder(path.dirname(file), file);
    this.relativeFilePathWithoutExtension = this.relativeFilePath.slice(0, this.relativeFilePath.length - path.extname(this.relativeFilePath).length);
    this.code = code;
  }

  getMD5(): string {
    const jsonStr = JSON.stringify(convertToPlainObject(this));
    return crypto.createHash('md5').update(jsonStr).digest('hex');
  }
}

function getRelativePathFromTargetFuncFileFolder(file: string, targetFuncFile: string): string {
  let relativePath = path.relative(file, targetFuncFile);
  if (!relativePath.startsWith(`.${path.sep}`) && !relativePath.startsWith(`..${path.sep}`)) {
    relativePath = `.${path.sep}` + relativePath;
  }

  return relativePath;
}

export async function doExtractUnitTestContextForJS(document: TextDocument, startIndex: number, endIndex: number): Promise<string> {
  Logger.info(`doExtractUnitTestContextForJS: '${document.uri}' (${startIndex}, ${endIndex})`);

  const result = new ContextResult();

  const workspaceFolderPath = getWorkspaceFolderPath();

  if (![LanguageIdEnum.Javascript, LanguageIdEnum.JavascriptReact, LanguageIdEnum.Jsx].map(item => item.toString()).includes(document.languageId)) {
    Logger.error(`doExtractUnitTestContextForJS: languageId should be '${LanguageIdEnum.Javascript}/${LanguageIdEnum.JavascriptReact}/${LanguageIdEnum.Jsx}' but got '${document.languageId}'`);
    return "{}";
  }

  if (!PSIParser.INSTANCE.initialized() && !LSPParser.INSTANCE.initialized()) {
    Logger.error(`doExtractUnitTestContextForJS: PSIParser and LSPParser are not initialized`);
    return "{}";
  }

  if (!workspaceFolderPath) {
    Logger.error(`doExtractUnitTestContextForJS: workspaceFolderPath is empty`);
    return "{}";
  }

  const tree = getCacheFileAstTreeAllowCreate(document);
  if (!tree) {
    Logger.error(`doExtractUnitTestContextForJS: failed to parse Tree for document '${document.uri}'`);
    return "{}";
  }

  const start = Date.now();

  const selectRange = {start: document.positionAt(startIndex), end: document.positionAt(endIndex)};

  let funcDeclNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetOutermostFuncParentNodeForRange, tree.rootNode, selectRange);
  if (!funcDeclNode) {
    funcDeclNode = getIncludingFuncDeclNode(document.languageId, tree.rootNode, selectRange);
  }
  if (!funcDeclNode) {
    Logger.error(`doExtractUnitTestContextForJS: failed to get funcDeclNode for document '${document.uri}' with range [${startIndex}, ${endIndex}]`);
    return "{}";
  }

  result.withFileName(getRelativePath(document.uri, workspaceFolderPath));

  const importResult: string[] = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetImportFileOrModule, tree.rootNode);
  const importStatements: string[] = [];
  const requireStatements: string[] = [];
  for (const item of importResult) {
    if (item.startsWith("import")) {
      importStatements.push(item);
    } else {
      requireStatements.push(item);
    }
  }
  result.withImports(importStatements);
  result.withRequires(requireStatements);

  const funcNameNode = getFuncNameNode(funcDeclNode);
  if (funcNameNode) {
    result.withName(funcNameNode.text);
  } else {
    result.withName("*anonymous*");
  }

  const signatureResult: string = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetFuncSignatureString, funcDeclNode);
  if (signatureResult) {
    result.withSignature(signatureResult);
  }

  result.withBody(funcDeclNode.text);

  const classTypeInfo = getClassTypeInfo(funcDeclNode, document.languageId, document.uri);
  if (classTypeInfo) {
    result.withClassInfo(getTypeInfoFromDefNode(classTypeInfo, workspaceFolderPath, result.fileName));
  }

  if (Date.now() - start > getConfig("analysis.utgen.javascript.timeoutMs")) {
    Logger.warn(`doExtractUnitTestContextForJS: timeout for function '${funcNameNode?.text ?? "*anonymous*"}', return partial result`);
    return result.toJsonString();
  }

  const usedCodeInfosResult = await getUsedCodeInfos(document.uri, funcDeclNode, document.languageId, workspaceFolderPath, requireStatements, result.fileName, start);
  result.withUsedCodeInfo(usedCodeInfosResult.data);

  if (usedCodeInfosResult.timeout) {
    Logger.warn(`doExtractUnitTestContextForJS: timeout for function '${funcNameNode?.text ?? "*anonymous*"}', return partial result`);
    return result.toJsonString();
  }

  if (funcNameNode) {
    const usageInfos: UsageContextInfo[] = [];
    for (const usageNode of await getUsageInfos(document.uri, funcNameNode, document.languageId)) {
      const usageContextInfo = getUsageInfoFromUsageFuncParentNode(usageNode, workspaceFolderPath);
      if (usageContextInfo) {
        usageInfos.push(usageContextInfo);
      }
    }
    result.withUsageInfo(usageInfos);
  }

  return result.toJsonString();
}

export function getIncludingFuncDeclNode(languageId: string, rootNode: SyntaxNode, range: Range): SyntaxNode {
  const queryStr = `
[
  (function_declaration) @func_decl
  (method_definition) @func_decl
  (generator_function_declaration) @func_decl
  (function) @func_decl
  (arrow_function) @func_decl
]
`;

  const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(languageId, queryStr, rootNode, positionToPoint(range.start), positionToPoint(range.end));

  const allDeclNodes: SyntaxNode[] = [];
  for (const match of matchResult) {
    const declNode = match.matches.get("func_decl");

    if (declNode.length === 1) {
      allDeclNodes.push(declNode[0]);
    }
  }

  return allDeclNodes.length > 0 ? allDeclNodes.sort((a, b) => a.startIndex - b.startIndex)[0] : undefined;
}

export function getRelativePath(uri: DocumentUri, prefix: string): string {
  return uri.slice(prefix.length).replace(/^\/+/, '');
}

function getClassTypeInfo(funcDeclNode: SyntaxNode, languageId: string, uri: DocumentUri): DefNodeInfo {
  const typeDeclNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(languageId, TreeSitterFunctionalityEnum.GetTypeParentNodeForNode, funcDeclNode);
  if (typeDeclNode) {
    return TreeSitterDispatcher.INSTANCE.dispatch(languageId, TreeSitterFunctionalityEnum.GenTypeInfoFromDefNode, uri, typeDeclNode);
  }

  return null;
}

export function getFuncNameNode(node: SyntaxNode): SyntaxNode {
  if (node.type === "function" || node.type === "arrow_function") {
    if (node.childForFieldName("name")?.type === "identifier") {
      return node.childForFieldName("name");
    } else if (node.parent?.type === "variable_declarator") {
      return node.parent.childForFieldName("name");
    }
  } else {
    return node.childForFieldName("name");
  }
  return null;
}

async function getUsedCodeInfos(uri: DocumentUri, node: SyntaxNode, languageId: string, workspaceFolderPath: string, requireStatements: string[], targetFuncFile: string, start: number): Promise<{data: (FuncContextInfo | VariableContextInfo)[], timeout: boolean}> {
  const result: (FuncContextInfo | VariableContextInfo)[] = [];

  const funcNameNodes = getUsedFuncNameNodes(node, languageId);
  const varNameNodes = getUsedOutsideVariableNameNodes(node, languageId);
  let indexFunc = 0;
  let indexVar = 0;
  while (indexFunc < funcNameNodes.length || indexVar < varNameNodes.length) {
    if (indexFunc < funcNameNodes.length && (indexVar === varNameNodes.length || funcNameNodes[indexFunc].startIndex < varNameNodes[indexVar].startIndex)) {
      const funcNameNode = funcNameNodes[indexFunc];
      const defNodeInfos: DefNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
        FunctionalityEnum.GetInvokeDefinition,
        languageId,
        uri,
        funcNameNode,
        inWorkspaceLocationFilter,
      );
      if (defNodeInfos && defNodeInfos.length === 1) {
        const defNodeInfo = defNodeInfos[0];

        if (defNodeInfo.uri !== uri || defNodeInfo.defNode.startIndex < node.startIndex || defNodeInfo.defNode.endIndex > node.endIndex) {
          if (!requireStatements.includes(defNodeInfo.defNode.text)) {
            const funcContextInfo = new FuncContextInfo(defNodeInfo.name, getRelativePath(defNodeInfo.uri, workspaceFolderPath), targetFuncFile);

            funcContextInfo.withBody(defNodeInfo.defNode.text);

            const classTypeInfo = getClassTypeInfo(defNodeInfo.defNode, defNodeInfo.languageId, defNodeInfo.uri);
            if (classTypeInfo) {
              funcContextInfo.withClassInfo(getTypeInfoFromDefNode(classTypeInfo, workspaceFolderPath, targetFuncFile));
            }

            result.push(funcContextInfo);
          }
        }

        const funcContextInfo = new FuncContextInfo(defNodeInfo.name, getRelativePath(defNodeInfo.uri, workspaceFolderPath), targetFuncFile);

        funcContextInfo.withBody(defNodeInfo.defNode.text);

        const classTypeInfo = getClassTypeInfo(defNodeInfo.defNode, defNodeInfo.languageId, defNodeInfo.uri);
        if (classTypeInfo) {
          funcContextInfo.withClassInfo(getTypeInfoFromDefNode(classTypeInfo, workspaceFolderPath, targetFuncFile));
        }
      }

      indexFunc++;
    } else if (indexVar < varNameNodes.length && (indexFunc === funcNameNodes.length || varNameNodes[indexVar].startIndex < funcNameNodes[indexFunc].startIndex)) {
      const varNameNode = varNameNodes[indexVar];
      const declNodeInfos: VarDeclNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
        FunctionalityEnum.GetVarDeclaration,
        languageId,
        uri,
        varNameNode,
        inWorkspaceLocationFilter,
      );
      if (declNodeInfos && declNodeInfos.length === 1) {
        const declNodeInfo = declNodeInfos[0];

        if (declNodeInfo.uri !== uri || declNodeInfo.declNode.startIndex < node.startIndex || declNodeInfo.declNode.endIndex > node.endIndex) {
          if (!requireStatements.includes(declNodeInfo.declNode.text)) {
            const variableContextInfo = new VariableContextInfo(declNodeInfo.name, getRelativePath(declNodeInfo.uri, workspaceFolderPath), targetFuncFile);

            variableContextInfo.withCode(declNodeInfo.declNode.text);

            result.push(variableContextInfo);
          }
        }
      }

      indexVar++;
    }

    if (Date.now() - start > getConfig("analysis.utgen.javascript.timeoutMs")) {
      return {data: result, timeout: true};
    }
  }

  return {data: result, timeout: false};
}

export function getUsedFuncNameNodes(node: SyntaxNode, languageId: string): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  const bodyNode = node.childForFieldName("body");
  if (bodyNode) {
    const queryStr = `
(call_expression) @invoke
`;

    const invokeMatchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(languageId, queryStr, bodyNode);

    for (const match of invokeMatchResult) {
      const invokeNodes = match.matches.get("invoke");

      if (invokeNodes.length === 1) {
        const nameName = getInvokeNameNode(invokeNodes[0].childForFieldName("function"));
        if (nameName) {
          result.push(nameName);
        }
      }
    }
  }

  return result;
}

function getInvokeNameNode(node: SyntaxNode): SyntaxNode {
  if (node) {
    switch (node.type) {
      case "identifier":
        return node;
      case "member_expression":
        return node.childForFieldName("property");
      default:
        return null;
    }
  }

  return null;
}

export function getUsedOutsideVariableNameNodes(node: SyntaxNode, languageId: string): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  const bodyNode = node.childForFieldName("body");
  if (bodyNode) {
    const excludeNames = [
      ...getUsedFuncNameNodes(node, languageId).map(item => item.text),
      ...getParameterNameNodes(node, languageId).map(item => item.text),
      ...getVariableDeclNameNodes(node, languageId).map(item => item.text),
      ...getAnonymousParameterNameNodes(node, languageId).map(item => item.text),
    ];

    const queryStr = `
[
  (identifier) @identifier
  (property_identifier) @identifier
]
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(languageId, queryStr, bodyNode);

    for (const match of matchResult) {
      const nameNodes = match.matches.get("identifier");

      if (nameNodes.length === 1 && !excludeNames.includes(nameNodes[0].text)) {
        result.push(nameNodes[0]);
      }
    }
  }

  return result;
}

export function getParameterNameNodes(node: SyntaxNode, languageId: string): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  const parametersNode = node.childForFieldName("parameters");
  if (parametersNode) {
    const queryStr = `
(formal_parameters
  [
    (identifier) @var_name
    (assignment_pattern
      left: (identifier) @var_name
    )
    (object_pattern
      [
        (shorthand_property_identifier_pattern) @var_name
        (object_assignment_pattern
          left: (shorthand_property_identifier_pattern) @var_name
        )
      ]
    )
    (rest_pattern
      (identifier) @var_name
    )
  ]
)
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(languageId, queryStr, parametersNode);

    for (const match of matchResult) {
      const nameNodes = match.matches.get("var_name");

      if (nameNodes.length === 1) {
        result.push(nameNodes[0]);
      }
    }
  }

  return result;
}

export function getVariableDeclNameNodes(node: SyntaxNode, languageId: string): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  const bodyNode = node.childForFieldName("body");
  if (bodyNode) {
    const queryStr = `
(variable_declarator
  name: (identifier) @var_name
)
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(languageId, queryStr, bodyNode);

    for (const match of matchResult) {
      const varNameNodes = match.matches.get("var_name");

      if (varNameNodes.length === 1) {
        result.push(varNameNodes[0]);
      }
    }
  }

  return result;
}

export function getAnonymousParameterNameNodes(node: SyntaxNode, languageId: string): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  const bodyNode = node.childForFieldName("body");
  if (bodyNode) {
    const queryStr = `
[
  (function
    parameters: (formal_parameters
      [
        (identifier) @var_name
        (assignment_pattern
          left: (identifier) @var_name
        )
        (object_pattern
          [
            (shorthand_property_identifier_pattern) @var_name
            (object_assignment_pattern
              left: (shorthand_property_identifier_pattern) @var_name
            )
          ]
        )
        (rest_pattern
          (identifier) @var_name
        )
      ]
    )
  )
  (arrow_function
    [
      parameter: (identifier) @var_name
      parameters: (formal_parameters
        [
          (identifier) @var_name
          (assignment_pattern
            left: (identifier) @var_name
          )
          (object_pattern
            [
              (shorthand_property_identifier_pattern) @var_name
              (object_assignment_pattern
                left: (shorthand_property_identifier_pattern) @var_name
              )
            ]
          )
          (rest_pattern
            (identifier) @var_name
          )
        ]
      )
    ]
  )
]
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(languageId, queryStr, bodyNode);

    for (const match of matchResult) {
      const nameNodes = match.matches.get("var_name");

      if (nameNodes.length === 1) {
        result.push(nameNodes[0]);
      }
    }
  }

  return result;
}

function getTypeInfoFromDefNode(defNodeInfo: DefNodeInfo, workspaceFolderPath: string, targetFuncFile: string): TypeContextInfo {
  const classContextInfo = new TypeContextInfo(defNodeInfo.name, getRelativePath(defNodeInfo.uri, workspaceFolderPath), targetFuncFile);

  const superClassNode = getSuperClassNode(defNodeInfo.defNode);
  if (superClassNode) {
    classContextInfo.withSuperClass(superClassNode.text);
  }

  classContextInfo.withFields(getFieldDefNodes(defNodeInfo.defNode).map(item => item.text));

  classContextInfo.withConstructors(getConstructorDefNodes(defNodeInfo.defNode).map(item => item.text));

  return classContextInfo;
}

export function getSuperClassNode(node: SyntaxNode): SyntaxNode {
  for (const child of node.namedChildren) {
    if (child.type === "class_heritage") {
      return child.namedChild(0);
    }
  }
  return null;
}

export function getFieldDefNodes(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  const bodyNode = node.childForFieldName("body");
  if (bodyNode) {
    for (const child of bodyNode.namedChildren) {
      if (child.type === "field_definition") {
        result.push(child);
      }
    }
  }

  return result;
}

export function getConstructorDefNodes(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  const bodyNode = node.childForFieldName("body");
  if (bodyNode) {
    for (const child of bodyNode.namedChildren) {
      if (child.type === "method_definition" && child.childForFieldName("name")?.text === "constructor") {
        result.push(child);
      }
    }
  }

  return result;
}

export async function getUsageInfos(uri: DocumentUri, funcNameNode: SyntaxNode, languageId: string): Promise<SyntaxNodeWithUri[]> {
  const result: SyntaxNodeWithUri[] = [];

  const usageNodes: UsageNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(FunctionalityEnum.GetUsage, languageId, uri, funcNameNode, inWorkspaceLocationFilter);
  for (const usageNode of usageNodes) {
    const funcParentNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(languageId, TreeSitterFunctionalityEnum.GetFuncParentNodeForNode, usageNode.usageNode);
    if (funcParentNode) {
      result.push({
        uri: usageNode.uri,
        node: funcParentNode,
      });
    }
  }

  return result;
}

function getUsageInfoFromUsageFuncParentNode(usageFuncParentNode: SyntaxNodeWithUri, workspaceFolderPath: string): UsageContextInfo {
  return new UsageContextInfo(getRelativePath(usageFuncParentNode.uri, workspaceFolderPath), usageFuncParentNode.node.text);
}