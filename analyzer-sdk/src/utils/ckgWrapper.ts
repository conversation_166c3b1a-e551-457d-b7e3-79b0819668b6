import {CK<PERSON>} from "@byted-ckg/client";
import {CKGClient} from "@byted-ckg/client/lib/api/client";

export class CKGWrapper {
  private static ckg: CKG;
  private static ckgClient: CKGClient;

  static setCKG(ckg: CKG): void {
    CKGWrapper.ckg = ckg;
  }

  static getCKGInstance(): CKG {
    return CKGWrapper.ckg;
  }

  static async setCKGClient(ckgClient: CKGClient): Promise<void> {
    CKGWrapper.ckgClient = ckgClient;
  }

  static getCKGClient(): CKGClient {
    return CKGWrapper.ckgClient;
  }
}