package data_storage

import (
	"context"
	"crypto/sha256"
	"database/sql"
	"fmt"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/atomic"
	"gorm.io/gorm"
	"ide/ckg/codekg/components/data_storage/consts"
	vec "ide/ckg/codekg/components/data_storage/sqlite_vec"
	"ide/ckg/codekg/components/data_storage/user_storage"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/util"
	"os"
	"path/filepath"
)

import "C"

// SQLiteVecStorage is implemented for chromem-go.
type SQLiteVecStorage struct {
	db        *sql.DB
	projectID model.URI

	isDeleted      *atomic.Bool
	dbFilePath     string
	embeddingModel string
}

const (
	SQLITE   = "sqlite3"
	DBSchema = `CREATE VIRTUAL TABLE IF NOT EXISTS vec_items USING vec0(
   	embedding float[%d] distance_metric=cosine,
    entityId text,
    entity_type text,
	rel_folder_path text,
    ext text,
)`
)

var (
	dimMap = map[string]int{
		"default-latest": 1024,
		"codekg_bge_m3":  1024,
		"codekg_oasis":   1536,
	}
)

// CreateSQLiteVecStorage 的 forceNew 表示强制创建新 db
func CreateSQLiteVecStorage(ctx context.Context, es *user_storage.UserStorage, storagePath model.URI, projectID model.URI,
	embeddingModel string, t consts.EmbeddingStorageType, forceNew bool) (*SQLiteVecStorage, error) {
	var (
		db  *sql.DB
		err error
	)
	if t != consts.EmbeddingStorage_SQLiteVec {
		return nil, errors.Errorf("[CreateSQLiteVecStorage] invalid embedding storage type: %s", t)
	}

	vec.Auto()
	dbFilePath := ""
	if len(storagePath) == 0 {
		db, err = sql.Open(SQLITE, "file::memory:")
	} else {
		dbFilePath, err = es.GetEmbeddingStorageFromUserStorage(ctx, projectID, t)
		dbFileInfo, err1 := file_system.RealFS.Stat(dbFilePath) // sqlite 在 Open 时不会因 dbFilePath 是 folder 而报错，需要手动检查。
		if forceNew || err != nil || err1 != nil || dbFileInfo.IsDir() || len(dbFilePath) == 0 {
			projectName := filepath.Base(projectID)
			sum := sha256.Sum256([]byte(projectID))
			randStr := util.GetRandStr(6)
			dbFilePath = filepath.Join(storagePath, fmt.Sprintf("%s_%x_%s_embedding_vec.db", projectName, sum[:7], randStr))
			logs.CtxInfo(ctx, "[CreateSQLiteVecStorage] embedding db not exist for project %s, new db path is %s", projectID, dbFilePath)
		}
		// 创建的 db file path 会在 Init 流程完全结束后写入
		db, err = sql.Open(SQLITE, dbFilePath)
	}
	if err != nil {
		logs.CtxError(ctx, "[CreateSQLiteVecStorage] open db failed", err)
		return nil, errors.WithMessagef(err, "[CreateSQLiteVecStorage] open db failed")
	}
	db.SetMaxOpenConns(1)

	// 初始化向量数据库表
	var dim int
	var ok bool
	if dim, ok = dimMap[embeddingModel]; !ok {
		dim = 1024
	}
	_, err = db.Exec(fmt.Sprintf(DBSchema, dim)) // ignore_security_alert SQL_INJECTION
	if err != nil {
		logs.CtxError(ctx, "[CreateSQLiteVecStorage] create vec table failed", err)
		return nil, errors.WithMessagef(err, "[CreateSQLiteVecStorage] create vec table failed")
	}
	return &SQLiteVecStorage{
		db:             db,
		projectID:      projectID,
		isDeleted:      atomic.NewBool(false),
		dbFilePath:     dbFilePath,
		embeddingModel: embeddingModel,
	}, nil
}

func (svs *SQLiteVecStorage) Delete() error {
	svs.isDeleted.Store(true)

	if sqlDB := svs.db; sqlDB == nil {
		return errors.New("db is nil")
	}
	if err := svs.db.Close(); err != nil {
		return err
	}
	// remove db file
	if err := os.Remove(svs.dbFilePath); err != nil {
		return err
	}
	return nil
}

func (svs *SQLiteVecStorage) IsExistsOnDisk() bool {
	_, err := file_system.RealFS.Stat(svs.dbFilePath)
	return err == nil
}

func (svs *SQLiteVecStorage) IsDeleted() bool           { return svs.isDeleted.Load() }
func (svs *SQLiteVecStorage) GetStoragePath() string    { return svs.dbFilePath }
func (svs *SQLiteVecStorage) GetType() string           { return consts.EmbeddingStorage_SQLiteVec }
func (svs *SQLiteVecStorage) GetEmbeddingModel() string { return svs.embeddingModel }
func (svs *SQLiteVecStorage) GetVersion() consts.StorageVersion {
	return consts.StorageVersionV1
}

func (svs *SQLiteVecStorage) AddEmbeddingDocument(ctx context.Context, doc *model.EmbeddingDocument, option AddEmbeddingDocumentOptions) error {
	panic("No need to implement temporarily")
}

func (svs *SQLiteVecStorage) AddEmbeddingDocuments(ctx context.Context, docs []*model.EmbeddingDocument, option AddEmbeddingDocumentOptions) error {
	const InsertStmt = "INSERT INTO vec_items (embedding, entityId, entity_type, rel_folder_path, ext) VALUES (?, ?, ?, ?, ?)"
	tx, err := svs.db.BeginTx(ctx, &sql.TxOptions{ReadOnly: false, Isolation: sql.LevelSerializable})
	if err != nil {
		logs.CtxError(ctx, "[AddEmbeddingDocuments] begin tx failed", err)
		return errors.WithMessagef(err, "[AddEmbeddingDocuments] begin tx failed")
	}
	defer func() {
		if p := recover(); p != nil {
			_ = tx.Rollback()
		} else {
			if err := tx.Commit(); err != nil {
				logs.CtxError(ctx, "[AddEmbeddingDocuments] commit tx failed", err)
			}
		}
	}()
	for _, doc := range docs {
		var folderRelPath, ext, entityId, entityType string
		var ok bool
		if folderRelPath, ok = doc.Metadata[string(model.EMK_RelPath)].(string); !ok {
			logs.CtxError(ctx, "[AddEmbeddingDocuments] folder rel path is nil")
			return errors.Errorf("folder rel path is nil. metadata: %v", doc.Metadata)
		}
		if ext, ok = doc.Metadata[string(model.EMK_Ext)].(string); !ok {
			logs.CtxError(ctx, "[AddEmbeddingDocuments] file ext is nil")
			return errors.Errorf("file ext is nil. metadata: %v", doc.Metadata)
		}
		if entityId, ok = doc.Metadata[string(model.EMK_EntityID)].(string); !ok {
			logs.CtxError(ctx, "[AddEmbeddingDocument] entity id is nil")
			return errors.Errorf("entity id is nil. metadata: %v", doc.Metadata)
		}
		if entityType, ok = doc.Metadata[string(model.EMK_EntityType)].(string); !ok {
			logs.CtxError(ctx, "[AddEmbeddingDocument] entity type is nil")
			return errors.Errorf("entity type is nil. metadata: %v", doc.Metadata)
		}
		q, err := vec.SerializeFloat32(doc.Embedding.Embedding)
		if err != nil {
			logs.CtxError(ctx, "[AddEmbeddingDocuments] serialize float32 failed", err)
			return errors.WithMessagef(err, "[AddEmbeddingDocuments] serialize float32 failed")
		}
		_, err = tx.Exec(InsertStmt, q, entityId, entityType, folderRelPath, ext)
		if err != nil {
			logs.CtxError(ctx, "[AddEmbeddingDocument] insert vec items failed", err)
			return errors.WithMessagef(err, "[AddEmbeddingDocument] insert vec items failed")
		}
	}
	return nil
}

func (svs *SQLiteVecStorage) GetById(ctx context.Context, id, collectionName string) (*model.EmbeddingDocument, error) {
	panic("No need to implement temporarily")
}

func (svs *SQLiteVecStorage) QueryByEmbedding(ctx context.Context, query *model.Embedding, nResults int, option EmbeddingQueryOptions) ([]*model.EmbeddingQueryResult, error) {
	const InitialQueryStmt = "SELECT rowid, entityId, entity_type, rel_folder_path, ext, distance, embedding FROM vec_items WHERE embedding match ? "
	queryStmt := InitialQueryStmt
	// 处理 metadata
	if option.WhereMetadata != nil {
		if relFolderPaths, ok := option.WhereMetadata[string(model.EMK_RelPath)].([]string); ok && len(relFolderPaths) > 0 {
			// 将 relFolderPaths 连接起来，方便 raw SQL 拼接
			concatFolderPaths := lo.Reduce(relFolderPaths, func(acc string, relFolderPath string, i int) string {
				if i == 0 {
					return fmt.Sprintf("'%s'", relFolderPath)
				}
				return fmt.Sprintf("%s, '%s'", acc, relFolderPath)
			}, "")
			queryStmt += fmt.Sprintf(" AND rel_folder_path IN (%s)", concatFolderPaths)
		}
		if ext, ok := option.WhereMetadata[string(model.EMK_Ext)].(string); ok {
			queryStmt += fmt.Sprintf(" AND ext = '%s'", ext)
		}
		if entityTypes, ok := option.WhereMetadata[string(model.EMK_EntityType)].([]model.EntityType); ok && len(entityTypes) > 0 {
			// 将 expectTypes 连接起来，方便 raw SQL 拼接
			concatExpectTypes := lo.Reduce(entityTypes, func(acc string, expectType model.EntityType, i int) string {
				if i == 0 {
					return fmt.Sprintf("'%s'", expectType)
				}
				return fmt.Sprintf("%s, '%s'", acc, expectType)
			}, "")
			queryStmt += fmt.Sprintf(" AND entity_type IN (%s)", concatExpectTypes)
		}
		if entityIds, ok := option.WhereMetadata[string(model.EMK_EntityID)].([]string); ok && len(entityIds) > 0 {
			concatEntityIds := lo.Reduce(entityIds, func(acc string, entityId string, i int) string {
				if i == 0 {
					return fmt.Sprintf("'%s'", entityId)
				}
				return fmt.Sprintf("%s, '%s'", acc, entityId)
			}, "")
			queryStmt += fmt.Sprintf(" AND entityId IN (%s)", concatEntityIds)
		}
	}
	q, err := vec.SerializeFloat32(query.Embedding)
	if err != nil {
		logs.CtxError(ctx, "[QueryByEmbedding] serialize float32 failed", err)
		return nil, errors.WithMessagef(err, "[QueryByEmbedding] serialize float32 failed")
	}
	queryStmt += " ORDER BY distance LIMIT ?"
	// 执行向量查询
	rows, err := svs.db.Query(queryStmt, q, nResults)
	if err != nil {
		logs.CtxError(ctx, "[QueryByEmbedding] query vec items failed", err)
		return nil, errors.WithMessagef(err, "[QueryByEmbedding] query vec items failed")
	}
	results := make([]*model.EmbeddingQueryResult, 0)
	for rows.Next() {
		var id int
		var entityId, relFolderPath, ext string
		var entityType model.EntityType
		var distance float32
		var embedding []uint8
		err := rows.Scan(&id, &entityId, &entityType, &relFolderPath, &ext, &distance, &embedding)
		if err != nil {
			logs.CtxError(ctx, "[QueryByEmbedding] scan vec items failed", err)
			return nil, errors.WithMessagef(err, "[QueryByEmbedding] scan vec items failed")
		}
		embeddingFloat32, err := vec.DeserializeFloat32(embedding)
		if err != nil {
			logs.CtxError(ctx, "[QueryByEmbedding] deserialize float32 failed", err)
			return nil, errors.WithMessagef(err, "[QueryByEmbedding] deserialize float32 failed")
		}
		res := ConvertSQLiteVecResultToEmbeddingQueryResult(entityId, entityType, relFolderPath, ext, 1-distance, embeddingFloat32)
		results = append(results, res)
	}

	results = lo.UniqBy(results, func(res *model.EmbeddingQueryResult) string { return res.ID })
	return results, nil
}

func (svs *SQLiteVecStorage) DeleteDocumentsByUriMeta(ctx context.Context, uriMeta *StorageURIMeta, option DeleteDocumentOptions) error {
	panic("No need to implement temporarily")
}

func (svs *SQLiteVecStorage) DeleteDocumentsByURI(ctx context.Context, file *model.URIStatus, option DeleteDocumentOptions) error {
	panic("No need to implement temporarily")
}

// DeleteDocumentsByIds SQLite-vec 不用这个功能
func (svs *SQLiteVecStorage) DeleteDocumentsByIds(ctx context.Context, option DeleteDocumentOptions, ids ...string) error {
	return nil
}

func (svs *SQLiteVecStorage) DeleteDocumentsByEntity(ctx context.Context, entities []StorageEntity, storage Storage, tx *gorm.DB, option DeleteDocumentOptions) error {
	entityIds := lo.Map(entities, func(entity StorageEntity, _ int) any { return entity.EntityID })
	concatQuestionMarks := lo.Reduce(entityIds, func(acc string, id any, i int) string {
		if i == 0 {
			return "?"
		}
		return fmt.Sprintf("%s, ?", acc)
	}, "")
	deleteStmt := fmt.Sprintf("DELETE FROM vec_items WHERE entityId IN (%s)", concatQuestionMarks)
	if _, err := svs.db.Exec(deleteStmt, entityIds...); err != nil {
		logs.CtxError(ctx, "[DeleteDocumentsByEntity] delete vec items failed, err: %v", err)
		return errors.WithMessagef(err, "[DeleteDocumentsByEntity] delete vec items failed")
	}
	return nil
}

func (svs *SQLiteVecStorage) DeleteDocumentsByMetadata(ctx context.Context, whereMetadata map[string]any, option DeleteDocumentOptions) error {
	panic("No need to implement temporarily")
}

func (svs *SQLiteVecStorage) EmbeddingDocumentCount(ctx context.Context) int {
	rows, err := svs.db.Query("SELECT COUNT(*) from vec_items")
	if err != nil {
		logs.CtxError(ctx, "[EmbeddingDocumentCount] get vec items rowids failed", err)
		return 0
	}
	var cnt int
	for rows.Next() {
		err := rows.Scan(&cnt)
		if err != nil {
			logs.CtxError(ctx, "[EmbeddingDocumentCount] scan vec items rowids failed", err)
			return 0
		}
	}
	return cnt
}

func ConvertSQLiteVecResultToEmbeddingQueryResult(entityId string, entityType model.EntityType, relFolderPath, ext string, similarity float32, embedding []float32) *model.EmbeddingQueryResult {
	metadata := make(map[string]any)
	metadata[string(model.EMK_RelPath)] = relFolderPath
	metadata[string(model.EMK_Ext)] = ext
	metadata[string(model.EMK_EntityType)] = entityType
	return &model.EmbeddingQueryResult{
		EmbeddingDocument: model.EmbeddingDocument{
			ID:        entityId,
			Content:   "",
			Embedding: &model.Embedding{Embedding: embedding},
			Metadata:  metadata,
		},
		Score: similarity,
	}
}
