package git_graph

import (
	"context"
	"fmt"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/env"
	"ide/ckg/codekg/components/validator"
	"math"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"sort"
	"strings"
	"time"
	"unsafe"

	"ide/ckg/codekg/components/logs"
	"github.com/mohae/deepcopy"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/pkg/errors"
)

// 添加性能监控结构
type PerformanceStats struct {
	startTime time.Time
	memStats  runtime.MemStats
}

func NewPerformanceStats() *PerformanceStats {
	return &PerformanceStats{
		startTime: time.Now(),
	}
}

func (ps *PerformanceStats) PrintStats(phase string) {
	runtime.ReadMemStats(&ps.memStats)
	fmt.Printf("\n=== Performance Stats (%s) ===\n", phase)
	fmt.Printf("Time elapsed: %v\n", time.Since(ps.startTime))

	// 当前实际内存使用情况
	fmt.Printf("\nCurrent Memory Usage:\n")
	fmt.Printf("- HeapAlloc (当前堆内存使用): %.2f MB\n", float64(ps.memStats.HeapAlloc)/1024/1024)
	fmt.Printf("- HeapInuse (当前正在使用的堆内存): %.2f MB\n", float64(ps.memStats.HeapInuse)/1024/1024)
	//
	//// 历史内存分配情况
	//fmt.Printf("\nMemory Allocation History:\n")
	//fmt.Printf("- TotalAlloc (累计分配): %.2f MB\n", float64(ps.memStats.TotalAlloc)/1024/1024)
	//fmt.Printf("- Mallocs (内存分配次数): %d\n", ps.memStats.Mallocs)
	//fmt.Printf("- Frees (内存释放次数): %d\n", ps.memStats.Frees)
	//
	//// 系统内存情况
	//fmt.Printf("\nSystem Memory:\n")
	//fmt.Printf("- Sys (系统分配): %.2f MB\n", float64(ps.memStats.Sys)/1024/1024)
	//fmt.Printf("- HeapSys (堆系统分配): %.2f MB\n", float64(ps.memStats.HeapSys)/1024/1024)
	//fmt.Printf("- HeapIdle (堆空闲): %.2f MB\n", float64(ps.memStats.HeapIdle)/1024/1024)
	//fmt.Printf("- HeapReleased (释放回系统): %.2f MB\n", float64(ps.memStats.HeapReleased)/1024/1024)
	//
	//// 其他统计
	//fmt.Printf("\nOther Stats:\n")
	//fmt.Printf("- Number of goroutines: %d\n", runtime.NumGoroutine())
	//fmt.Printf("- GC cycles: %d\n", ps.memStats.NumGC)
	//fmt.Printf("- Next GC target: %.2f MB\n", float64(ps.memStats.NextGC)/1024/1024)
	fmt.Printf("===========================\n\n")
}

type GitGraphNode struct {
	filePath       string  // 文件路径作为 ID
	Importance     float64 // 重要性得分
	Content        string  // 文件内容（动态读取）
	lastModifyTime int64   // 最后修改时间
	//commitHeat     int32   // 提交热度
	//pageRank       float64 // PageRank 得分
}

type GitGraphEdge struct {
	weight float64 // 两个文件同时提交的次数
}

type GitGraph struct {
	repoPath    string // 仓库的根目录
	hasAnalyzed bool   // 是否已经完成初始化分析

	graph *Graph[*GitGraphNode, *GitGraphEdge] // 文件共同提交构建的图

	hotNodeCache            []*GitGraphNode   // 缓存上一次 GetHotFiles 的结果
	renameMap               map[string]string // 记录重命名的文件，key 为旧路径，value 为新路径，graph中都使用新路径
	recentModifiedFiles     []string          // 记录最近修改的文件
	recentModifiedFilesTime int64             // 记录最近修改的文件时间
}

type BuildGitGraphOption struct {
	CommitLimit       int  // 分析 Commit 的数量
	MaxCommitSize     int  // 每个 commit 最大的文件数量，过大的 commit 直接忽略
	MinCommitSize     int  // 每个 commit 最小的文件数量，过小的 commit 直接忽略
	CurrentUserOnly   bool // 只分析当前用户提交的文件
	MinChangeSize     int  // 每个文件最小的改动数量，过小的改动直接忽略
	MaxChangeSize     int  // 每个文件最大的改动数量，过大的改动直接忽略
	MaxGraphNodeCount int  // 最大节点数量
	MaxGraphEdgeCount int  // 最大边数量
}

const (
	UpdateRecentModifiedFilesInterval = 20 * 60 // 每 20 分钟更新一次最近修改的文件
	GitFileSizeLimit                  = 512 * 1024
)

func (g *GitGraph) PrintGraphSize() {
	// 内存大小
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	fmt.Printf("\n=== Git Graph Statistics ===\n")

	// 基本结构大小
	gitGraphSize := unsafe.Sizeof(*g)
	fmt.Printf("GitGraph struct size: %d bytes\n", gitGraphSize)

	// 图结构详细信息
	nodeCount := len(g.getAllNodes())

	// 计算节点实际占用的内存
	var totalNodeSize uintptr = 0
	for _, node := range g.getAllNodes() {
		nodeSize := unsafe.Sizeof(*node)
		totalNodeSize += nodeSize
	}

	// 计算重命名映射的内存
	var renameMapSize uintptr = 0
	for oldPath, newPath := range g.renameMap {
		renameMapSize += uintptr(len(oldPath) + len(newPath))
	}

	// 计算最近修改文件列表的内存
	var recentFilesSize uintptr = 0
	for _, file := range g.recentModifiedFiles {
		recentFilesSize += uintptr(len(file))
	}

	fmt.Printf("\nGraph Structure:\n")
	fmt.Printf("- Nodes: %d (%.2f MB)\n", nodeCount, float64(totalNodeSize)/1024/1024)
	fmt.Printf("- Recent Files: %d (%.2f MB)\n", len(g.recentModifiedFiles), float64(recentFilesSize)/1024/1024)

	// 总内存使用
	// 实际运行时内存统计
	fmt.Printf("\nRuntime Memory Stats:\n")
	fmt.Printf("- Alloc: %.2f MB\n", float64(m.Alloc)/1024/1024)
	fmt.Printf("- TotalAlloc: %.2f MB\n", float64(m.TotalAlloc)/1024/1024)
	fmt.Printf("- Sys: %.2f MB\n", float64(m.Sys)/1024/1024)
	fmt.Printf("- HeapAlloc: %.2f MB\n", float64(m.HeapAlloc)/1024/1024)
	fmt.Printf("- HeapSys: %.2f MB\n", float64(m.HeapSys)/1024/1024)
	fmt.Printf("- HeapInuse: %.2f MB\n", float64(m.HeapInuse)/1024/1024)
	fmt.Printf("- HeapIdle: %.2f MB\n", float64(m.HeapIdle)/1024/1024)
	fmt.Printf("- HeapReleased: %.2f MB\n", float64(m.HeapReleased)/1024/1024)
	fmt.Printf("- HeapObjects: %d\n", m.HeapObjects)
	fmt.Printf("- NumGC: %d\n", m.NumGC)

}

func (n *GitGraphNode) getID() string {
	return n.filePath
}

func (n *GitGraphNode) getContent() string {
	// 如果内容为空，读取文件内容
	if n.Content == "" {
		content, err := os.ReadFile(n.filePath)
		if err != nil {
			logs.CtxError(context.Background(), "failed to read git-graph file: %v", err)
			return ""
		}
		n.Content = string(content)
	}
	return n.Content
}

func (e *GitGraphEdge) setWeight(weight float64) {
	e.weight = weight
}

func (e *GitGraphEdge) getWeight() float64 {
	return e.weight
}

func NewGitGraph(repo string) (*GitGraph, error) {
	// 检查路径是否存在
	if _, err := os.Stat(repo); os.IsNotExist(err) {
		return nil, errors.Errorf("repository %s does not exist", repo)
	}
	// 是否可以获取仓库对象
	_, err := git.PlainOpen(repo)
	if err != nil {
		return nil, err
	}

	return &GitGraph{
		graph:                   NewGraph[*GitGraphNode, *GitGraphEdge](),
		repoPath:                repo,
		hasAnalyzed:             false,
		renameMap:               make(map[string]string),
		recentModifiedFiles:     make([]string, 0),
		recentModifiedFilesTime: 0,
	}, nil
}

func (g *GitGraph) getOrCreateNode(filePath string, lastModifyTime int64) *GitGraphNode {
	// 检查是否已经存在
	node := g.graph.GetNode(filePath)
	if node != nil {
		if node.lastModifyTime < lastModifyTime {
			node.lastModifyTime = lastModifyTime
		}
		return node
	}
	// 检查下filePath是否还在磁盘上
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil
	}
	// 不存在则创建新节点
	newNode := &GitGraphNode{
		filePath:       filePath,
		Importance:     0,
		Content:        "",
		lastModifyTime: lastModifyTime,
	}
	g.graph.AddNode(newNode)
	return newNode
}

func (g *GitGraph) getHotGitNodes(TopK int) ([]*GitGraphNode, error) {
	if len(g.hotNodeCache) > 0 {
		return g.hotNodeCache, nil
	}
	hotNodes := make([]*GitGraphNode, 0, TopK)
	defer func() {
		g.hotNodeCache = hotNodes
	}()

	nodes, err := g.getImportantNodes(TopK)
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, errors.New("no important nodes found")
	}

	return nodes, nil
}

func (g *GitGraph) getImportantNodes(topK int) ([]*GitGraphNode, error) {
	// 对 hotNodes 进行排序
	allNodes := g.getAllNodes()
	sort.Slice(allNodes, func(i, j int) bool {
		return allNodes[i].Importance > allNodes[j].Importance
	})

	// 取前 TopK 个节点
	if len(allNodes) > topK {
		allNodes = allNodes[:topK]
	}

	return allNodes, nil
}

func (g *GitGraph) getAllNodes() []*GitGraphNode {
	nodes := make([]*GitGraphNode, 0, len(g.graph.nodes))
	for _, node := range g.graph.nodes {
		nodes = append(nodes, node)
	}
	return nodes
}

func (g *GitGraph) getRecentModifiedFiles(TopK int) (float64, error) {
	timeNow := time.Now()
	if timeNow.Unix()-g.recentModifiedFilesTime < UpdateRecentModifiedFilesInterval {
		return 0, nil
	}

	// 从 Staged 中获取最近修改的文件
	start := time.Now()
	repo, err := git.PlainOpen(g.repoPath)
	if err != nil {
		return 0, fmt.Errorf("failed to get repository: %w", err)
	}
	worktree, err := repo.Worktree()
	if err != nil {
		return 0, fmt.Errorf("failed to get worktree: %w", err)
	}

	// 这里有性能问题
	status, err := worktree.StatusWithOptions(git.StatusOptions{Strategy: 0})
	if err != nil {
		return 0, fmt.Errorf("failed to get status: %w", err)
	}
	logs.CtxInfo(context.Background(), "get status time: %v", time.Since(start))

	// 收集最近修改的（staged 和 unstaged）的文件
	stagedFiles := make([]string, 0)
	unstagedFiles := make([]string, 0)
	for file, fileStatus := range status {
		fullPath := filepath.Join(g.repoPath, file)
		if fileStatus.Staging != git.Unmodified {
			stagedFiles = append(stagedFiles, fullPath)
		} else {
			unstagedFiles = append(unstagedFiles, fullPath)
		}
	}

	modifiedFiles := deepcopy.Copy(stagedFiles).([]string)
	modifiedFiles = append(modifiedFiles, unstagedFiles...)

	if len(modifiedFiles) == 0 {
		return time.Since(start).Seconds(), nil
	}

	if g.hasAnalyzed {
		// 使用 Importance 排序
		sort.Slice(modifiedFiles, func(i, j int) bool {
			nodeI := g.graph.GetNode(modifiedFiles[i])
			nodeJ := g.graph.GetNode(modifiedFiles[j])
			nodeIImportance := 0.0
			nodeJImportance := 0.0
			if nodeI != nil {
				nodeIImportance = nodeI.Importance
			}
			if nodeJ != nil {
				nodeJImportance = nodeJ.Importance
			}
			return nodeIImportance > nodeJImportance
		})
	}
	logs.CtxInfo(context.Background(), "rank files time: %v", time.Since(start))
	if len(modifiedFiles) > TopK {
		modifiedFiles = modifiedFiles[:TopK]
	}

	// 更新缓存
	g.recentModifiedFilesTime = timeNow.Unix()
	g.recentModifiedFiles = modifiedFiles

	return time.Since(start).Seconds(), nil
}

func (g *GitGraph) shouldIgnoreCommit(stats object.FileStats, option *BuildGitGraphOption) bool {
	// 变化的文件数量太多或者太少，忽略
	if len(stats) > option.MaxCommitSize || len(stats) < option.MinCommitSize {
		return true
	}

	return false
}

func (g *GitGraph) analyzeCommit(commit *object.Commit, options *BuildGitGraphOption) ([]*GitGraphNode, error) {
	stats, err := commit.Stats()
	if err != nil {
		return nil, err
	}

	// 判断是否忽略这个 commit
	ignore := g.shouldIgnoreCommit(stats, options)
	if ignore {
		return nil, errors.New("ignore commit")
	}

	// 分析 commit，选出所有需要添加边的节点
	curNodes := make([]*GitGraphNode, 0)

	for _, stat := range stats {
		// 处理重构的时候Git重命名情况，永远使用最新的文件来构建关系
		names := strings.Split(stat.Name, " => ")
		var currentPath string
		if len(names) == 2 {
			oldPath, newPath := names[0], names[1]
			oldPath = path.Join(g.repoPath, oldPath)
			newPath = path.Join(g.repoPath, newPath)
			_, ok := g.renameMap[newPath]
			// 全部映射到最新的文件
			if ok {
				g.renameMap[oldPath] = g.renameMap[newPath]
			} else {
				g.renameMap[oldPath] = newPath
			}
			currentPath = g.renameMap[oldPath]
		} else {
			currentPath = stat.Name
			currentPath = path.Join(g.repoPath, currentPath)
		}

		if g.shouldIgnoreFile(currentPath, stat, commit, options) {
			continue
		}
		nodePath := g.getLatestPath(currentPath)
		node := g.getOrCreateNode(nodePath, commit.Author.When.Unix())
		if node == nil {
			logs.CtxWarn(context.Background(), "failed to create node for file: %s", currentPath)
			continue
		}

		// exists 判断
		existsNode := false
		for _, n := range curNodes {
			if n.filePath == nodePath {
				existsNode = true
				break
			}
		}

		if !existsNode {
			curNodes = append(curNodes, node)
		}
	}

	// 确保 node 不会重复

	//g.PrintGraphSize()
	return curNodes, nil
}

// 获取文件的最新路径
func (g *GitGraph) getLatestPath(path string) string {
	if _, ok := g.renameMap[path]; ok {
		return g.renameMap[path]
	}
	return path
}

// 检查是否应该忽略文件
func (g *GitGraph) shouldIgnoreFile(fullPath string, stat object.FileStat, commit *object.Commit, options *BuildGitGraphOption) bool {
	// 检查文件是否存在
	if _, err := os.Stat(fullPath); err != nil {
		if !os.IsNotExist(err) {
			logs.CtxWarn(context.Background(), "failed to check file existence: %v", err)
		}
		return true
	}

	// 检查文件类型
	if !validator.IsValidGitFile(context.Background(), fullPath, 512*1024) {
		return true
	}

	// 过滤改动太多或者太小的文件
	changeLines := stat.Addition + stat.Deletion
	if changeLines < options.MinChangeSize || changeLines > options.MaxChangeSize {
		return true
	}

	return false
}

func (g *GitGraph) printGraph(ctx context.Context) {
	if g == nil || g.graph == nil {
		return
	}

	logs.CtxInfo(ctx, "graph nodes: %d, graph edges: %d", g.graph.NodeCount(), g.graph.EdgeCount())
}

func (g *GitGraph) commitUpdate(commit *object.Commit, option *BuildGitGraphOption) error {
	// 获取有效的节点
	nodes, err := g.analyzeCommit(commit, option)
	if err != nil {
		return err
	}

	// 计算边权重
	nodeWeight := calculateChangeWeight(commit)

	// 更新边权重
	for i := 0; i < len(nodes); i++ {
		nodeA := nodes[i]
		for j := i + 1; j < len(nodes); j++ {
			nodeB := nodes[j]
			g.graph.AddEdge(nodeA, nodeB, &GitGraphEdge{weight: nodeWeight})
			g.graph.AddEdge(nodeB, nodeA, &GitGraphEdge{weight: nodeWeight})
		}
	}

	return nil
}

func calculateChangeWeight(commit *object.Commit) float64 {
	// TODO: 可以改成俩俩之间计算权重，考虑文件的层级关系，修改次数
	// 目前使用时间权重， Commit 约近，权重越大，每个月 * 0.9
	commitTime := commit.Author.When.Unix()
	now := time.Now().Unix()
	timeWeight := math.Pow(0.9, float64(now-commitTime)/2592000)
	weight := timeWeight

	return weight
}

func (g *GitGraph) calculateImportance() {
	startTime := time.Now()
	scores := g.graph.PageRank()
	endTime := time.Now()
	logs.CtxInfo(context.Background(), "calculate git-graph importance cost %v", endTime.Sub(startTime))
	for _, score := range scores {
		score.node.Importance = score.score
	}
}

func (g *GitGraph) BuildGitGraph(ctx context.Context, userID string, option *BuildGitGraphOption) error {
	reportEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGGitGraph, userID, "", string(env.GetSourceProduct()))
	defer reportEvent.Report(ctx, false)

	repo, _ := git.PlainOpen(g.repoPath)
	ref, err := repo.Head()
	if err != nil {
		reportEvent.AddParam(ckg_metrics.TeaParamGitGraphBuildSuccess, false)
		return err
	}

	startTime := time.Now()

	go func() {
		_, err := g.getRecentModifiedFiles(10)
		if err != nil {
			logs.CtxError(ctx, "get recent modified files error: %v", err)
		}
	}()

	commits, err := repo.Log(&git.LogOptions{From: ref.Hash()})
	if err != nil {
		reportEvent.AddParam(ckg_metrics.TeaParamGitGraphBuildSuccess, false)
		return err
	}

	//userEmail := ""
	//cfg, err := repo.Config()
	//if err != nil {
	//	return err
	//}
	//userEmail = cfg.User.Email
	//if userEmail == "" {
	//	if runtime.GOOS != "windows" {
	//		cmd := exec.Command("git", "config", "--global", "--get", "user.email")
	//		if emailBytes, err := cmd.Output(); err == nil {
	//			userEmail = strings.TrimSpace(string(emailBytes))
	//		}
	//	}
	//}

	//errorCommitLimit := errors.New("commit limit")
	count := 0
	ignoreCount := 0

	logs.CtxInfo(ctx, "start to analyze commits for repository %s", g.repoPath)

	_ = commits.ForEach(func(c *object.Commit) error {
		err := g.commitUpdate(c, option)
		if err != nil {
			if strings.Contains(err.Error(), "ignore commit") {
				ignoreCount++
			}
		}
		count++

		// Commit 最大数量
		if option.CommitLimit != 0 && count >= option.CommitLimit {
			return errors.New("reach max commit limit")
		}

		// 节点数量超过最大限制，边数量超过最大限制
		if g.graph.NodeCount() >= option.MaxGraphNodeCount || g.graph.EdgeCount() >= option.MaxGraphEdgeCount {
			logs.CtxInfo(ctx, "node count or edge count reach max limit, stop analyze")
			return errors.New("reach max node or edge limit")
		}

		//option.PerformanceStats.PrintStats("Commit ForEach")
		return nil
	})
	commits.Close()
	runtime.GC()

	// 打印边数量、节点数量
	logs.CtxInfo(ctx, "%d commits processed, ignore %d commits", count, ignoreCount)
	logs.CtxInfo(ctx, "graph edges num: %d, graph nodes num: %d", g.graph.EdgeCount(), g.graph.NodeCount())

	// 计算节点权重
	g.calculateImportance()
	g.hasAnalyzed = true

	// 打印分析时间
	analysisTime := time.Since(startTime)
	logs.CtxInfo(ctx, "git-graph analysis time: %v", analysisTime)
	reportEvent.AddParam(ckg_metrics.TeaParamGitGraphBuildSuccess, true)
	reportEvent.AddParam(ckg_metrics.TeaParamGitGraphCommitCount, count)
	reportEvent.AddParam(ckg_metrics.TeaParamGitGraphIgnoreCommitCount, ignoreCount)
	reportEvent.AddParam(ckg_metrics.TeaParamGitGraphAnalysisTime, analysisTime.Seconds())
	reportEvent.AddParam(ckg_metrics.TeaParamGitGraphNodeCount, g.graph.NodeCount())
	reportEvent.AddParam(ckg_metrics.TeaParamGitGraphEdgeCount, g.graph.EdgeCount())

	return nil
}

func (g *GitGraph) GetRelationScores(files []string) map[string]map[string]float64 {
	if !g.hasAnalyzed {
		return nil
	}

	// 创建返回结果的map
	scores := make(map[string]map[string]float64)

	// 初始化scores
	for _, file := range files {
		scores[file] = make(map[string]float64)
		for _, otherFile := range files {
			if file != otherFile {
				scores[file][otherFile] = 0.0
			}
		}
	}

	// 获取俩俩之间的得分
	for _, file1 := range files {
		node1 := g.graph.GetNode(file1)
		if node1 == nil {
			continue
		}
		for _, file2 := range files {
			if file1 != file2 {
				node2 := g.graph.GetNode(file2)
				if node2 == nil {
					continue
				}
				scores[file1][file2] = g.graph.GetEdge(node1, node2).getWeight()
			}
		}
	}

	// 归一化分数
	maxScore := 0.0
	for _, fileScores := range scores {
		for _, score := range fileScores {
			if score > maxScore {
				maxScore = score
			}
		}
	}

	// 归一化到0-1范围
	if maxScore > 0 {
		for file1 := range scores {
			for file2 := range scores[file1] {
				scores[file1][file2] /= maxScore
			}
		}
	}

	return scores
}

func (g *GitGraph) GetRelatedFiles(ctx context.Context, filePath string, topK int) ([]string, error) {
	if !g.hasAnalyzed {
		return nil, errors.New("git-graph has not been initialized")
	}

	node := g.graph.GetNode(filePath)
	if node == nil {
		return []string{}, errors.New("file not found in git-graph")
	}

	edges := g.graph.GetEdges(node)
	if len(edges) == 0 {
		return []string{}, errors.New("no related files found")
	}

	files := make([]string, 0)
	for file, edge := range edges {
		if edge.info.getWeight() == 0 {
			continue
		}
		files = append(files, file)
	}

	sort.Slice(files, func(i, j int) bool {
		return edges[files[i]].info.getWeight() > edges[files[j]].info.getWeight()
	})
	if len(files) > topK {
		files = files[:topK]
	}
	return files, nil
}

func (g *GitGraph) GetHotGitFiles(ctx context.Context, TopK int) ([]string, error) {
	if !g.hasAnalyzed {
		return nil, errors.New("git-graph has not been initialized")
	}

	// 先从 Staged/Unstaged 中的文件中找
	recentModifiedFiles := g.recentModifiedFiles
	logs.CtxInfo(ctx, "get recent modified nodes: %v", recentModifiedFiles)

	defer func() {
		// 异步获取最近修改的文件
		go func() {
			_, err := g.getRecentModifiedFiles(10)
			if err != nil {
				logs.CtxError(ctx, "get recent modified files error: %v", err)
			}
		}()
	}()

	filePaths := make([]string, 0)
	if len(recentModifiedFiles) > 0 {
		for _, filePath := range recentModifiedFiles {
			if validator.IsValidGitFile(ctx, filePath, GitFileSizeLimit) {
				filePaths = append(filePaths, filePath)
			}
		}
	}

	if len(filePaths) >= TopK {
		return filePaths[:TopK], nil
	}

	// 没有找完，获取 Importance 最高的文件
	hotNodes, err := g.getHotGitNodes(TopK)
	if err != nil {
		return nil, err
	}

	for _, node := range hotNodes {
		if validator.IsValidGitFile(ctx, node.filePath, GitFileSizeLimit) {
			filePaths = append(filePaths, node.filePath)
		}
		if len(filePaths) >= TopK {
			break
		}
	}

	return filePaths, nil
}

func (g *GitGraph) GetNode(filePath string) *GitGraphNode {
	if !g.hasAnalyzed {
		return nil
	}
	
	return g.graph.GetNode(filePath)
}
