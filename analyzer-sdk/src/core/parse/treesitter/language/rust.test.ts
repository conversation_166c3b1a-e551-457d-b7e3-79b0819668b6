import {beforeAll, describe, expect, it} from "vitest";
import {TreeSitterParser} from "../parser";
import {LanguageIdEnum} from "../../share";
import {RustTreeSitterHandler} from "./rust";

describe("test_getFuncParentNodeForRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
fn add(a: i32, b: i32) -> i32 {
    let result = a + b;
    result
}

fn subtract(a: i32, b: i32) -> i32 {
    a - b
}

struct Calculator {
    name: String,
}

impl Calculator {
    fn new(name: String) -> Self {
        Calculator { name }
    }
    
    fn add(&self, a: i32, b: i32) -> i32 {
        a + b
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Rust, docText);
    const result = RustTreeSitterHandler.INSTANCE.getFuncParentNodeForRange(tree.rootNode, {
      start: {
        line: 2,
        character: 4
      }, 
      end: {
        line: 2, 
        character: 10
      }
    });
    
    expect(result.text).toEqual(`fn add(a: i32, b: i32) -> i32 {
    let result = a + b;
    result
}`);
  });

  it("case2_method_in_impl", async () => {
    const docText = `
fn add(a: i32, b: i32) -> i32 {
    let result = a + b;
    result
}

struct Calculator {
    name: String,
}

impl Calculator {
    fn new(name: String) -> Self {
        Calculator { name }
    }
    
    fn add(&self, a: i32, b: i32) -> i32 {
        a + b
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Rust, docText);
    const result = RustTreeSitterHandler.INSTANCE.getFuncParentNodeForRange(tree.rootNode, {
      start: {
        line: 15,
        character: 8
      }, 
      end: {
        line: 15, 
        character: 13
      }
    });
    
    expect(result.text).toEqual(`fn add(&self, a: i32, b: i32) -> i32 {
        a + b
    }`);
  });
});

describe("test_getTypeParentNodeForRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_struct", async () => {
    const docText = `
fn add(a: i32, b: i32) -> i32 {
    let result = a + b;
    result
}

struct Calculator {
    name: String,
    value: i32,
}

impl Calculator {
    fn new(name: String) -> Self {
        Calculator { name, value: 0 }
    }
    
    fn add(&self, a: i32, b: i32) -> i32 {
        a + b
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Rust, docText);
    const result = RustTreeSitterHandler.INSTANCE.getTypeParentNodeForRange(tree.rootNode, {
      start: {
        line: 7,
        character: 4
      }, 
      end: {
        line: 7, 
        character: 10
      }
    });
    
    expect(result.text).toEqual(`struct Calculator {
    name: String,
    value: i32,
}`);
  });

  it("case2_impl", async () => {
    const docText = `
fn add(a: i32, b: i32) -> i32 {
    let result = a + b;
    result
}

struct Calculator {
    name: String,
    value: i32,
}

impl Calculator {
    fn new(name: String) -> Self {
        Calculator { name, value: 0 }
    }
    
    fn add(&self, a: i32, b: i32) -> i32 {
        a + b
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Rust, docText);
    const result = RustTreeSitterHandler.INSTANCE.getTypeParentNodeForRange(tree.rootNode, {
      start: {
        line: 15,
        character: 8
      }, 
      end: {
        line: 15, 
        character: 13
      }
    });
    
    expect(result.text).toEqual(`impl Calculator {
    fn new(name: String) -> Self {
        Calculator { name, value: 0 }
    }
    
    fn add(&self, a: i32, b: i32) -> i32 {
        a + b
    }
}`);
  });
});

describe("test_getMethodInRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
fn add(a: i32, b: i32) -> i32 {
    let result = a + b;
    result
}

fn subtract(a: i32, b: i32) -> i32 {
    a - b
}

struct Calculator {
    name: String,
}

impl Calculator {
    fn new(name: String) -> Self {
        Calculator { name }
    }
    
    fn add(&self, a: i32, b: i32) -> i32 {
        a + b
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Rust, docText);
    const range = {
      start: {
        line: 1,
        character: 0
      },
      end: {
        line: 4,
        character: 1
      }
    };
    
    const result = RustTreeSitterHandler.INSTANCE.getMethodInRange(tree.rootNode, range, 0, 50, false);
    
    expect(result.map(node => node.text)).toEqual([
      `fn add(a: i32, b: i32) -> i32 {
    let result = a + b;
    result
}`
    ]);
  });
}); 