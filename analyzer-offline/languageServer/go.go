package languageServer

import (
	"byted-ckg/analyzer-offline/util"
	"context"
	"fmt"
	"os"
	"os/exec"
)

const goServerPort = 50558

type GoLanguageServer struct {
	ctx context.Context
}

func NewGoLanguageServer() *GoLanguageServer {
	return &GoLanguageServer{
		ctx: context.Background(),
	}
}

func (p *GoLanguageServer) Start() error {
	cmd := exec.Command(fmt.Sprintf("./bin/gopls-%s-%s", util.GetOS(), util.GetArch()), "serve", "-rpc.trace", "-listen", fmt.Sprintf(":%d", goServerPort))

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start gopls server: %s", err.<PERSON>rror())
	}

	return nil
}

func (p *GoLanguageServer) IsStarted() bool {
	return util.IsPortInUse(goServerPort)
}

func (p *GoLanguageServer) Shutdown() error {
	return util.KillProcessByPort(goServerPort)
}

func (p *GoLanguageServer) GetServerPort() int {
	return goServerPort
}
