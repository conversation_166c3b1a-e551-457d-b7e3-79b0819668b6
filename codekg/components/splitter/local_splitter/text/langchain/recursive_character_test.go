package langchain

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

//nolint:dupword,funlen
func TestRecursiveCharacterSplitter(t *testing.T) {
	//tokenEncoder, _ := tiktoken.GetEncoding("cl100k_base")

	t.<PERSON>()
	type testCase struct {
		text          string
		chunkOverlap  int
		chunkSize     int
		separators    []string
		expectedDocs  []TestDocument
		keepSeparator bool
		LenFunc       func(string) int
	}
	testCases := []testCase{
		{
			text:         "哈里森\n很高兴遇见你\n欢迎来中国",
			chunkOverlap: 0,
			chunkSize:    10,
			separators:   []string{"\n\n", "\n", " "},
			expectedDocs: []TestDocument{
				{PageContent: "哈里森\n很高兴遇见你", Metadata: map[string]interface{}{}},
				{PageContent: "欢迎来中国", Metadata: map[string]interface{}{}},
			},
		},
		{
			text:         "Hi, <PERSON>. \nI am glad to meet you",
			chunkOverlap: 1,
			chunkSize:    20,
			separators:   []string{"\n", "$"},
			expectedDocs: []TestDocument{
				{PageContent: "Hi, <PERSON>.", Metadata: map[string]interface{}{}},
				{PageContent: "I am glad to meet you", Metadata: map[string]interface{}{}},
			},
		},
		{
			text:         "Hi.\nI'm Harrison.\n\nHow?\na\nbHi.\nI'm Harrison.\n\nHow?\na\nb",
			chunkOverlap: 1,
			chunkSize:    40,
			separators:   []string{"\n\n", "\n", " ", ""},
			expectedDocs: []TestDocument{
				{PageContent: "Hi.\nI'm Harrison.", Metadata: map[string]interface{}{}},
				{PageContent: "How?\na\nbHi.\nI'm Harrison.\n\nHow?\na\nb", Metadata: map[string]interface{}{}},
			},
		},
		{
			text:         "name: Harrison\nage: 30",
			chunkOverlap: 1,
			chunkSize:    40,
			separators:   []string{"\n\n", "\n", " ", ""},
			expectedDocs: []TestDocument{
				{PageContent: "name: Harrison\nage: 30", Metadata: map[string]interface{}{}},
			},
		},
		{
			text: `name: Harrison
age: 30

name: Joe
age: 32`,
			chunkOverlap: 1,
			chunkSize:    40,
			separators:   []string{"\n\n", "\n", " ", ""},
			expectedDocs: []TestDocument{
				{PageContent: "name: Harrison\nage: 30", Metadata: map[string]interface{}{}},
				{PageContent: "name: Joe\nage: 32", Metadata: map[string]interface{}{}},
			},
		},
		{
			text: `Hi.
I'm Harrison.

How? Are? You?
Okay then f f f f.
This is a weird text to write, but gotta test the splittingggg some how.

Bye!

-H.`,
			chunkOverlap: 1,
			chunkSize:    10,
			separators:   []string{"\n\n", "\n", " ", ""},
			expectedDocs: []TestDocument{
				{PageContent: "Hi.", Metadata: map[string]interface{}{}},
				{PageContent: "I'm", Metadata: map[string]interface{}{}},
				{PageContent: "Harrison.", Metadata: map[string]interface{}{}},
				{PageContent: "How? Are?", Metadata: map[string]interface{}{}},
				{PageContent: "You?", Metadata: map[string]interface{}{}},
				{PageContent: "Okay then", Metadata: map[string]interface{}{}},
				{PageContent: "f f f f.", Metadata: map[string]interface{}{}},
				{PageContent: "This is a", Metadata: map[string]interface{}{}},
				{PageContent: "a weird", Metadata: map[string]interface{}{}},
				{PageContent: "text to", Metadata: map[string]interface{}{}},
				{PageContent: "write, but", Metadata: map[string]interface{}{}},
				{PageContent: "gotta test", Metadata: map[string]interface{}{}},
				{PageContent: "the", Metadata: map[string]interface{}{}},
				{PageContent: "splittingg", Metadata: map[string]interface{}{}},
				{PageContent: "ggg", Metadata: map[string]interface{}{}},
				{PageContent: "some how.", Metadata: map[string]interface{}{}},
				{PageContent: "Bye!\n\n-H.", Metadata: map[string]interface{}{}},
			},
		},
		{
			text:          "Hi, Harrison. \nI am glad to meet you",
			chunkOverlap:  0,
			chunkSize:     10,
			separators:    []string{"\n", "$"},
			keepSeparator: true,
			expectedDocs: []TestDocument{
				{PageContent: "Hi, Harrison. ", Metadata: map[string]interface{}{}},
				{PageContent: "\nI am glad to meet you", Metadata: map[string]interface{}{}},
			},
		},
		{
			text:          strings.Repeat("The quick brown fox jumped over the lazy dog. ", 2),
			chunkOverlap:  0,
			chunkSize:     10,
			separators:    []string{" "},
			keepSeparator: true,
			LenFunc: func(s string) int {
				return len(s) / 4
			},
			expectedDocs: []TestDocument{
				{PageContent: "The quick brown fox jumped over the lazy dog.", Metadata: map[string]interface{}{}},
				{PageContent: "The quick brown fox jumped over the lazy dog.", Metadata: map[string]interface{}{}},
			},
		},
	}
	splitter := NewRecursiveCharacter()
	for _, tc := range testCases {
		splitter.ChunkOverlap = tc.chunkOverlap
		splitter.ChunkSize = tc.chunkSize
		splitter.Separators = tc.separators
		splitter.KeepSeparator = tc.keepSeparator
		if tc.LenFunc != nil {
			splitter.LenFunc = tc.LenFunc
		}

		// 创建测试文档
		chunks, err := splitter.SplitText(tc.text)
		require.NoError(t, err)

		docs := make([]TestDocument, 0, len(chunks))
		for _, chunk := range chunks {
			docs = append(docs, TestDocument{
				PageContent: chunk.Content,
				Metadata:    map[string]interface{}{},
			})
		}

		assert.Equal(t, tc.expectedDocs, docs)
	}
}
