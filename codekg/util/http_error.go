package util

import (
	"fmt"
	"net/http"
	"strings"
)

// HTTPError is the error occurred when requesting server.
type HTTPError struct {
	Code    int    `json:"code"`    // Common field to contain error code.
	Message string `json:"message"` // Common field to contain error message.
	// Extra is the error message when parsing response or requesting.
	Extra string `json:"-"`
	// StatusCode is the server returned HTTP status code.
	StatusCode int `json:"-"`
}

func (e *HTTPError) Error() string {
	var str []string
	if len(e.Message) != 0 {
		str = append(str, " err msg: "+e.Message)
	}
	if len(e.Extra) != 0 {
		str = append(str, " extra msg: "+e.Extra)
	}
	if e.StatusCode > 0 {
		str = append(str, " got code: "+http.StatusText(e.StatusCode))
	} else {
		str = append(str, fmt.Sprintf(" got code: %d", e.StatusCode))
	}

	return strings.TrimSpace(strings.Join(str, ","))
}
