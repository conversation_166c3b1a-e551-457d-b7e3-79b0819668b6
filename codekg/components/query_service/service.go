package query_service

import (
	"context"
	"encoding/json"
	"fmt"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/data_storage/consts"
	bizErr "ide/ckg/codekg/components/error"
	"os"
	"regexp"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"gorm.io/gorm"

	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/data_manager"
	"ide/ckg/codekg/components/env"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/components/ignore_service"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/protocol"
	"ide/ckg/codekg/util"

	"ide/ckg/codekg/components/logs"

	"github.com/gogo/protobuf/jsonpb"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type IntentType = int8

const (
	IntentExplain IntentType = iota + 1
	IntentCode
	IntentEdit
	IntentLinesDoc
	IntentFunctionDoc
	IntentFix
	IntentTests
	IntentGeneralQA

	MaxResponseBodyLimit int = 1.5 * 1024 * 1024 // 因为后续还要 dump json，多留点 buffer
)

const (
	RetrieveRelationErrStepInvalidFile     = "get_editor_var_invalid_file"
	RetrieveRelationErrStepReadContent     = "get_editor_var_read_content"
	RetrieveRelationErrStepOutdatedContent = "get_editor_var_outdated_content"
	RetrieveRelationErrStepNoEntityStorage = "get_editor_var_no_entity_storage"

	RecallErrStepNoEntityStorage               = "recall_no_entity_storage"
	LocalRecallErrStepEmbedding                = "local_recall_embedding"
	LocalRecallErrStepEmptyEmbedding           = "local_recall_empty_embedding"
	LocalRecallErrStepGetEntities              = "local_recall_get_entities"
	LocalRecallErrStepFolderEntityNotFoundInDB = "local_recall_folder_entity_not_found_in_db"
)

type QueryService interface {
	RetrieveEntity(ctx context.Context, cli knowledgebase.Client, request *protocol.RetrieveEntityRequest) (*protocol.RetrieveEntityResponse, error)
	RetrieveRelation(ctx context.Context, request *protocol.RetrieveRelationRequest) (*protocol.RetrieveRelationResponse, error)
	SearchCKGDB(ctx context.Context, request *protocol.SearchCKGDBRequest) (*protocol.SearchCKGDBResponse, error)
	RetrieveRelevantSnippet(ctx context.Context, cli knowledgebase.Client, request *protocol.RetrieveRelevantSnippetRequest) (*RelevantSnippets, error)
}

type service struct {
	dm     data_manager.DataManager
	is     ignore_service.IgnoreService
	config *ckg_config.Config
	fs     file_system.FileSystem
}

func NewQueryService(dm data_manager.DataManager, is ignore_service.IgnoreService, config *ckg_config.Config, fs file_system.FileSystem) QueryService {
	return &service{
		dm:     dm,
		is:     is,
		config: config,
		fs:     fs,
	}
}

func isValidPath(path string) bool {
	if runtime.GOOS == "windows" {
		windowsPattern := `^[a-zA-Z]:[/\\](?:[^<>:"|?*]+[/\\])*[^<>:"|?*]*$`
		matched, _ := regexp.MatchString(windowsPattern, path)
		return matched
	} else {
		unixPattern := `^(/[^/ ]*)+/?$`
		matched, _ := regexp.MatchString(unixPattern, path)
		return matched
	}
}

func getRecallType(entityType entitySearchType) knowledgebase.RecallType {
	switch entityType {
	case searchTypeKnowledgebase:
		return knowledgebase.EMBED
	case searchTypeNER:
		return knowledgebase.NER
	case searchTypeAlias:
		return knowledgebase.ALIAS
	case searchTypeLocal:
		return knowledgebase.LOCALEMBED
	}
	return knowledgebase.AT
}

func getEntityType(entity *entitySearchResult) knowledgebase.EntityType {
	switch entity.Entity.Type {
	case model.Clazz:
		return knowledgebase.ClazzType
	case model.CodeChunk:
		return knowledgebase.CodeChunkType
	case model.Method:
		return knowledgebase.MethodType
	case model.Folder:
		return knowledgebase.FolderType
	case model.Text:
		return knowledgebase.TextType
	case model.File:
		return knowledgebase.FileType
	case model.FileTopLevel:
		return knowledgebase.FileTopLevelType
	case model.ClassTopLevel:
		return knowledgebase.ClassTopLevelType
	}
	return knowledgebase.OtherType
}

func getIntentType(intent string) knowledgebase.IntentTag {
	switch intent {
	case model.IntentExplain:
		return knowledgebase.ExplainIntent
	case model.IntentFix:
		return knowledgebase.FixIntent
	case model.IntentDoc:
		return knowledgebase.DocIntent
	case model.IntentCode:
		return knowledgebase.CodeIntent
	case model.IntentComplex:
		return knowledgebase.ComplexIntent
	case model.IntentDangerous:
		return knowledgebase.DangerousIntent
	case model.IntentLinesDoc:
		return knowledgebase.LinesDocIntent
	case model.IntentEdit:
		return knowledgebase.EditIntent
	case model.IntentGeneralQaIntent:
		return knowledgebase.GeneralQaIntent
	case model.IntentTest:
		return knowledgebase.TestIntent
	case model.IntentInvalid:
		return knowledgebase.InvalidIntent
	case model.IntentLintErrorFix:
		return knowledgebase.LintErrorFixIntent
	case model.IntentProject:
		return knowledgebase.ProjectIntent
	case model.IntentWebSearch:
		return knowledgebase.WebSearchIntent
	}
	return knowledgebase.DefaultIntent
}

func getRerankCandidate(entity *entitySearchResult, content string) *knowledgebase.Candidate {
	resultCandidate := &knowledgebase.Candidate{}
	resultCandidate.ID = entity.Entity.ID
	resultCandidate.Content = content
	resultCandidate.EntityType = getEntityType(entity)
	resultCandidate.RecallType = getRecallType(entity.RecallOption.Type)
	if entity.RecallOption.Type == searchTypeKnowledgebase || entity.RecallOption.Type == searchTypeLocal {
		resultCandidate.EmbeddingCandidate = &knowledgebase.EmbeddingCandidate{
			RecallScore: float64(entity.Score),
			RecallTag:   entity.RecallOption.EmbeddingTag,
		}
	}
	if entity.RecallOption.Type == searchTypeNER {
		resultCandidate.NERCandidate = &knowledgebase.NerCandidate{
			RecallScore: float64(entity.Score),
			RecallTag:   entity.RecallOption.NERTag,
		}
	}
	if entity.RecallOption.Type == searchTypeAlias {
		resultCandidate.AliasCandidate = &knowledgebase.AliasCandidate{
			RecallScore: float64(entity.Score),
			RecallTag:   entity.RecallOption.AliasTag,
		}
	}
	return resultCandidate
}

func (s *service) buildEntityRankRequest(ctx context.Context, targetEntity []*entitySearchResult, userMessage, intent, sessionID string) *knowledgebase.EntityRerankRequest {
	candidates := make([]*knowledgebase.Candidate, 0)
	for _, item := range targetEntity {
		candidate := &knowledgebase.Candidate{}
		candidate.ID = item.Entity.ID
		candidate.EntityType = getEntityType(item)
		candidate.RecallType = getRecallType(item.RecallOption.Type)
		if item.RecallOption.Type == searchTypeKnowledgebase || item.RecallOption.Type == searchTypeLocal {
			candidate.EmbeddingCandidate = &knowledgebase.EmbeddingCandidate{
				RecallScore: float64(item.Score),
				RecallTag:   item.RecallOption.EmbeddingTag,
			}
		}
		if item.RecallOption.Type == searchTypeNER {
			candidate.NERCandidate = &knowledgebase.NerCandidate{
				RecallScore: float64(item.Score),
				RecallTag:   item.RecallOption.NERTag,
			}
		}
		if item.RecallOption.Type == searchTypeAlias {
			candidate.AliasCandidate = &knowledgebase.AliasCandidate{
				RecallScore: float64(item.Score),
				RecallTag:   item.RecallOption.AliasTag,
			}
		}
		candidate.Attributes = make(map[string]string)
		for key, value := range item.Entity.Attributes {
			floatValue, ok := value.(float64)
			if ok {
				value = strconv.Itoa(int(floatValue))
			}
			strValue, ok := value.(string)
			if !ok {
				strValue = ""
			}

			listValue, ok := value.([]interface{})
			if ok && len(listValue) > 0 {
				data, err := json.Marshal(listValue)
				if err == nil {
					strValue = string(data)
				}
			}

			candidate.Attributes[key] = strValue
		}
		candidate.Attributes["path"] = item.Entity.URI
		if item.Entity.Type == model.Clazz || item.Entity.Type == model.Method || item.Entity.Type == model.File {
			candidate.Attributes["name"] = item.Entity.Name
		}

		if item.Entity.Type == model.File {
			if _, ok := item.Entity.Attributes["simplified_content"]; !ok {
				fileContentBytes, err := os.ReadFile(item.Entity.URI)
				if err == nil {
					fileContent := string(fileContentBytes)
					if len(fileContent) > 4096 {
						fileContent = fileContent[:4096]
					}
					candidate.Attributes["simplified_content"] = fileContent
				}
			}
		}
		candidates = append(candidates, candidate)
	}
	request := &knowledgebase.EntityRerankRequest{
		UserInput:  userMessage,
		Intent:     getIntentType(intent),
		Candidates: candidates,
		SessionID:  sessionID,
	}
	return request
}

// entityRerank 对 entityRank 包了一层，主要处理 entityRank 失败 / 为空时的 fallback
func (s *service) entityRerank(ctx context.Context, cli knowledgebase.Client, searchResults []*entitySearchResult, userMessage, userId, intent, sessionID string) []*entitySearchResult {
	startRerankTime := time.Now()
	recallReqEvent := ckg_metrics.GetEvent(ctx)
	rerankResult := make([]*entitySearchResult, 0)
	defer func() {
		recallReqEvent.AddTeaParam(ckg_metrics.TeaParamRecallReqRerankCost, time.Since(startRerankTime).Milliseconds())
		recallReqEvent.AddParam(ckg_metrics.ParamRecallResultEntities, rerankResult)
		recallReqEvent.AddParam(ckg_metrics.ParamRerankRawResult, lo.Map(rerankResult, func(item *entitySearchResult, _ int) string {
			return item.Entity.ID
		}))
	}()
	rerankResult, err := s.entityRank(ctx, cli, searchResults, userMessage, userId, intent, sessionID)
	if err != nil {
		// rerank 失败时，fallback 到不启用 rerank
		logs.CtxWarn(ctx, "[entityRerank] entity rerank failed, fallback, err: %v", err)
		recallReqEvent.AddTeaParam(ckg_metrics.TeaParamRecallReqRerankFailed, true)
		if util.JudgeNewWorkError(err) {
			recallReqEvent.AddTeaParam(ckg_metrics.TeaParamRecallReqRerankErrCode, KnowledgebaseAPINetWorkErrCode)
		}
		return searchResults
	}
	logs.CtxDebug(ctx, "Rerank result num: %d", len(rerankResult))
	for _, r := range rerankResult {
		logs.CtxDebug(ctx, "Rerank result, id: %s, %+v", r.Entity.ID, r)
	}
	if len(rerankResult) == 0 {
		// rerank 返回为空时，fallback 到不启用 rerank
		logs.CtxWarn(ctx, "[entityRerank] rerank return empty, fallback to searchResults, num: %d", len(searchResults))
		recallReqEvent.AddTeaParam(ckg_metrics.TeaParamRecallReqRerankEmpty, true)
		return searchResults
	}
	return rerankResult
}

func (s *service) entityRank(ctx context.Context, cli knowledgebase.Client, targetEntity []*entitySearchResult, userMessage, userID, intent, sessionID string) ([]*entitySearchResult, error) {
	sort.Slice(targetEntity, func(i, j int) bool {
		return targetEntity[i].Score > targetEntity[j].Score
	})

	targetEntityMap := make(map[string]*entitySearchResult)
	for _, target := range targetEntity {
		targetEntityMap[target.Entity.ID] = target
	}

	request := s.buildEntityRankRequest(ctx, targetEntity, userMessage, intent, sessionID)
	ckg_metrics.GetEvent(ctx).AddParam(ckg_metrics.ParamRerankCandidates, request.Candidates)

	if len(request.Candidates) == 0 {
		return []*entitySearchResult{}, nil
	}

	logs.CtxInfo(ctx, "Rerank candidate num %d", len(request.Candidates))
	resp, err := cli.EntityRerank(ctx, env.GetToken(userID), request)
	if err != nil {
		logs.CtxError(ctx, "EntityRerank err is %v", err)
		return nil, err
	}

	sort.Slice(resp.Candidates, func(i, j int) bool {
		return resp.Candidates[i].Score > resp.Candidates[j].Score
	})

	result := make([]*entitySearchResult, 0)
	lo.ForEach(resp.Candidates, func(item *knowledgebase.CandidateResponse, index int) {
		origin, ok := targetEntityMap[item.ID]
		if !ok {
			logs.CtxWarn(ctx, "unexpected rerank result item %+v", item)
			return
		}

		origin.Score = float32(item.Score)
		result = append(result, origin)
	})

	return result, nil
}

func (s *service) searchEntitiesHelper(ctx context.Context, cli knowledgebase.Client, projectID, userID, userMessage, intent string,
	editorInfo *protocol.CurrentEditorInfo, absFolderPaths []string, expectTypes []model.EntityType, recallNum int) ([]*entitySearchResult, string, string, error) {
	recallEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGRecall, userID, "", string(env.GetSourceProduct()))
	defer recallEvent.Report(ctx, true)
	ctx = ckg_metrics.SetEvent(ctx, recallEvent)
	recallEvent.AddTeaParam(ckg_metrics.TeaParamRecallEvent, "search_entities")

	searchResultChan := make(chan []*entitySearchResult)
	embeddingModel := ""
	entityStorage, err := s.dm.GetEntityStorage(ctx, projectID)
	if err != nil {
		errMsg := fmt.Sprintf("err get storage for project %s, err: %v", projectID, err)
		logs.CtxWarn(ctx, errMsg)
		recallEvent.AddTeaParam(ckg_metrics.TeaParamEmptyRecallNonExistedEntityDB, true)
		recallEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		recallEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, RecallErrStepNoEntityStorage)
		return []*entitySearchResult{}, "", errMsg, nil
	}

	// 召回前，上报当前索引状态。
	if status, ok := s.dm.GetProjectsBuildStatus(ctx, &protocol.GetBuildStatusRequest{ProjectIds: []string{projectID}})[projectID]; ok {
		recallEvent.AddTeaParam(ckg_metrics.TeaParamIndexProgressWhenRecall, status.Progress)
	} else {
		const buildStatusNotFound = -1
		recallEvent.AddTeaParam(ckg_metrics.TeaParamIndexProgressWhenRecall, float32(buildStatusNotFound))
	}

	errMsg := ""
	util.SafeGo(ctx, func() {
		wg := sync.WaitGroup{}

		// Search by embedding
		wg.Add(1)
		util.SafeGo(ctx, func() {
			defer wg.Done()

			selectedCode := ""
			if editorInfo != nil {
				selectedCode = editorInfo.SelectCodeContent
			}
			var result []*entitySearchResult
			startTime := time.Now()
			{ // 只走本地召回
				logs.CtxInfo(ctx, "Enable to search entities at local, since support local embedding storage")
				recallEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingRecallType, data_manager.EmbeddingStorageTypeLocal)
				chunkingMethod, datastoreName := s.config.GetLocalStrategyOrDefault(ctx, userID, projectID)
				if chunkingMethod == consts.NoChunkingMethodSinceNoEntityStorage {
					logs.CtxWarn(ctx, "no entity storage for projectID: %s", projectID)
					recallEvent.AddTeaParam(ckg_metrics.TeaParamNoEntityStorage, true)
					return
				}
				embeddingStorage, err := s.dm.GetLocalEmbeddingStorage(ctx, projectID)
				if err != nil {
					logs.CtxWarn(ctx, "error get local embedding storage, err: %v", err)
					recallEvent.AddTeaParam(ckg_metrics.TeaParamNoLocalEmbeddingStorage, true)
					return
				}
				result, embeddingModel, errMsg, err = s.searchEntityAtLocal(ctx, cli, datastoreName, entityStorage, embeddingStorage, userID, userMessage, selectedCode, absFolderPaths, expectTypes, recallNum)
				if err != nil {
					logs.CtxError(ctx, "error search entity at local, err: %v", err)
					return
				}
			}
			duration := time.Since(startTime).Milliseconds()
			logs.CtxDebug(ctx, "Embedding query result: %+v, cost: %d ms", result, duration)
			for _, r := range result {
				logs.CtxDebug(ctx, "Embedding: %+v", r.Entity.ID)
			}
			searchResultChan <- result
		})

		// Search by alias
		wg.Add(1)
		util.SafeGo(ctx, func() {
			defer wg.Done()

			result, err := s.searchEntityByAlias(ctx, entityStorage, userMessage, []model.EntityType{model.Folder})
			if err != nil {
				logs.CtxError(ctx, "error search entity by alias, err: %v", err)
				return
			}
			logs.CtxDebug(ctx, "Alias query result: %+v", result)
			for _, r := range result {
				logs.CtxDebug(ctx, "Alias: %+v", r.Entity.ID)
			}
			searchResultChan <- result
		})

		// Search by NER
		wg.Add(1)
		util.SafeGo(ctx, func() {
			defer wg.Done()

			result, err := s.searchEntityByNER(ctx, cli, entityStorage, userID, userMessage, editorInfo, expectTypes)
			if err != nil {
				logs.CtxError(ctx, "error search entity by ner, err: %v", err)
				return
			}
			logs.CtxInfo(ctx, "NER query result: %+v", result)
			for _, r := range result {
				logs.CtxInfo(ctx, "NER: %+v", r.Entity.ID)
			}
			searchResultChan <- result
		})

		wg.Wait()
		close(searchResultChan)
	})

	uniqueEntityIDMap := make(map[string]bool)
	mergedResult := make([]*entitySearchResult, 0)
	for results := range searchResultChan {
		for _, result := range results {
			if _, ok := uniqueEntityIDMap[result.Entity.ID]; ok {
				continue
			}
			uniqueEntityIDMap[result.Entity.ID] = true
			mergedResult = append(mergedResult, result)
		}
	}

	// Filter out binary-content entities
	mergedResult = lo.Filter(mergedResult, func(item *entitySearchResult, _ int) bool {
		// Only need to check text and file entities
		if item.Entity.Type != model.Text && item.Entity.Type != model.File {
			return true
		}
		if item.Entity.Type == model.Text {
			texts := item.Entity.GetContent()
			for _, text := range texts {
				if isUTF8 := utf8.ValidString(text); !isUTF8 {
					logs.CtxWarn(ctx, "filtered text entity with invalid control character: %s", item.Entity.ID)
					return false
				}
			}
		}
		if item.Entity.Type == model.File {
			simplifiedContent := item.Entity.GetSimplifiedContent()
			if isUTF8 := utf8.ValidString(simplifiedContent); !isUTF8 {
				logs.CtxWarn(ctx, "filtered file entity with invalid control character: %s", item.Entity.ID)
				return false
			}
		}
		// Valid text content or file content, keep it
		return true
	})
	return mergedResult, embeddingModel, errMsg, nil
}

func (s *service) RetrieveEntity(ctx context.Context, cli knowledgebase.Client, request *protocol.RetrieveEntityRequest) (*protocol.RetrieveEntityResponse, error) {
	recallReqEvent := ckg_metrics.NewEvent(ckg_metrics.EVentNameCKGRecallReq, request.UserId, "", string(env.GetSourceProduct()))
	defer recallReqEvent.Report(ctx, true)
	ctx = ckg_metrics.SetEvent(ctx, recallReqEvent)
	recallReqEvent.AddParam(ckg_metrics.ParamRetrieveEntityRequest, request)
	recallReqEvent.AddTeaParam(ckg_metrics.TeaParamRecallReqProjectNum, len(request.ProjectIds))
	recallReqEvent.AddTeaParam(ckg_metrics.TeaParamRecallReqFolderNum, len(request.FolderPaths))
	recallReqEvent.AddTeaParam(ckg_metrics.TeaParamRecallReqEntityNum, int(request.EntityNum))

	expectTypes := make([]model.EntityType, 0)
	for _, t := range request.ExpectEntityTypes {
		switch t {
		case protocol.EntityType_folder:
			expectTypes = append(expectTypes, model.Folder)
		case protocol.EntityType_file:
			expectTypes = append(expectTypes, model.File)
		case protocol.EntityType_class:
			expectTypes = append(expectTypes, model.Clazz)
		case protocol.EntityType_method:
			expectTypes = append(expectTypes, model.Method)
		case protocol.EntityType_text:
			expectTypes = append(expectTypes, model.Text)
		}
	}

	finalEntityNum := int(request.EntityNum)
	if finalEntityNum == 0 {
		useVirtualProjectEntityNum := true
		for _, projectID := range request.ProjectIds {
			if s.dm.GetProjectType(ctx, projectID) != model.ProjectTypeVirtual {
				useVirtualProjectEntityNum = false
				break
			}
		}
		if useVirtualProjectEntityNum {
			// 文档实体召回
			finalEntityNum = s.config.GetTCCConfig(ctx, cli, request.UserId).VirtualProjectConfig.RecallNum
		} else if s.config.GetABConfig(ctx, cli, request.UserId).GetWorkspaceRecallNum() != 0 {
			// 命中实验的召回
			finalEntityNum = s.config.GetABConfig(ctx, cli, request.UserId).GetWorkspaceRecallNum()
		} else if s.config.IsIndexFeatureEnabled(ctx, request.UserId, ckg_config.EnableV2ChunkingMethod) {
			// feature gate 放量到 v2 切分
			finalEntityNum = ckg_config.DefaultChunkingV2RecallNum
		} else {
			// feature gate 未放到，仍使用 v1 切分，对应 25 个实体
			finalEntityNum = ckg_config.DefaultChunkingV1RecallNum
		}
		if finalEntityNum == 0 {
			// 总兜底
			finalEntityNum = s.config.GetTCCConfig(ctx, cli, request.UserId).DefaultRecallNum
		}
	}

	projectIds := make([]string, 0)
	hashFolderMap := make(map[string][]string)
	if len(request.FolderPaths) != 0 {
		// 对 #Folder，只对传入 #Folder:path 的 path 所在的 project 召回
		// 挑选出 path 对应的 project，填入 projectIds，通过 hashFolderMap 维护 projectId -> folders 的关系
		for _, projectID := range request.ProjectIds {
			matchingFolders := lo.Filter(request.FolderPaths, func(p string, _ int) bool { return strings.HasPrefix(p, projectID) })
			for _, folderPath := range matchingFolders {
				hashFolderMap[projectID] = append(hashFolderMap[projectID], folderPath)
			}
		}
		projectIds = lo.Keys(lo.PickBy(hashFolderMap, func(_ string, folders []string) bool { return len(folders) != 0 }))
		if len(projectIds) == 0 {
			logs.CtxWarn(ctx, "project ids is empty, origin project ids: %v, folders is %v", request.ProjectIds, request.FolderPaths)
			recallReqEvent.AddTeaParam(ckg_metrics.TeaParamRecallReqNoHashFolderProject, true)
			// 预期 projectIds 不为空，兜底逻辑，变成 #workspace 召回
			projectIds = request.ProjectIds
			hashFolderMap = make(map[string][]string)
		}
	} else {
		// 对 #Workspace，需要对所有传入 ProjectIds 召回
		projectIds = request.ProjectIds
	}

	// 对每个 project 分别召回
	allSearchResults, entityIdProjectIdMap := make([]*entitySearchResult, 0), make(map[string]string)
	queryEmbeddingModels, vectorDBEmbeddingModels, entityDBChunkingMethods := make([]string, 0), make([]string, 0), make([]string, 0)
	errMsgs := make([]string, 0)
	for _, projectID := range projectIds {
		// 1. 召回实体，每个 project 都召回 finalEntityNum 个实体
		// 2. 记录 entity id -> project id 关系
		searchResults, m, errMsg, err := s.searchEntitiesHelper(ctx, cli, projectID, request.UserId,
			request.UserMessage, request.Intent, request.EditorInfo, hashFolderMap[projectID], expectTypes, finalEntityNum)
		if err != nil {
			logs.CtxError(ctx, "error search entity in storage, err: %v", err)
			return nil, errors.WithMessage(err, "error query storage")
		}
		for _, searchResult := range searchResults {
			entityIdProjectIdMap[searchResult.Entity.ID] = projectID
		}
		allSearchResults = append(allSearchResults, searchResults...)
		chunkingMethodLocal, dataStoreNameLocal := s.config.GetLocalStrategyOrDefault(ctx, request.UserId, projectID)
		queryEmbeddingModels = append(queryEmbeddingModels, m)
		vectorDBEmbeddingModels = append(vectorDBEmbeddingModels, dataStoreNameLocal)
		entityDBChunkingMethods = append(entityDBChunkingMethods, chunkingMethodLocal)
		errMsgs = append(errMsgs, errMsg)
	}
	// 多个 project 间未排序，使 allSearchResults 整体按照 Score 从高到低排序，
	// 最终 result 也是 Score 从高到低排序。
	sort.Slice(allSearchResults, func(i, j int) bool { return allSearchResults[i].Score > allSearchResults[j].Score })

	// 若 rerank，覆盖 allSearchResults 为 rerank 结果
	if s.config.GetABConfig(ctx, cli, request.UserId).IsRerankEnabled() {
		recallReqEvent.AddTeaParam(ckg_metrics.TeaParamRecallReqEnableRerank, true)
		recallReqEvent.AddTeaParam(ckg_metrics.TeaParamRecallReqRerankCandidateNum, len(allSearchResults))
		allSearchResults = s.entityRerank(ctx, cli, allSearchResults, request.UserMessage, request.UserId, request.Intent, request.SessionId)
	}

	result := lo.Map(
		lo.Filter(allSearchResults, func(r *entitySearchResult, _ int) bool {
			projectId, ok := entityIdProjectIdMap[r.Entity.ID]
			return ok && len(projectId) > 0
		}), func(r *entitySearchResult, _ int) *protocol.Entity {
			return &protocol.Entity{
				ProjectId: entityIdProjectIdMap[r.Entity.ID],
				EntityId:  r.Entity.ID,
			}
		})

	result = lo.Slice(result, 0, finalEntityNum)
	logs.CtxDebug(ctx, "Retrieve entity num %d", len(result))
	recallReqEvent.AddTeaParam(ckg_metrics.TeaParamRecallReqFinalEntityNum, len(result))
	for _, r := range result {
		logs.CtxDebug(ctx, "Retrieve entity result: %+v", r)
	}

	return &protocol.RetrieveEntityResponse{
		Code:                  protocol.Code_succeed,
		EntitiesByUserMessage: lo.Reverse(result),
		QueryEmbeddingModels:  lo.Uniq(queryEmbeddingModels),
		VectorEmbeddingModels: lo.Uniq(vectorDBEmbeddingModels),
		EntityChunkingMethods: lo.Uniq(entityDBChunkingMethods),
		EmptyRecallReason: lo.Reduce(lo.Filter(errMsgs, func(msg string, _ int) bool {
			return len(msg) > 0
		}), func(agg string, msg string, _ int) string {
			return agg + " " + msg
		}, ""),
	}, nil
}

func (s *service) RetrieveRelation(ctx context.Context, request *protocol.RetrieveRelationRequest) (*protocol.RetrieveRelationResponse, error) {
	references := make([]*protocol.Reference, 0)
	relationEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGRelation, request.UserId, "", string(env.GetSourceProduct()))
	ctx = ckg_metrics.SetEvent(ctx, relationEvent)
	defer relationEvent.Report(ctx, false)
	relationEvent.AddTeaParam(ckg_metrics.TeaParamRetrieveRelationEntityNum, len(request.EntitiesByUserMessage))

	entityVariables, entityReferences, errMetric, err := s.getEntityVariable(ctx, request.EntitiesByUserMessage)
	if err != nil {
		// 当前 block 不会进入，err 始终为 nil
		relationEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		logs.CtxError(ctx, "error get entity variable, err: %v", err)
		return nil, errors.WithMessagef(err, "error retrieve relation")
	}
	references = append(references, entityReferences...)

	// marshal err metric
	errMetricStr, err := json.Marshal(errMetric)
	if err != nil {
		logs.CtxError(ctx, "error marshal errMetric, err: %v", err)
		return nil, errors.WithMessage(err, "error marshal errMetric")
	}
	// 限制 entity variable 大小
	entityVariables = limitReferenceSize(ctx, entityVariables)

	if request.EditorInfo == nil {
		relationEvent.AddTeaParam(ckg_metrics.TeaParamVariableNum, len(entityVariables))
		relationEvent.AddTeaParam(ckg_metrics.TeaParamReferenceNum, len(references))
		return &protocol.RetrieveRelationResponse{
			CurrentEditor: &protocol.CurrentEditorVariable{
				SelectRange:  new(protocol.Range),
				VisibleRange: new(protocol.Range),
			},
			Variables:  entityVariables,
			References: lo.Reverse(references),
			ErrMetric:  string(errMetricStr),
		}, nil
	}

	logs.CtxInfo(ctx, "retrieve relation for editor info: %+v", request.EditorInfo)
	relationEvent.AddTeaParam(ckg_metrics.TeaParamGetEditorVar, true)
	editorVariable, _, err := s.getCurrentEditorVariable(ctx, request.EditorInfo)
	if err != nil {
		// 当前 block 仅在读取文件内容失败时才进入，其他异常情况并不返回不为空的 err，
		// 仅有读取文件失败，才会 failed == true；其余场景仅通过 err_step 上报数据。
		relationEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		logs.CtxError(ctx, "error get current editor variable, err: %v", err)
		return nil, errors.WithMessagef(err, "error retreive relation")
	}

	relationEvent.AddTeaParam(ckg_metrics.TeaParamVariableNum, len(entityVariables))
	relationEvent.AddTeaParam(ckg_metrics.TeaParamReferenceNum, len(references))
	return &protocol.RetrieveRelationResponse{
		CurrentEditor: editorVariable,
		Variables:     entityVariables,
		References:    lo.Reverse(references),
		ErrMetric:     string(errMetricStr),
	}, nil
}

func (s *service) getEntityVariable(ctx context.Context, entities []*protocol.Entity) ([]*protocol.Variable, []*protocol.Reference, *RetrieveRelationErrorMetric, error) {
	relationEvent := ckg_metrics.GetEvent(ctx)
	metric := &RetrieveRelationMetric{}
	defer func() {
		// variable and reference num
		relationEvent.AddTeaParam(ckg_metrics.TeaParamCodeChunkVarNum, metric.codeChunkVarNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamCodeChunkRefNum, metric.codeChunkRefNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamTextVarNum, metric.textVarNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamTextRefNum, metric.textRefNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamFolderVarNum, metric.folderVarNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamFolderRefNum, metric.folderRefNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamFileVarNum, metric.fileVarNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamFileRefNum, metric.fileRefNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamFileTopLevelVarNum, metric.fileTopLevelVarNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamFileTopLevelRefNum, metric.fileTopLevelRefNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamClassVarNum, metric.classVarNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamClassRefNum, metric.classRefNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamClassTopLevelVarNum, metric.classTopLevelVarNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamClassTopLevelRefNum, metric.classTopLevelRefNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamMethodVarNum, metric.methodVarNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamMethodRefNum, metric.methodRefNum)
		// err num
		relationEvent.AddTeaParam(ckg_metrics.TeaParamTextErrNum, metric.textErrNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamFolderErrNum, metric.folderErrNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamFileErrNum, metric.fileErrNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamClassErrNum, metric.classErrNum)
		relationEvent.AddTeaParam(ckg_metrics.TeaParamMethodErrNum, metric.methodErrNum)
	}()
	variables := make([]*protocol.Variable, 0)
	references := make([]*protocol.Reference, 0)
	errMetric := &RetrieveRelationErrorMetric{}

	// 将 entity 对应到 entity storage 上
	storageToEntities := make(map[data_storage.Storage][]string)
	for _, entityInfo := range entities {
		entityStorage, err := s.dm.GetEntityStorage(ctx, entityInfo.ProjectId)
		if err != nil {
			logs.CtxWarn(ctx, "failed to get storage for entity variable for project id: %s, err: %v", entityInfo.ProjectId, err)
			continue
		}
		storageToEntities[entityStorage] = append(storageToEntities[entityStorage], entityInfo.EntityId)
	}
	// 对每一个 project，都创建一个 tx 原子地查询 relation 和 reference。
	logs.CtxTrace(ctx, "[getEntityVariable] trying to begin transaction...")
	for storage, entityIds := range storageToEntities {
		_ = storage.GetConn().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			logs.CtxTrace(ctx, "[getEntityVariable] began transaction for retrieve reference")
			// 此处只能调 GetEntityByEntityID，因为需要保证 variable 和 reference 的顺序
			res := make([]*model.Entity, 0)
			for _, entityId := range entityIds {
				entity, err := storage.GetEntityByEntityID(ctx, tx, entityId)
				if err != nil {
					logs.CtxWarn(ctx, "failed to get entity (%v) for project id: %s, err: %v", entityId, storage.GetProjectID(), err)
					continue
				}
				res = append(res, entity)
			}
			for _, entity := range res {
				uriMeta, err := storage.GetURIMetaByURI(ctx, tx, entity.URI)
				if err != nil {
					logs.CtxWarn(ctx, "failed to get uri_meta for entity uri: %s, err: %v", entity.URI, err)
					continue
				}
				logs.CtxInfo(ctx, "retrieve relation for entity: %+v", entity.ID)
				vars, refs, err := s.getVariableAndReference(ctx, entity, storage, uriMeta, tx, metric)
				if err != nil {
					if errors.Is(err, bizErr.ErrGetFileRelation) {
						errMetric.GetFileErrNum++
					} else if errors.Is(err, bizErr.ErrGetFolderRelation) {
						errMetric.GetFolderErrNum++
					} else if errors.Is(err, bizErr.ErrGetClassRelation) {
						errMetric.GetClassErrNum++
					} else if errors.Is(err, bizErr.ErrGetMethodRelation) {
						errMetric.GetMethodErrNum++
					}
					continue
				}
				variables = append(variables, vars...)
				references = append(references, refs...)
			}
			return nil
		})
	}
	return variables, references, errMetric, nil
}

func (s *service) getVariableAndReference(ctx context.Context, entity *model.Entity, entityStorage data_storage.Storage,
	uriMeta *data_storage.StorageURIMeta, tx *gorm.DB, metric *RetrieveRelationMetric) ([]*protocol.Variable, []*protocol.Reference, error) {
	variables := make([]*protocol.Variable, 0)
	references := make([]*protocol.Reference, 0)
	switch entity.Type {
	case model.CodeChunk:
		// 无 db 查询
		variable, refs, err := s.getCodeRelation(ctx, entity, uriMeta)
		if err != nil {
			logs.CtxError(ctx, "getCodeRelation err is %s", err)
			return nil, nil, errors.WithMessagef(err, "getCodeRelation err")
		}
		variables = append(variables, variable)
		references = append(references, refs...)
		metric.codeChunkVarNum++
		metric.codeChunkRefNum += len(refs)
	case model.Text:
		// 无 db 查询
		variable, refs, err := s.getTextRelation(ctx, entity, uriMeta)
		if err != nil {
			metric.textErrNum++
			logs.CtxError(ctx, "getTextRelation err is %s, entity id: %s", err, entity.ID)
			return nil, nil, errors.WithMessagef(err, "getTextRelation err, entity id: %s", entity.ID)
		}
		variables = append(variables, variable)
		references = append(references, refs...)
		metric.textVarNum++
		metric.textRefNum += len(refs)
	case model.Folder:
		// 无 db 查询
		variable, refs, err := s.getFolderRelation(ctx, entity, entityStorage.GetProjectID())
		if err != nil {
			metric.folderErrNum++
			logs.CtxError(ctx, "getFolderRelation err is %s", err)
			return nil, nil, bizErr.ErrGetFolderRelation
		}
		variables = append(variables, variable)
		references = append(references, refs...)
		metric.folderVarNum++
		metric.folderRefNum += len(refs)
	case model.File:
		// 无 db 查询
		variable, refs, err := s.getFileRelation(ctx, entity, uriMeta)
		if err != nil {
			metric.fileErrNum++
			logs.CtxError(ctx, "getFileRelation err is %s", err)
			return nil, nil, bizErr.ErrGetFileRelation
		}
		variables = append(variables, variable)
		references = append(references, refs...)
		metric.fileVarNum++
		metric.fileRefNum += len(refs)
	case model.FileTopLevel:
		// 无 db 查询
		variable, refs, err := s.getFileTopLevelRelation(ctx, entity, uriMeta)
		if err != nil {
			logs.CtxError(ctx, "getFileTopLevelRelation err is %s", err)
			return nil, nil, errors.WithMessagef(err, "getFileTopLevelRelation err")
		}
		variables = append(variables, variable)
		references = append(references, refs...)
		metric.fileTopLevelVarNum++
		metric.fileTopLevelRefNum += len(refs)
	case model.Clazz:
		variable, refs, err := s.getClassRelation(ctx, entity, entityStorage, uriMeta, tx)
		if err != nil {
			metric.classErrNum++
			logs.CtxError(ctx, "getClassRelation err is %s", err)
			return nil, nil, bizErr.ErrGetClassRelation
		}
		variables = append(variables, variable)
		references = append(references, refs...)
		metric.classVarNum++
		metric.classRefNum += len(refs)
	case model.ClassTopLevel:
		// 无 db 查询
		variable, refs, err := s.getClassTopLevelRelation(ctx, entity, uriMeta)
		if err != nil {
			logs.CtxError(ctx, "getClassTopLevelRelation err is %s", err)
			return nil, nil, errors.WithMessagef(err, "getClassTopLevelRelation err")
		}
		variables = append(variables, variable)
		references = append(references, refs...)
		metric.classTopLevelVarNum++
		metric.classTopLevelRefNum += len(refs)
	case model.Method:
		variable, refs, err := s.getMethodRelation(ctx, entity, entityStorage, uriMeta, tx)
		if err != nil {
			metric.methodErrNum++
			logs.CtxError(ctx, "getMethodRelation err is %s", err)
			return nil, nil, bizErr.ErrGetMethodRelation
		}
		variables = append(variables, variable)
		references = append(references, refs...)
		metric.methodVarNum++
		metric.methodRefNum += len(refs)
	}
	return variables, references, nil
}

func (s *service) getCurrentEditorVariable(ctx context.Context, editorInfo *protocol.CurrentEditorInfo) (*protocol.CurrentEditorVariable, []*protocol.Reference, error) {
	retrieveEvent := ckg_metrics.GetEvent(ctx)
	selectCodeRange := lo.FromPtr(editorInfo.SelectCodeRange)
	visibleCodeRange := lo.FromPtr(editorInfo.VisibleCodeRange)
	variable := &protocol.CurrentEditorVariable{
		FilePath:     editorInfo.FilePath,
		Content:      editorInfo.UserFileContent,
		CursorLine:   editorInfo.CursorLine,
		SelectRange:  &selectCodeRange,
		VisibleRange: &visibleCodeRange,
		Entity: &protocol.CurrentEditorEntity{
			SelectedClasses: []*protocol.SelectedClassInfo{},
			SelectedMethods: []*protocol.SelectedMethodInfo{},
		},
		RefClasses: nil, // this field will be filled by LSP in ts side
		RefTypes:   nil, // this field will be filled by LSP in ts side
	}
	references := make([]*protocol.Reference, 0)

	// 判断文件路径是否合法
	if !isValidPath(editorInfo.FilePath) {
		retrieveEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, RetrieveRelationErrStepInvalidFile)
		logs.CtxWarn(ctx, "[getCurrentEditorVariable] invalid path: %s", editorInfo.FilePath)
		return variable, references, nil
	}
	_, err := os.Stat(editorInfo.FilePath)
	if err != nil {
		retrieveEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, RetrieveRelationErrStepInvalidFile)
		logs.CtxWarn(ctx, "[getCurrentEditorVariable] %v is not exist, err: %v", editorInfo.FilePath, err)
		return variable, references, nil
	}
	// 获取文件内容
	fileContent, err := os.ReadFile(editorInfo.FilePath)
	if err != nil {
		retrieveEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, RetrieveRelationErrStepReadContent)
		logs.CtxWarn(ctx, "[getCurrentEditorVariable] read file content failed: %s, err: %v", editorInfo.FilePath, err)
		return nil, nil, errors.WithMessagef(err, "error read file")
	}
	if variable.Content == "" {
		logs.CtxInfo(ctx, "using file content from file %s", variable.FilePath)
		variable.Content = string(fileContent)
	}
	references = append(references, &protocol.Reference{
		FilePath:  editorInfo.FilePath,
		StartLine: 1,
		EndLine:   int32(len(strings.Split(string(variable.Content), "\n"))),
	})

	if string(fileContent) != variable.Content {
		retrieveEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, RetrieveRelationErrStepOutdatedContent)
		logs.CtxInfo(ctx, "file %s is not up to date, skip relation retrieval", variable.FilePath)
		return variable, references, nil
	}

	entityStorage, err := s.dm.GetEntityStorage(ctx, editorInfo.ProjectId)
	if err != nil {
		retrieveEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, RetrieveRelationErrStepNoEntityStorage)
		logs.CtxWarn(ctx, "failed to get storage for current editor's project: %s", editorInfo.ProjectId)
		return variable, references, nil // 这里只是 warning，不强制要求有 entity
	}

	var selectedRange *protocol.Range = nil
	if editorInfo.SelectCodeRange != nil && editorInfo.SelectCodeRange.StartLine > 0 {
		selectedRange = editorInfo.SelectCodeRange
	} else if editorInfo.VisibleCodeRange != nil && editorInfo.VisibleCodeRange.StartLine > 0 {
		selectedRange = editorInfo.VisibleCodeRange
	} else if editorInfo.CursorLine > 0 {
		selectedRange = &protocol.Range{
			StartLine: editorInfo.CursorLine,
			EndLine:   editorInfo.CursorLine,
		}
	}

	if selectedRange != nil && selectedRange.StartLine > 0 {
		uriMeta, err := entityStorage.GetURIMetaByURI(ctx, entityStorage.GetConn(), editorInfo.FilePath)
		if err != nil {
			logs.CtxWarn(ctx, "failed to get uri meta by uri: %s, err: %v", editorInfo.FilePath, err)
			return variable, references, nil
		}
		relation, err := s.getEditorRelation(ctx, entityStorage, editorInfo.FilePath, selectedRange, uriMeta)
		if err != nil {
			logs.CtxWarn(ctx, "error get select code relation, err: %v", err)
			return variable, references, nil // 不强制报错
		}
		variable.Entity = relation.editorEntity
		variable.Callees = relation.callees
	}

	return variable, references, nil
}

func (s *service) SearchCKGDB(ctx context.Context, request *protocol.SearchCKGDBRequest) (*protocol.SearchCKGDBResponse, error) {
	// validate requests
	if request.SymbolIdentifier == "" && request.SymbolRegex == "" {
		logs.CtxError(ctx, "invalid SearchCKGDB request, symbol identifier or regex is empty")
		return nil, errors.New("invalid SearchCKGDB request")
	}

	entityStorage, err := s.dm.GetEntityStorage(ctx, request.ProjectId)
	if err != nil {
		logs.CtxWarn(ctx, "error SearchCKGDB, get storage for project err: %v", err)
		return nil, errors.WithMessagef(err, "error get storage for project")
	}

	var entityTypes []model.EntityType
	for _, t := range request.Types {
		switch t {
		case protocol.EntityType_folder:
			entityTypes = append(entityTypes, model.File)
		case protocol.EntityType_file:
			entityTypes = append(entityTypes, model.File)
		case protocol.EntityType_class:
			entityTypes = append(entityTypes, model.Clazz)
		case protocol.EntityType_method:
			entityTypes = append(entityTypes, model.Method)
		case protocol.EntityType_text:
			entityTypes = append(entityTypes, model.Text)
		}
	}
	var resp protocol.SearchCKGDBResponse

	if request.SymbolIdentifier != "" {
		entities, err := entityStorage.SearchEntityByNameAndTypes(ctx, entityStorage.GetConn(), request.SymbolIdentifier, entityTypes, int(request.EntityNum))
		if err != nil {
			logs.CtxWarn(ctx, "error SearchCKGDB, search entity err: %v", err)
			return nil, errors.WithMessagef(err, "error search entity")
		}
		variables, references, _, err := s.getEntityVariable(ctx, lo.Map(entities, func(entity *model.Entity, _ int) *protocol.Entity {
			return &protocol.Entity{
				ProjectId: request.ProjectId,
				EntityId:  entity.ID,
			}
		}))
		if err != nil {
			logs.CtxWarn(ctx, "error SearchCKGDB, get entity variable err: %v", err)
			return nil, errors.WithMessagef(err, "error get entity variable")
		}
		resp = protocol.SearchCKGDBResponse{
			Variables:  variables,
			References: references,
		}
	} else if request.SymbolRegex != "" {
		entities, err := entityStorage.SearchEntitiesByRegexAndTypes(ctx, entityStorage.GetConn(), request.SymbolRegex, entityTypes, int(request.EntityNum))
		if err != nil {
			logs.CtxWarn(ctx, "error SearchCKGDB, search entity err: %v", err)
			return nil, errors.WithMessagef(err, "error search entity")
		}
		variables, references, _, err := s.getEntityVariable(ctx, lo.Map(entities, func(entity *model.Entity, _ int) *protocol.Entity {
			return &protocol.Entity{
				ProjectId: request.ProjectId,
				EntityId:  entity.ID,
			}
		}))
		if err != nil {
			logs.CtxWarn(ctx, "error SearchCKGDB, get entity variable err: %v", err)
			return nil, errors.WithMessagef(err, "error get entity variable")
		}
		resp = protocol.SearchCKGDBResponse{
			Variables:  variables,
			References: references,
		}
	}

	jsonData, err := (&jsonpb.Marshaler{
		EmitDefaults: true,
	}).MarshalToString(&protocol.SearchCKGDBResponse{
		Variables: resp.Variables,
	})
	if err != nil {
		logs.CtxWarn(ctx, "error marshal retrieve relation result, err: %v", err)
	} else {
		resp.JsonResult = jsonData
	}

	return &resp, nil
}

// limitReferenceSize 旨在通过限制 variables 的大小控制整体 response body 大小
// variables 在 RetrieveRelation API 的 response 中空间占比较大，因为涉及 “content”
func limitReferenceSize(ctx context.Context, vars []*protocol.Variable) []*protocol.Variable {
	recallEvent := ckg_metrics.GetEvent(ctx)
	recallEvent.AddTeaParam(ckg_metrics.TeaParamOriginalVariableNum, len(vars))
	// 统计所有 variable 的大小
	vs := lo.Map(vars, func(v *protocol.Variable, _ int) *variableWithSize {
		jsonData, err := (&jsonpb.Marshaler{EmitDefaults: true}).MarshalToString(v)
		if err != nil {
			logs.CtxWarn(ctx, "error marshal variable, err: %v", err)
			return &variableWithSize{variable: v, size: 0}
		}
		return &variableWithSize{variable: v, size: len(jsonData)}
	})
	sort.Slice(vs, func(i, j int) bool { return vs[i].size < vs[j].size })
	// 找到即将超出限制的那个 variable，[0, maxIndex) 表示后续要保留的 var
	// e.g. 如果第一个最小的就超过 1.5MB，那么返回 [0, 0) 即什么都不取
	maxIndex, accSize := -1, 0
	for i, v := range vs {
		accSize += v.size
		if accSize >= MaxResponseBodyLimit {
			maxIndex = i
			break
		}
	}
	// 记录后续所有需要剔除的 var，并将仍需保留的 var 存在新的 slice 中
	varsToRemoved := make(map[*protocol.Variable]bool)
	if maxIndex >= 0 {
		for i := maxIndex; i < len(vs); i++ {
			varsToRemoved[vs[i].variable] = true
		}
	}
	limitedVars := make([]*protocol.Variable, 0)
	for _, v := range vars {
		if _, ok := varsToRemoved[v]; ok {
			continue
		}
		limitedVars = append(limitedVars, v)
	}
	recallEvent.AddTeaParam(ckg_metrics.TeaParamVariableSize, accSize)
	recallEvent.AddTeaParam(ckg_metrics.TeaParamVarCuttingNum, len(vars)-len(limitedVars))
	return limitedVars
}

type variableWithSize struct {
	variable *protocol.Variable
	size     int
}

type RetrieveRelationMetric struct {
	codeChunkVarNum, textVarNum, folderVarNum, fileVarNum              int
	fileTopLevelVarNum, classVarNum, classTopLevelVarNum, methodVarNum int
	codeChunkRefNum, textRefNum, folderRefNum, fileRefNum              int
	fileTopLevelRefNum, classRefNum, classTopLevelRefNum, methodRefNum int
	textErrNum, folderErrNum, fileErrNum, classErrNum, methodErrNum    int
}

type RetrieveRelationErrorMetric struct {
	GetFileErrNum   int `json:"get_file_err_num"`
	GetFolderErrNum int `json:"get_folder_err_num"`
	GetClassErrNum  int `json:"get_class_err_num"`
	GetMethodErrNum int `json:"get_method_err_num"`
}
