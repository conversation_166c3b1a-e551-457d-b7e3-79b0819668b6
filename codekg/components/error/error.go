package error

import "errors"

var (
	ErrParamNil                 = errors.New("param is nil")
	ErrProjectNotFound          = errors.New("project not found")
	ErrNotFoundRecord           = errors.New("record not found")
	ErrUriNotMatchProject       = errors.New("uri not match project")
	ErrNotMatchEntityType       = errors.New("entity type not match")
	ErrUnmarshal                = errors.New("Unmarshal failed")
	ErrMarshal                  = errors.New("marshal failed")
	ErrFindUriMeta              = errors.New("not find uri meta record")
	ErrParamTypeNotMatch        = errors.New("param type not match")
	ErrRelationAttributeInvalid = errors.New("relation attribute invalid")
	ErrUnrecognizedIntent       = errors.New("unrecognized intent")
	ErrUnrecognizedEntityType   = errors.New("unrecognized entity type")
	ErrPanic                    = errors.New("panic")
	ErrNotMatchStorage          = errors.New("not match storage")
	ErrStorageNotFound          = errors.New("storage not found")
	ErrStorageNotEqual          = errors.New("storage not found")
	ErrOptionInvalid            = errors.New("option is invalid")
	ErrTokenOutOfDate           = errors.New("token is out of date")
	ErrFileCountExceed          = errors.New("file count exceed")
	ErrProjectPathIsVfs         = errors.New("project path is virtual file path")
	ErrUnregisterListener       = errors.New("unregister listener")
	ErrEmptyUserID              = errors.New("empty user id")
	ErrIllegalProjectID         = errors.New("project id is illegal")

	// Retrieve Relation Error
	ErrGetFileRelation   = errors.New("get file relation")
	ErrGetFolderRelation = errors.New("get folder relation")
	ErrGetClassRelation  = errors.New("get class relation")
	ErrGetMethodRelation = errors.New("get method relation")
)
