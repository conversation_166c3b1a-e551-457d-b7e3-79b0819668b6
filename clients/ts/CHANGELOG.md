# CHANGELOG

## 0.0.82
- Fix: 在本地缓存写入Device ID, 从而避免极端场景因Device ID缺失导致的拉取不到实验的问题
- Fix: 补充日志中的文件加密

## 0.0.81
- Fix: main 函数 panic recover + panic 埋点上报
- Fix: 调整 file tree 的输出格式
- Fix: CKG 初始化时删除过期文件的索引数据的速率优化。需要通过 fg 放量。
- Feat: ignore service 新实现，整合了黑白名单过滤规则，统一了 “初始化” 和 “文件变更” 两个场景的文件忽略流程。

## 0.0.80
- Feat: 粗糙地支持 oasis a/b。后续会继续优化

## 0.0.79
- Revert: project not exist 

## 0.0.78
- Fix：日志中加密文件路径
- Fix：SetUp host 使用缓存文件，防止 empty host
- Fix：init 接口 project 粒度黑名单
- Feat：接入 ignore service v2

## 0.0.77
- Fix：日志优化
- Fix：cancel 时支持取消初始化流程，包括文件收集、清除过期数据
- Fix：按照 “不可见字符的比例” 过滤文件，不再根据 “前 1024 位 utf-8 是否合法” 判断，容易误判
- Metric：增加文件变更埋点
- Fix：.trae/.ignore 和 tcc default ignore 的两种忽略规则判断有误

## 0.0.76
- Fix: 通过 sleep 的方式降低 delete 期间的 cpu 占用

## 0.0.75
- Fix：ppid 不通过命令行参数传入，而是从环境变量读取

## 0.0.74
- Fix: 添加 doc 上传文档匹配数量限制
- Fix: 暂时关闭 DEBUG、TRACE 等级日志

## 0.0.73
- Fix: 修复 doc 索引 utf8 校验

## 0.0.72
- Fix: 修复了替换 glob 库后没有正确处理 Windows 路径大小写不敏感个的问题

## 0.0.71
- Fix: 修复了 RetrieveRelevantSnippet 召回为空的情况
- Feat: 降低 log 中 trace/debug 的实际输出等级；优化本地上传文档 glob 匹配效率
- Feat: CKG SDK Debounce

## 0.0.70
- Fix: 修复了 #doc 不能索引的 bug

## 0.0.69
- Fix: 修复了一个类型转换 bug

## 0.0.68
- Fix：文件变更的任务，在 Pop 前后都判断 ignore 规则
- Fix：windows 上无 ppid 自动退出
- Fix：ignore service 从下向上匹配忽略规则，修复 go-gitignore 的 bug
- Fix：进一步修复 UserStorage 的 panic 问题
- Fix：索引相关的 feature gate 只在启动时读取一次，避免索引相关 fg 影响 api 请求逻辑
- Feat：优化查询构建状态返回逻辑
- Metric：TS SDK 增加 “启动时间” 和 “创建 ckg client” 的埋点
- Fix：支持 merlin 用户从 tos 进行下载

## 0.0.67
- Fix: reset tick
- Fix: 读取文件只读前 1024 个字符
- Fix: CKG 删除 skiplndex & ignoreFileLimit 相关逻辑
- Fix: 每个文件 / 文档的变更封成一个 task push 到 queue 里

## 0.0.66
- Feat: 区分索引 docs 和 codes 的索引队列，防止阻塞 workspace 的 ckg 构建
- Fix: 修复每次 RetrieveRelation 的结果多去除了一个 variable 的问题
- Fix: 修复切换新版本服务端 API 后，FileCreate 对 virtual projects 不生效的问题
- Chore: 调整本地 Markdown 切分器的默认参数
- Log: 优化 DocumentRequest handler 的日志输出，不打印 content
- Fix: 启动时不再同步等待 env_codekg.db 创建，懒连接 db
- Fix: 索引状态更新中使用 transction
- Fix: 内存问题，Pop 后读取文件内容，不一次性读所有
- Fix: 修复 windows 路径；优化索引内存占用
- Fix: CKG 完全强制走本地 embedding 索引&召回

## 0.0.65
- Fix: 修复 CKG Embedding V1 接口传错字段参数导致的 500 问题

## 0.0.64
- Fix: 去除字节依赖 & slardar
- Fix：windows 增加 -s -w ldflags 压缩体积
- Fix：handler recover panic
- Fix：切分 & Embedding 切换新版本服务端 API
- Fix：去除未使用的 splitter 和 tokenizer
- Fix：修复用户行为召回 bug
- Fix：CKG 丢弃 5000 行以上文件，与 splitter 对齐

## 0.0.63
= Feat: 本地文档集支持 glob 模式选择部分文件/文件夹进行索引
- Feat: 文档集支持单个文档删除事件（DocumentDelete）
- Fix: 修复 indexFinish channel 设置

## 0.0.62
- Fix：文档集允许任意设置 project id
- Fix：过滤带有不可见字符的文件

## 0.0.61
- Fix: 增加索引总行数限制

## 0.0.60
- Metric: CKG RetrieveEntity 事件上报优化，透传至外部
- Fix: grpc body 过长裁剪

## 0.0.59
- Feat: CKG 支持索引用户上传的文档 & 本地已爬取的文档
- Feat: 用户行为召回二期，Git-Graph + 非 CKG 实体
- Fix: TS SDK folder_paths 参数未传
- Fix: 大仓库索引时 RetrieveRelation 过慢，统一事务
- Metric: CKG RetrieveRelation 事件上报优化，透传至外部
- Test: 流水线测试用例适配 batch index 场景
- Feat: 增加 chunking v2 的 feature gate 开关

## 0.0.58
- Refactor: 拆分 data_manager.go
- Feat: 支持风控参数上报
- Feat: 增加用户行为召回的 feature gate 开关

## 0.0.57
- Feat: 支持自定义 ignore 文件
- Feat: 支持索引时 Embedding 请求组 Batch 发送
- Fix: CKG 默认使用本地 Embedding 存储流程、默认使用 sqlite_vec 作为向量库
- Fix: CKG 启动时异步请求 bytegate
- Fix: 透出 CKG 端口号
- Fix: 索引时仅忽略项目目录下的隐藏文件夹和隐藏文件

## 0.0.56
- Fix: 修复了内存中 db 路径无法对应磁盘 db 的问题
- Fix: 增加了针对 Native IDE 内场的 SourceProduct

## 0.0.55
- Fix: 修复了在 Windows 上不能删除 sqlite db 导致的重复索引 bug。目前只写入新 project 记录到 env db

## 0.0.54
- Feat: CKG 召回数量改为 AB/TCC 下发
- Feat: Embedding/切分 AB
- Feat: CKG 支持 deamon 方式启动
- Fix: 增加下载失败时的错误分类

## 0.0.53
- Fix: 去掉启动参数的强制校验
- Fix: 修复 “空项目” 判断错误
- Fix: IDE 使用 user id 替换 user name 后 token 可能冲突的问题

## 0.0.52
- Feat: 支持 ForceLocalEmbedding
- Fix: 修复grpc召回实体content序列化失败的问题
- Fix: 添加调用知识库api网络问题、远程虚拟文件系统路径相关埋点
- Fix: 新增知识库api网络问题重试类型
- Fix: getCKGBuildStatus接口增加索引成功、失败、总索引文件数作为返回结果
- Feat: CKG 当 PPID 变化时自动退出

## 0.0.51
- Fix: CKG 倒序返回

## 0.0.50
- Feat: CKG 支持 Rerank
- Feat: CKG 支持 AB 接口

## 0.0.47
- Fix：修复之前召回时检查是否为二进制的 bug
- Fix：索引时检查是否为二进制
- Fix：远端索引接口增加重试
- Fix：索引进度只算文件
- Fix：多 project 召回太多实体 & 调用太多次 rerank，迁出 rerank
- Fix：修复之前 “readonly database” 的 bug
- Fix：修复删除《entity id 包含 “单引号”》的实体时的错误

## 0.0.46
- Fix: 修复了本地 SQLite-Vec 召回过慢的问题。

## 0.0.45
- Fix: 增加了一系列埋点，用于展示 CKG 各类行为。

## 0.0.44
- Fix: 修复了多 project 场景下，#workspace 走到 #folder 逻辑的错误。

## 0.0.43
- Fix: ckg启动埋点上报新增版本号以及新增启动错误类型

## 0.0.42
- Fix: 增加了失败时默认 TCC 配置

## 0.0.41
- Fix: 删除了测试时带上的 ppe

## 0.0.40
- Fix: 增加了召回为空时的埋点上报，上报了召回为空时的索引进度上报

## 0.0.39
- Fix: 修复了 CKG 进程不退出的 bug。

## 0.0.38
- Fix: 修复了实际 “embedding storage” 与其类型在数据库中不匹配的 bug。

## 0.0.37
- Fix: 增加 “index_type” 索引方式以及 “embedding_storage_type” 向量库类型的埋点属性。
- Fix: 上报 “ide_version” 和 “extension_version” 的埋点属性。
- Fix: 不再逆序输出 reference。

## 0.0.36
- Fix: 修改数据库写入项目记录 project_id 的位置。避免大仓库重复索引。
- Fix: 增加通过TCC配置的远端强制索引的最大限制。
- Fix: 增加通过TCC配置的 debounce，仅对 DocumentChange 生效。
- Fix: 修改127.0.0.1。
- Fix: 增加 retry 条件，避免 AGW 限流导致的 SplitFiles 和 Embedding 失败。
- Fix: 增加 SplitFiles 错误日志。

## 0.0.35
- Fix: 检查二进制文件时，stat 报错的 bug。

## 0.0.34
- Feat: 增加了召回为空时的埋点上报，进一步增加了索引时数据落库的埋点上报。

## 0.0.33
- Feat: 支持 #Folder 召回。
- Deps: 引入了 sqlite3，作为 sqlite-vec 插件的依赖。sqlite3 和 sqlite-vec 均为源码引入。
- Feat: 关闭 Marscode 参数校验

## 0.0.32
- Fix: 去除 CurrentEditorInfo 字段中的references字段
- Feat: 默认忽略 go.sum，yarn.lock，package-lock.json，pnpm-lock.yaml

## 0.0.31
- Fix: 修复 windows 跨平台打包的问题
- Fix: 修复 CKG.Init 中 client 初始化失败的问题

## 0.0.30
- Feat: 新增架构不匹配时的错误码

## 0.0.29
- Feat: 将 CKG 二进制从项目目录中挪出，集中在某个 binary storage path 下管理

## 0.0.28
- Feat: 接入 Slarder SDK，支持 Linux（x64），Mac（x64 & arm64），Windows（x64）
- Feat: 统计 10 万文件的仓库大小

## 0.0.27
- Fix: CKG 提供 static 的初始化方法，去除 createProcess，createClient 方法
- Fix: CKG 在重启时能保留先前的索引进度

# 0.0.26
- Feat: 添加 logPath 参数
- Fix：只有非手动触发状态下，才会走 local embedding

# 0.0.25
- Feat: 增加了版本与特性管理。引入了查询 CKG 是否需要重新索引的接口

# 0.0.24
- Fix: 修复了 “embeddingStorage 数据不完整导致读取失败” 引发的 panic
- Update: ckg sdk 中加了 local embedding 参数

# 0.0.23
- Fix: 删除 config 定时获取逻辑
- Fix: 删除 cpu/mem 监控组件

# 0.0.22
- Fix: 修复了 “无法读取 feature_config 对 TCC config `Enabled` 字段的封装” 的问题

# 0.0.21
- Feat: 新增了 rerank/NER/Alias 的 TCC 开关，新增了新版本 Rerank 生效的逻辑
- Fix: 修复了 rerank score = 0 的问题
- Feat: CKG 不再校验 codeverse 的启动参数

# 0.0.20
- Fix: 修复了 RetrieveEntity 和 RetrieveRelation 时可能召回二进制文件的问题

# 0.0.19
- Feat: 增加了 native_ide 的 product 类型

# 0.0.18
- Fix: 修复了查询向量库时，由于未初始化仓库，导致的向量库为空 & chromem-go 报错的问题。

# 0.0.17
- Fix: 修复了实体召回时未去重的问题。

# 0.0.16
- Fix: 修复 delete/cancel index bug

# 0.0.15
- Fix: 关闭了 rerank，删除了 default ignore 规则。

# 0.0.14
- Fix: 增加了召回实体上报埋点，修复了 SplitFiles 命中 embedding 缓存的逻辑。
- Fix: 修复了在实体召回、计算 embedding 两个不同场景的 OnlineCluster，OfflineCluster 的选择。

## 0.0.13
- Feat: 启动Error日志完善
- Fix: 修复启动上报error code错误问题
- Fix: 降低linux编译镜像版本

## 0.0.12
- Fix: 修复了 0.0.11 版本的缺陷：本地索引遇到大量文件时，需要返回特定类型错误。

## 0.0.11
- Feat: 增加本地索引逻辑

## 0.0.9
- Fix: error_code上报为空修复

## 0.0.8
- Feat: 索引进度接口

## 0.0.7
- Fix: user file content修复

## 0.0.5
- Feat: 新增 searchCKGDB 接口用于 dev0

## 0.0.4
- Feat: 更新版本号，正式接入ai-local-server、codeverse

## 0.0.3    
- Feat: 增加二进制md5校验
- Fix: 同步golang版本，接入codeverse适配
