from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class Code(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    succeed: _ClassVar[Code]
    unknown_error: _ClassVar[Code]
    panic: _ClassVar[Code]
    user_id_is_missing: _ClassVar[Code]
    file_limit_exceed: _ClassVar[Code]
    invalid_token: _ClassVar[Code]
    nil_client: _ClassVar[Code]

class EntityType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    folder: _ClassVar[EntityType]
    file: _ClassVar[EntityType]
    class: _ClassVar[EntityType]
    method: _ClassVar[EntityType]
    text: _ClassVar[EntityType]

class BuildStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    building: _ClassVar[BuildStatus]
    finished: _ClassVar[BuildStatus]
    failed: _ClassVar[BuildStatus]

class RecallType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    recall_type_user_specified: _ClassVar[RecallType]
    recall_type_embedding: _ClassVar[RecallType]
    recall_type_ner: _ClassVar[RecallType]
    recall_type_relation_by_user_action_trace: _ClassVar[RecallType]
    recall_type_relation_by_git_relevance: _ClassVar[RecallType]

class SnippetType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    snippet_type_code: _ClassVar[SnippetType]
    snippet_type_folder_tree: _ClassVar[SnippetType]
    snippet_type_file: _ClassVar[SnippetType]
succeed: Code
unknown_error: Code
panic: Code
user_id_is_missing: Code
file_limit_exceed: Code
invalid_token: Code
nil_client: Code
folder: EntityType
file: EntityType
class: EntityType
method: EntityType
text: EntityType
building: BuildStatus
finished: BuildStatus
failed: BuildStatus
recall_type_user_specified: RecallType
recall_type_embedding: RecallType
recall_type_ner: RecallType
recall_type_relation_by_user_action_trace: RecallType
recall_type_relation_by_git_relevance: RecallType
snippet_type_code: SnippetType
snippet_type_folder_tree: SnippetType
snippet_type_file: SnippetType

class Empty(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class Error(_message.Message):
    __slots__ = ("message", "stack")
    MESSAGE_FIELD_NUMBER: _ClassVar[int]
    STACK_FIELD_NUMBER: _ClassVar[int]
    message: str
    stack: str
    def __init__(self, message: _Optional[str] = ..., stack: _Optional[str] = ...) -> None: ...

class RefreshTokenRequest(_message.Message):
    __slots__ = ("token", "user_id")
    TOKEN_FIELD_NUMBER: _ClassVar[int]
    USER_ID_FIELD_NUMBER: _ClassVar[int]
    token: str
    user_id: str
    def __init__(self, token: _Optional[str] = ..., user_id: _Optional[str] = ...) -> None: ...

class RefreshTokenResponse(_message.Message):
    __slots__ = ("code", "error")
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    code: Code
    error: Error
    def __init__(self, code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...

class Project(_message.Message):
    __slots__ = ("project_id", "storage_path", "ignore_file")
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    STORAGE_PATH_FIELD_NUMBER: _ClassVar[int]
    IGNORE_FILE_FIELD_NUMBER: _ClassVar[int]
    project_id: str
    storage_path: str
    ignore_file: str
    def __init__(self, project_id: _Optional[str] = ..., storage_path: _Optional[str] = ..., ignore_file: _Optional[str] = ...) -> None: ...

class InitRequest(_message.Message):
    __slots__ = ("project_ids", "ignore_file_limit", "projects", "user_id")
    PROJECT_IDS_FIELD_NUMBER: _ClassVar[int]
    IGNORE_FILE_LIMIT_FIELD_NUMBER: _ClassVar[int]
    PROJECTS_FIELD_NUMBER: _ClassVar[int]
    USER_ID_FIELD_NUMBER: _ClassVar[int]
    project_ids: _containers.RepeatedScalarFieldContainer[str]
    ignore_file_limit: bool
    projects: _containers.RepeatedCompositeFieldContainer[Project]
    user_id: str
    def __init__(self, project_ids: _Optional[_Iterable[str]] = ..., ignore_file_limit: bool = ..., projects: _Optional[_Iterable[_Union[Project, _Mapping]]] = ..., user_id: _Optional[str] = ...) -> None: ...

class InitResponse(_message.Message):
    __slots__ = ("code", "error")
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    code: Code
    error: Error
    def __init__(self, code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...

class VirtualProject(_message.Message):
    __slots__ = ("project_id", "uri", "relative_globs_to_load")
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    URI_FIELD_NUMBER: _ClassVar[int]
    RELATIVE_GLOBS_TO_LOAD_FIELD_NUMBER: _ClassVar[int]
    project_id: str
    uri: str
    relative_globs_to_load: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, project_id: _Optional[str] = ..., uri: _Optional[str] = ..., relative_globs_to_load: _Optional[_Iterable[str]] = ...) -> None: ...

class InitVirtualProjectsRequest(_message.Message):
    __slots__ = ("projects", "load_files_from_fs", "user_id")
    PROJECTS_FIELD_NUMBER: _ClassVar[int]
    LOAD_FILES_FROM_FS_FIELD_NUMBER: _ClassVar[int]
    USER_ID_FIELD_NUMBER: _ClassVar[int]
    projects: _containers.RepeatedCompositeFieldContainer[VirtualProject]
    load_files_from_fs: bool
    user_id: str
    def __init__(self, projects: _Optional[_Iterable[_Union[VirtualProject, _Mapping]]] = ..., load_files_from_fs: bool = ..., user_id: _Optional[str] = ...) -> None: ...

class InitVirtualProjectsResponse(_message.Message):
    __slots__ = ("code", "error")
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    code: Code
    error: Error
    def __init__(self, code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...

class Document(_message.Message):
    __slots__ = ("uri", "name", "content", "project_id")
    URI_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    uri: str
    name: str
    content: str
    project_id: str
    def __init__(self, uri: _Optional[str] = ..., name: _Optional[str] = ..., content: _Optional[str] = ..., project_id: _Optional[str] = ...) -> None: ...

class DocumentRequest(_message.Message):
    __slots__ = ("file_paths", "documents", "userID")
    FILE_PATHS_FIELD_NUMBER: _ClassVar[int]
    DOCUMENTS_FIELD_NUMBER: _ClassVar[int]
    USERID_FIELD_NUMBER: _ClassVar[int]
    file_paths: _containers.RepeatedScalarFieldContainer[str]
    documents: _containers.RepeatedCompositeFieldContainer[Document]
    userID: str
    def __init__(self, file_paths: _Optional[_Iterable[str]] = ..., documents: _Optional[_Iterable[_Union[Document, _Mapping]]] = ..., userID: _Optional[str] = ...) -> None: ...

class DocumentResponse(_message.Message):
    __slots__ = ("err_msgs", "code", "error")
    class ErrMsgsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    ERR_MSGS_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    err_msgs: _containers.ScalarMap[str, str]
    code: Code
    error: Error
    def __init__(self, err_msgs: _Optional[_Mapping[str, str]] = ..., code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...

class DeleteIndexRequest(_message.Message):
    __slots__ = ("project_id",)
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    project_id: str
    def __init__(self, project_id: _Optional[str] = ...) -> None: ...

class DeleteIndexResponse(_message.Message):
    __slots__ = ("succeed", "code", "error")
    SUCCEED_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    succeed: bool
    code: Code
    error: Error
    def __init__(self, succeed: bool = ..., code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...

class CurrentEditorInfo(_message.Message):
    __slots__ = ("file_path", "project_id", "cursor_line", "select_code_range", "select_code_content", "visible_code_range", "visible_code_content", "user_file_content")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    CURSOR_LINE_FIELD_NUMBER: _ClassVar[int]
    SELECT_CODE_RANGE_FIELD_NUMBER: _ClassVar[int]
    SELECT_CODE_CONTENT_FIELD_NUMBER: _ClassVar[int]
    VISIBLE_CODE_RANGE_FIELD_NUMBER: _ClassVar[int]
    VISIBLE_CODE_CONTENT_FIELD_NUMBER: _ClassVar[int]
    USER_FILE_CONTENT_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    project_id: str
    cursor_line: int
    select_code_range: Range
    select_code_content: str
    visible_code_range: Range
    visible_code_content: str
    user_file_content: str
    def __init__(self, file_path: _Optional[str] = ..., project_id: _Optional[str] = ..., cursor_line: _Optional[int] = ..., select_code_range: _Optional[_Union[Range, _Mapping]] = ..., select_code_content: _Optional[str] = ..., visible_code_range: _Optional[_Union[Range, _Mapping]] = ..., visible_code_content: _Optional[str] = ..., user_file_content: _Optional[str] = ...) -> None: ...

class RetrieveEntityRequest(_message.Message):
    __slots__ = ("project_ids", "user_message", "editor_info", "expect_entity_types", "entity_num", "intent", "folder_paths", "user_id", "session_id")
    PROJECT_IDS_FIELD_NUMBER: _ClassVar[int]
    USER_MESSAGE_FIELD_NUMBER: _ClassVar[int]
    EDITOR_INFO_FIELD_NUMBER: _ClassVar[int]
    EXPECT_ENTITY_TYPES_FIELD_NUMBER: _ClassVar[int]
    ENTITY_NUM_FIELD_NUMBER: _ClassVar[int]
    INTENT_FIELD_NUMBER: _ClassVar[int]
    FOLDER_PATHS_FIELD_NUMBER: _ClassVar[int]
    USER_ID_FIELD_NUMBER: _ClassVar[int]
    SESSION_ID_FIELD_NUMBER: _ClassVar[int]
    project_ids: _containers.RepeatedScalarFieldContainer[str]
    user_message: str
    editor_info: CurrentEditorInfo
    expect_entity_types: _containers.RepeatedScalarFieldContainer[EntityType]
    entity_num: int
    intent: str
    folder_paths: _containers.RepeatedScalarFieldContainer[str]
    user_id: str
    session_id: str
    def __init__(self, project_ids: _Optional[_Iterable[str]] = ..., user_message: _Optional[str] = ..., editor_info: _Optional[_Union[CurrentEditorInfo, _Mapping]] = ..., expect_entity_types: _Optional[_Iterable[_Union[EntityType, str]]] = ..., entity_num: _Optional[int] = ..., intent: _Optional[str] = ..., folder_paths: _Optional[_Iterable[str]] = ..., user_id: _Optional[str] = ..., session_id: _Optional[str] = ...) -> None: ...

class Entity(_message.Message):
    __slots__ = ("project_id", "entity_id")
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    ENTITY_ID_FIELD_NUMBER: _ClassVar[int]
    project_id: str
    entity_id: str
    def __init__(self, project_id: _Optional[str] = ..., entity_id: _Optional[str] = ...) -> None: ...

class RetrieveEntityResponse(_message.Message):
    __slots__ = ("entities_by_user_message", "query_embedding_models", "vector_embedding_models", "entity_chunking_methods", "empty_recall_reason", "code", "error")
    ENTITIES_BY_USER_MESSAGE_FIELD_NUMBER: _ClassVar[int]
    QUERY_EMBEDDING_MODELS_FIELD_NUMBER: _ClassVar[int]
    VECTOR_EMBEDDING_MODELS_FIELD_NUMBER: _ClassVar[int]
    ENTITY_CHUNKING_METHODS_FIELD_NUMBER: _ClassVar[int]
    EMPTY_RECALL_REASON_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    entities_by_user_message: _containers.RepeatedCompositeFieldContainer[Entity]
    query_embedding_models: _containers.RepeatedScalarFieldContainer[str]
    vector_embedding_models: _containers.RepeatedScalarFieldContainer[str]
    entity_chunking_methods: _containers.RepeatedScalarFieldContainer[str]
    empty_recall_reason: str
    code: Code
    error: Error
    def __init__(self, entities_by_user_message: _Optional[_Iterable[_Union[Entity, _Mapping]]] = ..., query_embedding_models: _Optional[_Iterable[str]] = ..., vector_embedding_models: _Optional[_Iterable[str]] = ..., entity_chunking_methods: _Optional[_Iterable[str]] = ..., empty_recall_reason: _Optional[str] = ..., code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...

class RetrieveRelationRequest(_message.Message):
    __slots__ = ("entities_by_user_message", "editor_info", "user_id")
    ENTITIES_BY_USER_MESSAGE_FIELD_NUMBER: _ClassVar[int]
    EDITOR_INFO_FIELD_NUMBER: _ClassVar[int]
    USER_ID_FIELD_NUMBER: _ClassVar[int]
    entities_by_user_message: _containers.RepeatedCompositeFieldContainer[Entity]
    editor_info: CurrentEditorInfo
    user_id: str
    def __init__(self, entities_by_user_message: _Optional[_Iterable[_Union[Entity, _Mapping]]] = ..., editor_info: _Optional[_Union[CurrentEditorInfo, _Mapping]] = ..., user_id: _Optional[str] = ...) -> None: ...

class CodeChunkVariable(_message.Message):
    __slots__ = ("file_path", "start_line", "end_line", "content", "provider")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    PROVIDER_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    start_line: int
    end_line: int
    content: str
    provider: str
    def __init__(self, file_path: _Optional[str] = ..., start_line: _Optional[int] = ..., end_line: _Optional[int] = ..., content: _Optional[str] = ..., provider: _Optional[str] = ...) -> None: ...

class TextVariable(_message.Message):
    __slots__ = ("file_path", "start_line", "end_line", "content", "provider")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    PROVIDER_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    start_line: int
    end_line: int
    content: str
    provider: str
    def __init__(self, file_path: _Optional[str] = ..., start_line: _Optional[int] = ..., end_line: _Optional[int] = ..., content: _Optional[str] = ..., provider: _Optional[str] = ...) -> None: ...

class UsefulFileInfo(_message.Message):
    __slots__ = ("file_path", "content")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    content: str
    def __init__(self, file_path: _Optional[str] = ..., content: _Optional[str] = ...) -> None: ...

class FolderVariable(_message.Message):
    __slots__ = ("file_path", "folder_tree", "useful_files", "provider")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    FOLDER_TREE_FIELD_NUMBER: _ClassVar[int]
    USEFUL_FILES_FIELD_NUMBER: _ClassVar[int]
    PROVIDER_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    folder_tree: str
    useful_files: _containers.RepeatedCompositeFieldContainer[UsefulFileInfo]
    provider: str
    def __init__(self, file_path: _Optional[str] = ..., folder_tree: _Optional[str] = ..., useful_files: _Optional[_Iterable[_Union[UsefulFileInfo, _Mapping]]] = ..., provider: _Optional[str] = ...) -> None: ...

class FileVariable(_message.Message):
    __slots__ = ("file_path", "content", "comment", "provider")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    COMMENT_FIELD_NUMBER: _ClassVar[int]
    PROVIDER_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    content: str
    comment: str
    provider: str
    def __init__(self, file_path: _Optional[str] = ..., content: _Optional[str] = ..., comment: _Optional[str] = ..., provider: _Optional[str] = ...) -> None: ...

class FileTopLevelVariable(_message.Message):
    __slots__ = ("file_path", "start_line", "end_line", "content", "provider")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    PROVIDER_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    start_line: int
    end_line: int
    content: str
    provider: str
    def __init__(self, file_path: _Optional[str] = ..., start_line: _Optional[int] = ..., end_line: _Optional[int] = ..., content: _Optional[str] = ..., provider: _Optional[str] = ...) -> None: ...

class Member(_message.Message):
    __slots__ = ("content",)
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    content: str
    def __init__(self, content: _Optional[str] = ...) -> None: ...

class ClassVariable(_message.Message):
    __slots__ = ("file_path", "start_line", "end_line", "name", "comment", "content", "members", "methods", "test_functions", "provider")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    COMMENT_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    MEMBERS_FIELD_NUMBER: _ClassVar[int]
    METHODS_FIELD_NUMBER: _ClassVar[int]
    TEST_FUNCTIONS_FIELD_NUMBER: _ClassVar[int]
    PROVIDER_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    start_line: int
    end_line: int
    name: str
    comment: str
    content: str
    members: _containers.RepeatedCompositeFieldContainer[Member]
    methods: _containers.RepeatedCompositeFieldContainer[MethodInfo]
    test_functions: _containers.RepeatedCompositeFieldContainer[MethodInfo]
    provider: str
    def __init__(self, file_path: _Optional[str] = ..., start_line: _Optional[int] = ..., end_line: _Optional[int] = ..., name: _Optional[str] = ..., comment: _Optional[str] = ..., content: _Optional[str] = ..., members: _Optional[_Iterable[_Union[Member, _Mapping]]] = ..., methods: _Optional[_Iterable[_Union[MethodInfo, _Mapping]]] = ..., test_functions: _Optional[_Iterable[_Union[MethodInfo, _Mapping]]] = ..., provider: _Optional[str] = ...) -> None: ...

class ClassTopLevelVariable(_message.Message):
    __slots__ = ("file_path", "start_line", "end_line", "content", "provider")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    PROVIDER_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    start_line: int
    end_line: int
    content: str
    provider: str
    def __init__(self, file_path: _Optional[str] = ..., start_line: _Optional[int] = ..., end_line: _Optional[int] = ..., content: _Optional[str] = ..., provider: _Optional[str] = ...) -> None: ...

class MethodInfo(_message.Message):
    __slots__ = ("file_path", "start_line", "end_line", "name", "signature", "comment", "content")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    SIGNATURE_FIELD_NUMBER: _ClassVar[int]
    COMMENT_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    start_line: int
    end_line: int
    name: str
    signature: str
    comment: str
    content: str
    def __init__(self, file_path: _Optional[str] = ..., start_line: _Optional[int] = ..., end_line: _Optional[int] = ..., name: _Optional[str] = ..., signature: _Optional[str] = ..., comment: _Optional[str] = ..., content: _Optional[str] = ...) -> None: ...

class MethodVariable(_message.Message):
    __slots__ = ("file_path", "start_line", "end_line", "name", "signature", "comment", "content", "callers", "callees", "test_functions", "provider")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    SIGNATURE_FIELD_NUMBER: _ClassVar[int]
    COMMENT_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    CALLERS_FIELD_NUMBER: _ClassVar[int]
    CALLEES_FIELD_NUMBER: _ClassVar[int]
    TEST_FUNCTIONS_FIELD_NUMBER: _ClassVar[int]
    PROVIDER_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    start_line: int
    end_line: int
    name: str
    signature: str
    comment: str
    content: str
    callers: _containers.RepeatedCompositeFieldContainer[MethodInfo]
    callees: _containers.RepeatedCompositeFieldContainer[MethodInfo]
    test_functions: _containers.RepeatedCompositeFieldContainer[MethodInfo]
    provider: str
    def __init__(self, file_path: _Optional[str] = ..., start_line: _Optional[int] = ..., end_line: _Optional[int] = ..., name: _Optional[str] = ..., signature: _Optional[str] = ..., comment: _Optional[str] = ..., content: _Optional[str] = ..., callers: _Optional[_Iterable[_Union[MethodInfo, _Mapping]]] = ..., callees: _Optional[_Iterable[_Union[MethodInfo, _Mapping]]] = ..., test_functions: _Optional[_Iterable[_Union[MethodInfo, _Mapping]]] = ..., provider: _Optional[str] = ...) -> None: ...

class Variable(_message.Message):
    __slots__ = ("code_chunk", "text", "folder", "file", "file_top_level", "class_top_level", "method")
    CODE_CHUNK_FIELD_NUMBER: _ClassVar[int]
    TEXT_FIELD_NUMBER: _ClassVar[int]
    FOLDER_FIELD_NUMBER: _ClassVar[int]
    FILE_FIELD_NUMBER: _ClassVar[int]
    FILE_TOP_LEVEL_FIELD_NUMBER: _ClassVar[int]
    CLASS_FIELD_NUMBER: _ClassVar[int]
    CLASS_TOP_LEVEL_FIELD_NUMBER: _ClassVar[int]
    METHOD_FIELD_NUMBER: _ClassVar[int]
    code_chunk: CodeChunkVariable
    text: TextVariable
    folder: FolderVariable
    file: FileVariable
    file_top_level: FileTopLevelVariable
    class_top_level: ClassTopLevelVariable
    method: MethodVariable
    def __init__(self, code_chunk: _Optional[_Union[CodeChunkVariable, _Mapping]] = ..., text: _Optional[_Union[TextVariable, _Mapping]] = ..., folder: _Optional[_Union[FolderVariable, _Mapping]] = ..., file: _Optional[_Union[FileVariable, _Mapping]] = ..., file_top_level: _Optional[_Union[FileTopLevelVariable, _Mapping]] = ..., class_top_level: _Optional[_Union[ClassTopLevelVariable, _Mapping]] = ..., method: _Optional[_Union[MethodVariable, _Mapping]] = ..., **kwargs) -> None: ...

class SelectedMethodInfo(_message.Message):
    __slots__ = ("start_line", "end_line", "name", "comment", "content")
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    COMMENT_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    start_line: int
    end_line: int
    name: str
    comment: str
    content: str
    def __init__(self, start_line: _Optional[int] = ..., end_line: _Optional[int] = ..., name: _Optional[str] = ..., comment: _Optional[str] = ..., content: _Optional[str] = ...) -> None: ...

class SelectedClassInfo(_message.Message):
    __slots__ = ("start_line", "end_line", "name", "comment", "content", "selected_methods")
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    COMMENT_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    SELECTED_METHODS_FIELD_NUMBER: _ClassVar[int]
    start_line: int
    end_line: int
    name: str
    comment: str
    content: str
    selected_methods: _containers.RepeatedCompositeFieldContainer[SelectedMethodInfo]
    def __init__(self, start_line: _Optional[int] = ..., end_line: _Optional[int] = ..., name: _Optional[str] = ..., comment: _Optional[str] = ..., content: _Optional[str] = ..., selected_methods: _Optional[_Iterable[_Union[SelectedMethodInfo, _Mapping]]] = ...) -> None: ...

class CurrentEditorEntity(_message.Message):
    __slots__ = ("selected_classes", "selected_methods")
    SELECTED_CLASSES_FIELD_NUMBER: _ClassVar[int]
    SELECTED_METHODS_FIELD_NUMBER: _ClassVar[int]
    selected_classes: _containers.RepeatedCompositeFieldContainer[SelectedClassInfo]
    selected_methods: _containers.RepeatedCompositeFieldContainer[SelectedMethodInfo]
    def __init__(self, selected_classes: _Optional[_Iterable[_Union[SelectedClassInfo, _Mapping]]] = ..., selected_methods: _Optional[_Iterable[_Union[SelectedMethodInfo, _Mapping]]] = ...) -> None: ...

class Range(_message.Message):
    __slots__ = ("start_line", "end_line")
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    start_line: int
    end_line: int
    def __init__(self, start_line: _Optional[int] = ..., end_line: _Optional[int] = ...) -> None: ...

class RefClassInfo(_message.Message):
    __slots__ = ("file_path", "start_line", "end_line", "name", "content")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    start_line: str
    end_line: str
    name: str
    content: str
    def __init__(self, file_path: _Optional[str] = ..., start_line: _Optional[str] = ..., end_line: _Optional[str] = ..., name: _Optional[str] = ..., content: _Optional[str] = ...) -> None: ...

class RefTypeInfo(_message.Message):
    __slots__ = ("file_path", "start_line", "end_line", "name", "content")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    start_line: str
    end_line: str
    name: str
    content: str
    def __init__(self, file_path: _Optional[str] = ..., start_line: _Optional[str] = ..., end_line: _Optional[str] = ..., name: _Optional[str] = ..., content: _Optional[str] = ...) -> None: ...

class CurrentEditorVariable(_message.Message):
    __slots__ = ("file_path", "content", "cursor_line", "select_range", "visible_range", "entity", "callees", "ref_classes", "ref_types")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    CURSOR_LINE_FIELD_NUMBER: _ClassVar[int]
    SELECT_RANGE_FIELD_NUMBER: _ClassVar[int]
    VISIBLE_RANGE_FIELD_NUMBER: _ClassVar[int]
    ENTITY_FIELD_NUMBER: _ClassVar[int]
    CALLEES_FIELD_NUMBER: _ClassVar[int]
    REF_CLASSES_FIELD_NUMBER: _ClassVar[int]
    REF_TYPES_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    content: str
    cursor_line: int
    select_range: Range
    visible_range: Range
    entity: CurrentEditorEntity
    callees: _containers.RepeatedCompositeFieldContainer[MethodInfo]
    ref_classes: _containers.RepeatedCompositeFieldContainer[RefClassInfo]
    ref_types: _containers.RepeatedCompositeFieldContainer[RefTypeInfo]
    def __init__(self, file_path: _Optional[str] = ..., content: _Optional[str] = ..., cursor_line: _Optional[int] = ..., select_range: _Optional[_Union[Range, _Mapping]] = ..., visible_range: _Optional[_Union[Range, _Mapping]] = ..., entity: _Optional[_Union[CurrentEditorEntity, _Mapping]] = ..., callees: _Optional[_Iterable[_Union[MethodInfo, _Mapping]]] = ..., ref_classes: _Optional[_Iterable[_Union[RefClassInfo, _Mapping]]] = ..., ref_types: _Optional[_Iterable[_Union[RefTypeInfo, _Mapping]]] = ...) -> None: ...

class Reference(_message.Message):
    __slots__ = ("file_path", "start_line", "end_line", "uri", "name")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    URI_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    start_line: int
    end_line: int
    uri: str
    name: str
    def __init__(self, file_path: _Optional[str] = ..., start_line: _Optional[int] = ..., end_line: _Optional[int] = ..., uri: _Optional[str] = ..., name: _Optional[str] = ...) -> None: ...

class RetrieveRelationResponse(_message.Message):
    __slots__ = ("current_editor", "variables", "references", "json_result", "err_metric", "code", "error")
    CURRENT_EDITOR_FIELD_NUMBER: _ClassVar[int]
    VARIABLES_FIELD_NUMBER: _ClassVar[int]
    REFERENCES_FIELD_NUMBER: _ClassVar[int]
    JSON_RESULT_FIELD_NUMBER: _ClassVar[int]
    ERR_METRIC_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    current_editor: CurrentEditorVariable
    variables: _containers.RepeatedCompositeFieldContainer[Variable]
    references: _containers.RepeatedCompositeFieldContainer[Reference]
    json_result: str
    err_metric: str
    code: Code
    error: Error
    def __init__(self, current_editor: _Optional[_Union[CurrentEditorVariable, _Mapping]] = ..., variables: _Optional[_Iterable[_Union[Variable, _Mapping]]] = ..., references: _Optional[_Iterable[_Union[Reference, _Mapping]]] = ..., json_result: _Optional[str] = ..., err_metric: _Optional[str] = ..., code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...

class GetBuildStatusRequest(_message.Message):
    __slots__ = ("project_ids",)
    PROJECT_IDS_FIELD_NUMBER: _ClassVar[int]
    project_ids: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, project_ids: _Optional[_Iterable[str]] = ...) -> None: ...

class DocumentBuildStatus(_message.Message):
    __slots__ = ("status", "uri", "name")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    URI_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    status: BuildStatus
    uri: str
    name: str
    def __init__(self, status: _Optional[_Union[BuildStatus, str]] = ..., uri: _Optional[str] = ..., name: _Optional[str] = ...) -> None: ...

class ProjectBuildStatus(_message.Message):
    __slots__ = ("status", "progress", "success_index_files", "failed_index_files", "total_index_files", "empty_project", "documents_status")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    PROGRESS_FIELD_NUMBER: _ClassVar[int]
    SUCCESS_INDEX_FILES_FIELD_NUMBER: _ClassVar[int]
    FAILED_INDEX_FILES_FIELD_NUMBER: _ClassVar[int]
    TOTAL_INDEX_FILES_FIELD_NUMBER: _ClassVar[int]
    EMPTY_PROJECT_FIELD_NUMBER: _ClassVar[int]
    DOCUMENTS_STATUS_FIELD_NUMBER: _ClassVar[int]
    status: BuildStatus
    progress: float
    success_index_files: int
    failed_index_files: int
    total_index_files: int
    empty_project: bool
    documents_status: _containers.RepeatedCompositeFieldContainer[DocumentBuildStatus]
    def __init__(self, status: _Optional[_Union[BuildStatus, str]] = ..., progress: _Optional[float] = ..., success_index_files: _Optional[int] = ..., failed_index_files: _Optional[int] = ..., total_index_files: _Optional[int] = ..., empty_project: bool = ..., documents_status: _Optional[_Iterable[_Union[DocumentBuildStatus, _Mapping]]] = ...) -> None: ...

class GetBuildStatusResponse(_message.Message):
    __slots__ = ("status", "code", "error")
    class StatusEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: ProjectBuildStatus
        def __init__(self, key: _Optional[str] = ..., value: _Optional[_Union[ProjectBuildStatus, _Mapping]] = ...) -> None: ...
    STATUS_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    status: _containers.MessageMap[str, ProjectBuildStatus]
    code: Code
    error: Error
    def __init__(self, status: _Optional[_Mapping[str, ProjectBuildStatus]] = ..., code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...

class GetDocumentsIndexStatusRequest(_message.Message):
    __slots__ = ("project_ids",)
    PROJECT_IDS_FIELD_NUMBER: _ClassVar[int]
    project_ids: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, project_ids: _Optional[_Iterable[str]] = ...) -> None: ...

class ProjectDocumentsIndexStatus(_message.Message):
    __slots__ = ("status", "documents_status")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    DOCUMENTS_STATUS_FIELD_NUMBER: _ClassVar[int]
    status: BuildStatus
    documents_status: _containers.RepeatedCompositeFieldContainer[DocumentIndexStatus]
    def __init__(self, status: _Optional[_Union[BuildStatus, str]] = ..., documents_status: _Optional[_Iterable[_Union[DocumentIndexStatus, _Mapping]]] = ...) -> None: ...

class DocumentIndexStatus(_message.Message):
    __slots__ = ("status", "uri", "name")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    URI_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    status: BuildStatus
    uri: str
    name: str
    def __init__(self, status: _Optional[_Union[BuildStatus, str]] = ..., uri: _Optional[str] = ..., name: _Optional[str] = ...) -> None: ...

class GetDocumentsIndexStatusResponse(_message.Message):
    __slots__ = ("projects_status", "code", "error")
    class ProjectsStatusEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: ProjectDocumentsIndexStatus
        def __init__(self, key: _Optional[str] = ..., value: _Optional[_Union[ProjectDocumentsIndexStatus, _Mapping]] = ...) -> None: ...
    PROJECTS_STATUS_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    projects_status: _containers.MessageMap[str, ProjectDocumentsIndexStatus]
    code: Code
    error: Error
    def __init__(self, projects_status: _Optional[_Mapping[str, ProjectDocumentsIndexStatus]] = ..., code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...

class IsVersionMatchedRequest(_message.Message):
    __slots__ = ("version",)
    VERSION_FIELD_NUMBER: _ClassVar[int]
    version: str
    def __init__(self, version: _Optional[str] = ...) -> None: ...

class IsVersionMatchedResponse(_message.Message):
    __slots__ = ("matched", "code", "error")
    MATCHED_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    matched: bool
    code: Code
    error: Error
    def __init__(self, matched: bool = ..., code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...

class CancelIndexRequest(_message.Message):
    __slots__ = ("project_id",)
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    project_id: str
    def __init__(self, project_id: _Optional[str] = ...) -> None: ...

class CancelIndexResponse(_message.Message):
    __slots__ = ("succeed", "code", "error")
    SUCCEED_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    succeed: bool
    code: Code
    error: Error
    def __init__(self, succeed: bool = ..., code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...

class ImportAnalysisRequest(_message.Message):
    __slots__ = ("project_id", "file", "import_statement")
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    FILE_FIELD_NUMBER: _ClassVar[int]
    IMPORT_STATEMENT_FIELD_NUMBER: _ClassVar[int]
    project_id: str
    file: str
    import_statement: str
    def __init__(self, project_id: _Optional[str] = ..., file: _Optional[str] = ..., import_statement: _Optional[str] = ...) -> None: ...

class FilesImportAnalysisRequest(_message.Message):
    __slots__ = ("project_id", "rela_files")
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    RELA_FILES_FIELD_NUMBER: _ClassVar[int]
    project_id: str
    rela_files: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, project_id: _Optional[str] = ..., rela_files: _Optional[_Iterable[str]] = ...) -> None: ...

class ImportAnalysisResponse(_message.Message):
    __slots__ = ("code", "error", "result")
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    RESULT_FIELD_NUMBER: _ClassVar[int]
    code: Code
    error: Error
    result: ImportAnalysisResult
    def __init__(self, code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ..., result: _Optional[_Union[ImportAnalysisResult, _Mapping]] = ...) -> None: ...

class ImportAnalysisResult(_message.Message):
    __slots__ = ("message", "data")
    MESSAGE_FIELD_NUMBER: _ClassVar[int]
    DATA_FIELD_NUMBER: _ClassVar[int]
    message: str
    data: str
    def __init__(self, message: _Optional[str] = ..., data: _Optional[str] = ...) -> None: ...

class Symbol(_message.Message):
    __slots__ = ("name", "file_path", "start_line", "end_line")
    NAME_FIELD_NUMBER: _ClassVar[int]
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    name: str
    file_path: str
    start_line: int
    end_line: int
    def __init__(self, name: _Optional[str] = ..., file_path: _Optional[str] = ..., start_line: _Optional[int] = ..., end_line: _Optional[int] = ...) -> None: ...

class SearchCKGDBRequest(_message.Message):
    __slots__ = ("project_id", "symbol_identifier", "symbol_regex", "types", "entity_num")
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    SYMBOL_IDENTIFIER_FIELD_NUMBER: _ClassVar[int]
    SYMBOL_REGEX_FIELD_NUMBER: _ClassVar[int]
    TYPES_FIELD_NUMBER: _ClassVar[int]
    ENTITY_NUM_FIELD_NUMBER: _ClassVar[int]
    project_id: str
    symbol_identifier: str
    symbol_regex: str
    types: _containers.RepeatedScalarFieldContainer[EntityType]
    entity_num: int
    def __init__(self, project_id: _Optional[str] = ..., symbol_identifier: _Optional[str] = ..., symbol_regex: _Optional[str] = ..., types: _Optional[_Iterable[_Union[EntityType, str]]] = ..., entity_num: _Optional[int] = ...) -> None: ...

class SearchCKGDBResponse(_message.Message):
    __slots__ = ("variables", "references", "json_result", "code", "error")
    VARIABLES_FIELD_NUMBER: _ClassVar[int]
    REFERENCES_FIELD_NUMBER: _ClassVar[int]
    JSON_RESULT_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    variables: _containers.RepeatedCompositeFieldContainer[Variable]
    references: _containers.RepeatedCompositeFieldContainer[Reference]
    json_result: str
    code: Code
    error: Error
    def __init__(self, variables: _Optional[_Iterable[_Union[Variable, _Mapping]]] = ..., references: _Optional[_Iterable[_Union[Reference, _Mapping]]] = ..., json_result: _Optional[str] = ..., code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...

class CodeSnippet(_message.Message):
    __slots__ = ("file_path", "start_line", "end_line", "content")
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    file_path: str
    start_line: int
    end_line: int
    content: str
    def __init__(self, file_path: _Optional[str] = ..., start_line: _Optional[int] = ..., end_line: _Optional[int] = ..., content: _Optional[str] = ...) -> None: ...

class RetrieveRelevantSnippetRequest(_message.Message):
    __slots__ = ("project_id", "user_query", "workspace", "selected_folders", "selected_files", "selected_codes", "current_editor", "version", "user_id", "session_id")
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    USER_QUERY_FIELD_NUMBER: _ClassVar[int]
    WORKSPACE_FIELD_NUMBER: _ClassVar[int]
    SELECTED_FOLDERS_FIELD_NUMBER: _ClassVar[int]
    SELECTED_FILES_FIELD_NUMBER: _ClassVar[int]
    SELECTED_CODES_FIELD_NUMBER: _ClassVar[int]
    CURRENT_EDITOR_FIELD_NUMBER: _ClassVar[int]
    VERSION_FIELD_NUMBER: _ClassVar[int]
    USER_ID_FIELD_NUMBER: _ClassVar[int]
    SESSION_ID_FIELD_NUMBER: _ClassVar[int]
    project_id: str
    user_query: str
    workspace: bool
    selected_folders: _containers.RepeatedScalarFieldContainer[str]
    selected_files: _containers.RepeatedScalarFieldContainer[str]
    selected_codes: _containers.RepeatedCompositeFieldContainer[CodeSnippet]
    current_editor: CurrentEditorInfo
    version: str
    user_id: str
    session_id: str
    def __init__(self, project_id: _Optional[str] = ..., user_query: _Optional[str] = ..., workspace: bool = ..., selected_folders: _Optional[_Iterable[str]] = ..., selected_files: _Optional[_Iterable[str]] = ..., selected_codes: _Optional[_Iterable[_Union[CodeSnippet, _Mapping]]] = ..., current_editor: _Optional[_Union[CurrentEditorInfo, _Mapping]] = ..., version: _Optional[str] = ..., user_id: _Optional[str] = ..., session_id: _Optional[str] = ...) -> None: ...

class Snippet(_message.Message):
    __slots__ = ("project_id", "type", "file_path", "start_line", "end_line", "content", "recall_type", "ckg_entity_id")
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    START_LINE_FIELD_NUMBER: _ClassVar[int]
    END_LINE_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    RECALL_TYPE_FIELD_NUMBER: _ClassVar[int]
    CKG_ENTITY_ID_FIELD_NUMBER: _ClassVar[int]
    project_id: str
    type: SnippetType
    file_path: str
    start_line: int
    end_line: int
    content: str
    recall_type: RecallType
    ckg_entity_id: str
    def __init__(self, project_id: _Optional[str] = ..., type: _Optional[_Union[SnippetType, str]] = ..., file_path: _Optional[str] = ..., start_line: _Optional[int] = ..., end_line: _Optional[int] = ..., content: _Optional[str] = ..., recall_type: _Optional[_Union[RecallType, str]] = ..., ckg_entity_id: _Optional[str] = ...) -> None: ...

class RetrieveRelevantSnippetResponse(_message.Message):
    __slots__ = ("snippets_by_workspace", "snippets_by_folder", "snippets_by_file", "snippets_by_code", "snippets_by_current_file", "snippets_by_interaction", "code", "error")
    SNIPPETS_BY_WORKSPACE_FIELD_NUMBER: _ClassVar[int]
    SNIPPETS_BY_FOLDER_FIELD_NUMBER: _ClassVar[int]
    SNIPPETS_BY_FILE_FIELD_NUMBER: _ClassVar[int]
    SNIPPETS_BY_CODE_FIELD_NUMBER: _ClassVar[int]
    SNIPPETS_BY_CURRENT_FILE_FIELD_NUMBER: _ClassVar[int]
    SNIPPETS_BY_INTERACTION_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    snippets_by_workspace: _containers.RepeatedCompositeFieldContainer[Snippet]
    snippets_by_folder: _containers.RepeatedCompositeFieldContainer[Snippet]
    snippets_by_file: _containers.RepeatedCompositeFieldContainer[Snippet]
    snippets_by_code: _containers.RepeatedCompositeFieldContainer[Snippet]
    snippets_by_current_file: _containers.RepeatedCompositeFieldContainer[Snippet]
    snippets_by_interaction: _containers.RepeatedCompositeFieldContainer[Snippet]
    code: Code
    error: Error
    def __init__(self, snippets_by_workspace: _Optional[_Iterable[_Union[Snippet, _Mapping]]] = ..., snippets_by_folder: _Optional[_Iterable[_Union[Snippet, _Mapping]]] = ..., snippets_by_file: _Optional[_Iterable[_Union[Snippet, _Mapping]]] = ..., snippets_by_code: _Optional[_Iterable[_Union[Snippet, _Mapping]]] = ..., snippets_by_current_file: _Optional[_Iterable[_Union[Snippet, _Mapping]]] = ..., snippets_by_interaction: _Optional[_Iterable[_Union[Snippet, _Mapping]]] = ..., code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...

class RerankSnippetRequest(_message.Message):
    __slots__ = ("snippets_by_workspace", "snippets_by_folder", "snippets_by_file", "snippets_by_code", "snippets_by_current_file", "token", "session_id")
    SNIPPETS_BY_WORKSPACE_FIELD_NUMBER: _ClassVar[int]
    SNIPPETS_BY_FOLDER_FIELD_NUMBER: _ClassVar[int]
    SNIPPETS_BY_FILE_FIELD_NUMBER: _ClassVar[int]
    SNIPPETS_BY_CODE_FIELD_NUMBER: _ClassVar[int]
    SNIPPETS_BY_CURRENT_FILE_FIELD_NUMBER: _ClassVar[int]
    TOKEN_FIELD_NUMBER: _ClassVar[int]
    SESSION_ID_FIELD_NUMBER: _ClassVar[int]
    snippets_by_workspace: _containers.RepeatedCompositeFieldContainer[Snippet]
    snippets_by_folder: _containers.RepeatedCompositeFieldContainer[Snippet]
    snippets_by_file: _containers.RepeatedCompositeFieldContainer[Snippet]
    snippets_by_code: _containers.RepeatedCompositeFieldContainer[Snippet]
    snippets_by_current_file: _containers.RepeatedCompositeFieldContainer[Snippet]
    token: str
    session_id: str
    def __init__(self, snippets_by_workspace: _Optional[_Iterable[_Union[Snippet, _Mapping]]] = ..., snippets_by_folder: _Optional[_Iterable[_Union[Snippet, _Mapping]]] = ..., snippets_by_file: _Optional[_Iterable[_Union[Snippet, _Mapping]]] = ..., snippets_by_code: _Optional[_Iterable[_Union[Snippet, _Mapping]]] = ..., snippets_by_current_file: _Optional[_Iterable[_Union[Snippet, _Mapping]]] = ..., token: _Optional[str] = ..., session_id: _Optional[str] = ...) -> None: ...

class SnippetWithScore(_message.Message):
    __slots__ = ("snippet", "score")
    SNIPPET_FIELD_NUMBER: _ClassVar[int]
    SCORE_FIELD_NUMBER: _ClassVar[int]
    snippet: Snippet
    score: float
    def __init__(self, snippet: _Optional[_Union[Snippet, _Mapping]] = ..., score: _Optional[float] = ...) -> None: ...

class RerankSnippetResponse(_message.Message):
    __slots__ = ("snippets", "code", "error")
    SNIPPETS_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    snippets: _containers.RepeatedCompositeFieldContainer[SnippetWithScore]
    code: Code
    error: Error
    def __init__(self, snippets: _Optional[_Iterable[_Union[SnippetWithScore, _Mapping]]] = ..., code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...

class CursorMoveRequest(_message.Message):
    __slots__ = ("project_id", "file", "line", "user_id", "version")
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    FILE_FIELD_NUMBER: _ClassVar[int]
    LINE_FIELD_NUMBER: _ClassVar[int]
    USER_ID_FIELD_NUMBER: _ClassVar[int]
    VERSION_FIELD_NUMBER: _ClassVar[int]
    project_id: str
    file: str
    line: int
    user_id: str
    version: str
    def __init__(self, project_id: _Optional[str] = ..., file: _Optional[str] = ..., line: _Optional[int] = ..., user_id: _Optional[str] = ..., version: _Optional[str] = ...) -> None: ...

class IsCKGEnabledForNonWorkspaceScenarioRequest(_message.Message):
    __slots__ = ("user_id",)
    USER_ID_FIELD_NUMBER: _ClassVar[int]
    user_id: str
    def __init__(self, user_id: _Optional[str] = ...) -> None: ...

class IsCKGEnabledForNonWorkspaceScenarioResponse(_message.Message):
    __slots__ = ("is_enabled", "version", "code", "error")
    IS_ENABLED_FIELD_NUMBER: _ClassVar[int]
    VERSION_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    is_enabled: bool
    version: str
    code: Code
    error: Error
    def __init__(self, is_enabled: bool = ..., version: _Optional[str] = ..., code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...

class SetUpRequest(_message.Message):
    __slots__ = ("host", "region", "source_product", "device_cpu", "device_id", "machine_id", "device_brand", "device_type", "os_version")
    HOST_FIELD_NUMBER: _ClassVar[int]
    REGION_FIELD_NUMBER: _ClassVar[int]
    SOURCE_PRODUCT_FIELD_NUMBER: _ClassVar[int]
    DEVICE_CPU_FIELD_NUMBER: _ClassVar[int]
    DEVICE_ID_FIELD_NUMBER: _ClassVar[int]
    MACHINE_ID_FIELD_NUMBER: _ClassVar[int]
    DEVICE_BRAND_FIELD_NUMBER: _ClassVar[int]
    DEVICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    OS_VERSION_FIELD_NUMBER: _ClassVar[int]
    host: str
    region: str
    source_product: str
    device_cpu: str
    device_id: str
    machine_id: str
    device_brand: str
    device_type: str
    os_version: str
    def __init__(self, host: _Optional[str] = ..., region: _Optional[str] = ..., source_product: _Optional[str] = ..., device_cpu: _Optional[str] = ..., device_id: _Optional[str] = ..., machine_id: _Optional[str] = ..., device_brand: _Optional[str] = ..., device_type: _Optional[str] = ..., os_version: _Optional[str] = ...) -> None: ...

class SetUpResponse(_message.Message):
    __slots__ = ("code", "error")
    CODE_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    code: Code
    error: Error
    def __init__(self, code: _Optional[_Union[Code, str]] = ..., error: _Optional[_Union[Error, _Mapping]] = ...) -> None: ...
