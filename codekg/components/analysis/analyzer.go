package analysis

import (
	"context"
	"fmt"
	"ide/ckg/codekg/components/analysis/import_analysis"
	"ide/ckg/codekg/components/data_manager"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/protocol"
)

type Analy<PERSON> struct {
	Ctx         context.Context
	DataManager data_manager.DataManager
}

func NewAnalyzer(ctx context.Context, dm data_manager.DataManager) *Analyzer {
	return &Analyzer{
		Ctx:         ctx,
		DataManager: dm,
	}
}

func (p *Analyzer) ImportAnalysis(projectID string, file string, importStatement string) (*protocol.ImportAnalysisResult, error) {
	var storage data_storage.Storage
	var err error

	storage, err = p.DataManager.GetEntityStorage(p.Ctx, projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to GetOrCreateStorage: %s", err.Error())
	}

	return import_analysis.NewImportAnalyzer(p.Ctx, projectID, storage, file_system.RealFS, file, importStatement).Analyze()
}

func (p *Analyzer) FilesImportAnalysis(projectID string, files []string) (*protocol.ImportAnalysisResult, error) {
	var storage data_storage.Storage
	var err error

	storage, err = p.DataManager.GetEntityStorage(p.Ctx, projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to GetOrCreateStorage: %s", err.Error())
	}

	importAnalyzer := import_analysis.NewImportAnalyzer(p.Ctx, projectID, storage, file_system.RealFS, "", "")
	return importAnalyzer.AnalyzeFiles(files)
}
