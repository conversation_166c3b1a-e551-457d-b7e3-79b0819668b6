import type {Position, TextDocument} from 'vscode-languageserver-textdocument';
import {getActiveFileCachedTreeAndDocument} from '../../../cache/astCache';
import {TreeSitterParser} from '../../../parse/treesitter/parser';
import {Logger} from '../../../../utils/logger';
import {
  EVENT_CATEGORY_COMPLETION,
  EVENT_NAME_FAILED,
  EVENT_VALUE_NO_CACHE,
  reportEvent,
} from '../../../../utils/event';
import {
  genDocState,
  spliceCompletionText,
} from './share';
import {TimeoutError} from "../../../../utils/error";
import {FILE_URI_PREFIX} from "../../../../utils/common";

export const EVENT_PROCESS_COMPLETION_SYNTAX_CHECK = "completion_syntax_check";

const EVENT_VALUE_PARSE_ERROR = "parse_error";

export interface SyntaxCheckResult {
  syntaxCheckPass: boolean
}

export function doSyntaxCheck(document: TextDocument, position: Position, completionText: string, timeoutMs: number): SyntaxCheckResult {
  if (!document.uri.startsWith(FILE_URI_PREFIX)) {
    return;
  }

  const startTime = Date.now();

  const cachedTree = getActiveFileCachedTreeAndDocument(document)?.tree;
  if (!cachedTree) {
    Logger.error(`doSyntaxCheck: no cached tree found for '${document.uri}'`);
    reportEvent(document.languageId, EVENT_CATEGORY_COMPLETION, EVENT_PROCESS_COMPLETION_SYNTAX_CHECK, EVENT_NAME_FAILED, EVENT_VALUE_NO_CACHE).then();
    return null;
  }

  const docState = genDocState(document, position);

  const {
    textAfterModification,
    edit,
  } = spliceCompletionText(document.getText(), completionText, docState, 0);

  const copiedCachedTree = cachedTree.copy();
  copiedCachedTree.edit(edit);

  const treeForCompletionInsert = TreeSitterParser.INSTANCE.parse(document.languageId, textAfterModification, copiedCachedTree, timeoutMs);
  if (!treeForCompletionInsert) {
    Logger.error(`doSyntaxCheck: failed to get AST after inserting completion text for '${document.uri}'`);
    reportEvent(document.languageId, EVENT_CATEGORY_COMPLETION, EVENT_PROCESS_COMPLETION_SYNTAX_CHECK, EVENT_NAME_FAILED, EVENT_VALUE_PARSE_ERROR).then();
    return null;
  }
  if (Date.now() - startTime > timeoutMs) {
    throw new TimeoutError(`after parsing AST`);
  }

  if (treeForCompletionInsert.rootNode.hasError) {
    return {
      syntaxCheckPass: false,
    };
  } else {
    return {
      syntaxCheckPass: true,
    };
  }
}