package data_manager

import (
	"context"
	"crypto/sha1"
	"encoding/hex"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/ckg_metrics"
	bizErr "ide/ckg/codekg/components/error"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/components/validator"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/util"

	"github.com/samber/lo"
)

func (m *manager) collectAllURIs(ctx context.Context, userId string, project model.URI, fileLimit int, fileSizeLimit int64) ([]model.URI, error) {
	// 如果是远程路径，无法进行索引
	if validator.IsVFSPath(project) {
		ckg_metrics.GetEvent(ctx).AddParam(ckg_metrics.TeaParamProjectPathIsVfs, true)
		logs.CtxError(ctx, "project path is virtual file path, path is %v", project)
		return nil, bizErr.ErrProjectPathIsVfs
	}
	// 目前 fileLimit 传入默认为 10000，所以这里默认统计 10 万以下的仓库
	// 如果 fileLimit > 10 万，那么统计更多文件
	stopCollectionLimit := lo.Ternary(fileLimit > 100000, fileLimit, 100000)
	totalFileNum := 0
	defer func() {
		ckg_metrics.GetEvent(ctx).AddParam(ckg_metrics.TeaParamFileCountBeforeIgnore, totalFileNum)
	}()

	filePath := make([]model.URI, 0)
	processedMap := make(map[model.URI]bool)
	err := m.fs.Walk(project, func(path string, info os.FileInfo, err error) error {
		defer func() {
			processedMap[path] = true
		}()
		if err != nil {
			logs.CtxError(ctx, "file walk param err != nil, err: %v", err)
			return nil
		}

		if processedMap[path] {
			logs.CtxInfo(ctx, "file path %v is collected", validator.EncryptPath(path))
			return filepath.SkipDir
		}
		totalFileNum++

		cancelFlg, ok := m.projectCancelFlag.Load(project)
		if !ok || cancelFlg.(*atomic.Bool).Load() {
			logs.CtxInfo(ctx, "project %s init already canceled, skip collect uri ", project)
			return filepath.SkipAll
		}

		useV2Ignore := m.config.IsFeatureEnabled(ctx, userId, ckg_config.UseV2Ignore)
		if useV2Ignore {
			ignored, skipped, err := m.ignoreServiceV2.IsIgnore(ctx, userId, project, path, info)
			if err != nil {
				logs.CtxError(ctx, "v2 ignore %s failed err is %s", validator.EncryptPath(path), err)
				return nil
			}
			if ignored {
				return skipped
			}
		} else {
			var result bool
			result, err = validator.IsValidForKnowledgeGraph(ctx, project, path, fileSizeLimit, m.ignoreService, m.fs)
			if err != nil {
				logs.CtxError(ctx, "file %s IsIgnored failed err is %s", validator.EncryptPath(path), err)
				return nil
			}
			if !result {
				if info.IsDir() {
					return file_system.SkipDir
				}
				return nil
			}
		}

		if !info.IsDir() {
			file, err := os.Open(path)
			if err != nil {
				logs.CtxError(ctx, "Failed to open file %s: %v", validator.EncryptPath(path), err)
				return nil
			}

			content, err := util.ReadFirstCharFromFile(file, 1024)
			if err != nil {
				logs.CtxError(ctx, "Failed to read file %s: %v", validator.EncryptPath(path), err)
				return nil
			}
			if validator.FileContainsNonPrintableChar(ctx, path, content) {
				logs.CtxWarn(ctx, "file %s not utf-8 unicode", validator.EncryptPath(path))
				ckg_metrics.GetEvent(ctx).AddParam(ckg_metrics.TeaParamFileSkipIndex, path)
				return nil
			}
		}

		absPath, err := m.fs.Abs(path)
		if err != nil {
			return nil
		}

		filePath = append(filePath, absPath)
		if stopCollectionLimit != 0 && len(filePath) > stopCollectionLimit {
			return filepath.SkipAll
		}
		// 每 1000 个文件 GC 一次
		if len(filePath)%1000 == 0 {
			var rtm runtime.MemStats
			runtime.ReadMemStats(&rtm)
			logs.CtxInfo(ctx, "[collectAllURIs] before gc, mem stat, alloc: %.2f MB, in use: %.2f MB",
				float64(rtm.HeapAlloc)/1024/1024, float64(rtm.HeapInuse)/1024/1024)
			gcStartTime := time.Now()
			runtime.GC()
			logs.CtxInfo(ctx, "[collectAllURIs] gc cost: %d ms", time.Since(gcStartTime).Milliseconds())
			runtime.ReadMemStats(&rtm)
			logs.CtxInfo(ctx, "[collectAllURIs] after gc, mem stat, alloc: %.2f MB, in use: %.2f MB",
				float64(rtm.HeapAlloc)/1024/1024, float64(rtm.HeapInuse)/1024/1024)
		}
		return nil
	})
	if err != nil {
		return []model.URI{}, err
	}

	return filePath, nil
}

// To Be Deprecated
func (m *manager) getProjectID(file model.URI) model.URI {
	var result model.URI
	m.projects.Range(func(key, value interface{}) bool {
		if strings.HasPrefix(filepath.Clean(file), filepath.Clean(key.(string))) {
			result = key.(string)
		}
		return true
	})
	logs.Info("get project id from file: %s, project id: %s", validator.EncryptPath(filepath.Clean(file)), filepath.Clean(result))
	return result
}

func (m *manager) getProjectURIFromProjectID(projectID string) model.URI {
	projectURI, ok := m.projectUris.Load(projectID)
	if !ok {
		return ""
	}
	return projectURI.(model.URI)
}

func (m *manager) getProjectPathFromProjectID(projectID string) model.URI {
	projectURI := m.getProjectURIFromProjectID(projectID)
	if projectURI == "" {
		return ""
	}

	projectType := m.GetProjectType(context.Background(), projectID)
	return util.GetFilePathFromURI(projectURI, projectType == model.ProjectTypeVirtual)
}

func (m *manager) getFileRelaPathFromProjectID(projectID string, fileUri model.URI) model.URI {
	projectPath := m.getProjectPathFromProjectID(projectID)
	if projectPath == "" {
		return fileUri
	}

	projectType := m.GetProjectType(context.Background(), projectID)
	filePath := util.GetFilePathFromURI(fileUri, projectType == model.ProjectTypeVirtual)
	if filePath == "" {
		return fileUri
	}

	relPath, err := m.fs.Rel(projectPath, filePath)
	if err != nil {
		return fileUri
	}
	return relPath
}

func (m *manager) createDocumentWithContentInfo(ctx context.Context, document *model.Document) (*model.URIStatus, error) {
	uriStatus := &model.URIStatus{
		AbsPath:      document.Uri,
		RelPath:      document.Uri,
		UriCanonical: document.Uri,
		// ContentHash: document.ContentHash,
		// UniqueID:    document.UniqueID,
		Name:      document.Name,
		Content:   document.Content,
		ProjectID: document.ProjectID,
	}

	data := document.Content

	// 计算 content hash
	header := "blob " + strconv.Itoa(len(data)) + string('\x00')
	store := append([]byte(header), data...)

	sha := sha1.New()
	sha.Write(store)
	hashed := sha.Sum(nil)
	result := hex.EncodeToString(hashed)

	// 计算 unique id
	relPath := m.getFileRelaPathFromProjectID(document.ProjectID, document.Uri)
	uniqueID := util.GetFileUniqueID(relPath, string(data))

	uriStatus.RelPath = relPath
	uriStatus.ContentHash = result
	uriStatus.UniqueID = uniqueID

	return uriStatus, nil
}

func (m *manager) createFileInfo(ctx context.Context, project string, file model.URI) (*fileInformation, error) {
	data, err := m.fs.ReadFile(file)
	if err != nil {
		logs.CtxError(ctx, "createContentHash err is %s", err)
		return nil, err
	}

	// 计算 content hash
	header := "blob " + strconv.Itoa(len(data)) + string('\x00')
	store := append([]byte(header), data...)

	sha := sha1.New()
	sha.Write(store)
	hashed := sha.Sum(nil)
	result := hex.EncodeToString(hashed)

	// 计算 unique id
	relPath, err := m.fs.Rel(project, file)
	if err != nil {
		return nil, err
	}
	uniqueID := util.GetFileUniqueID(relPath, string(data))

	return &fileInformation{
		hash:      result,
		uniqueID:  uniqueID,
		lineCount: len(strings.Split(string(data), "\n")),
		size:      len(data),
	}, nil
}

func (m *manager) createFolderInfo(ctx context.Context, folder model.URI) (*fileInformation, error) {
	var data string
	subInfos, err := m.fs.ReadDir(folder)
	if err != nil {
		logs.CtxError(ctx, "read folder err path is %v, err: %v", validator.EncryptPath(folder), err)
		return nil, err
	}

	for _, info := range subInfos {
		data += info.Name()
	}

	header := "blob " + strconv.Itoa(len(data)) + string('\x00')
	store := append([]byte(header), data...)

	sha := sha1.New()
	sha.Write(store)
	hashed := sha.Sum(nil)
	result := hex.EncodeToString(hashed)
	return &fileInformation{
		hash: result,
	}, nil
}
