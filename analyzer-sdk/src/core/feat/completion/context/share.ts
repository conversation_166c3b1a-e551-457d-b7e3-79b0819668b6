export enum PriorityEnum {
  P0 = 0,
  P1 = 1,
  P2 = 2,
  P3 = 3,
}

export interface CompletionContextInfo {
  priority: number;
  distanceFromPosition: number;
  symbolName: string;
  definitionKey: string;
  contextInfo: ContextItem;
}

export interface CompletionContextResult {
  priority: number;
  distanceFromPosition: number;
  symbolNames: string[];
  contextInfo: ContextItem;
}

export interface ContextItem {
  path: string;
  startIndex: number;
  code: string;
  subInfo: ContextItem[];
}

export function postProcessingAndSort(resultContexts: CompletionContextInfo[]): CompletionContextResult[] {
  const infoMap = new Map<string, {priority: number, minDistance: number, symbolNames: string[], contextInfo: ContextItem}>();
  resultContexts.forEach(resultContext => {
    if (resultContext.contextInfo === null) {
      return;
    }

    const definitionKey = resultContext.definitionKey;
    if (definitionKey) {
      if (!infoMap.has(definitionKey)) {
        infoMap.set(definitionKey, {
          priority: resultContext.priority,
          minDistance: resultContext.distanceFromPosition,
          symbolNames: [resultContext.symbolName],
          contextInfo: resultContext.contextInfo,
        });
      } else {
        const info = infoMap.get(definitionKey);

        if (resultContext.priority < info.priority) {
          info.priority = resultContext.priority;
        }
        if (resultContext.distanceFromPosition < info.minDistance) {
          info.minDistance = resultContext.distanceFromPosition;
        }
        info.symbolNames.push(resultContext.symbolName);

        infoMap.set(definitionKey, info);
      }
    }
  });

  return Array.from(infoMap.values()).sort((a, b) => {
    if (a.priority !== b.priority) {
      return a.priority - b.priority;
    } else {
      return a.minDistance - b.minDistance;
    }
  }).map(info => {
    return {
      priority: info.priority,
      distanceFromPosition: info.minDistance,
      symbolNames: Array.from(new Set(info.symbolNames)),
      contextInfo: info.contextInfo,
    };
  });
}