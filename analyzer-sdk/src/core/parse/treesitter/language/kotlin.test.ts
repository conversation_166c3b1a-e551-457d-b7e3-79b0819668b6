import {beforeAll, describe, expect, it} from "vitest";
import {TreeSitterParser} from "../parser";
import {LanguageIdEnum} from "../../share";
import {KotlinTreeSitterHandler} from "./kotlin";

describe("test_getMethodInRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
fun sayHello() {
    println("Hello!")
}

class Greeter {
    fun greet() {
        println("Hello!")
    }
}

fun performOperation(x: Int, y: Int, operation: (Int, Int) -> Int): Int {
    return operation(x, y)
}

val printMessage = { message: String -> println(message) }

fun square(x: Int): Int = x * x
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Kotlin, docText);

    const range = {
      start: {
        line: 1,
        character: 5
      },
      end: {
        line: 1,
        character: 10
      }
    };
    const result = KotlinTreeSitterHandler.INSTANCE.getMethodInRange(tree.rootNode, range, 6, 11, false);

    expect(result.map(node => node.text)).toEqual([
      `
fun sayHello() {
    println("Hello!")
}      
`.trim(),
    ]);
  });
});