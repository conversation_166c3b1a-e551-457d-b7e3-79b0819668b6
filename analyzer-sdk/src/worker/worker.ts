import {MessagePort, parentPort, workerData} from "worker_threads";
import {MethodType} from "./share";
import {CKGWrapper} from "../utils/ckgWrapper";
import {TreeSitterParser} from "../core/parse/treesitter/parser";
import {<PERSON>Handler} from "./base";
import {Logger} from "../utils/logger";
import {wait} from "../utils/common";
import {loggingQueue} from "./queue/logging";
import {eventQueue} from "./queue/event";
import {doEditorSelectionListener, doFileActiveListener, doFileEditListener} from "../core/common/listener";
import {setWorkspaceFolderPath} from "../utils/path";
import {TextDocument} from "vscode-languageserver-textdocument";
import {doExtractCompletionContext} from "../core/feat/completion/context/common";
import {CompletionContextResult} from "../core/feat/completion/context/share";
import {lspQueue} from "./queue/lsp";
import {psiQueue} from "./queue/psi";
import {PSIParser} from "../core/parse/psi/parser";
import {LSPParser} from "../core/parse/lsp/parser";
import {doSyntaxCheck, SyntaxCheckResult} from "../core/feat/completion/post/syntaxCheck";
import {doSyntaxFix, SyntaxFixResult} from "../core/feat/completion/post/syntaxFix";
import {doExtractUnitTestContext} from "../core/feat/utgen/common";
import {doGetDefinitionsForRange} from "../core/sync/definition";
import {doSetConfigById, FeatureGateManager, FeatureName} from "../core/common/config";
import {eventConsumerSetMainThreadInitialized} from "../utils/event";
import {getLastTimeoutTimestamp, updateLastTimeoutTimestamp} from "../utils/control";

export class WorkerHandler extends BaseHandler {
  static INSTANCE = new WorkerHandler();

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
    super();
  }

  init(): void {
    super.init();

    setWorkspaceFolderPath(workerData.workspaceFolderPath);
    PSIParser.INSTANCE.setMainThreadInitialized(workerData.psiInitialized);
    LSPParser.INSTANCE.setMainThreadInitialized(workerData.lspInitialized);
    eventConsumerSetMainThreadInitialized(workerData.eventConsumerInitialized);

    processLoggingTask().then();
    processEventTask().then();
    if (PSIParser.INSTANCE.initialized()) {
      processPsiTask().then();
    }
    if (LSPParser.INSTANCE.initialized()) {
      processLspTask().then();
    }
  }

  override port(): MessagePort {
    return parentPort;
  }

  override getMethodHandlers(): Map<MethodType, (args: any) => Promise<any>> {
    const result: Map<MethodType, (args: any) => Promise<any>> = new Map();

    result.set(MethodType.ToWorker_InitAnalyzer, initAnalyzerFunc);
    result.set(MethodType.ToWorker_SetConfigById, setConfigByIdFunc);
    result.set(MethodType.ToWorker_FileActiveListener, fileActiveListenerFunc);
    result.set(MethodType.ToWorker_FileEditListener, fileEditListenerFunc);
    result.set(MethodType.ToWorker_EditorSelectionListener, editorSelectionListenerFunc);
    result.set(MethodType.ToWorker_ExtractCompletionContext, extractCompletionContextFunc);
    result.set(MethodType.ToWorker_CompletionSyntaxCheck, completionSyntaxCheckFunc);
    result.set(MethodType.ToWorker_CompletionSyntaxFix, completionSyntaxFixFunc);
    result.set(MethodType.ToWorker_ExtractUnitTestContext, extractUnitTestContextFunc);
    result.set(MethodType.ToWorker_GetDefinitionForRange, getDefinitionForRangeFunc);
    result.set(MethodType.ToWorker_OpenFeature, openFeatureFunc);
    result.set(MethodType.ToWorker_CloseFeature, closeFeatureFunc);
    result.set(MethodType.ToWorker_GetOpenedFeature, getOpenedFeatureFunc);
    result.set(MethodType.ToWorker_CacheFeature, cacheFeatureFunc);
    result.set(MethodType.ToWorker_RestoreFeature, restoreFeatureFunc);
    result.set(MethodType.ToWorker_UpdateLastTimeoutTimestamp, updateLastTimeoutTimestampFunc);
    result.set(MethodType.ToWorker_GetLastTimeoutTimestamp, getLastTimeoutTimestampFunc);

    return result;
  }

  async lspExecute(params: any): Promise<any> {
    return this.sendToPort(MethodType.ToMain_LspExecute, params);
  }

  async psiExecute(params: any): Promise<any> {
    return this.sendToPort(MethodType.ToMain_PsiExecute, params);
  }

  async logging(params: any): Promise<void> {
    return this.sendToPort(MethodType.ToMain_Logging, params);
  }

  async reportEvent(params: any): Promise<void> {
    return this.sendToPort(MethodType.ToMain_ReportEvent, params);
  }
}

async function processLoggingTask(): Promise<void> {
  while (loggingQueue) {
    while (loggingQueue.length > 0) {
      const task = loggingQueue.shift();
      WorkerHandler.INSTANCE.logging({level: task.level, msg: task.msg}).then();
    }

    await wait(1);
  }
}

async function processEventTask(): Promise<void> {
  while (eventQueue) {
    while (eventQueue.length > 0) {
      const task = eventQueue.shift();
      WorkerHandler.INSTANCE.reportEvent({languageId: task.languageId, category: task.category, process: task.process, name: task.name, value: task.value}).then();
    }

    await wait(1);
  }
}

async function processPsiTask(): Promise<void> {
  while (psiQueue) {
    while (psiQueue.length > 0) {
      const task = psiQueue.shift();
      WorkerHandler.INSTANCE.psiExecute({command: task.command, languageId: task.languageId, uri: task.uri, position: task.position, args: task.args}).then(result => {
        task.promise.resolve(result);
      }).catch(error => {
        task.promise.reject(error);
      });
    }

    await wait(1);
  }
}

async function processLspTask(): Promise<void> {
  while (lspQueue) {
    while (lspQueue.length > 0) {
      const task = lspQueue.shift();
      WorkerHandler.INSTANCE.lspExecute({command: task.command, languageId: task.languageId, uri: task.uri, position: task.position}).then(result => {
        task.promise.resolve(result);
      }).catch(error => {
        task.promise.reject(error);
      });
    }

    await wait(1);
  }
}

async function initAnalyzerFunc(params: any): Promise<void> {
  if (params.ckg) {
    CKGWrapper.setCKG(params.ckg);
    Logger.info(`initAnalyzerFunc: CKG Parser is initialized`);
  }

  if (params.treeSitterResourceDir) {
    await TreeSitterParser.INSTANCE.init(params.treeSitterResourceDir);
    Logger.info(`initAnalyzerFunc: TreeSitter Parser is initialized`);
  }
}

async function setConfigByIdFunc(params: any): Promise<void> {
  doSetConfigById(params.configId, params.value);
}

async function fileActiveListenerFunc(params: any): Promise<void> {
  const document = TextDocument.create(params.document._uri, params.document._languageId, params.document._version, params.document._content);
  await doFileActiveListener(document);
}

async function fileEditListenerFunc(params: any): Promise<void> {
  const document = TextDocument.create(params.document._uri, params.document._languageId, params.document._version, params.document._content);
  await doFileEditListener({document, contentChanges: params.contentChanges});
}

async function editorSelectionListenerFunc(params: any): Promise<void> {
  const document = TextDocument.create(params.document._uri, params.document._languageId, params.document._version, params.document._content);
  await doEditorSelectionListener({document, selectionRange: params.selectionRange});
}

async function extractCompletionContextFunc(params: any): Promise<CompletionContextResult[]> {
  const document = TextDocument.create(params.document._uri, params.document._languageId, params.document._version, params.document._content);
  return doExtractCompletionContext(document, params.position);
}

async function completionSyntaxCheckFunc(params: any): Promise<SyntaxCheckResult> {
  const document = TextDocument.create(params.document._uri, params.document._languageId, params.document._version, params.document._content);
  return doSyntaxCheck(document, params.position, params.completionText, params.timeoutMs);
}

async function completionSyntaxFixFunc(params: any): Promise<SyntaxFixResult> {
  const document = TextDocument.create(params.document._uri, params.document._languageId, params.document._version, params.document._content);
  return doSyntaxFix(document, params.position, params.completionText, params.timeoutMs);
}

async function extractUnitTestContextFunc(params: any): Promise<string> {
  const document = TextDocument.create(params.document._uri, params.document._languageId, params.document._version, params.document._content);
  return doExtractUnitTestContext(document, params.startIndex, params.endIndex);
}

async function getDefinitionForRangeFunc(params: any): Promise<string> {
  const document = TextDocument.create(params.document._uri, params.document._languageId, params.document._version, params.document._content);
  return doGetDefinitionsForRange(document, params.startPosition, params.endPosition);
}

async function openFeatureFunc(params: any): Promise<void> {
  return FeatureGateManager.INSTANCE.openFeature(params.feature);
}

async function closeFeatureFunc(params: any): Promise<void> {
  return FeatureGateManager.INSTANCE.closeFeature(params.feature);
}

async function getOpenedFeatureFunc(_params: any): Promise<FeatureName[]> {
  return FeatureGateManager.INSTANCE.getOpenedFeature();
}

async function cacheFeatureFunc(params: any): Promise<void> {
  return FeatureGateManager.INSTANCE.cacheFeature(params.flag);
}

async function restoreFeatureFunc(params: any): Promise<void> {
  return FeatureGateManager.INSTANCE.restoreFeature(params.flag);
}

async function updateLastTimeoutTimestampFunc(params: any): Promise<void> {
  return updateLastTimeoutTimestamp(params.timestamp);
}

async function getLastTimeoutTimestampFunc(_params: any): Promise<number> {
  return getLastTimeoutTimestamp();
}