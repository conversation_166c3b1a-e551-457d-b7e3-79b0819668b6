import type {Document<PERSON><PERSON>, Position} from "vscode-languageserver-types";
import {FILE_URI_PREFIX, wait, withTimeout} from "../../../utils/common";
import {Logger} from "../../../utils/logger";
import {TimeoutError} from "../../../utils/error";
import {FeatureGateManager, FeatureName, getConfig} from "../../common/config";
import {isMainThread} from "worker_threads";
import {addWorkerLspTask} from "../../../worker/queue/lsp";
import {
  EVENT_CATEGORY_PROXY,
  EVENT_NAME_FAILED,
  EVENT_VALUE_EXCEPTION,
  EVENT_VALUE_TIMEOUT,
  reportEvent,
} from "../../../utils/event";
import {updateLastTimeoutTimestamp} from "../../../utils/control";

const EVENT_PROCESS_LSP = "lsp";

export interface LSPProxy {
  execute(command: string, ...args: any[]): Promise<any>;
}

export class LSPParser {
  static INSTANCE = new LSPParser();

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
  }

  private proxy: LSPProxy = null;
  private mainThreadInitialized = false;

  private requestCount = 0;

  init(proxy: LSPProxy): void {
    this.proxy = proxy;
  }

  setMainThreadInitialized(value: boolean): void {
    this.mainThreadInitialized = value;
  }

  initialized(): boolean {
    if (isMainThread) {
      return this.proxy !== null;
    } else {
      return this.mainThreadInitialized;
    }
  }

  async executeInMainThread(command: string, languageId: string, uri: DocumentUri, position: Position): Promise<any> {
    if (isMainThread) {
      return this.execute(command, languageId, uri, position);
    } else {
      return addWorkerLspTask({command, languageId, uri, position});
    }
  }

  async execute(command: string, languageId: string, uri: DocumentUri, position: Position): Promise<any> {
    if (this.proxy) {
      if (uri.startsWith(FILE_URI_PREFIX)) {
        while (this.requestCount >= getConfig("parse.lsp.maxRequestCount")) {
          await wait(getConfig("parse.lsp.waitInterval"));
        }

        try {
          this.requestCount++;
          if (FeatureGateManager.INSTANCE.isFeatureOpen(FeatureName.ENABLE_PSI_LSP_REQUEST)) {
            return await withTimeout(this.proxy.execute(command, uri, position), getConfig("parse.lsp.timeoutMs"), false);
          } else {
            return [];
          }
        } catch (e) {
          if (e instanceof TimeoutError) {
            reportEvent(languageId, EVENT_CATEGORY_PROXY, EVENT_PROCESS_LSP, EVENT_NAME_FAILED, EVENT_VALUE_TIMEOUT).then();
            Logger.warn(`LSPParser.execute: execution timeout for '${uri} (${position.line}, ${position.character})'`);
            await updateLastTimeoutTimestamp(Date.now());
          } else {
            reportEvent(languageId, EVENT_CATEGORY_PROXY, EVENT_PROCESS_LSP, EVENT_NAME_FAILED, EVENT_VALUE_EXCEPTION).then();
            Logger.error(`LSPParser.execute: exception for '${uri}': ${e}`);
            Logger.error(e.stack);
          }
          return [];
        } finally {
          this.requestCount--;
        }
      } else {
        return [];
      }
    } else {
      return [];
    }
  }
}