import {DocumentUri, Position} from "vscode-languageserver-types";
import {TextDocument} from "vscode-languageserver-textdocument";

export class ResolvedTypeInfo {
  static OPTIONAL_CONTENT_FUNC_SIGNATURE = "func_signature";
  static OPTIONAL_CONTENT_METHODS_IN_CLASS = "methods_in_class";
  static OPTIONAL_CONTENT_CLASS_SIMPLIFIED_CONTENT = "class_simplified_content";

  path: string;
  startIndex: number;
  name: string;
  category: string;
  content: string;
  subTypeInfo: ResolvedTypeInfo[];
  optionalContent: Record<string, string>;

  constructor(path: string, startIndex: number, name: string, category: string, content: string, subTypeInfo: ResolvedTypeInfo[]) {
    this.path = path;
    this.startIndex = startIndex;
    this.name = name;
    this.category = category;
    this.content = content;
    this.subTypeInfo = subTypeInfo;
    this.optionalContent = {};
  }

  addOptionalContent(key: string, value: string): void {
    this.optionalContent[key] = value;
  }

  uniqueKeyString(): string {
    return `${this.category}@${this.path}@${this.name}`;
  }
}

export class FileSymbolDef {
  uri: DocumentUri;
  docText: string;
  indexDefInfo: SymbolDefInfo[];

  constructor(document: TextDocument) {
    this.uri = document.uri;
    this.docText = document.getText();
    this.indexDefInfo = null;
  }

  offsetAt(position: Position): number {
    const lines = this.docText.split('\n');
    let offset = 0;

    for (let i = 0; i < position.line; i++) {
      offset += lines[i].length + 1; // +1 for newline character
    }

    offset += position.character;

    return offset;
  }

  positionAt(offset: number): Position {
    const lines = this.docText.split('\n');
    let line = 0;
    let character = 0;
    let currentOffset = 0;

    while (currentOffset <= offset && line < lines.length) {
      if (currentOffset + lines[line].length + 1 > offset) {
        character = offset - currentOffset;
        break;
      }
      currentOffset += lines[line].length + 1; // +1 for newline character
      line++;
    }

    return {
      line,
      character,
    };
  }

  getSymbolDefInfo(index: number): SymbolDefInfo {
    return this.indexDefInfo[index];
  }
}

export enum SymbolDefKind {
  Invoke = "invoke",
  Type = "type",
  DeclVar = "declVar",
  UsageVar = "usageVar",
}

export interface SymbolDefInfo {
  name: string;
  kind: SymbolDefKind;
  definitionKey: string;
}