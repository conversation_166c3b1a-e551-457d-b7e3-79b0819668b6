import {describe, expect, it} from "vitest";
import {createDocument, withTimeout} from "./common";
import {LanguageIdEnum} from "../core/parse/share";

describe("test_withTimeout", () => {
  it("case1_no_timeout", async () => {
    const asyncOperation = async (p: number): Promise<number> => {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(p);
        }, 5);
      });
    };

    const result = await withTimeout(asyncOperation(1), 10, false)

    expect(result).toEqual(1);
  });

  it("case2_timeout", async () => {
    const asyncOperation = async (p: number): Promise<number> => {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(p);
        }, 5000);
      });
    };

    try {
      await withTimeout(asyncOperation(1), 10, false)
      expect(false).toEqual(true);
    } catch (e) {
      expect(e.message).toEqual('Operation execution timeout (10ms)');
    }
  });

  it("case3_fallback", async () => {
    const asyncOperation = async (p: number): Promise<number> => {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(p);
        }, 5000);
      });
    };

    try {
      const result = await withTimeout(asyncOperation(1), 10, true, 0)
      expect(result).toEqual(0);
    } catch (e) {
      expect(false).toEqual(true);
    }
  });
});

describe("test_createDocument", () => {
  it("case1_offsetAt", async () => {
    const text = `
func main() {
}
`;

    const result = createDocument(text, LanguageIdEnum.Go, "file:///test.go");

    expect(result.getText()).toEqual(text);
    expect(result.offsetAt({line: 1, character: 0})).toEqual(1);
    expect(result.offsetAt({line: 100, character: 0})).toEqual(17);
  });

  it("case2_positionAt", async () => {
    const text = `
func main() {
}
`;

    const result = createDocument(text, LanguageIdEnum.Go, "file:///test.go");

    expect(result.getText()).toEqual(text);
    expect(result.positionAt(1)).toEqual({line: 1, character: 0});
    expect(result.positionAt(-1)).toEqual({line: 0, character: 0});
    expect(result.positionAt(1000)).toEqual({line: 3, character: 0});
  });
});