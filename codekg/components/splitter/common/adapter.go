package common

import (
	"context"

	kb "ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/splitter/local_splitter/code"
	"ide/ckg/codekg/components/splitter/local_splitter/text"
	"ide/ckg/codekg/components/splitter/local_splitter/text/langchain"
	"ide/ckg/codekg/components/splitter/model"
)

// OptionAdapter 将model.SplitOption转换为common.SplitterOption
func OptionAdapter(opt *model.SplitOption) *SplitterOption {
	if opt == nil {
		return nil
	}

	// 初始化基本字段
	commonOpt := &SplitterOption{
		AsPlainText:  opt.AsPlainText,
		SplitterType: string(opt.SplitterType),
		Advanced:     make(map[string]interface{}),
	}

	// 从LocalOpt中获取相关字段
	if opt.LocalOpt != nil {
		commonOpt.ContentType = string(opt.LocalOpt.ContentType)
		commonOpt.LocalSubtype = string(opt.LocalOpt.LocalSubtype)

		// 从LangchainOpt中获取相关字段
		if opt.LocalOpt.LangchainOpt != nil {
			commonOpt.LangchainType = string(opt.LocalOpt.LangchainOpt.SplitterType)
			commonOpt.ChunkSize = opt.LocalOpt.LangchainOpt.ChunkSize
			commonOpt.ChunkOverlap = opt.LocalOpt.LangchainOpt.ChunkOverlap
		}
	}

	// 将特定选项添加到Advanced字段
	if opt.LocalOpt != nil {
		commonOpt.Advanced["LocalOpt"] = opt.LocalOpt
	}

	if opt.KBOpt != nil {
		commonOpt.Advanced["KBOpt"] = opt.KBOpt
	}

	if opt.LocalOpt != nil && opt.LocalOpt.LangchainOpt != nil {
		commonOpt.Advanced["LangchainOpt"] = opt.LocalOpt.LangchainOpt
	}

	return commonOpt
}

// SplitWithRegistry 使用注册表分割文件
func SplitWithRegistry(ctx context.Context, registry *Registry, factory *Factory, opt *model.SplitOption, files []*kb.SplitFile) (*kb.SplitFilesResponse, error) {
	// 转换选项
	commonOpt := OptionAdapter(opt)

	// 创建分割器键
	key := NewSplitterKey(commonOpt)

	// 尝试获取批处理分割器
	batchSplitter, ok := registry.GetBatchSplitterWithKey(key)
	if !ok {
		// 如果没有找到，尝试使用工厂创建
		var err error
		batchSplitter, err = factory.CreateBatchSplitterFromOption(commonOpt)
		if err != nil {
			return nil, err
		}

		// 注册创建的分割器，以便下次使用
		registry.RegisterBatchSplitterWithKey(key, batchSplitter)
	}

	// 使用分割器处理文件
	return batchSplitter.SplitFiles(ctx, files)
}

// RegisterStandardSplitters 注册标准分割器
func RegisterStandardSplitters(registry *Registry, factory *Factory) {
	// 注册本地文本分割器
	factory.RegisterCreator("local.text.basic", createBasicTextSplitter)

	// 注册Langchain分割器
	factory.RegisterCreator("local.text.langchain.recursive", createRecursiveSplitter)
	factory.RegisterCreator("local.text.langchain.markdown", createMarkdownSplitter)

	// 注册代码分割器
	factory.RegisterCreator("local.code", createCodeSplitter)

	// 注册知识库分割器
	// factory.RegisterCreator("kb", createKBSplitter)
}

// 以下是创建具体分割器的函数

// createBasicTextSplitter 创建基本文本分割器
func createBasicTextSplitter(options map[string]interface{}) (Splitter, error) {
	// 从options中提取参数
	chunkSize, _ := options["ChunkSize"].(int)
	chunkOverlap, _ := options["ChunkOverlap"].(int)

	// 创建分割器选项
	opt := &text.TextSplitterOption{
		MaxPara: chunkSize,
		Overlap: chunkOverlap,
	}

	// 创建分割器
	return text.NewTextSplitter(opt), nil
}

// createRecursiveSplitter 创建递归字符分割器
func createRecursiveSplitter(options map[string]interface{}) (Splitter, error) {
	// 从options中提取参数
	chunkSize, _ := options["ChunkSize"].(int)
	chunkOverlap, _ := options["ChunkOverlap"].(int)

	// 创建分割器
	return langchain.NewRecursiveCharacterSplitterAdapter(chunkSize, chunkOverlap), nil
}

// createMarkdownSplitter 创建Markdown分割器
func createMarkdownSplitter(options map[string]interface{}) (Splitter, error) {
	// 从options中提取参数
	chunkSize, _ := options["ChunkSize"].(int)
	chunkOverlap, _ := options["ChunkOverlap"].(int)

	// 创建分割器
	return langchain.NewMarkdownSplitter(chunkSize, chunkOverlap), nil
}

// createCodeSplitter 创建代码分割器
func createCodeSplitter(options map[string]interface{}) (Splitter, error) {
	// 创建分割器
	return code.NewCodeSplitter(), nil
}
