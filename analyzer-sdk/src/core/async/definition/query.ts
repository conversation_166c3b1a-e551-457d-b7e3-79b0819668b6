import {TextDocument} from "vscode-languageserver-textdocument";
import {Position} from "vscode-languageserver-types";
import {ResolvedTypeInfo} from "../../share/common";
import {getCachedFileSymbolDef} from "../../cache/symbolDefCache";
import {getCachedDefinitionInfoByKey} from "../../cache/definitionCache";
import {EVENT_CATEGORY_TYPE_ANALYSIS, EVENT_NAME_FAILED, reportEvent} from "../../../utils/event";
import {EVENT_VALUE_SYMBOL_FILE_CACHE_MISS, EVENT_PROCESS_QUERY} from "./share";
import {Logger} from "../../../utils/logger";

export interface DefinitionQueryResult {
  symbolName: string;
  definitionKey: string;
  typeInfo: ResolvedTypeInfo;
}

export function queryDefinitionForPosition(document: TextDocument, position: Position): DefinitionQueryResult {
  const cachedFileSymbolDef = getCachedFileSymbolDef(document, false);
  if (!cachedFileSymbolDef) {
    Logger.warn(`queryDefinitionForPosition: no cached symbol file data found for '${document.uri}'`);
    reportEvent(document.languageId, EVENT_CATEGORY_TYPE_ANALYSIS, EVENT_PROCESS_QUERY, EVENT_NAME_FAILED, EVENT_VALUE_SYMBOL_FILE_CACHE_MISS).then();
    return null;
  }

  const offset = document.offsetAt(position);
  const symbolDefInfo = cachedFileSymbolDef.getSymbolDefInfo(offset);
  if (!symbolDefInfo) {
    return null;
  }

  const symbolName = symbolDefInfo.name;
  const definitionKey = symbolDefInfo.definitionKey;
  if (definitionKey) {
    return {symbolName, definitionKey, typeInfo: getCachedDefinitionInfoByKey(document.languageId, definitionKey)};
  }

  return null;
}

export function queryDefinitionForRange(document: TextDocument, startIndex: number, endIndex: number): Map<number, DefinitionQueryResult> {
  if (startIndex >= endIndex) {
    return new Map();
  }

  const cachedFileSymbolDef = getCachedFileSymbolDef(document, false);
  if (!cachedFileSymbolDef) {
    Logger.warn(`queryDefinitionForRange: no cached symbol file data found for '${document.uri}'`);
    reportEvent(document.languageId, EVENT_CATEGORY_TYPE_ANALYSIS, EVENT_PROCESS_QUERY, EVENT_NAME_FAILED, EVENT_VALUE_SYMBOL_FILE_CACHE_MISS).then();
    return new Map();
  }

  const result: Map<number, DefinitionQueryResult> = new Map();

  for (let i = startIndex; i < endIndex; i++) {
    const symbolDefInfo = cachedFileSymbolDef.getSymbolDefInfo(i);
    if (!symbolDefInfo) {
      continue;
    }

    const symbolName = symbolDefInfo.name;
    const definitionKey = symbolDefInfo.definitionKey;
    if (definitionKey) {
      if (i === startIndex || cachedFileSymbolDef.getSymbolDefInfo(i - 1)?.name !== symbolName) {
        const typeInfo = getCachedDefinitionInfoByKey(document.languageId, definitionKey);
        if (typeInfo) {
          result.set(i, {symbolName, definitionKey, typeInfo});
        }
      }
    }
  }

  return result;
}