import {TextDocument} from "vscode-languageserver-textdocument";
import {Position} from "vscode-languageserver-types";
import {Logger} from "../../../utils/logger";
import {MainHandler} from "../../../worker/main";

export async function getDefinitionsForRange(document: TextDocument, startPosition?: Position, endPosition?: Position): Promise<string> {
  try {
    return await MainHandler.INSTANCE.getDefinitionForRange({document, startPosition, endPosition});
  } catch (e) {
    Logger.error(`getDefinitionsForRange: exception for '${document.uri}': ${e}`);
    Logger.error(e.stack);
    return null;
  }
}