import {beforeAll, describe, expect, it} from "vitest";
import {TreeSitterParser} from "../parser";
import {LanguageIdEnum} from "../../share";
import {CTreeSitterHandler} from "./c";

describe("test_getFuncParentNodeForRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
int add(int a, int b) {
    int result = a + b;
    return result;
}

int subtract(int a, int b) {
    return a - b;
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.C, docText);
    const result = CTreeSitterHandler.INSTANCE.getFuncParentNodeForRange(tree.rootNode, {
      start: {
        line: 2,
        character: 4
      }, 
      end: {
        line: 2, 
        character: 10
      }
    });
    
    expect(result.text).toEqual(`int add(int a, int b) {
    int result = a + b;
    return result;
}`);
  });

  it("case2_null", async () => {
    const docText = `
int add(int a, int b) {
    int result = a + b;
    return result;
}

int subtract(int a, int b) {
    return a - b;
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.C, docText);
    const result = CTreeSitterHandler.INSTANCE.getFuncParentNodeForRange(tree.rootNode, {
      start: {
        line: 0,
        character: 0
      }, 
      end: {
        line: 0, 
        character: 5
      }
    });
    
    expect(result).toEqual(null);
  });
});

describe("test_getTypeParentNodeForRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_struct", async () => {
    const docText = `
struct Point {
    int x;
    int y;
};

int calculate_distance(struct Point p1, struct Point p2) {
    int dx = p2.x - p1.x;
    int dy = p2.y - p1.y;
    return dx * dx + dy * dy;
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.C, docText);
    const result = CTreeSitterHandler.INSTANCE.getTypeParentNodeForRange(tree.rootNode, {
      start: {
        line: 2,
        character: 4
      }, 
      end: {
        line: 2, 
        character: 10
      }
    });
    
    expect(result.text).toEqual(`struct Point {
    int x;
    int y;
}`);
  });

  it("case2_enum", async () => {
    const docText = `
enum Color {
    RED,
    GREEN,
    BLUE
};

void print_color(enum Color color) {
    switch (color) {
        case RED:
            printf("Red");
            break;
        case GREEN:
            printf("Green");
            break;
        case BLUE:
            printf("Blue");
            break;
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.C, docText);
    const result = CTreeSitterHandler.INSTANCE.getTypeParentNodeForRange(tree.rootNode, {
      start: {
        line: 3,
        character: 4
      }, 
      end: {
        line: 3, 
        character: 9
      }
    });
    
    expect(result.text).toEqual(`enum Color {
    RED,
    GREEN,
    BLUE
}`);
  });
});

describe("test_getVarParentNode", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
int global_var = 10;

int main() {
    int local_var = 5;
    return 0;
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.C, docText);
    const result = CTreeSitterHandler.INSTANCE.getVarParentNode(tree.rootNode, {
      start: {
        line: 1,
        character: 4
      }, 
      end: {
        line: 1, 
        character: 14
      }
    });
    
    expect(result.text).toEqual(`int global_var = 10;`);
  });
});

describe("test_getMethodInRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
int add(int a, int b) {
    int result = a + b;
    return result;
}

int subtract(int a, int b) {
    return a - b;
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.C, docText);
    const range = {
      start: {
        line: 1,
        character: 0
      },
      end: {
        line: 4,
        character: 1
      }
    };
    
    const result = CTreeSitterHandler.INSTANCE.getMethodInRange(tree.rootNode, range, 0, 50, false);
    
    expect(result.map(node => node.text)).toEqual([
      `int add(int a, int b) {
    int result = a + b;
    return result;
}`
    ]);
  });
}); 