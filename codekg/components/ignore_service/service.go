package ignore_service

import (
	"context"
	_ "embed"
	gitignore "github.com/denormal/go-gitignore"
	"github.com/samber/lo"
	"go.uber.org/atomic"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/model"
	"io/fs"
	"os"
	"path/filepath"
	"slices"
	"sort"
	"strings"
	"sync"
)

//go:generate go run go.uber.org/mock/mockgen -destination mock_ignore_service/mock_ignore_service.go -package mock_ignore_service . IgnoreService
type IgnoreService interface {
	AddProject(ctx context.Context, uri model.URI, defaultIgnoreRules string, customizedIgnoreRules string) error
	CreateOrChangeURI(ctx context.Context, uri model.URI) error
	DeleteURI(ctx context.Context, uri model.URI) error
	IsIgnored(ctx context.Context, uri model.URI) (bool, error)
	GetInnerFolders(ctx context.Context, folderAbsPath string) []string
	IsNotEmptyFolder(ctx context.Context, folderAbsPath string) bool
}

type service struct {
	mu                       *sync.RWMutex
	ignoreFileType           model.URI
	ignores                  map[model.URI]gitignore.GitIgnore
	projectCustomizedIgnores map[string]gitignore.GitIgnore
	defaultIgnores           map[model.URI]gitignore.GitIgnore
}

func NewIgnoreService(ignoreFileType model.URI) IgnoreService {
	return &service{
		mu:                       new(sync.RWMutex),
		ignoreFileType:           ignoreFileType,
		ignores:                  make(map[model.URI]gitignore.GitIgnore),
		projectCustomizedIgnores: make(map[string]gitignore.GitIgnore),
		defaultIgnores:           make(map[model.URI]gitignore.GitIgnore),
	}
}

func (s *service) build(ctx context.Context, root model.URI) error {
	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			logs.CtxDebug(ctx, "[build] walk argument err != nil: %+v", err)
			return nil
		}

		// 忽略 .git 目录
		if info.IsDir() && info.Name() == ".git" {
			return filepath.SkipDir
		}

		if info.IsDir() {
			isIgnored, err := s.isIgnoredHelper(path)
			if err != nil {
				logs.CtxDebug(ctx, "[build] isIgnoredHelper return err: %+v", err)
				return nil
			}

			if isIgnored {
				return filepath.SkipDir
			}
		}

		// 如果找到一个 .gitignore 文件，编译它的忽略模式
		if !info.IsDir() && info.Name() == s.ignoreFileType {
			base := filepath.Dir(path)
			isIgnored, err := s.isIgnoredHelper(base)
			if err != nil {
				logs.CtxDebug(ctx, "[build] isIgnoredHelper gitignore return err: %+v", err)
				return nil
			}

			if isIgnored {
				return nil
			}

			ignore, err := gitignore.NewFromFile(path)
			if err != nil {
				logs.CtxDebug(ctx, "[build] gitignore.NewFromFile failed: %+v", err)
				return nil
			}

			s.ignores[base] = ignore
		}
		return nil
	})

	return err
}

func (s *service) AddProject(ctx context.Context, projectRoot model.URI, defaultIgnoreRules string, customizedIgnoreRules string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// CKG 默认忽略的内容
	defaultIgnore := gitignore.New(strings.NewReader(defaultIgnoreRules), projectRoot, nil)
	s.defaultIgnores[projectRoot] = defaultIgnore

	// 用户自定义的忽略内容
	if customizedIgnoreRules != "" {
		customizedIgnore := gitignore.New(strings.NewReader(customizedIgnoreRules), projectRoot, nil)
		s.projectCustomizedIgnores[projectRoot] = customizedIgnore
	} else {
		delete(s.projectCustomizedIgnores, projectRoot)
	}

	return s.build(ctx, projectRoot)
}

func (s *service) CreateOrChangeURI(ctx context.Context, uri model.URI) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	info, err := os.Stat(uri)
	if err != nil {
		if os.IsNotExist(err) {
			return nil
		}
		return err
	}

	if info.IsDir() {
		return s.build(ctx, uri)
	} else {
		if info.Name() == s.ignoreFileType {
			return s.build(ctx, filepath.Dir(uri))
		}
	}

	return nil
}

func (s *service) DeleteURI(ctx context.Context, uri model.URI) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if filepath.Base(uri) == s.ignoreFileType {
		base := filepath.Dir(uri)
		delete(s.ignores, base)
		return s.build(ctx, base)
	}

	separator := string(os.PathSeparator)
	a := strings.TrimSuffix(uri, separator) + separator
	for base := range s.ignores {
		if strings.HasPrefix(base, a) || base == uri {
			delete(s.ignores, base)
		}
	}

	return nil
}

func (s *service) isIgnoredHelper(uri model.URI) (bool, error) {
	_, err := os.Stat(uri)
	if err != nil {
		if os.IsNotExist(err) {
			return false, nil
		}

		return false, err
	}

	candidateIgnore := make([]string, 0)
	for base := range s.ignores {
		if strings.HasPrefix(uri, base) {
			candidateIgnore = append(candidateIgnore, base)
		}
	}

	// 文件夹匹配规则从子目录向上匹配
	sort.Slice(candidateIgnore, func(i, j int) bool {
		return len(candidateIgnore[i]) > len(candidateIgnore[j])
	})

	for _, base := range candidateIgnore {
		if uri == base {
			continue
		}
		if s.bottomUpIgnoreChecker(base, uri, s.ignores) {
			return true, nil
		}
	}

	for project, _ := range s.defaultIgnores {
		if uri == project {
			continue
		}
		if strings.HasPrefix(uri, project) {
			if s.bottomUpIgnoreChecker(project, uri, s.defaultIgnores) {
				return true, nil
			}
		}
	}

	for project, _ := range s.projectCustomizedIgnores {
		if uri == project {
			continue
		}
		if strings.HasPrefix(uri, project) {
			if s.bottomUpIgnoreChecker(project, uri, s.projectCustomizedIgnores) {
				return true, nil
			}
		}
	}

	return false, nil
}

func (s *service) bottomUpIgnoreChecker(base model.URI, uri model.URI, ignores map[string]gitignore.GitIgnore) bool {
	// 如果 base 不是 uri 的 prefix，那么返回 false，
	// 表示 “base 的 ignore 规则” 无法忽略 uri
	base, uri = filepath.Clean(base), filepath.Clean(uri)
	if !strings.HasPrefix(uri, base) {
		return false
	}
	var ignore gitignore.GitIgnore
	var ok bool
	if ignore, ok = ignores[base]; !ok || ignore == nil {
		return false
	}
	// 根据 uri "/Users/<USER>/a/b/c/d/e" 和 base "/Users/<USER>/a/b"
	// 获得 "/Users/<USER>/a/b", "/Users/<USER>/a/b/c", "/Users/<USER>/a/b/c/d", "/Users/<USER>/a/b/c/d/e"
	path, err := filepath.Rel(base, uri)
	if err != nil {
		return false
	}
	parts := strings.Split(path, string(os.PathSeparator))
	parts = lo.Filter(parts, func(part string, _ int) bool { return len(part) > 0 })
	uriArr := make([]string, 0)
	lo.Reduce(parts, func(agg string, item string, i int) string {
		path := filepath.Join(agg, item)
		uriArr = append(uriArr, path)
		return path
	}, base)
	slices.SortFunc(
		lo.Filter(uriArr, func(u string, _ int) bool { return len(u) >= len(base) }),
		func(a, b string) int { return len(b) - len(a) },
	)
	// 从该文件开始，层层向上直到 base，匹配忽略规则
	for _, u := range uriArr {
		if ignore.Ignore(u) {
			return true
		}
	}
	return false
}

func (s *service) IsIgnored(ctx context.Context, uri model.URI) (bool, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return s.isIgnoredHelper(uri)
}

func (s *service) GetInnerFolders(ctx context.Context, folderAbsPath string) []string {
	var innerFolders []string
	cleanPath := filepath.Clean(folderAbsPath)
	err := filepath.Walk(cleanPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 忽略所有文件
		if !info.IsDir() {
			return nil
		}

		// ignore 服务
		ignored, err := s.IsIgnored(ctx, path)
		if err != nil {
			return err
		}
		if ignored {
			return filepath.SkipDir
		}

		// 忽略隐藏文件夹
		segments := strings.Split(path, string(os.PathSeparator))
		if len(segments) >= 1 && strings.HasPrefix(segments[len(segments)-1], ".") {
			return filepath.SkipDir
		}

		if info.Mode()&os.ModeSymlink == 0 {
			innerFolders = append(innerFolders, filepath.Clean(path))
		}
		return nil
	})
	if err != nil {
		logs.Warn("[GetInnerFolders] walk failed, err: %v", err)
		return []string{}
	}
	return innerFolders
}

func (s *service) IsNotEmptyFolder(ctx context.Context, folderAbsPath string) bool {
	isNotEmptyFolder := atomic.NewBool(false)
	cleanPath := filepath.Clean(folderAbsPath)
	folderInfo, err := file_system.RealFS.Stat(cleanPath)
	if err != nil || !folderInfo.IsDir() {
		logs.CtxInfo(ctx, "folder info: %v, err: %v", folderInfo, err)
		return false
	}
	_ = filepath.Walk(cleanPath, func(path string, info fs.FileInfo, err error) error {
		if err != nil || info.IsDir() || info.Mode()&os.ModeSymlink != 0 {
			return nil
		}
		// ignore 服务
		ignored, err := s.IsIgnored(ctx, path)
		if err != nil {
			// 如果 ignore 错误，一般是找不到文件导致的，属于极端 case。
			return nil
		}
		if ignored {
			return filepath.SkipDir
		}
		// 这里同样忽略隐藏文件夹和文件，与 #Folder 对齐，后续更该时需要与 GetInnerFolders 一起改
		segments := strings.Split(path, string(os.PathSeparator))
		if len(segments) >= 1 && strings.HasPrefix(segments[len(segments)-1], ".") {
			return filepath.SkipDir
		}
		isNotEmptyFolder.Store(true)
		return filepath.SkipAll
	})
	return isNotEmptyFolder.Load()
}
