package langchain

import (
	"context"

	kb "ide/ckg/codekg/components/knowledgebase"
	lsmodel "ide/ckg/codekg/components/splitter/local_splitter/model"
	"ide/ckg/codekg/components/splitter/model"
)

// 使用model包中定义的LangchainType
// 为了向后兼容，保留这些常量，但使用model包中的类型
const (
	// RecursiveCharacterType 是基于字符的递归分割器
	RecursiveCharacterType = model.LangchainTypeRecursive
	// MarkdownType 是专门用于Markdown文本的分割器
	MarkdownType = model.LangchainTypeMarkdown
	// DefaultType 是默认的分割器（使用RecursiveCharacter）
	DefaultType = model.LangchainTypeRecursive
)

// DefaultChunkSize 是默认的chunk大小
const DefaultChunkSize = 1000

// DefaultChunkOverlap 是默认的chunk重叠大小
const DefaultChunkOverlap = 100

// SplitterOption 定义了创建splitter的选项
type SplitterOption struct {
	// SplitterType 是要使用的splitter类型
	SplitterType model.LangchainType
	// ChunkSize 是每个chunk的最大大小
	ChunkSize int
	// ChunkOverlap 是相邻chunk之间的重叠大小
	ChunkOverlap int
	// AllSplitAsText 是否将所有文件都作为文本处理
	AllSplitAsText bool
}

// NewSplitter 创建一个新的splitter
func NewSplitter(opt *SplitterOption) lsmodel.LocalSplitter {
	// 如果选项为nil，使用默认值
	if opt == nil {
		opt = &SplitterOption{
			SplitterType:   DefaultType,
			ChunkSize:      DefaultChunkSize,
			ChunkOverlap:   DefaultChunkOverlap,
			AllSplitAsText: false,
		}
	} else {
		// 应用默认值
		if opt.ChunkSize == 0 {
			opt.ChunkSize = DefaultChunkSize
		}
		if opt.ChunkOverlap == 0 {
			opt.ChunkOverlap = DefaultChunkOverlap
		}
	}

	// 根据类型创建对应的splitter
	switch opt.SplitterType {
	case RecursiveCharacterType:
		return NewRecursiveCharacterSplitterAdapter(opt.ChunkSize, opt.ChunkOverlap)
	case MarkdownType:
		return NewMarkdownSplitter(opt.ChunkSize, opt.ChunkOverlap)
	default:
		// 默认使用RecursiveCharacter
		return NewRecursiveCharacterSplitterAdapter(opt.ChunkSize, opt.ChunkOverlap)
	}
}

// SplitFiles 使用指定的splitter分割文件
func SplitFiles(ctx context.Context, opt *SplitterOption, files []*kb.SplitFile) (*kb.SplitFilesResponse, error) {
	// 创建splitter
	splitter := NewSplitter(opt)

	// 处理文件
	results := make(map[string]*kb.SplitFileResult_)
	failedResults := make(map[string]string)

	for _, file := range files {
		if file == nil {
			continue
		}

		result, err := splitter.SplitFile(ctx, file)
		if err != nil {
			failedResults[file.Path] = err.Error()
			continue
		}

		results[file.Path] = result
	}

	return &kb.SplitFilesResponse{
		Results:     results,
		FailedFiles: failedResults,
	}, nil
}

// SplitterOptionFromLangchainOpt 从LangchainSplitOption创建SplitterOption
// 这个函数可以帮助从新的分层结构中创建SplitterOption
func SplitterOptionFromLangchainOpt(opt *model.LangchainSplitOption) *SplitterOption {
	// 设置默认值
	finalChunkSize := DefaultChunkSize
	finalChunkOverlap := DefaultChunkOverlap

	if opt == nil {
		return &SplitterOption{
			SplitterType:   DefaultType,
			ChunkSize:      finalChunkSize,
			ChunkOverlap:   finalChunkOverlap,
			AllSplitAsText: false,
		}
	}

	// 如果传入的值大于0，则使用传入的值
	if opt.ChunkSize > 0 {
		finalChunkSize = opt.ChunkSize
	}

	if opt.ChunkOverlap > 0 {
		finalChunkOverlap = opt.ChunkOverlap
	}

	return &SplitterOption{
		SplitterType:   opt.SplitterType,
		ChunkSize:      finalChunkSize,
		ChunkOverlap:   finalChunkOverlap,
		AllSplitAsText: opt.AllSplitAsText,
	}
}

// SplitFilesWithLangchainOpt 使用LangchainSplitOption分割文件
// 这个函数是为了适配新的分层结构
func SplitFilesWithLangchainOpt(ctx context.Context, opt *model.LangchainSplitOption, files []*kb.SplitFile) (*kb.SplitFilesResponse, error) {
	splitterOpt := SplitterOptionFromLangchainOpt(opt)
	return SplitFiles(ctx, splitterOpt, files)
}
