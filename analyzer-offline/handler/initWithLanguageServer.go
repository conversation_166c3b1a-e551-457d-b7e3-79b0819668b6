package handler

import (
	"byted-ckg/analyzer-offline/manager"
	"code.byted.org/gopkg/logs/v2"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

type InitRequestData struct {
	Repo     string `json:"repo"`
	Language string `json:"language"`
}

func InitWithLanguageServerHandler(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	if r.Method != http.MethodPost {
		msg := fmt.Sprintf("unsupported request method, required POST, got %s", r.Method)
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		msg := fmt.Sprintf("failed to read request body: %s", err.Error())
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	var data InitRequestData
	if err = json.Unmarshal(body, &data); err != nil {
		msg := fmt.Sprintf("failed to read body data: %s", err.Error())
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusBadRequest)
		return
	}

	if !manager.GetLanguageServerManager().IsSupportedLanguage(data.Language) {
		msg := fmt.Sprintf("unsupported language: %s", data.Language)
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusBadRequest)
		return
	}

	if manager.GetLanguageServerManager().IsStarted(data.Language) {
		logs.CtxInfo(ctx, "language server for '%s' is already started, shutdown it", data.Language)
		if err = manager.GetLanguageServerManager().Shutdown(data.Language); err != nil {
			msg := fmt.Sprintf("failed to shutdown language server: %s", err.Error())
			logs.CtxError(ctx, msg)
			http.Error(w, msg, http.StatusInternalServerError)
			return
		}
	}

	if err = manager.GetLanguageServerManager().Start(data.Language); err != nil {
		msg := fmt.Sprintf("failed to start language server: %s", err.Error())
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusInternalServerError)
		return
	}

	if err = manager.GetLanguageServerManager().Initialize(data.Language, data.Repo); err != nil {
		msg := fmt.Sprintf("failed to initialize language server for '%s': %s", data.Repo, err.Error())
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusInternalServerError)
		return
	}

	if manager.GetAnalyzerManager().IsStarted() {
		logs.CtxInfo(ctx, "analyzer is already started, shutdown it")
		if err = manager.GetAnalyzerManager().Shutdown(); err != nil {
			msg := fmt.Sprintf("failed to shutdown analyzer: %s", err.Error())
			logs.CtxError(ctx, msg)
			http.Error(w, msg, http.StatusInternalServerError)
			return
		}
	}

	if err = manager.GetAnalyzerManager().Start(); err != nil {
		msg := fmt.Sprintf("failed to start analyzer: %s", err.Error())
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusInternalServerError)
		return
	}

	if err = manager.GetAnalyzerManager().InitializeAnalyzer(data.Repo, data.Language); err != nil {
		msg := fmt.Sprintf("failed to initialize analzyer for '%s': %s", data.Repo, err.Error())
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusInternalServerError)
		return
	}

	logs.CtxInfo(ctx, "Init analyzer success for '%s' with language '%s'", data.Repo, data.Language)
}
