package text

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"unicode/utf8"

	"github.com/samber/lo"

	"ide/ckg/codekg/components/knowledgebase"
	lsmodel "ide/ckg/codekg/components/splitter/local_splitter/model"
	"ide/ckg/codekg/components/splitter/local_splitter/util"
	"ide/ckg/codekg/model"
)

// DefaultTextSplitterOption defines the default configuration values for TextSplitter
var DefaultTextSplitterOption = TextSplitterOption{
	MaxPara:       1000,
	Overlap:       100,
	MaxHeading:    5,
	MaxLineLength: 500,
	MaxLineKeep:   97,
}

// TextSplitter defines the interface for splitting markdown content
type TextSplitter interface {
	lsmodel.LocalSplitter

	// splitContent splits a single markdown content into text segments
	splitContent(ctx context.Context, content string, title string, fileID string) []*knowledgebase.TextSegment
}

// TextSplitterOption defines configuration options for creating a MarkdownSplitter.
// Fields that are not set (zero values) will use default values when creating a TextSplitter.
type TextSplitterOption struct {
	MaxPara       int
	Overlap       int
	MaxHeading    int
	MaxLineLength int
	MaxLineKeep   int
}

// textSplitterImpl is the implementation of MarkdownSplitter interface.
// It contains the actual implementation for splitting markdown content.
type textSplitterImpl struct {
	MaxPara       int
	Overlap       int
	MaxHeading    int
	MaxLineLength int
	MaxLineKeep   int

	SupportedLanguageMap map[model.LanguageType]bool
}

// NewTextSplitter creates a new TextSplitter
func NewTextSplitter(opt *TextSplitterOption) TextSplitter {
	// If opt is nil, use default options
	if opt == nil {
		optCopy := DefaultTextSplitterOption
		opt = &optCopy
	} else {
		// Apply defaults for any zero values
		if opt.MaxPara == 0 {
			opt.MaxPara = DefaultTextSplitterOption.MaxPara
		}
		if opt.Overlap == 0 {
			opt.Overlap = DefaultTextSplitterOption.Overlap
		}
		if opt.MaxHeading == 0 {
			opt.MaxHeading = DefaultTextSplitterOption.MaxHeading
		}
		if opt.MaxLineLength == 0 {
			opt.MaxLineLength = DefaultTextSplitterOption.MaxLineLength
		}
		if opt.MaxLineKeep == 0 {
			opt.MaxLineKeep = DefaultTextSplitterOption.MaxLineKeep
		}
	}

	// Create splitter with the options
	return &textSplitterImpl{
		MaxPara:       opt.MaxPara,
		Overlap:       opt.Overlap,
		MaxHeading:    opt.MaxHeading,
		MaxLineLength: opt.MaxLineLength,
		MaxLineKeep:   opt.MaxLineKeep,

		SupportedLanguageMap: map[model.LanguageType]bool{
			model.LanguageText:     true,
			model.LanguageMarkdown: true,
		},
	}
}

func (s *textSplitterImpl) SupportedLanguages() map[model.LanguageType]bool {
	return s.SupportedLanguageMap
}

// splitContent implements the MarkdownSplitter interface
// It splits a single markdown content into text segments
func (s *textSplitterImpl) splitContent(ctx context.Context, markdownText string, title string, fileID string) []*knowledgebase.TextSegment {
	var (
		// Process up to four levels at most.
		currentHeadings           = []string{"", "", "", "", ""}
		currentParagraph          = make([]string, 0)
		currentParagraphStartLine = 0
		currentLevel              = s.MaxHeading + 1
		isCode                    = false
		isTable                   bool
	)

	lines := strings.Split(markdownText, "\n")
	var segments []*knowledgebase.TextSegment
	for lineNo, line := range lines {
		if strings.HasPrefix(strings.TrimSpace(line), "```") || strings.HasPrefix(strings.TrimSpace(line), "> ```") {
			isCode = !isCode
		}
		isTable = strings.HasPrefix(line, "|")
		// No need to limit the length of table.
		if !isTable && utf8.RuneCountInString(line) >= s.MaxLineLength {
			line = s.truncateString(line, s.MaxLineKeep)
		}
		// Get the heading level of the current line, considering any level above 5 as level 6.
		lineLevel := s.getCurrentLevel(line, isCode)
		if s.toSplit(lineLevel, currentParagraph, isCode, isTable) {
			newSegments := s.getNewParagraph(currentHeadings, title, currentParagraph, currentLevel)
			if len(newSegments) != 0 {
				lo.ForEach(newSegments, func(p *knowledgebase.TextSegment, i int) {
					p.StartLine = int(currentParagraphStartLine) + p.StartLine
					p.EndLine = int(currentParagraphStartLine) + p.EndLine
				})
				segments = append(segments, newSegments...)
				currentParagraph = []string{}
			}
		}
		if lineLevel > s.MaxHeading {
			if len(currentParagraph) == 0 {
				// Record first line number.
				currentParagraphStartLine = lineNo
			}
			currentParagraph = append(currentParagraph, line)
		} else {
			currentHeadings, currentLevel = s.updateHeading(currentHeadings, line, lineLevel, currentLevel)
		}
	}
	if len(currentParagraph) > 0 {
		newSegments := s.getNewParagraph(currentHeadings, title, currentParagraph, currentLevel)
		if len(newSegments) != 0 {
			lo.ForEach(newSegments, func(p *knowledgebase.TextSegment, i int) {
				p.StartLine = int(currentParagraphStartLine) + p.StartLine
				p.EndLine = int(currentParagraphStartLine) + p.EndLine
			})
			segments = append(segments, newSegments...)
		}
	}
	generator := util.NewSegmentIDGenerator(fileID)
	lo.ForEach(segments, func(p *knowledgebase.TextSegment, i int) {
		// Convert to 1-based line numbers, but avoid double incrementing
		// We only need to increment once since currentParagraphStartLine is already 0-indexed
		p.ID = generator.GenerateTextSegmentID(p)
		// Add boundary check - using 0-indexed for array access
		if p.StartLine >= 0 && p.EndLine > p.StartLine && p.EndLine <= len(lines) {
			p.Content = strings.Join(lines[p.StartLine:p.EndLine], "\n")
		} else {
			// If bounds are invalid, use empty content to prevent panic
			p.Content = ""
		}
		// Now convert to 1-based line numbers for external representation
		p.StartLine++
		p.EndLine++
	})

	return segments
}

func (s *textSplitterImpl) updateHeading(currentHeadings []string, line string, lineLevel int, currentLevel int) ([]string, int) {
	if lineLevel > s.MaxHeading {
		return currentHeadings, currentLevel
	}
	currentHeadings[lineLevel-1] = line
	for i := lineLevel; i < len(currentHeadings); i++ {
		currentHeadings[i] = ""
	}
	return currentHeadings, lineLevel
}

func (s *textSplitterImpl) truncateString(str string, maxLen int) string {
	if len(str) > maxLen {
		runes := []rune(str)
		if len(runes) > maxLen {
			str = string(runes[:maxLen])
		}
		str += "..."
	}
	return str
}

func (s *textSplitterImpl) getCurrentLevel(line string, isCode bool) int {
	if isCode { // 如果是code 就默认为正文
		return s.MaxHeading + 1
	}
	// Detect the format of: 1. ##
	if strings.Contains(line, ". #") {
		components := strings.Split(line, ".")
		if len(components) > 1 {
			digits := "0123456789"
			failed := false
			for _, char := range components[0] {
				if !strings.Contains(digits, string(char)) {
					failed = true
					break
				}
			}
			if !failed {
				line = strings.Join(components[1:], ".")
				line = strings.TrimSpace(line)
			}
		}
	}

	// For levels 5 and above, consider them as body content level 6.
	if !strings.HasPrefix(line, "#") {
		return s.MaxHeading + 1
	}
	headingLevel := len(regexp.MustCompile(`^#+`).FindString(line))
	if headingLevel > s.MaxHeading {
		return s.MaxHeading + 1
	}
	return headingLevel
}

func (s *textSplitterImpl) toSplit(lineLevel int, currentParagraph []string, isCode bool, isTable bool) bool {
	// Do not truncate code or tables.
	if isCode || isTable {
		return false
	}
	// If a new heading is encountered and the current paragraph has content, create a new paragraph.
	if len(currentParagraph) != 0 && lineLevel <= s.MaxHeading {
		return true
	}
	return false
}

func (s *textSplitterImpl) getNewParagraph(currentHeadings []string, title string, currentParagraph []string, currentLevel int) []*knowledgebase.TextSegment {
	newParagraph := fmt.Sprintf("%s\n", title)
	var results []*knowledgebase.TextSegment
	for level := 0; level < currentLevel; level++ {
		if level > s.MaxHeading-1 {
			break
		}
		if currentHeadings[level] == "" {
			continue
		}
		newParagraph += fmt.Sprintf("%s\n", currentHeadings[level])
	}
	if len(currentParagraph) != 0 { // If there is content, generate a new paragraph.
		// check empty lines
		// TODO: refactor it
		tmpParagraphs := map[string]bool{}
		for _, p := range currentParagraph {
			if p != "" {
				tmpParagraphs[p] = true
			}
		}
		if len(tmpParagraphs) == 0 { // If the current content only contains blank lines, ignore it.
			return nil
		}
		contents := strings.Join(currentParagraph, "\n")
		if utf8.RuneCountInString(contents) > s.MaxPara {
			splitContents := s.splitTextBySize(contents, s.Overlap)
			for _, content := range splitContents {
				content := content
				content.Content = fmt.Sprintf("%s%s", newParagraph, content.Content)
				results = append(results, content)
			}
		} else {
			results = append(results, &knowledgebase.TextSegment{
				Content:   fmt.Sprintf("%s%s", newParagraph, contents),
				StartLine: 0,
				EndLine:   int(len(currentParagraph)),
			})
		}
	} else {
		return nil
	}
	return results
}

// splitTextBySize splits text into smaller chunks based on size
func (s *textSplitterImpl) splitTextBySize(text string, overlap int) []*knowledgebase.TextSegment {
	paras := make([]*knowledgebase.TextSegment, 0)
	splitInfo := make([]struct{ start, end int }, 0)
	lineCount := make([]int, 0)
	currentStart := 0
	currentLength := 0
	lines := strings.Split(text, "\n")
	line2table := map[int]string{}
	tableTitle := ""
	isTable := false
	for idx, line := range lines {
		if strings.HasPrefix(line, "|") {
			line = s.processTableLine(line)
			lines[idx] = line
		}
		if !isTable && strings.HasPrefix(line, "|") { // Find the first table title.
			tableTitle = line
			isTable = true
		}
		if isTable && !strings.HasPrefix(line, "|") {
			tableTitle = ""
			isTable = false
		}
		line2table[idx] = tableTitle
		lineCount = append(lineCount, utf8.RuneCountInString(line))
		if currentLength+utf8.RuneCountInString(line) > s.MaxPara {
			splitInfo = append(splitInfo, struct{ start, end int }{currentStart, idx})
			overlapLength := 0
			for i := idx - 1; i >= 0; i-- {
				if overlapLength > overlap {
					break
				} else {
					currentStart = i
					overlapLength += lineCount[i]
				}
			}
			currentLength = overlapLength + utf8.RuneCountInString(line)
		} else {
			currentLength += utf8.RuneCountInString(line)
		}
	}
	splitInfo = append(splitInfo, struct{ start, end int }{currentStart, len(lines)})
	for _, info := range splitInfo {
		startIdx, endIdx := info.start, info.end
		para := &knowledgebase.TextSegment{
			Content:   strings.Join(lines[startIdx:endIdx], "\n"),
			StartLine: int(startIdx),
			EndLine:   int(endIdx),
		}
		if para.Content != "" && strings.HasPrefix(para.Content, "|") && startIdx >= 0 && line2table[startIdx] != "" && !strings.HasPrefix(para.Content, line2table[startIdx]) {
			tableSplitter := strings.Repeat("| --- ", strings.Count(line2table[startIdx], "|"))
			para.Content = line2table[startIdx] + "\n" + tableSplitter + "\n" + para.Content
		}
		// It might be empty.
		paras = append(paras, para)

	}
	return paras
}

func (s *textSplitterImpl) processTableLine(line string) string {
	// 减少大量重复的符号导致占用过多字数
	// 判断是否为分割行 ex. |--|--|
	isSplitter := s.isSplitTableLine(line)
	currentCells := strings.Split(line, "|") // format like ["", "123", "456", ""]
	if len(currentCells) < 3 {               // 合法的内容至少包含3个内容，即 |--|
		return line
	}
	for i := 1; i < len(currentCells)-1; i++ {
		newContent := "--"
		if !isSplitter {
			newContent = fmt.Sprintf(" %v ", strings.TrimSpace(currentCells[i]))
		}
		currentCells[i] = newContent
	}
	return strings.Join(currentCells, "|")
}

func (s *textSplitterImpl) isSplitTableLine(line string) bool {
	cells := strings.Split(line, "|")
	for _, cell := range cells {
		if cell != "" && !s.containOnlyDash(cell) {
			return false
		}
	}
	return true
}

func (s *textSplitterImpl) containOnlyDash(tableCellText string) bool {
	element := strings.TrimSpace(tableCellText)
	for _, c := range element {
		if c != '-' {
			return false
		}
	}
	return true
}

// SplitFile is a convenience method that handles file metadata
func (s *textSplitterImpl) SplitFile(ctx context.Context, file *knowledgebase.SplitFile) (*knowledgebase.SplitFileResult_, error) {
	if file == nil {
		return nil, fmt.Errorf("file cannot be nil")
	}

	// 使用splitContent处理单个文件内容
	textSegments := s.splitContent(ctx, file.Content, file.Title, file.Path)

	// 转换结果
	segmentsWithEmbedding := util.ConvertTextSegmentsToSegments(textSegments)

	return &knowledgebase.SplitFileResult_{
		SegmentsWithEmbedding: segmentsWithEmbedding,
	}, nil
}
