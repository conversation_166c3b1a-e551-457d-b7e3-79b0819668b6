import {doSetConfig<PERSON>yId, FeatureGateManager, FeatureName} from "../../../core/common/config";
import {Logger} from "../../../utils/logger";
import {MainHandler} from "../../../worker/main";

export async function setConfigById(configId: string, value: any): Promise<void> {
  try {
    doSetConfigById(configId, value);
    await MainHandler.INSTANCE.setConfigById({configId, value});
  } catch (e) {
    Logger.error(`setConfigById: exception: ${e}`);
    Logger.error(e.stack);
  }
}

export async function openFeature(featureName: FeatureName): Promise<any> {
  try {
    FeatureGateManager.INSTANCE.openFeature(featureName);
    await MainHandler.INSTANCE.openFeature({feature: featureName});
  } catch (e) {
    Logger.error(`openFeature: exception: ${e}`);
    Logger.error(e.stack);
  }
}

export async function closeFeature(featureName: FeatureName): Promise<any> {
  try {
    FeatureGateManager.INSTANCE.closeFeature(featureName);
    await MainHandler.INSTANCE.closeFeature({feature: featureName});
  } catch (e) {
    Logger.error(`closeFeature: exception: ${e}`);
    Logger.error(e.stack);
  }
}