import {beforeAll, describe, expect, it} from "vitest";
import {FeatureGateManager, FeatureName} from "./config";
import {handleFeatureChangeListener} from "./init";
import {setEnableLocalLogging} from "../../utils/logger";
import {MainHandler} from "../../worker/main";
import {wait} from "../../utils/common";

describe("test_handleFeatureChangeListener", () => {
  beforeAll(async () => {
    setEnableLocalLogging(true);
  });

  it("case1_normal", async () => {
    MainHandler.INSTANCE.init_("./dist/resources", "/test", false, false, false);

    const featureMap: Map<string, FeatureName> = new Map();
    featureMap.set("f1", FeatureName.DISABLE_EVENT);
    featureMap.set("f2", FeatureName.DISABLE_LOG);

    const featureState = {
      "f1": true,
      "f2": false,
    }

    const listener = function(feature: string, onFetchSuccess: (enable: boolean) => void): void {
      onFetchSuccess(featureState[feature]);
    }

    handleFeatureChangeListener(featureMap, listener)
    await wait(50);

    expect(FeatureGateManager.INSTANCE.getOpenedFeature()).toEqual([FeatureName.DISABLE_EVENT]);
    expect(await MainHandler.INSTANCE.getOpenedFeature({})).toEqual([FeatureName.DISABLE_EVENT]);
  });
});