package interaction_graph

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/components/relation_manager/git_graph"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/util"
	"math"
	"os"
	"sort"
	"strings"
	"sync"
)

/** Interaction Graph, 用户行为图 */

const (
	MaxJumpHistorySize    = 15
	NodeExpirationPeriod  = 2 * 60 * 60 // 2h 的前节点全部清除
	GraphMaxNodeCount     = 300         //
	GraphDeleteTargetSize = 150

	JumpScoreWeight      = 2
	CkgScoreWeight       = 1
	TimeScoreWeight      = 1
	VisitScoreWeight     = 1
	EmbeddingScoreWeight = 2
	GitScoreWeight       = 1
	NotCKGEntityPrefix   = "nockg"
)

// TODO: 支持非实体代码
// type NonEntityCode struct {
// 	Code      string
// 	FilePath  string
// 	StartLine int
// 	EndLine   int
// }

type InteractionEdge struct {
	Count       int64   `json:"count"`        // Number of jumps between entities
	JumpHistory []int64 `json:"jump_history"` // History of recent jump timestamps, limited size
}

type InteractionNode struct {
	ID         string           `json:"id"`          // Entity ID
	IsCKG      bool             `json:"is_ckg"`      // Is CKG entity
	SourceType string           `json:"source_type"` // Source type, from user_interaction or git
	Embedding  *model.Embedding `json:"-"`           // Embedding vector, only for notCKG entity
	Entity     *model.Entity    `json:"entity"`      // Entity object

	ViewCount int64 `json:"view_count"` // View count
	EditCount int64 `json:"edit_count"` // Edit count

	LastViewTime int64 `json:"last_view_time"` // Last view timestamp
	LastEditTime int64 `json:"last_edit_time"` // Last edit timestamp

	InEdges  map[string]*InteractionEdge `json:"-"` // ID -> edge
	OutEdges map[string]*InteractionEdge `json:"-"` // ID -> edge
}

func (n *InteractionNode) String() string {
	return fmt.Sprintf("InteractionNode{ID: %s, Entity-FilePath: %v, ViewCount: %d, EditCount: %d, LastViewTime: %d, LastEditTime: %d, InEdges: %d, OutEdges: %d}",
		n.ID, n.Entity.URI, n.ViewCount, n.EditCount, n.LastViewTime, n.LastEditTime, len(n.InEdges), len(n.OutEdges))
}

func NewInteractionNode(entity *model.Entity) *InteractionNode {
	isNotCKG := strings.HasPrefix(entity.ID, NotCKGEntityPrefix)
	return &InteractionNode{
		ID:           entity.ID,
		IsCKG:        !isNotCKG,
		SourceType:   "user_interaction", // default source type
		Entity:       entity,
		ViewCount:    0,
		EditCount:    0,
		LastViewTime: 0,
		LastEditTime: 0,
		InEdges:      make(map[string]*InteractionEdge),
		OutEdges:     make(map[string]*InteractionEdge),
	}
}

func (n *InteractionNode) GetSourceType() string {
	return n.SourceType
}

func (n *InteractionNode) SetSourceType(sourceType string) {
	n.SourceType = sourceType
}

func (n *InteractionNode) View(timestamp int64) {
	n.ViewCount++
	n.LastViewTime = timestamp
}

func (n *InteractionNode) Edit(timestamp int64) {
	n.EditCount++
	n.LastEditTime = timestamp
}

func (n *InteractionNode) GetNodeContent(ctx context.Context) string {
	content := strings.Join(n.Entity.GetContent(), "\n")
	if strings.TrimSpace(content) != "" {
		return content
	}

	// 如果内容为空，尝试从文件读取
	fullContent, err := os.ReadFile(n.Entity.URI)
	if err != nil {
		logs.CtxInfo(ctx, "failed to read file %s: %v", n.Entity.URI, err)
		return ""
	}

	lines := strings.Split(string(fullContent), "\n")
	startLine := n.Entity.GetStartLine()
	endLine := n.Entity.GetEndLine()
	if startLine < 1 || startLine > endLine {
		return ""
	}

	contentLines := lines[startLine-1 : endLine] // 1-based to 0-based
	content = strings.Join(contentLines, "\n")
	return content
}

// InteractionType is the type of interaction
type InteractionType int

const (
	InteractionTypeCursorMove InteractionType = iota
	InteractionTypeDocumentChange
)

type EdgeDirection int

const (
	From2To EdgeDirection = iota
	To2From
)

type InteractionGraph struct {
	mu    *sync.Mutex
	nodes map[string]*InteractionNode
}

func (g *InteractionGraph) MarshalJSON() ([]byte, error) {
	g.mu.Lock()
	defer g.mu.Unlock()

	type edge struct {
		FromID int              `json:"from_id"`
		ToID   int              `json:"to_id"`
		Edge   *InteractionEdge `json:"edge"`
	}

	type serializedGraph struct {
		Nodes []*InteractionNode `json:"nodes"`
		Edges []*edge            `json:"edges"`
	}

	graph := new(serializedGraph)

	nodes := make([]*InteractionNode, 0)
	nodesMap := make(map[string]int)
	for _, n := range g.nodes {
		nodesMap[n.ID] = len(nodes)
		nodes = append(nodes, n)
	}
	graph.Nodes = nodes

	edges := make([]*edge, 0)
	for _, n := range nodes {
		for toID, e := range n.OutEdges {
			edges = append(edges, &edge{
				FromID: nodesMap[n.ID],
				ToID:   nodesMap[toID],
				Edge:   e,
			})
		}
	}
	graph.Edges = edges
	return json.Marshal(graph)
}

func NewInteractionGraph() *InteractionGraph {
	return &InteractionGraph{
		nodes: make(map[string]*InteractionNode),
		mu:    &sync.Mutex{},
	}
}

func (g *InteractionGraph) printGraph() {
	if g == nil {
		fmt.Println("Graph: nil")
	}

	if g.nodes == nil {
		fmt.Println("Graph: empty (nodes map is nil)")
	}

	// 收集统计信息
	var totalOutEdges, totalInEdges int
	var totalOutEdgeCount, totalInEdgeCount int64
	nodeCount := len(g.nodes)

	for _, node := range g.nodes {
		totalOutEdges += len(node.OutEdges)
		totalInEdges += len(node.InEdges)

		for _, edge := range node.OutEdges {
			totalOutEdgeCount += edge.Count
		}
		for _, edge := range node.InEdges {
			totalInEdgeCount += edge.Count
		}
	}

	// 使用 strings.Builder 构建返回字符串
	var b strings.Builder
	b.WriteString(fmt.Sprintf("Graph Statistics:\n"))
	b.WriteString(fmt.Sprintf("  Nodes: %d\n", nodeCount))
	b.WriteString(fmt.Sprintf("  Edges:\n"))
	b.WriteString(fmt.Sprintf("    - Out edges: %d (total jumps: %d)\n", totalOutEdges, totalOutEdgeCount))
	b.WriteString(fmt.Sprintf("    - In edges: %d (total jumps: %d)\n", totalInEdges, totalInEdgeCount))

	fmt.Println(b.String())
}

func (g *InteractionGraph) HasNode(id string) bool {
	g.mu.Lock()
	defer g.mu.Unlock()

	return g.hasNode(id)
}

func (g *InteractionGraph) InsertNode(ctx context.Context, n *InteractionNode) *InteractionNode {
	g.mu.Lock()
	defer g.mu.Unlock()

	node, err := g.insertNode(n)
	if err != nil {
		logs.CtxError(ctx, "[InteractionGraph] InsertNode err %s", err)
	}
	return node
}

func (g *InteractionGraph) DeleteNode(id string) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	return g.deleteNode(id)
}

func (g *InteractionGraph) GetNode(id string) *InteractionNode {
	g.mu.Lock()
	defer g.mu.Unlock()

	if !g.hasNode(id) {
		return nil
	}
	return g.nodes[id]
}

func (g *InteractionGraph) GetConnectedNodes(n *InteractionNode) []*InteractionNode {
	g.mu.Lock()
	defer g.mu.Unlock()

	if n == nil {
		return nil
	}
	allNodes := make([]*InteractionNode, 0)
	for nid, _ := range n.InEdges {
		allNodes = append(allNodes, g.nodes[nid])
	}
	for nid, _ := range n.OutEdges {
		allNodes = append(allNodes, g.nodes[nid])
	}

	return allNodes
}

func (g *InteractionGraph) HandleCursorMove(ctx context.Context, currentNode *InteractionNode, recentNode *InteractionNode, timestamp int64) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	// g.printGraph()
	// check graph need to be cleaned or not
	g.cleanGraph(ctx, timestamp)

	// visit and insert nodes
	_, err := g.insertNode(currentNode)
	if err != nil {
		return err
	}
	currentNode.View(timestamp)

	// update edges
	if recentNode == nil {
		return nil
	} else if recentNode.ID == currentNode.ID {
		return nil
	}

	err = g.updateEdge(recentNode.ID, currentNode.ID, timestamp)
	if err != nil {
		return err
	}

	//g.printGraph()
	return nil
}

func (g *InteractionGraph) HandleDocumentChange(ctx context.Context, entity *model.Entity, recentNode *InteractionNode, timestamp int64) error {
	g.mu.Lock()
	defer g.mu.Unlock()
	// TODO: 处理 DocumentChange(Edit)
	// // 文档变更代表编辑操作
	// if entity == nil {
	// 	return nil
	// }

	// // 1. 获取或创建节点
	// node := g.getOrCreateNode(entity, timestamp)
	// node.EditCount++
	// node.LastEditTime = timestamp

	// // 3. 更新热点实体列表
	// g.updateHotEntities(node)

	return nil
}

func (g *InteractionGraph) hasNode(id string) bool {
	_, exists := g.nodes[id]
	return exists
}

func (g *InteractionGraph) insertNode(n *InteractionNode) (*InteractionNode, error) {
	if n == nil {
		return nil, errors.New("insert node failed, node to insert is not exists")
	}

	if g.hasNode(n.ID) {
		return g.nodes[n.ID], nil
	}

	g.nodes[n.ID] = n
	return n, nil
}

func (g *InteractionGraph) getOrCreateEdge(fromID string, toID string, edgeDirection EdgeDirection) (*InteractionEdge, error) {
	if edgeDirection == From2To {
		// Get Edges in fromNode
		fromNode, exists := g.nodes[fromID]
		if !exists {
			return nil, errors.New("fromID node not found")
		}
		fromNodeEdge, exists := fromNode.OutEdges[toID]
		if !exists {
			fromNodeEdge = &InteractionEdge{
				Count:       0,
				JumpHistory: make([]int64, 0, MaxJumpHistorySize),
			}
			fromNode.OutEdges[toID] = fromNodeEdge
		}
		return fromNodeEdge, nil
	} else if edgeDirection == To2From {
		// Get Edges in toNode
		toNode, exists := g.nodes[toID]
		if !exists {
			return nil, errors.New("toID node not found")
		}
		toNodeEdge, exists := toNode.InEdges[fromID]
		if !exists {
			toNodeEdge = &InteractionEdge{
				Count:       0,
				JumpHistory: make([]int64, 0, MaxJumpHistorySize),
			}
			toNode.InEdges[fromID] = toNodeEdge
		}
		return toNodeEdge, nil
	} else {
		return nil, errors.New("invalid edge direction")
	}
}

func (g *InteractionGraph) updateEdge(fromID string, toID string, timestamp int64) error {
	fromNodeEdge, err := g.getOrCreateEdge(fromID, toID, From2To)
	if err != nil {
		return err
	}
	fromNodeEdge.Count++
	if len(fromNodeEdge.JumpHistory) == MaxJumpHistorySize {
		copy(fromNodeEdge.JumpHistory, fromNodeEdge.JumpHistory[1:])
		fromNodeEdge.JumpHistory[MaxJumpHistorySize-1] = timestamp
	} else {
		fromNodeEdge.JumpHistory = append(fromNodeEdge.JumpHistory, timestamp)
	}

	toNodeEdge, err := g.getOrCreateEdge(fromID, toID, To2From)
	if err != nil {
		return err
	}
	toNodeEdge.Count++
	if len(toNodeEdge.JumpHistory) == MaxJumpHistorySize {
		copy(fromNodeEdge.JumpHistory, fromNodeEdge.JumpHistory[1:])
		toNodeEdge.JumpHistory[MaxJumpHistorySize-1] = timestamp
	} else {
		toNodeEdge.JumpHistory = append(toNodeEdge.JumpHistory, timestamp)
	}
	return nil
}

func (g *InteractionGraph) deleteNode(id string) error {
	_, exists := g.nodes[id]
	if !exists {
		return errors.New("node not found in graph")
	}
	// 先删除和当前节点有关系的节点上的边
	deleteNode := g.nodes[id]
	for toID := range deleteNode.OutEdges {
		delete(g.nodes[toID].InEdges, id)
	}
	for fromID := range deleteNode.InEdges {
		delete(g.nodes[fromID].OutEdges, id)
	}
	// 在图上删除当前节点（包括了当前节点的边）
	delete(g.nodes, id)

	return nil
}

func (g *InteractionGraph) cleanGraph(ctx context.Context, timestamp int64) {
	if g == nil || g.nodes == nil {
		return
	}

	// 节点数量较少的时候，不清理
	graphSize := len(g.nodes)
	if graphSize < GraphMaxNodeCount {
		return
	}
	logs.CtxInfo(ctx, "clean interaction graph")

	// 根据最近活动的时间确定要删除的节点： 1. 删除到给定的阈值 2. 检查剩余节点中是否有过期节点
	type nodeTimeInfo struct {
		id           string
		lastActivity int64
	}
	nodeInfos := make([]nodeTimeInfo, 0, graphSize)
	for id, n := range g.nodes {
		lastActivity := n.LastViewTime
		if n.LastEditTime > lastActivity {
			lastActivity = n.LastEditTime
		}
		nodeInfos = append(nodeInfos, nodeTimeInfo{
			id:           id,
			lastActivity: lastActivity,
		})
	}
	sort.Slice(nodeInfos, func(i, j int) bool {
		return nodeInfos[i].lastActivity < nodeInfos[j].lastActivity
	})

	nodesToDelete := make(map[string]bool)
	nodesToRemove := len(g.nodes) - GraphDeleteTargetSize
	for i := 0; i < nodesToRemove; i++ {
		nodesToDelete[nodeInfos[i].id] = true
	}
	expirationTimestamp := timestamp - NodeExpirationPeriod
	for i := nodesToRemove; i < len(nodeInfos); i++ {
		if nodeInfos[i].lastActivity < expirationTimestamp {
			nodesToDelete[nodeInfos[i].id] = true
		}
	}

	// 删除节点
	for id := range nodesToDelete {
		err := g.deleteNode(id)
		if err != nil {
			logs.CtxError(ctx, "[InteractionGraph] delete node id(%s) error", id, err)
			continue
		}
	}

	// 最后再遍历下，清理 isolate 边或者节点
	for nid, n := range g.nodes {
		// 清理可能存在的isolate边
		for toID, edge := range n.OutEdges {
			if len(edge.JumpHistory) == 0 || edge.Count == 0 {
				delete(n.OutEdges, toID)
			}
		}
		for fromID, edge := range n.InEdges {
			if len(edge.JumpHistory) == 0 || edge.Count == 0 {
				delete(n.InEdges, fromID)
			}
		}
		// 清理没有任何边的节点
		if len(n.InEdges) == 0 && len(n.OutEdges) == 0 {
			delete(g.nodes, nid)
		}
	}
}

type NodeRankOptions struct {
	topK             int
	Now              int64
	OriginalQuery    string
	Embedding        *model.Embedding
	Keywords         []string
	EmbeddingStorage data_storage.EmbeddingStorage
	GitGraph         *git_graph.GitGraph
}

func (g *InteractionGraph) RankNodes(ctx context.Context, curNode *InteractionNode, nodes []*InteractionNode, opts NodeRankOptions) ([]*InteractionNode, error) {
	g.mu.Lock()
	defer g.mu.Unlock()

	jumpScores := make(map[string]float64, len(nodes))
	ckgScores := make(map[string]float64, len(nodes))
	timeScores := make(map[string]float64, len(nodes))
	visitScores := make(map[string]float64, len(nodes))
	embeddingScores := make(map[string]float64, len(nodes))
	gitScores := make(map[string]float64, len(nodes))

	errChan := make(chan error, 6)
	wg := sync.WaitGroup{}
	wg.Add(6)

	util.SafeGo(nil, func() {
		defer wg.Done()
		for _, n := range nodes {
			jumpScores[n.ID] = calculateJumpScore(curNode, n)
		}
		jumpScores = minMaxNormalizeMap(jumpScores)
	})

	util.SafeGo(nil, func() {
		defer wg.Done()
		for _, n := range nodes {
			ckgScores[n.ID] = calculateCKGScore(curNode, n)
		}
		ckgScores = minMaxNormalizeMap(ckgScores)
	})

	util.SafeGo(nil, func() {
		defer wg.Done()
		for _, n := range nodes {
			timeScores[n.ID] = calculateTimeScore(n, opts.Now)
		}
	})

	util.SafeGo(nil, func() {
		defer wg.Done()
		for _, n := range nodes {
			visitScores[n.ID] = calculateVisitScore(n)
		}
		visitScores = minMaxNormalizeMap(visitScores)
	})

	util.SafeGo(ctx, func() {
		defer wg.Done()
		// Embedding scores 在 0-1 之间
		scores, err := calculateEmbeddingScore(ctx, nodes, opts)
		if err != nil {
			errChan <- err
			return
		}
		for k, v := range scores {
			embeddingScores[k] = v
		}
	})

	util.SafeGo(ctx, func() {
		defer wg.Done()
		// 计算 Git 得分
		scores, err := calculateGitScore(ctx, nodes, opts)
		if err != nil {
			errChan <- err
			return
		}
		for k, v := range scores {
			gitScores[k] = v
		}
		gitScores = minMaxNormalizeMap(gitScores)
	})

	wg.Wait()
	close(errChan)
	for err := range errChan {
		if err != nil {
			logs.CtxInfo(ctx, "[rankNodes] calculate scores err %v", err)
		}
	}

	// 计算最终得分并排序
	type nodeScore struct {
		node  *InteractionNode
		score float64
	}

	rankedNodes := make([]nodeScore, 0, len(nodes))
	for _, node := range nodes {
		finalScore := jumpScores[node.ID]*JumpScoreWeight +
			ckgScores[node.ID]*CkgScoreWeight +
			timeScores[node.ID]*TimeScoreWeight +
			visitScores[node.ID]*VisitScoreWeight +
			embeddingScores[node.ID]*EmbeddingScoreWeight +
			gitScores[node.ID]*GitScoreWeight
		rankedNodes = append(rankedNodes, nodeScore{node: node, score: finalScore})
	}

	sort.Slice(rankedNodes, func(i, j int) bool {
		return rankedNodes[i].score > rankedNodes[j].score
	})

	result := make([]*InteractionNode, len(rankedNodes))
	for i, ns := range rankedNodes {
		result[i] = ns.node
	}

	return result, nil
}

func calculateJumpScore(curNode, node *InteractionNode) float64 {
	if curNode == nil || node == nil {
		return 0.0
	}

	// 检查直接跳转关系
	if edge, exists := curNode.OutEdges[node.ID]; exists {
		return math.Log1p(float64(edge.Count))
	}
	if edge, exists := curNode.InEdges[node.ID]; exists {
		return math.Log1p(float64(edge.Count))
	}

	return 0.0
}

func calculateCKGScore(curNode, node *InteractionNode) float64 {
	if curNode == nil || node == nil {
		return 0.0
	}
	// TODO: 从 CKG 获取两个节点之间的关系强度
	return 0.0
}

func calculateTimeScore(node *InteractionNode, now int64) float64 {
	if node == nil {
		return 0.0
	}
	lastTime := node.LastViewTime
	if node.LastEditTime > lastTime {
		lastTime = node.LastEditTime
	}
	timeDiff := float64(now - lastTime)
	timeDiffHour := timeDiff / float64(60*60)
	// e^(1min * -1 / 60)=0.98, e^(5min * -1 / 60)=0.92
	// e^(20min * -1 / 60)=0.72, e^(60min * -1 / 60)=0.36
	return math.Exp(-1 * timeDiffHour)
}

func calculateVisitScore(node *InteractionNode) float64 {
	if node == nil {
		return 0.0
	}
	return math.Log1p(float64(node.ViewCount + node.EditCount))
}

func cosineSimilarity(a, b []float32) float64 {
	if len(a) != len(b) {
		return 0.0
	}

	var dotProduct, normA, normB float32
	for i := 0; i < len(a); i++ {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}
	// 使用float32版本的Sqrt
	return float64(dotProduct / (float32(math.Sqrt(float64(normA))) * float32(math.Sqrt(float64(normB)))))
}

func calculateEmbeddingScore(ctx context.Context, nodes []*InteractionNode, opts NodeRankOptions) (map[string]float64, error) {
	if len(nodes) == 0 || opts.Embedding == nil || len(opts.Embedding.Embedding) == 0 {
		return nil, errors.New("calculateEmbeddingScore failed, no nodes or embedding")
	}

	embeddingScores := make(map[string]float64, len(nodes))
	ckgNodeIDs := make([]string, 0)
	for _, node := range nodes {
		if node.IsCKG {
			ckgNodeIDs = append(ckgNodeIDs, node.ID)
		}
		embeddingScores[node.ID] = 0.0
	}

	// 先计算 CKG 实体的 Embedding Score
	if len(ckgNodeIDs) > 0 && opts.EmbeddingStorage != nil {
		option := data_storage.NewEmbeddingQueryOptions().
			SetWhereMetadata(map[string]any{
				string(model.EMK_EntityID): ckgNodeIDs,
			}).Build()
		results, err := opts.EmbeddingStorage.QueryByEmbedding(ctx, opts.Embedding, len(ckgNodeIDs), option)
		if err != nil {
			logs.CtxError(ctx, "[calculateEmbeddingScore] query ckg embedding failed, err: %v", err)
		} else {
			for _, result := range results {
				embeddingScores[result.ID] = float64(result.Score)
			}
		}
	}

	// 再计算非 CKG 实体的 Embedding
	for _, node := range nodes {
		if node.IsCKG {
			continue
		}
		embeddingScores[node.ID] = cosineSimilarity(node.Embedding.Embedding, opts.Embedding.Embedding)
	}
	return embeddingScores, nil
}

func calculateGitScore(ctx context.Context, nodes []*InteractionNode, opts NodeRankOptions) (map[string]float64, error) {
	if opts.GitGraph == nil {
		return nil, nil
	}

	// 获取 Node 对应文件的权重
	scores := make(map[string]float64, len(nodes))
	for _, node := range nodes {
		nodeFilePath := node.Entity.URI
		if nodeFilePath == "" {
			continue
		}
		gitNode := opts.GitGraph.GetNode(nodeFilePath)
		if gitNode == nil {
			scores[node.ID] = 0.0
		} else {
			scores[node.ID] = gitNode.Importance
		}
	}

	return scores, nil
}

func minMaxNormalizeMap(scores map[string]float64) map[string]float64 {
	if len(scores) <= 1 {
		normalized := make(map[string]float64, len(scores))
		for k := range scores {
			normalized[k] = 1.0
		}
		return normalized
	}

	var minv, maxv float64
	first := true
	for _, v := range scores {
		if first {
			minv, maxv = v, v
			first = false
			continue
		}
		minv, maxv = min(minv, v), max(maxv, v)
	}

	normalized := make(map[string]float64, len(scores))
	for k, v := range scores {
		if maxv == minv {
			normalized[k] = 1.0
		} else {
			normalized[k] = (v - minv) / (maxv - minv)
		}
	}
	return normalized
}
