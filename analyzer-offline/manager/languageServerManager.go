package manager

import (
	"context"
	"fmt"
	"net"
	"slices"
	"time"

	"byted-ckg/analyzer-offline/languageServer"

	"go.lsp.dev/jsonrpc2"
	"go.lsp.dev/protocol"
)

var supportedLanguages = []string{"go"}

type LanguageServerManager struct {
	servers     map[string]languageServer.LanguageServer
	connections map[string]jsonrpc2.Conn

	ctx context.Context
}

var languageServerManagerInstance *LanguageServerManager

func init() {
	languageServerManagerInstance = &LanguageServerManager{
		servers:     make(map[string]languageServer.LanguageServer),
		connections: make(map[string]jsonrpc2.Conn),
		ctx:         context.Background(),
	}

	for _, language := range supportedLanguages {
		switch language {
		case "go":
			languageServerManagerInstance.servers[language] = languageServer.NewGoLanguageServer()
		}
	}
}

func GetLanguageServerManager() *LanguageServerManager {
	return languageServerManagerInstance
}

func (p *LanguageServerManager) IsSupportedLanguage(language string) bool {
	return slices.Contains([]string{"go"}, language)
}

func (p *LanguageServerManager) IsStarted(language string) bool {
	return p.servers[language].IsStarted()
}

func (p *LanguageServerManager) Shutdown(language string) error {
	return p.servers[language].Shutdown()
}

func (p *LanguageServerManager) Start(language string) error {
	if err := p.servers[language].Start(); err != nil {
		return err
	}

	for i := 0; i < 10; i++ {
		if p.servers[language].IsStarted() {
			return nil
		}
		time.Sleep(time.Second)
	}

	return fmt.Errorf("failed to start '%s' language server", language)
}

func (p *LanguageServerManager) Initialize(language string, repo string) error {
	conn, err := p.getConnection(language)
	if err != nil {
		return err
	}

	initializeParams := &protocol.InitializeParams{
		WorkspaceFolders: []protocol.WorkspaceFolder{
			{
				URI:  fmt.Sprintf("file://%s", repo),
				Name: "repo",
			},
		},
		Capabilities: protocol.ClientCapabilities{
			TextDocument: &protocol.TextDocumentClientCapabilities{
				Definition: &protocol.DefinitionTextDocumentClientCapabilities{
					DynamicRegistration: true,
				},
			},
		},
	}

	if _, err = conn.Call(p.ctx, protocol.MethodInitialize, initializeParams, nil); err != nil {
		return fmt.Errorf("failed to send initialize request: %s", err.Error())
	}

	if err = conn.Notify(p.ctx, protocol.MethodInitialized, &protocol.InitializedParams{}); err != nil {
		return fmt.Errorf("failed to send initialized notification: %s", err.Error())
	}

	return nil
}

func (p *LanguageServerManager) ExecuteDefinition(language string, uri protocol.DocumentURI, position protocol.Position) ([]protocol.Location, error) {
	conn, err := p.getConnection(language)
	if err != nil {
		return []protocol.Location{}, err
	}

	definitionParams := &protocol.DefinitionParams{
		TextDocumentPositionParams: protocol.TextDocumentPositionParams{
			TextDocument: protocol.TextDocumentIdentifier{
				URI: uri,
			},
			Position: position,
		},
	}

	var result []protocol.Location
	if _, err = conn.Call(p.ctx, protocol.MethodTextDocumentDefinition, definitionParams, &result); err != nil {
		return []protocol.Location{}, fmt.Errorf("failed to send textDocument/definition request: %s", err.Error())
	}

	return result, nil
}

func (p *LanguageServerManager) ExecuteReference(language string, uri protocol.DocumentURI, position protocol.Position) ([]protocol.Location, error) {
	conn, err := p.getConnection(language)
	if err != nil {
		return []protocol.Location{}, err
	}

	referenceParams := &protocol.ReferenceParams{
		TextDocumentPositionParams: protocol.TextDocumentPositionParams{
			TextDocument: protocol.TextDocumentIdentifier{
				URI: uri,
			},
			Position: position,
		},
	}

	var result []protocol.Location
	if _, err = conn.Call(p.ctx, protocol.MethodTextDocumentReferences, referenceParams, &result); err != nil {
		return []protocol.Location{}, fmt.Errorf("failed to send textDocument/references request: %s", err.Error())
	}

	return result, nil
}

func (p *LanguageServerManager) getConnection(language string) (jsonrpc2.Conn, error) {
	if p.connections[language] == nil {
		conn, err := net.Dial("tcp", fmt.Sprintf(":%d", p.servers[language].GetServerPort()))
		if err != nil {
			return nil, fmt.Errorf("failed to connect to language server: %s", err.Error())
		}

		stream := jsonrpc2.NewStream(conn)
		client := jsonrpc2.NewConn(stream)

		clientHandler := func(ctx context.Context, reply jsonrpc2.Replier, req jsonrpc2.Request) error {
			return nil
		}
		client.Go(p.ctx, clientHandler)

		p.connections[language] = client
	}

	return p.connections[language], nil
}
