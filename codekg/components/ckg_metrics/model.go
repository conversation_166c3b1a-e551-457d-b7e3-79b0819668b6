package ckg_metrics

type ReportDataRequest struct {
	Event   string `json:"event"`
	Time    int    `json:"time"`
	Payload string `json:"payload"`
}

type ReportDataResponse struct {
	Message string `json:"message"`
	Code    int    `json:"code"`
}

type FormData struct {
	EnvMetadata Metadata            `json:"env_metadata"`
	Events      []ReportDataRequest `json:"events"`
}

type Metadata struct {
	Channel          string `json:"channel"`
	IdeVersion       string `json:"ide_version"`
	ExtensionVersion string `json:"extension_version"`
	VersionCode      int64  `json:"version_code"`
	Region           string `json:"region"`
	AppID            string `json:"app_id"`
	UserID           string `json:"user_id"`
}
