{"root": true, "extends": ["@artus/eslint-config-artus/typescript", "plugin:import/recommended", "plugin:import/typescript"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "plugins": ["@typescript-eslint", "import"], "settings": {"import/resolver": {"typescript": {}}}, "rules": {"no-unused-vars": "off", "@typescript-eslint/ban-types": "error", "@typescript-eslint/indent": ["error", 2], "@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-unused-vars": ["error", {"varsIgnorePattern": "^_", "argsIgnorePattern": "^_"}], "@typescript-eslint/naming-convention": ["off", {"selector": "default", "format": ["camelCase"]}, {"selector": "variable", "format": ["camelCase", "UPPER_CASE"]}, {"selector": "parameter", "format": ["camelCase"], "leadingUnderscore": "allow"}, {"selector": "classProperty", "modifiers": ["private"], "format": ["camelCase"], "leadingUnderscore": "require"}, {"selector": "enum", "format": []}, {"selector": "enumMember", "format": []}, {"selector": "interface", "format": []}, {"selector": "classMethod", "modifiers": ["private"], "format": ["camelCase"], "leadingUnderscore": "allow"}, {"selector": "typeLike", "format": ["PascalCase"]}, {"selector": "function", "format": ["camelCase"], "leadingUnderscore": "allow"}], "@typescript-eslint/semi": "warn", "curly": "warn", "eqeqeq": "warn", "no-throw-literal": "warn", "semi": "off"}, "ignorePatterns": ["**/*.d.ts", "**/*.test.ts", "**/*.js"]}