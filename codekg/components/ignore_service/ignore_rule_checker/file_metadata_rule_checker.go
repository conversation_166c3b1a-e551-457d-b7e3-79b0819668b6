package ignore_rule_checker

import (
	"context"
	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/logs"
	"os"
	"path/filepath"
)

const (
	FileMetadataRule     = "FileMetadataRuleChecker"
	FileMetadataRuleRank = 40
)

type FileMetadataRuleChecker struct {
	is IgnoreServiceV2
}

func NewFileMetadataRuleChecker() *FileMetadataRuleChecker {
	return &FileMetadataRuleChecker{}
}

func (r *FileMetadataRuleChecker) Name() CheckerName                   { return FileMetadataRule }
func (r *FileMetadataRuleChecker) Rank() int                           { return FileMetadataRuleRank }
func (r *FileMetadataRuleChecker) Kind() CheckerKind                   { return File }
func (r *FileMetadataRuleChecker) SetIgnoreService(is IgnoreServiceV2) { r.is = is }
func (r *FileMetadataRuleChecker) AddRules(_ context.Context, _, _ string, _ []IgnoreRule) error {
	return nil
}
func (r *FileMetadataRuleChecker) UpdateRulesFromTCC(_ context.Context, _, _ string) error {
	return nil
}

// IsIgnore for FileMetadataRuleChecker
// 内存：不需要读取文件内容。为文件时，根据文件大小过滤。
// 动态下发：TCC 配置在 GetTCCConfig 时更新，并返回 FileSizeThreshold 的最新值
func (r *FileMetadataRuleChecker) IsIgnore(ctx context.Context, userId, _ string, path string, info os.FileInfo) (ignored bool, skip error, err error) {
	if info.IsDir() {
		return info.Mode() == os.ModeSymlink, filepath.SkipDir, nil
	}
	// 从 tcc 读取，兜底 1MB
	var fileSizeThreshold int64 = ckg_config.DefaultFileSizeThreshold
	if r.is != nil && r.is.GetConfig() != nil && r.is.GetClient() != nil {
		config := r.is.GetConfig().GetTCCConfig(ctx, r.is.GetClient(), userId)
		fileSizeThreshold = config.FileSizeThreshold
	}
	if fileSizeThreshold != 0 && info.Size() > fileSizeThreshold {
		logs.CtxTrace(ctx, "file %s is too large, ignored for index", path)
		return true, nil, nil
	}
	return info.Mode() == os.ModeSymlink, nil, nil
}
