import {CommonFunctionalityExecutor} from "./common";
import {DefNodeInfo, DefNodeKindEnum, LanguageIdEnum, ParserEnum, VarDeclNodeInfo} from "../../share";
import {DocumentUri, Location} from "vscode-languageserver-types";
import {SyntaxNode} from "web-tree-sitter";
import {TreeSitterDispatcher} from "../../treesitter/dispatcher";
import {TreeSitterFunctionalityEnum} from "../../treesitter/share";
import {TreeSitterTypeCategoryEnum} from "../../treesitter/language/common";

export class TypescriptFunctionalityExecutor extends CommonFunctionalityExecutor {
  static TYPESCRIPT_INSTANCE = new TypescriptFunctionalityExecutor(LanguageIdEnum.Typescript);
  static TSX_INSTANCE = new TypescriptFunctionalityExecutor(LanguageIdEnum.Tsx);
  static TYPESCRIPT_REACT_INSTANCE = new TypescriptFunctionalityExecutor(LanguageIdEnum.TypescriptReact);

  languageId: LanguageIdEnum;

  private constructor(languageId: LanguageIdEnum) {
    super();
    this.languageId = languageId;
  }

  async getInvokeDefinition(parses: ParserEnum[], uri: DocumentUri, invokeNameNode: SyntaxNode, resultFilter: (location: Location) => boolean): Promise<DefNodeInfo[]> {
    const result: DefNodeInfo[] = [];

    for (const node of await this.getDefinitionNode(parses, uri, invokeNameNode, resultFilter)) {
      let justifiedNode = node.node;
      if (justifiedNode.type === "export_statement") {
        justifiedNode = justifiedNode.namedChildren[0];
      }

      if (justifiedNode) {
        const realDefNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(
          this.languageId,
          TreeSitterFunctionalityEnum.GetParentNodeWithTypeListForNode,
          justifiedNode,
          ["function_declaration", "method_definition", "generator_function_declaration", "variable_declarator", "required_parameter", "public_field_definition", "pair"],
        );

        if (realDefNode) {
          let typeKind = DefNodeKindEnum.Unknown;
          switch (realDefNode.type) {
            case "function_declaration":
            case "method_definition":
            case "generator_function_declaration":
              typeKind = DefNodeKindEnum.Function;
              break;
            case "variable_declarator":
            case "required_parameter":
            case "public_field_definition":
            case "pair":
              typeKind = DefNodeKindEnum.VarDecl;
              break;
          }
          result.push({
            languageId: this.languageId,
            uri: node.uri,
            kind: typeKind,
            name: invokeNameNode.text,
            defNode: realDefNode,
            subDefNodeInfo: null,
          });
        }
      }
    }

    return result;
  }

  async getTypeDefinition(parses: ParserEnum[], uri: DocumentUri, typeNameNode: SyntaxNode, preFilter: (node: SyntaxNode) => boolean, resultFilter: (location: Location) => boolean): Promise<DefNodeInfo[]> {
    if (preFilter && !preFilter(typeNameNode)) {
      return [];
    }

    const result: DefNodeInfo[] = [];

    for (const node of await this.getDefinitionNode(parses, uri, typeNameNode, resultFilter)) {
      let justifiedNode = node.node;
      if (justifiedNode.type === "export_statement") {
        justifiedNode = justifiedNode.namedChildren[0];
      }
      if (justifiedNode) {
        const typeNodeTypes = TreeSitterDispatcher.INSTANCE.dispatch(this.languageId, TreeSitterFunctionalityEnum.GetTreeSitterNodeTypes, TreeSitterTypeCategoryEnum.TYPE);

        const realDefNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(
          this.languageId,
          TreeSitterFunctionalityEnum.GetParentNodeWithTypeListForNode,
          justifiedNode,
          [...typeNodeTypes, "method_definition"],
        );

        if (realDefNode) {
          result.push({
            languageId: this.languageId,
            uri: node.uri,
            kind: this.getTypeKind(realDefNode),
            name: typeNameNode.text,
            defNode: realDefNode,
            subDefNodeInfo: null,
          });
        }
      }
    }

    return result;
  }

  async getVarDeclaration(parses: ParserEnum[], uri: DocumentUri, varNameNode: SyntaxNode, resultFilter: (location: Location) => boolean): Promise<VarDeclNodeInfo[]> {
    const nodes = await this.getDefinitionNode(parses, uri, varNameNode, resultFilter);

    return nodes.map(node => {
      let justifiedNode = node.node;
      if (justifiedNode.type === "export_statement") {
        justifiedNode = justifiedNode.namedChildren[0];
      }

      if (justifiedNode) {
        const typeNodeTypes = TreeSitterDispatcher.INSTANCE.dispatch(this.languageId, TreeSitterFunctionalityEnum.GetTreeSitterNodeTypes, TreeSitterTypeCategoryEnum.TYPE);
        const varNodeTypes = TreeSitterDispatcher.INSTANCE.dispatch(this.languageId, TreeSitterFunctionalityEnum.GetTreeSitterNodeTypes, TreeSitterTypeCategoryEnum.VARIABLE);

        const typeList = [...varNodeTypes];
        if (varNameNode.parent && varNameNode.parent.type === "member_expression") {
          typeList.push(...typeNodeTypes);
        }
        if (varNameNode.parent && varNameNode.parent.type === "new_expression") {
          typeList.push(...typeNodeTypes);
          typeList.push("method_definition");
        }
        const realDeclNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(
          this.languageId,
          TreeSitterFunctionalityEnum.GetParentNodeWithTypeListForNode,
          justifiedNode,
          typeList,
        );

        if (realDeclNode) {
          return {
            languageId: this.languageId,
            uri: node.uri,
            kind: this.getTypeKind(realDeclNode),
            name: varNameNode.text,
            declNode: realDeclNode,
          };
        } else {
          return null;
        }
      } else {
        return null;
      }
    }).filter(item => item !== null);
  }

  private getTypeKind(node: SyntaxNode): DefNodeKindEnum {
    switch (node.type) {
      case "interface_declaration":
        return DefNodeKindEnum.Interface;
      case "class_declaration":
        return DefNodeKindEnum.Class;
      case "enum_declaration":
        return DefNodeKindEnum.Enum;
      case "type_alias_declaration":
        return DefNodeKindEnum.Alias;
      case "internal_module":
        return DefNodeKindEnum.Namespace;
      case "method_definition":
        return DefNodeKindEnum.Function;
      case "variable_declarator":
      case "required_parameter":
      case "public_field_definition":
        return DefNodeKindEnum.VarDecl;
    }

    return DefNodeKindEnum.Unknown;
  }
}