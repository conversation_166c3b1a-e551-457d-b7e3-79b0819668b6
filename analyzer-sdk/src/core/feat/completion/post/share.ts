import type {Point, Edit} from 'web-tree-sitter';
import type {TextDocument, Position} from 'vscode-languageserver-textdocument';

export interface DocState {
  position: Position
  positionIndexInDoc: number
  positionLine: string
  positionLinePrefix: string
  positionLineSuffix: string
}

export function genDocState(document: TextDocument, position: Position): DocState {
  const lineText = document.getText({
    start: {line: position.line, character: 0},
    end: {line: position.line + 1, character: 0},
  });
  const positionIndexInDoc = document.offsetAt(position);
  const positionLinePrefix = lineText.slice(0, position.character);
  const positionLineSuffix = lineText.slice(position.character).trimEnd();
  return {position, positionIndexInDoc, positionLine: lineText, positionLinePrefix, positionLineSuffix};
}

export function needCheckBracketLanguage(languageId: string): boolean {
  const noBracketLanguages = [
    "python",
    'ruby',
  ];
  return !noBracketLanguages.includes(languageId);
}

interface ModifyTextResult {
  textAfterModification: string
  edit: Edit
}

export function spliceCompletionText(docText: string, completionText: string, docState: DocState, suffixLengthToRemove: number): ModifyTextResult {
  const startIndex = docState.positionIndexInDoc;
  const startPosition = getExtentPoint(docText.slice(0, startIndex));

  const oldEndIndex = startIndex + suffixLengthToRemove;
  const oldEndPosition = getExtentPoint(docText.slice(0, oldEndIndex));

  const textWithCompletion = docText.slice(0, startIndex) + completionText + docText.slice(oldEndIndex);

  const newEndIndex = startIndex + completionText.length;
  const newEndPosition = getExtentPoint(textWithCompletion.slice(0, newEndIndex));

  return {
    textAfterModification: textWithCompletion,
    edit: {
      startIndex,
      startPosition,
      oldEndIndex,
      oldEndPosition,
      newEndIndex,
      newEndPosition,
    },
  };
}

export function getMatchingSuffixLength(completionText: string, docState: DocState): number {
  let length = 0;
  for (let i = 0; i < completionText.length; i++) {
    if (completionText[i] === docState.positionLineSuffix[length]) {
      length++;
    }
  }

  return length;
}

export function getExtentPoint(text: string): Point {
  let row = 0;
  let index = -1;
  let findLastLineBreak = false;
  while (!findLastLineBreak) {
    index++;

    const newIndex = text.indexOf('\n', index);
    if (newIndex === -1) {
      findLastLineBreak = true;
    } else {
      index = newIndex;
      row++;
    }
  }

  return {row, column: text.length - index};
}