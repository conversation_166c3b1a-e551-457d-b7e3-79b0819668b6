name: Analyzer publish
trigger:
  change:
    types: [create, push]
    paths:
      - "analyzer-sdk/**"
jobs:
  build:
    image: hub.byted.org/compile/ci.node_v18.go1_20:e2183ab894d15ce0b90aa2d21a74a608
    name: Build
    steps:
      - name: install
        commands:
          - npm i -g pnpm --registry=https://bnpm.byted.org
          - pnpm i --registry=https://bnpm.byted.org
      - name: build
        commands:
          - cd analyzer-sdk
          - pnpm run build

  alpha:
    name: Publish alpha version
    image: hub.byted.org/compile/ci.node_v18.go1_20:306641eba484adfe4b8bab6e8d2afd1d
    manual: true
    depends:
      - build
    envs:
      npm_config_grpc_node_binary_host_mirror: https://bnpm.bytedance.net/mirrors
      npm_config_grpc_tools_binary_host_mirror: https://bnpm.bytedance.net/mirrors
      # 从项目 gitlab 主页获取
      CI_PROJECT_ID: 32221
      CI_COMMIT_SHA: ${{Head.SHA}}
    steps:
      - name: install
        commands:
          - npm i -g pnpm --registry=https://bnpm.byted.org
          - pnpm i --registry=https://bnpm.byted.org
      - name: build
        commands:
          - cd analyzer-sdk
          - pnpm run build
      - name: publish alpha
        commands:
          - ls -al analyzer-sdk/dist
          - npm config set "registry=https://bnpm.byted.org"
          - echo "//bnpm.byted.org/:_authToken=${{ANALYZER_NPM_TOKEN}}" >> ~/.npmrc
          - npm whoami
          - npx @bytedance-dev/mrv pnpm --tag=alpha
          - pnpm run publish:analyzer-sdk:alpha
