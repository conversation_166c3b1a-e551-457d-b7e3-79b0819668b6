package langchain

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMarkdownHeaderTextSplitter_SplitText(t *testing.T) {
	t.<PERSON>()

	type testCase struct {
		markdown     string
		expectedDocs []TestDocument
	}

	testCases := []testCase{
		{
			markdown: `
## First header: h2
Some content below the first h2.
## Second header: h2
### Third header: h3

- This is a list item of bullet type.
- This is another list item.

 *Everything* is going according to **plan**.

# Fourth header: h1
Some content below the first h1.
## Fifth header: h2
#### Sixth header: h4

Some content below h1>h2>h4.
`,
			expectedDocs: []TestDocument{
				{
					PageContent: `## First header: h2
Some content below the first h2.`,
					Metadata: map[string]interface{}{},
				},
				{
					PageContent: `## Second header: h2`,
					Metadata:    map[string]interface{}{},
				},
				{
					PageContent: `## Second header: h2
### Third header: h3
- This is a list item of bullet type.`,
					Metadata: map[string]interface{}{},
				},
				{
					PageContent: `## Second header: h2
### Third header: h3
- This is another list item.`,
					Metadata: map[string]interface{}{},
				},
				{
					PageContent: `## Second header: h2
### Third header: h3
*Everything* is going according to **plan**.`,
					Metadata: map[string]interface{}{},
				},
				{
					PageContent: `# Fourth header: h1
Some content below the first h1.`,
					Metadata: map[string]interface{}{},
				},
				{
					PageContent: `# Fourth header: h1
## Fifth header: h2`,
					Metadata: map[string]interface{}{},
				},
				{
					PageContent: `# Fourth header: h1
## Fifth header: h2
#### Sixth header: h4
Some content below h1>h2>h4.`,
					Metadata: map[string]interface{}{},
				},
			},
		},
	}

	splitter := NewMarkdownTextSplitter(WithChunkSize(64), WithChunkOverlap(32), WithHeadingHierarchy(true))
	for _, tc := range testCases {
		// 创建测试文档
		chunks, err := splitter.SplitText(tc.markdown)
		require.NoError(t, err)

		docs := make([]TestDocument, 0, len(chunks))
		for _, chunk := range chunks {
			docs = append(docs, TestDocument{
				PageContent: chunk.Content,
				Metadata:    map[string]interface{}{},
			})
		}

		assert.Equal(t, tc.expectedDocs, docs)
	}
}

// TestMarkdownHeaderTextSplitter_Table markdown always split by line.
//
//nolint:funlen
func TestMarkdownHeaderTextSplitter_Table(t *testing.T) {
	t.Parallel()

	type testCase struct {
		name         string
		markdown     string
		options      []Option
		expectedDocs []TestDocument
	}

	testCases := []testCase{
		{
			name: "size(64)-overlap(32)",
			options: []Option{
				WithChunkSize(64),
				WithChunkOverlap(32),
			},
			markdown: `| Syntax      | Description |
| ----------- | ----------- |
| Header      | Title       |
| Paragraph   | Text        |`,
			expectedDocs: []TestDocument{
				{
					PageContent: `| Syntax | Description |
| --- | --- |
| Header | Title |`,
					Metadata: map[string]interface{}{},
				},
				{
					PageContent: `| Syntax | Description |
| --- | --- |
| Paragraph | Text |`,
					Metadata: map[string]interface{}{},
				},
			},
		},
		{
			name: "size(512)-overlap(64)",
			options: []Option{
				WithChunkSize(512),
				WithChunkOverlap(64),
			},
			markdown: `| Syntax      | Description |
| ----------- | ----------- |
| Header      | Title       |
| Paragraph   | Text        |`,
			expectedDocs: []TestDocument{
				{
					PageContent: `| Syntax | Description |
| --- | --- |
| Header | Title |`,
					Metadata: map[string]interface{}{},
				},
				{
					PageContent: `| Syntax | Description |
| --- | --- |
| Paragraph | Text |`,
					Metadata: map[string]interface{}{},
				},
			},
		},
		{
			name: "big-tables-overflow",
			options: []Option{
				WithChunkSize(64),
				WithChunkOverlap(32),
				WithJoinTableRows(true),
			},
			markdown: `| Syntax      | Description |
| ----------- | ----------- |
| Header      | Title       |
| Paragraph   | Text        |`,
			expectedDocs: []TestDocument{
				{
					PageContent: `| Syntax | Description |
| --- | --- |
| Header | Title |`,
					Metadata: map[string]interface{}{},
				},
				{
					PageContent: `| Syntax | Description |
| --- | --- |
| Paragraph | Text |`,
					Metadata: map[string]interface{}{},
				},
			},
		},
		{
			name: "big-tables",
			options: []Option{
				WithChunkSize(128),
				WithChunkOverlap(32),
				WithJoinTableRows(true),
			},
			markdown: `| Syntax      | Description |
| ----------- | ----------- |
| Header      | Title       |
| Paragraph   | Text        |`,
			expectedDocs: []TestDocument{
				{
					PageContent: `| Syntax | Description |
| --- | --- |
| Header | Title |
| Paragraph | Text |`,
					Metadata: map[string]interface{}{},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			rq := require.New(t)

			splitter := NewMarkdownTextSplitter(tc.options...)

			// 创建测试文档
			chunks, err := splitter.SplitText(tc.markdown)
			rq.NoError(err)

			docs := make([]TestDocument, 0, len(chunks))
			for _, chunk := range chunks {
				docs = append(docs, TestDocument{
					PageContent: chunk.Content,
					Metadata:    map[string]interface{}{},
				})
			}

			rq.Equal(tc.expectedDocs, docs)
		})
	}
}

func TestMarkdownHeaderTextSplitter(t *testing.T) {
	t.Parallel()

	data, err := os.ReadFile("./testdata/example.md")
	if err != nil {
		t.Fatal(err)
	}

	splitter := NewMarkdownTextSplitter(WithChunkSize(512), WithChunkOverlap(64))
	chunks, err := splitter.SplitText(string(data))
	if err != nil {
		t.Fatal(err)
	}

	var pages string
	for _, chunk := range chunks {
		pages += chunk.Content + "\n\n---\n\n"
	}

	err = os.WriteFile("./testdata/example_markdown_header_512.md", []byte(pages), os.ModeExclusive|os.ModePerm)
	if err != nil {
		t.Fatal(err)
	}
}

func TestMarkdownHeaderTextSplitter_TOC(t *testing.T) {
	t.Parallel()

	type testCase struct {
		markdown     string
		expectedDocs []TestDocument
	}
	testCases := []testCase{
		{
			markdown: `- [Code of Conduct](#code-of-conduct)
- [I Have a Question](#i-have-a-question)
- [I Want To Contribute](#i-want-to-contribute)
  - [Reporting Bugs](#reporting-bugs)
    - [Before Submitting a Bug Report](#before-submitting-a-bug-report)
    - [How Do I Submit a Good Bug Report?](#how-do-i-submit-a-good-bug-report)
  - [Suggesting Enhancements](#suggesting-enhancements)
    - [Before Submitting an Enhancement](#before-submitting-an-enhancement)
    - [How Do I Submit a Good Enhancement Suggestion?](#how-do-i-submit-a-good-enhancement-suggestion)
  - [Your First Code Contribution](#your-first-code-contribution)
  - [Improving The Documentation](#improving-the-documentation)
- [Styleguides](#styleguides)
  - [Commit Messages](#commit-messages)
- [Join The Project Team](#join-the-project-team)
- [Attribution](#attribution)
- [Appendix](#appendix)
  - [Pull Request](#pull-request)
  - [Your PR is merged!](#your-pr-is-merged)
`,
			expectedDocs: []TestDocument{
				{
					PageContent: `- [Code of Conduct](#code-of-conduct)
- [I Have a Question](#i-have-a-question)`,
					Metadata: map[string]interface{}{},
				},
				{
					PageContent: `- [I Want To Contribute](#i-want-to-contribute)
  - [Reporting Bugs](#reporting-bugs)
    - [Before Submitting a Bug Report](#before-submitting-a-bug-report)
    - [How Do I Submit a Good Bug Report?](#how-do-i-submit-a-good-bug-report)
  - [Suggesting Enhancements](#suggesting-enhancements)
    - [Before Submitting an Enhancement](#before-submitting-an-enhancement)
    - [How Do I Submit a Good Enhancement Suggestion?](#how-do-i-submit-a-good-enhancement-suggestion)`,
					Metadata: map[string]interface{}{},
				},
				{
					PageContent: `  - [Your First Code Contribution](#your-first-code-contribution)
  - [Improving The Documentation](#improving-the-documentation)
- [Styleguides](#styleguides)
  - [Commit Messages](#commit-messages)
- [Join The Project Team](#join-the-project-team)
- [Attribution](#attribution)
- [Appendix](#appendix)
  - [Pull Request](#pull-request)
  - [Your PR is merged!](#your-pr-is-merged)`,
					Metadata: map[string]interface{}{},
				},
			},
		},
	}

	for _, tc := range testCases {
		splitter := NewMarkdownTextSplitter(WithChunkSize(512), WithChunkOverlap(64))
		chunks, err := splitter.SplitText(tc.markdown)
		require.NoError(t, err)

		docs := make([]TestDocument, 0, len(chunks))
		for _, chunk := range chunks {
			docs = append(docs, TestDocument{
				PageContent: chunk.Content,
				Metadata:    map[string]interface{}{},
			})
		}

		assert.Equal(t, tc.expectedDocs, docs)
	}
}

func TestMarkdownHeaderTextSplitter_Headers(t *testing.T) {
	t.Parallel()

	type testCase struct {
		markdown     string
		expectedDocs []TestDocument
	}

	testCases := []testCase{
		{
			markdown: `### Your First Code Contribution

#### Make Changes

##### Make changes in the UI
Click **Make a contribution** at the bottom of any docs page to make small changes such as a typo, sentence fix, or a
broken link. This takes you to the .md file where you can make your changes and [create a pull request](#pull-request)
for a review.

##### Make changes locally
1. Fork the repository.
2. Install or make sure **Golang** is updated.
3. Create a working branch and start with your changes!
`,
			expectedDocs: []TestDocument{
				{
					PageContent: `### Your First Code Contribution`, Metadata: map[string]interface{}{},
				},
				{
					PageContent: `#### Make Changes`, Metadata: map[string]interface{}{},
				},
				{
					PageContent: `##### Make changes in the UI
Click **Make a contribution** at the bottom of any docs page to make small changes such as a typo, sentence fix, or a
broken link. This takes you to the .md file where you can make your changes and [create a pull request](#pull-request)
for a review.`, Metadata: map[string]interface{}{},
				},
				{
					PageContent: `##### Make changes locally
1. Fork the repository.
2. Install or make sure **Golang** is updated.
3. Create a working branch and start with your changes!`, Metadata: map[string]interface{}{},
				},
			},
		},
	}

	for _, tc := range testCases {
		splitter := NewMarkdownTextSplitter(WithChunkSize(512), WithChunkOverlap(64))
		chunks, err := splitter.SplitText(tc.markdown)
		require.NoError(t, err)

		docs := make([]TestDocument, 0, len(chunks))
		for _, chunk := range chunks {
			docs = append(docs, TestDocument{
				PageContent: chunk.Content,
				Metadata:    map[string]interface{}{},
			})
		}

		assert.Equal(t, tc.expectedDocs, docs)
	}
}

func TestMarkdownHeaderTextSplitter_CodeFence(t *testing.T) {
	t.Parallel()

	type testCase struct {
		name         string
		options      []Option
		markdown     string
		expectedDocs []TestDocument
	}

	testCases := []testCase{
		{
			name:     "fence-false",
			markdown: "example code:\n```go\nfunc main() {}\n```",
			expectedDocs: []TestDocument{
				{
					PageContent: "example code:",
					Metadata:    map[string]interface{}{},
				},
			},
		},
		{
			name: "fence-true",
			options: []Option{
				WithCodeBlocks(true),
			},
			markdown: "example code:\n```go\nfunc main() {}\n```",
			expectedDocs: []TestDocument{
				{
					PageContent: "example code:\n\n```go\nfunc main() {}\n```\n",
					Metadata:    map[string]interface{}{},
				},
			},
		},
		{
			name: "codeblock-false",
			markdown: `example code:

    func main() {
	}
    `,
			expectedDocs: []TestDocument{
				{
					PageContent: `example code:`,
					Metadata:    map[string]interface{}{},
				},
			},
		},
		{
			name: "codeblock-true",
			options: []Option{
				WithCodeBlocks(true),
			},
			markdown: `example code:

    func main() {
	}
    `,
			expectedDocs: []TestDocument{
				{
					PageContent: `example code:

    func main() {
    }
    `,
					Metadata: map[string]interface{}{},
				},
			},
		},
		{
			name: "hr",
			markdown: `example code:

---
more text
`,
			expectedDocs: []TestDocument{
				{
					PageContent: `example code:

---
more text`,
					Metadata: map[string]interface{}{},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			rq := require.New(t)

			splitter := NewMarkdownTextSplitter(append(
				[]Option{
					WithChunkSize(64),
					WithChunkOverlap(32),
				},
				tc.options...,
			)...)

			chunks, err := splitter.SplitText(tc.markdown)
			rq.NoError(err)

			docs := make([]TestDocument, 0, len(chunks))
			for _, chunk := range chunks {
				docs = append(docs, TestDocument{
					PageContent: chunk.Content,
					Metadata:    map[string]interface{}{},
				})
			}

			rq.Equal(tc.expectedDocs, docs)
		})
	}
}

func TestMarkdownHeaderTextSplitter_Inline(t *testing.T) {
	t.Parallel()

	type testCase struct {
		name         string
		options      []Option
		markdown     string
		expectedDocs []TestDocument
	}

	testCases := []testCase{
		{
			name:     "break",
			markdown: "text with\\\nhard break\nsoft break",
			expectedDocs: []TestDocument{
				{
					PageContent: "text with\\\nhard break\nsoft break",
					Metadata:    map[string]interface{}{},
				},
			},
		},
		{
			name:     "emphasis",
			markdown: "text with *emphasis*, **strong emphasis** and ~~strikethrough~~",
			expectedDocs: []TestDocument{
				{
					PageContent: "text with *emphasis*, **strong emphasis** and ~~strikethrough~~",
					Metadata:    map[string]interface{}{},
				},
			},
		},
		{
			name: "image",
			markdown: `images:
![one](/path/to/one.png)
![two](/path/to/two.png "two")
`,
			expectedDocs: []TestDocument{
				{
					PageContent: `images:
![one](/path/to/one.png)
![two](/path/to/two.png "two")`,
					Metadata: map[string]interface{}{},
				},
			},
		},
		{
			name: "link-false",
			markdown: `links:
[foo][bar]

[bar]: /url "title"

[regular](/url)
`,
			expectedDocs: []TestDocument{
				{
					PageContent: `links:
[foo][bar]
[regular](/url)`,
					Metadata: map[string]interface{}{},
				},
			},
		},
		{
			name: "link-true",
			options: []Option{
				WithReferenceLinks(true),
			},
			markdown: `links:
[foo][bar]

[bar]: /url "title"

[regular](/url)
`,
			expectedDocs: []TestDocument{
				{
					PageContent: `links:
[foo](/url "title")
[regular](/url)`,
					Metadata: map[string]interface{}{},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			rq := require.New(t)

			splitter := NewMarkdownTextSplitter(append(
				[]Option{
					WithChunkSize(64),
					WithChunkOverlap(32),
				},
				tc.options...,
			)...)

			chunks, err := splitter.SplitText(tc.markdown)
			rq.NoError(err)

			docs := make([]TestDocument, 0, len(chunks))
			for _, chunk := range chunks {
				docs = append(docs, TestDocument{
					PageContent: chunk.Content,
					Metadata:    map[string]interface{}{},
				})
			}

			rq.Equal(tc.expectedDocs, docs)
		})
	}
}

func TestMarkdownHeaderTextSplitter_ChunkSize(t *testing.T) {
	t.Parallel()

	sampleText := "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."

	type testCase struct {
		markdown     string
		expectedDocs []TestDocument
	}

	testCases := []testCase{
		{
			markdown: `# Title` + "\n" + sampleText + "\n" + sampleText,
			expectedDocs: []TestDocument{
				{
					PageContent: "# Title" + "\n" + sampleText,
					Metadata:    map[string]interface{}{},
				},
				{
					PageContent: "# Title" + "\n" + sampleText,
					Metadata:    map[string]interface{}{},
				},
			},
		},
	}

	splitter := NewMarkdownTextSplitter(WithChunkSize(512), WithChunkOverlap(64))
	for _, tc := range testCases {
		chunks, err := splitter.SplitText(tc.markdown)
		require.NoError(t, err)

		docs := make([]TestDocument, 0, len(chunks))
		for _, chunk := range chunks {
			docs = append(docs, TestDocument{
				PageContent: chunk.Content,
				Metadata:    map[string]interface{}{},
			})
		}

		assert.Equal(t, tc.expectedDocs, docs)
	}
}
