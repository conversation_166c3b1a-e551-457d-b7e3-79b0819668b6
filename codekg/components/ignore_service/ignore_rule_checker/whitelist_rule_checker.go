package ignore_rule_checker

import (
	"context"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/samber/lo"
)

const (
	IncludeExtFileRule = "IncludeExtFileRuleChecker"
)

// IncludeExtFileRuleChecker 用来检查文件扩展名是否在忽略列表中
type IncludeExtFileRuleChecker struct {
	extFiles map[IgnoreRule]struct{}
	is       IgnoreServiceV2
	expire   time.Time
}

func NewIncludeExtFileRuleChecker() *IncludeExtFileRuleChecker {
	return &IncludeExtFileRuleChecker{
		extFiles: make(map[IgnoreRule]struct{}),
		expire:   time.Now(),
	}
}

func (e *IncludeExtFileRuleChecker) Name() CheckerName                   { return IncludeExtFileRule }
func (e *IncludeExtFileRuleChecker) SetIgnoreService(is IgnoreServiceV2) { e.is = is }
func (e *IncludeExtFileRuleChecker) AddRules(ctx context.Context, _, _ string, rules []IgnoreRule) error {
	for _, rule := range rules {
		e.extFiles[rule] = struct{}{}
	}
	return nil
}
func (e *IncludeExtFileRuleChecker) UpdateRulesFromTCC(ctx context.Context, userId string, base string) error {
	if time.Now().Before(e.expire) {
		return nil
	} else {
		e.expire = time.Now().Add(5 * time.Minute)
	}
	config, cli := e.is.GetConfig(), e.is.GetClient()
	if config == nil || cli == nil {
		return nil
	}
	tcc := config.GetTCCConfig(ctx, cli, userId)
	rules := strings.Split(tcc.IncludeExtFileRule, "\n")
	rules = lo.Filter(rules, func(rule string, _ int) bool { return len(rule) > 0 })
	if len(rules) == 0 {
		return nil
	}
	// 不能完全清除掉现有的过滤规则，因为用户可能通过 AddRules 添加规则
	for _, rule := range rules {
		e.extFiles[IgnoreRule(rule)] = struct{}{}
	}
	return nil
}

// IsInclude of IncludeExtFileRuleChecker，返回 true 表示在白名单中，后续不需要校验 “utf-8 合法” 和 “不可见字符”
func (e *IncludeExtFileRuleChecker) IsInclude(ctx context.Context, path string, info os.FileInfo) (bool, error) {
	ext := strings.ToLower(filepath.Ext(path))
	if _, ok := e.extFiles[IgnoreRule(ext)]; ok {
		return true, nil
	}
	return false, nil
}
