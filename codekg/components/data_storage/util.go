package data_storage

import (
	"context"
	"fmt"
	"github.com/samber/lo"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/data_storage/consts"
	bizErr "ide/ckg/codekg/components/error"
	"ide/ckg/codekg/components/validator"
	"ide/ckg/codekg/model"

	"ide/ckg/codekg/components/logs"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

// WriteChunkingAndEmbeddingData 将切分结果和 embedding 计算结果落盘
func WriteChunkingAndEmbeddingData(ctx context.Context, storage Storage, embeddingStorage EmbeddingStorage,
	uri *model.URIStatus, uriData []*model.URIData, embeddingData []*model.EmbeddingData) error {
	localIndexEvent := ckg_metrics.GetEvent(ctx)
	if len(uriData) == 0 || storage.IsDeleted() || embeddingStorage.IsDeleted() {
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmptyUriData, len(uriData) == 0)
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEntityStorageDeleted, storage.IsDeleted())
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbeddingStorageDeleted, embeddingStorage.IsDeleted())
		errMsg := fmt.Sprintf("file init failed %s, uriData size: %d, storage: %v, embedding storage: %v",
			validator.EncryptPath(uri.AbsPath), len(uriData), storage.IsDeleted(), embeddingStorage.IsDeleted())
		logs.CtxWarn(ctx, "[WriteChunkingAndEmbeddingData] %s", errMsg)
		return errors.New(errMsg)
	}
	if !storage.IsExistsOnDisk() || !embeddingStorage.IsExistsOnDisk() {
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEntityStorageNotOnDisk, !storage.IsExistsOnDisk())
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbeddingStorageNotOnDisk, !embeddingStorage.IsExistsOnDisk())
		errMsg := fmt.Sprintf("file init failed, storage is not on disk: %v, embedding storage is not on disk: %v",
			!storage.IsExistsOnDisk(), !embeddingStorage.IsExistsOnDisk())
		logs.CtxWarn(ctx, "[WriteChunkingAndEmbeddingData] %s", errMsg)
		return errors.New(errMsg)
	}
	for _, data := range uriData {
		err := InsertStorageWithEmbedding(ctx, storage, embeddingStorage, data)
		if err != nil {
			localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexUriDataInsertFailed, true)
			errMsg := fmt.Sprintf("insertStorage err is %v path is %v", err, validator.EncryptPath(uri.AbsPath))
			logs.CtxError(ctx, "[WriteChunkingAndEmbeddingData] %s", errMsg)
			return errors.New(errMsg)
		}
	}
	for _, data := range uriData {
		if err := MarkFileAsEmbedded(ctx, storage, data); err != nil {
			localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexUpdateStatusFailed, true)
			errMsg := fmt.Sprintf("updateFileEmbeddingStatus err is %v path is %s", err, validator.EncryptPath(uri.AbsPath))
			logs.CtxError(ctx, "[WriteChunkingAndEmbeddingData] %s", errMsg)
			return errors.New(errMsg)
		}

		if err := storage.UpdateURIMetaStatus(ctx, storage.GetConn(), data.Status.AbsPath, int(model.BuildStatusFinished)); err != nil {
			errMsg := fmt.Sprintf("MarkURIMetaAsIndexedAndClearContent err is %v path is %s", err, validator.EncryptPath(uri.AbsPath))
			logs.CtxError(ctx, "[WriteChunkingAndEmbeddingData] %s", errMsg)
		}
	}
	if len(embeddingData) == 0 {
		// 没有 embedding 数据，可能是没有命中缓存，或是 embedding 计算错误。
		return nil
	}
	for _, eData := range embeddingData {
		if err := InsertEmbeddingStorage(ctx, storage, embeddingStorage, eData); err != nil {
			localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbeddingInsertFailed, true)
			errMsg := fmt.Sprintf("insertEmbeddingStorage err is %v path is %v", err, validator.EncryptPath(uri.AbsPath))
			logs.CtxError(ctx, "[WriteChunkingAndEmbeddingData] %s", errMsg)
			return errors.New(errMsg)
		}
	}
	logs.CtxTrace(ctx, "file %s init succeed", uri.AbsPath)
	return nil
}

func WriteOnlyEmbeddingData(ctx context.Context, storage Storage, embeddingStorage EmbeddingStorage, embeddingData []*model.EmbeddingData) error {
	localIndexEvent := ckg_metrics.GetEvent(ctx)
	if len(embeddingData) == 0 || storage.IsDeleted() || embeddingStorage.IsDeleted() {
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamBatchEmbeddingEmptyEmbeddingData, len(embeddingData) == 0)
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEntityStorageDeleted, storage.IsDeleted())
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbeddingStorageDeleted, embeddingStorage.IsDeleted())
		errMsg := fmt.Sprintf("embedding failed, embeddingData size: %d, storage: %v, embedding storage: %v",
			len(embeddingData), storage.IsDeleted(), embeddingStorage.IsDeleted())
		logs.CtxWarn(ctx, "[WriteOnlyEmbeddingData] %s", errMsg)
		return errors.New(errMsg)
	}
	if !storage.IsExistsOnDisk() || !embeddingStorage.IsExistsOnDisk() {
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEntityStorageNotOnDisk, !storage.IsExistsOnDisk())
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbeddingStorageNotOnDisk, !embeddingStorage.IsExistsOnDisk())
		errMsg := fmt.Sprintf("file init failed, storage is not on disk: %v, embedding storage is not on disk: %v",
			!storage.IsExistsOnDisk(), !embeddingStorage.IsExistsOnDisk())
		logs.CtxWarn(ctx, "[WriteOnlyEmbeddingData] %s", errMsg)
		return errors.New(errMsg)
	}
	for _, eData := range embeddingData {
		if err := InsertEmbeddingStorage(ctx, storage, embeddingStorage, eData); err != nil {
			localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbeddingInsertFailed, true)
			errMsg := fmt.Sprintf("insertEmbeddingStorage err is %v", err)
			logs.CtxError(ctx, "[WriteChunkingAndEmbeddingData] %s", errMsg)
			return errors.New(errMsg)
		}
	}
	logs.CtxTrace(ctx, "write embedding (%d) data succeed", len(embeddingData))
	return nil
}

func InsertStorage(ctx context.Context, storage Storage, data *model.URIData) error {
	if storage == nil {
		return bizErr.ErrParamNil
	}
	err := storage.GetConn().Transaction(func(tx *gorm.DB) error {
		var err error
		uriValue, err := storage.UpsertUriMeta(ctx, tx, &data.Status)
		if err != nil {
			return err
		}

		err = storage.DeleteEntitiesFromURIMeta(ctx, tx, uriValue)
		if err != nil {
			logs.CtxError(ctx, "DeleteEntitiesFromURIMeta err %v", err)
			return err
		}
		err = storage.DeleteRelationsFromURIMeta(ctx, tx, uriValue)
		if err != nil {
			logs.CtxError(ctx, "DeleteRelationsFromURIMeta err %v", err)
			return err
		}
		err = storage.DeleteAliasFromURIMeta(ctx, tx, uriValue)
		if err != nil {
			logs.CtxError(ctx, "DeleteAliasFromURIMeta err %v", err)
			return err
		}
		err = storage.InsertEntities(ctx, tx, &data.Status, data.Entities)
		if err != nil {
			return err
		}

		err = storage.InsertRelations(ctx, tx, &data.Status, data.Relation)
		if err != nil {
			return err
		}

		err = storage.InsertEntityAlias(ctx, tx, &data.Status, data.AliasEntity)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

func InsertStorageWithEmbedding(ctx context.Context, storage Storage, eStorage EmbeddingStorage, data *model.URIData) error {
	if storage == nil {
		return bizErr.ErrParamNil
	}
	err := storage.GetConn().Transaction(func(tx *gorm.DB) error {
		var err error
		uriValue, err := storage.UpsertUriMeta(ctx, tx, &data.Status)
		if err != nil {
			return err
		}

		// 首先确定待删除的 entities 和 vectors 的 ID
		entities, err := storage.SearchEntitiesByURIMeta(ctx, tx, uriValue)
		if err != nil {
			logs.CtxError(ctx, "SearchEntitiesByURIMeta err %v", err)
			return err
		}
		if eStorage.GetVersion() == consts.StorageVersionV1 {
			option := NewDeleteDocumentOptionBuilder().Build()
			if err = eStorage.DeleteDocumentsByEntity(ctx, entities, storage, tx, option); err != nil {
				return err
			}
		} else if eStorage.GetVersion() == consts.StorageVersionV2 {
			entityIds := lo.Map(entities, func(e StorageEntity, _ int) string { return e.EntityID })
			vectors, err := storage.SearchVectorIDsByEntityIDs(ctx, tx, entityIds)
			if err != nil {
				logs.CtxError(ctx, "SearchVectorIDsByEntityIDs err %v", err)
				return err
			}
			// 先在向量库中删除，再删除 EntityStorage 中的关系
			option := NewDeleteDocumentOptionBuilder().Build()
			err = eStorage.DeleteDocumentsByIds(ctx, option, vectors...)
			if err != nil {
				return err
			}
		}
		//vectors, err := storage.SearchVectorIDsByEntityIDs(ctx, tx, entities)
		//if err != nil {
		//	logs.CtxError(ctx, "SearchVectorIDsByEntityIDs err %v", err)
		//	return err
		//}
		//先在向量库中删除，再删除 EntityStorage 中的关系
		//option := data_storage.NewDeleteDocumentOptionBuilder().Build()
		//err = eStorage.DeleteDocumentsByIds(ctx, option, vectors...)
		//if err != nil {
		//	return err
		//}

		// 先删除 vector_to_entity，再删除 entity，因为需要先根据 uriMeta.Uri 找到对应的 entity 中的 entities，
		// 这样才能先在 vector_to_entity 中删除对应的记录。
		if err = storage.DeleteVectorToEntityFromURIMeta(ctx, tx, uriValue); err != nil {
			logs.CtxError(ctx, "DeleteVectorToEntityFromURIMeta err %v", err)
			return err
		}

		err = storage.DeleteEntitiesFromURIMeta(ctx, tx, uriValue)
		if err != nil {
			logs.CtxError(ctx, "DeleteEntitiesFromURIMeta err %v", err)
			return err
		}
		err = storage.DeleteRelationsFromURIMeta(ctx, tx, uriValue)
		if err != nil {
			logs.CtxError(ctx, "DeleteRelationsFromURIMeta err %v", err)
			return err
		}
		err = storage.DeleteAliasFromURIMeta(ctx, tx, uriValue)
		if err != nil {
			logs.CtxError(ctx, "DeleteAliasFromURIMeta err %v", err)
			return err
		}

		err = storage.InsertEntities(ctx, tx, &data.Status, data.Entities)
		if err != nil {
			return err
		}

		err = storage.InsertRelations(ctx, tx, &data.Status, data.Relation)
		if err != nil {
			return err
		}

		err = storage.InsertEntityAlias(ctx, tx, &data.Status, data.AliasEntity)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func InsertEmbeddingStorage(ctx context.Context, storage Storage, eStorage EmbeddingStorage, eData *model.EmbeddingData) error {
	if eStorage == nil {
		return bizErr.ErrParamNil
	}
	if len(eData.Embeddings) == 0 || len(eData.Relations) == 0 {
		// relation: embeddings or relations is empty, no need to insert
		return nil
	}
	if len(eData.Embeddings) != len(eData.Relations) {
		logs.CtxWarn(ctx, "embeddings and relations size not equal: %d vs. %d", len(eData.Embeddings), len(eData.Relations))
		return nil
	}
	return storage.GetConn().Transaction(func(tx *gorm.DB) error {
		var err error
		// 先在向量库中插入，再插入 vector_to_entity，保证查询 vector_to_entity 能定位到 vector。
		addOption := NewAddEmbeddingDocumentOptionBuilder().Build()
		if err = eStorage.AddEmbeddingDocuments(ctx, eData.Embeddings, addOption); err != nil {
			return err
		}
		// 可能有问题，没在这个事务中确保所有 relation 中的 entityID 一定在 SQLite 中
		if err = storage.InsertVectorToEntity(ctx, tx, eData.Relations); err != nil {
			return err
		}
		return nil
	})
}

// MarkFileAsEmbedded 给 uri_meta 的 hasEmbedding 字段置位 1，表示文件已经计算过 embedding。
func MarkFileAsEmbedded(ctx context.Context, storage Storage, uriData *model.URIData) error {
	return storage.UpdateEntityEmbeddingStatus(ctx, storage.GetConn(), uriData.Status.AbsPath, 1)
}
