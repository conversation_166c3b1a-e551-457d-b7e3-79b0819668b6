package langchain

// createTextChunk 创建带有行号信息的TextChunk
func createTextChunk(content string, startLine, endLine int) TextChunk {
	return TextChunk{
		Content:   content,
		StartLine: startLine,
		EndLine:   endLine,
	}
}

// processChunksWithLineIndices 使用基于行索引的方式处理文本块，添加行号信息
// 这是一个更高效的实现，直接使用行索引而不是字符位置
// chunks是文本块内容，lineIndices是每个块对应的行索引信息
func processChunksWithLineIndices(chunks []string, lineIndices [][2]int) ([]TextChunk, error) {
	result := make([]TextChunk, 0, len(chunks))

	// 处理每个分割后的文本块
	for i, chunk := range chunks {
		if chunk == "" {
			continue
		}

		// 获取块的起始和结束行号
		startLine := lineIndices[i][0]
		endLine := lineIndices[i][1]

		// 创建TextChunk
		textChunk := createTextChunk(chunk, startLine, endLine)
		result = append(result, textChunk)
	}

	return result, nil
}

// findLineIndices 计算文本中的行索引
// 返回一个数组，其中每个元素是文本中每行的起始字符位置
func findLineIndices(text string) []int {
	indices := []int{0} // 第一行从0开始

	for i := 0; i < len(text); i++ {
		if text[i] == '\n' {
			indices = append(indices, i+1) // 下一行的起始位置
		}
	}

	return indices
}

// getLineNumberFromPosition 根据字符位置获取行号（1-indexed）
func getLineNumberFromPosition(lineIndices []int, position int) int {
	// 二分查找找到最后一个小于等于position的行索引
	left, right := 0, len(lineIndices)-1
	for left <= right {
		mid := (left + right) / 2
		if lineIndices[mid] <= position {
			left = mid + 1
		} else {
			right = mid - 1
		}
	}

	return right + 1 // 转换为1-indexed行号
}
