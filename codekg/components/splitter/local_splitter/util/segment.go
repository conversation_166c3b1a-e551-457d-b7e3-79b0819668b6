package util

import (
	"crypto/md5"
	"encoding/gob"
	"fmt"

	"ide/ckg/codekg/components/knowledgebase"
)

func hashSegment(a interface{}) string {
	hasher := md5.New()
	encoder := gob.NewEncoder(hasher)
	_ = encoder.Encode(a)
	return fmt.Sprintf("%x", hasher.Sum(nil))
}

func ConvertTextSegmentsToSegments(textSegments []*knowledgebase.TextSegment) map[string]*knowledgebase.SegmentWithEmbedding {
	segments := make(map[string]*knowledgebase.SegmentWithEmbedding)

	for i, ts := range textSegments {
		segmentID := ts.ID
		if segmentID == "" {
			segmentID = fmt.Sprintf("segment_%d", i)
		}

		// 创建 Segment 对象
		segment := &knowledgebase.Segment{
			ID:          segmentID,
			Type:        "text", // 设置类型为文本
			TextSegment: ts,
		}

		// 创建 SegmentWithEmbedding 对象
		segments[segmentID] = &knowledgebase.SegmentWithEmbedding{
			EntityID:          segmentID,
			SegmentUniqueID:   segmentID,
			Segment:           segment,
			IsAllEmbedding:    false,                      // 初始设置为 false，表示需要计算 embedding
			EmbeddingContents: []string{ts.Content},       // 使用文本内容作为 embedding 内容
			Embeddings:        make(map[string][]float64), // 初始化空的 embeddings map
		}
	}

	return segments
}
