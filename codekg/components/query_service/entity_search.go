package query_service

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"

	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/data_storage/consts"
	"ide/ckg/codekg/components/env"
	bizErr "ide/ckg/codekg/components/error"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/protocol"
	"ide/ckg/codekg/util"

	"ide/ckg/codekg/components/logs"

	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type entitySearchType = int8

const (
	searchTypeKnowledgebase entitySearchType = iota + 1
	searchTypeAlias
	searchTypeNER
	searchTypeLocal
)

type recallOption struct {
	Type         entitySearchType
	NERTag       knowledgebase.NerRecallTag
	AliasTag     knowledgebase.AliasRecallTag
	EmbeddingTag knowledgebase.EmbeddingRecallTag
}

const nerCurrentName = "$current$"
const workspaceRelPath = "."
const KnowledgebaseAPINetWorkErrCode = 2001

const (
	EmptyRecallShouldRemoteRecall = "should remote index, but local index. localEmbedding: %v, uri size %d > %d"
	EmptyRecallUseChromemGoFolder = "shouldn't use chromem-go when using #Folder"
	EmptyRecallWithEmptyProject   = "empty project: %s"
	EmptyRecallWithAllEmptyFolder = "all folders are empty, folders: %v"
	EmptyRecallWithEmptyVecDB     = "embedding db is empty"
	EmptyRecallWithEmptyEntityDB  = "entity db is empty"
	EmptyRecallAllEntityAreFolder = "all uri meta are of folder type"
	EmptyRecallNarrowCondition    = "narrow condition contributes to empty recall"
)

type entitySearchResult struct {
	Entity       *model.Entity
	Score        float32
	RecallOption *recallOption
}

func getEmbeddingSearchResult(ctx context.Context, searchType knowledgebase.EmbeddingRecallTag, response knowledgebase.RetrieveResponse, storage data_storage.Storage) []*entitySearchResult {
	recallEvent := ckg_metrics.GetEvent(ctx)
	folderErrNum, codeChunkErrNum, textErrNum, fileErrNum, classErrNum, methodErrNum := 0, 0, 0, 0, 0, 0
	defer func() {
		recallEvent.AddTeaParam(lo.Ternary(searchType == knowledgebase.Query, ckg_metrics.TeaParamQueryFolderErrNum, ckg_metrics.TeaParamSelectedFolderErrNum), folderErrNum)
		recallEvent.AddTeaParam(lo.Ternary(searchType == knowledgebase.Query, ckg_metrics.TeaParamQueryCodeChunkErrNum, ckg_metrics.TeaParamSelectedCodeChunkErrNum), codeChunkErrNum)
		recallEvent.AddTeaParam(lo.Ternary(searchType == knowledgebase.Query, ckg_metrics.TeaParamQueryTextErrNum, ckg_metrics.TeaParamSelectedTextErrNum), textErrNum)
		recallEvent.AddTeaParam(lo.Ternary(searchType == knowledgebase.Query, ckg_metrics.TeaParamQueryFileErrNum, ckg_metrics.TeaParamSelectedFileErrNum), fileErrNum)
		recallEvent.AddTeaParam(lo.Ternary(searchType == knowledgebase.Query, ckg_metrics.TeaParamQueryClassErrNum, ckg_metrics.TeaParamSelectedClassErrNum), classErrNum)
		recallEvent.AddTeaParam(lo.Ternary(searchType == knowledgebase.Query, ckg_metrics.TeaParamQueryMethodErrNum, ckg_metrics.TeaParamSelectedMethodErrNum), methodErrNum)
	}()
	results := make([]*entitySearchResult, 0)
	for _, seg := range response.Segments {
		var entity *model.Entity = nil
		var err error = nil
		switch seg.Type {
		case "folder":
			entity, err = storage.GetEntityByEntityID(ctx, storage.GetConn(), seg.FolderSegment.ID)
			if err != nil {
				folderErrNum++
			}
		case "code_chunk":
			entity, err = storage.GetEntityByEntityID(ctx, storage.GetConn(), seg.CodeChunkSegment.ID)
			if err != nil {
				codeChunkErrNum++
			}
		case "text":
			entity, err = storage.GetEntityByEntityID(ctx, storage.GetConn(), seg.TextSegment.ID)
			if err != nil {
				textErrNum++
			}
		case "file":
			entity, err = storage.GetEntityByEntityID(ctx, storage.GetConn(), seg.FileSegment.ID)
			if err != nil {
				fileErrNum++
			}
		case "class":
			entity, err = storage.GetEntityByEntityID(ctx, storage.GetConn(), seg.ClassSegment.ID)
			if err != nil {
				classErrNum++
			}
		case "method":
			entity, err = storage.GetEntityByEntityID(ctx, storage.GetConn(), seg.MethodSegment.ID)
			if err != nil {
				methodErrNum++
			}
		default:
			break
		}
		if err != nil {
			logs.CtxWarn(ctx, "[getEmbeddingSearchResult] GetEntityByEntityID err is %v", err)
			continue
		}
		if entity == nil {
			logs.CtxWarn(ctx, "[getEmbeddingSearchResult] segment type is %v, entity is not assigned", seg.Type)
			continue
		}
		results = append(results, &entitySearchResult{
			Entity: entity,
			Score:  seg.Score,
			RecallOption: &recallOption{
				Type:         searchTypeKnowledgebase,
				NERTag:       -1,
				AliasTag:     -1,
				EmbeddingTag: searchType,
			},
		})
	}
	return results
}

func (s *service) searchEntityInKnowledgebase(ctx context.Context, cli knowledgebase.Client, knowledgebaseID string, storage data_storage.Storage,
	userID, userMessage, selectedCode string, expectTypes []model.EntityType, entityNum int) ([]*entitySearchResult, error) {
	recallEvent := ckg_metrics.GetEvent(ctx)

	startTime := time.Now()
	recallEvent.AddParam(ckg_metrics.ParamEmbeddingRecallParams, map[string]interface{}{
		"top_n": entityNum,
	})

	resultCh := make(chan []*entitySearchResult)
	util.SafeGo(ctx, func() {
		wg := sync.WaitGroup{}
		wg.Add(2)

		util.SafeGo(ctx, func() {
			defer wg.Done()
			if userMessage == "" {
				return
			}

			userMessageResp, err := cli.Retrieve(ctx, env.GetToken(userID), knowledgebaseID, &knowledgebase.RetrieveRequest{
				ProjectIDs: []string{storage.GetProjectID()},
				Content:    userMessage,
				TopN:       entityNum,
			})
			if err != nil {
				logs.CtxError(ctx, "Retrieve failed err is %s", err)
				recallEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingRecallQueryErr, true)
				if util.JudgeNewWorkError(err) {
					recallEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingRecallErrCode, KnowledgebaseAPINetWorkErrCode)
				}
				return
			}
			if len((*userMessageResp).Segments) == 0 {
				logs.CtxWarn(ctx, "[searchEntityInKnowledgebase] segment is empty")
				recallEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingRecallQuerySegEmpty, true)
			}
			results := getEmbeddingSearchResult(ctx, knowledgebase.Query, *userMessageResp, storage)
			if len(results) == 0 {
				logs.CtxWarn(ctx, "[searchEntityInKnowledgebase] entity search result (query) is empty")
				recallEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingRecallQuerySearchResultEmpty, true)
			}
			resultCh <- results
		})

		util.SafeGo(ctx, func() {
			defer wg.Done()
			if selectedCode == "" {
				return
			}

			selectCodeResp, err := cli.Retrieve(ctx, env.GetToken(userID), knowledgebaseID, &knowledgebase.RetrieveRequest{
				ProjectIDs: []string{storage.GetProjectID()},
				Content:    selectedCode,
				TopN:       entityNum,
			})
			if err != nil {
				logs.CtxError(ctx, "Retrieve failed err is %s", err)
				if util.JudgeNewWorkError(err) {
					recallEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingRecallErrCode, KnowledgebaseAPINetWorkErrCode)
				}
				recallEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingRecallSelectedErr, true)
				return
			}
			if len((*selectCodeResp).Segments) == 0 {
				logs.CtxWarn(ctx, "[searchEntityInKnowledgebase] segment is empty")
				recallEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingRecallSelectedSegEmpty, true)
			}
			results := getEmbeddingSearchResult(ctx, knowledgebase.Selected, *selectCodeResp, storage)
			if len(results) == 0 {
				logs.CtxWarn(ctx, "[searchEntityInKnowledgebase] entity search result (selected) is empty")
				recallEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingRecallSelectedSearchResultEmpty, true)
			}
			resultCh <- results
		})

		wg.Wait()
		close(resultCh)
	})

	resultMap := make(map[string]*entitySearchResult)
	for r := range resultCh {
		lo.ForEach(r, func(item *entitySearchResult, _ int) {
			if item.Entity.IsTopLevel() {
				// TODO: 这个是否应该去掉？
				return
			}

			if len(expectTypes) != 0 && !lo.Contains(expectTypes, item.Entity.Type) {
				return
			}

			if v, ok := resultMap[item.Entity.ID]; ok {
				if item.Score > v.Score {
					v.Score = item.Score
				}
				return
			}

			resultMap[item.Entity.ID] = item
		})
	}

	result := lo.Values(resultMap)
	if len(result) == 0 {
		logs.CtxWarn(ctx, "[searchEntityInKnowledgebase] empty entity search results")
		recallEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingEmptySearchResult, true)
	}

	sort.Slice(result, func(i, j int) bool { return result[i].Score > result[j].Score })
	recallEvent.AddParam(ckg_metrics.ParamEmbeddingRecallRawEntities, lo.Map(result, func(item *entitySearchResult, index int) entitySearchResult {
		return *item
	}))

	finalResult := lo.Slice(result, 0, entityNum)
	recallEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingRecallNum, len(finalResult))
	recallEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingRecallCost, time.Since(startTime).Milliseconds())
	return finalResult, nil
}

func (s *service) searchEntityAtLocal(ctx context.Context, cli knowledgebase.Client, datastoreName string, storage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage,
	userID, userMessage, selectedCode string, absFolderPaths []string, expectTypes []model.EntityType, entityNum int) ([]*entitySearchResult, string, string, error) {
	recallEvent := ckg_metrics.GetEvent(ctx)
	startTime := time.Now()
	recallEvent.AddParam(ckg_metrics.ParamLocalEmbeddingRecallParams, map[string]interface{}{
		"top_n": entityNum,
	})
	resultCh := make(chan []*entitySearchResult)
	embeddingModel := ""
	util.SafeGo(ctx, func() {
		wg := sync.WaitGroup{}
		wg.Add(2)
		util.SafeGo(ctx, func() {
			defer wg.Done()
			if userMessage == "" {
				return
			}
			res, m, err := s.searchEntityAtLocalInternal(ctx, cli, datastoreName, storage, embeddingStorage, userID, userMessage, absFolderPaths, knowledgebase.Query, expectTypes, 2*entityNum)
			if err != nil {
				logs.CtxError(ctx, "searchEntityAtLocalInternal error: %s. userMessage %s", err.Error(), userMessage)
				return
			}
			resultCh <- res
			embeddingModel = m
		})

		util.SafeGo(ctx, func() {
			defer wg.Done()
			if selectedCode == "" {
				return
			}
			res, m, err := s.searchEntityAtLocalInternal(ctx, cli, datastoreName, storage, embeddingStorage, userID, selectedCode, absFolderPaths, knowledgebase.Selected, expectTypes, 2*entityNum)
			if err != nil {
				logs.CtxError(ctx, "searchEntityAtLocalInternal error: %s, selectedCode %s", err.Error(), selectedCode)
				return
			}
			resultCh <- res
			embeddingModel = m
		})
		wg.Wait()
		close(resultCh)
	})
	// copied from searchEntityInKnowledgebase
	resultMap := make(map[string]*entitySearchResult)
	for r := range resultCh {
		lo.ForEach(r, func(item *entitySearchResult, _ int) {
			// 如果当前 chunking method 是 v2，那么需要召回 TopLevel 实体，此处不应该 return
			if storage.GetChunkingMethod() != ckg_config.ChunkingMethodV2 && item.Entity.IsTopLevel() {
				return
			}
			if len(expectTypes) != 0 && !lo.Contains(expectTypes, item.Entity.Type) {
				return
			}
			if v, ok := resultMap[item.Entity.ID]; ok {
				if item.Score > v.Score {
					v.Score = item.Score
				}
				return
			}
			resultMap[item.Entity.ID] = item
		})
	}
	result := lo.Values(resultMap)
	errMsg := ""
	if len(result) == 0 {
		logs.CtxWarn(ctx, "[searchEntityAtLocal] return empty")
		errMsg = s.uploadReasonWhyEmptyRecall(ctx, cli, userID, storage, embeddingStorage, absFolderPaths)
	}
	sort.Slice(result, func(i, j int) bool { return result[i].Score > result[j].Score })
	recallEvent.AddParam(ckg_metrics.ParamLocalEmbeddingRecallRawEntities, lo.Map(result, func(item *entitySearchResult, index int) entitySearchResult {
		return *item
	}))
	finalResult := lo.Slice(result, 0, entityNum)
	recallEvent.AddTeaParam(ckg_metrics.TeaParamLocalEmbeddingRecallNum, len(finalResult))
	recallEvent.AddTeaParam(ckg_metrics.TeaParamLocalEmbeddingRecallCost, time.Since(startTime).Milliseconds())
	return finalResult, embeddingModel, errMsg, nil
}

func (s *service) searchEntityAtLocalInternal(ctx context.Context, cli knowledgebase.Client, datastoreName string, storage data_storage.Storage,
	embeddingStorage data_storage.EmbeddingStorage, userID, content string, absFolderPaths []string,
	recallTag knowledgebase.EmbeddingRecallTag, expectTypes []model.EntityType, entityNum int) ([]*entitySearchResult, string, error) {
	recallEvent := ckg_metrics.GetEvent(ctx)
	recallEvent.AddTeaParam(ckg_metrics.TeaParamLocalEmbeddingRecallTag, recallTag)
	recallEvent.AddTeaParam(ckg_metrics.TeaParamLocalEmbeddingStorageType, embeddingStorage.GetType())
	results := make([]*entitySearchResult, 0)
	startTime := time.Now()
	var resp *knowledgebase.EmbeddingResponse
	var err error
	if s.config.IsIndexFeatureEnabled(ctx, userID, ckg_config.UseV2SplitEmbeddingAPI) {
		recallEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingModel, "v2")
		resp, err = cli.EmbeddingV2(ctx, env.GetToken(userID), ckg_metrics.EventNameCKGRecall, &knowledgebase.EmbeddingRequestV2{
			EmbeddingModel:    datastoreName,
			EmbeddingContents: []string{content},
			OfflineCluster:    false,
		})
	} else {
		recallEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingModel, "v1")
		resp, err = cli.Embedding(ctx, env.GetToken(userID), ckg_metrics.EventNameCKGRecall, &knowledgebase.EmbeddingRequest{
			DatastoreName:     datastoreName,
			EmbeddingContents: []string{content},
			OfflineCluster:    false,
		})
	}
	recallEvent.AddTeaParam(ckg_metrics.TeaParamLocalRecallEmbeddingCost, time.Since(startTime).Milliseconds())
	if err != nil {
		logs.CtxError(ctx, "[searchEntityAtLocalInternal] Embedding failed err is %s", err.Error())
		recallEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, LocalRecallErrStepEmbedding)
		if util.JudgeNewWorkError(err) {
			recallEvent.AddTeaParam(ckg_metrics.TeaParamLocalEmbeddingRecallErrCode, KnowledgebaseAPINetWorkErrCode)
		}
		return results, "", err
	}
	if len(resp.Embeddings) == 0 {
		logs.CtxError(ctx, "[searchEntityAtLocalInternal] Embedding failed resp, embeddings' length = 0")
		recallEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, LocalRecallErrStepEmptyEmbedding)
		return results, resp.EmbeddingModel, err
	}
	embedding := knowledgebase.ConvertKBEmbeddingToEmbedding(resp.Embeddings[0])

	// 通过 absFolderPaths 计算出所有 relFolderPaths，方便 SQLite-vec metadata 过滤
	projectID := storage.GetProjectID()
	allInnerAbsFolderPaths, relFolderPaths := make([]string, 0), make([]string, 0)
	if len(absFolderPaths) > 0 {
		notExistedFolderNum := 0
		for _, absFolderPath := range absFolderPaths {
			// 不存在的文件夹会导致其对应的一次召回为空，因此不参与最终召回
			if _, err := s.fs.Stat(absFolderPath); os.IsNotExist(err) {
				notExistedFolderNum++
				continue
			}
			allInnerAbsFolderPaths = append(allInnerAbsFolderPaths, s.is.GetInnerFolders(ctx, absFolderPath)...)
		}
		recallEvent.AddTeaParam(ckg_metrics.TeaParamNotExistedFolderNum, notExistedFolderNum)
		recallEvent.AddTeaParam(ckg_metrics.TeaParamEmptyRecallAllFolderNotExisted, notExistedFolderNum == len(absFolderPaths))
	}
	allInnerAbsFolderPaths = lo.Uniq(allInnerAbsFolderPaths)
	if len(allInnerAbsFolderPaths) > 0 {
		relErrFolderNum := 0
		for _, folderPath := range allInnerAbsFolderPaths {
			relFolderPath, err := filepath.Rel(projectID, folderPath)
			if err != nil {
				relErrFolderNum++
				logs.CtxWarn(ctx, "error get relative folder path, err: %v", err)
				continue
			}
			relFolderPaths = append(relFolderPaths, relFolderPath)
		}
		recallEvent.AddTeaParam(ckg_metrics.TeaParamRelErrFolderNum, relErrFolderNum)
		recallEvent.AddTeaParam(ckg_metrics.TeaParamEmptyRecallAllFolderRelErr, relErrFolderNum == len(allInnerAbsFolderPaths))
	}

	// 计算 embedding 后在本地向量库查找
	// 1. #Workspace
	// 2. #Folder
	resCh := make(chan []*model.EmbeddingQueryResult)
	util.SafeGo(ctx, func() {
		wg := sync.WaitGroup{}
		wg.Add(2)
		// #Workspace
		util.SafeGo(ctx, func() {
			defer wg.Done()
			if len(absFolderPaths) != 0 {
				return
			}
			startTime := time.Now()
			res, err := s.searchWorkspaceEntityAtLocalInternal(ctx, embedding, embeddingStorage, expectTypes, entityNum)
			recallEvent.AddTeaParam(ckg_metrics.TeaParamLocalRecallWorkspaceVecSearchCost, time.Since(startTime).Milliseconds())
			if err != nil {
				logs.CtxError(ctx, "[searchEntityAtLocalInternal] #Workspace recall failed err is %v", err)
				recallEvent.AddTeaParam(ckg_metrics.TeaParamLocalEmbeddingWorkspaceErr, true)
				return
			}
			if len(res) == 0 {
				logs.CtxWarn(ctx, "[searchEntityAtLocalInternal] #Workspace return empty results")
				recallEvent.AddTeaParam(ckg_metrics.TeaParamLocalEmbeddingWorkspaceEmpty, true)
				return
			}
			resCh <- res
		})
		// #Folder
		util.SafeGo(ctx, func() {
			defer wg.Done()
			if len(absFolderPaths) == 0 {
				return
			}
			startTime := time.Now()
			res, err := s.searchFolderEntityAtLocalInternal(ctx, embedding, storage, embeddingStorage, relFolderPaths, expectTypes, entityNum)
			recallEvent.AddTeaParam(ckg_metrics.TeaParamLocalRecallFolderVecSearchCost, time.Since(startTime).Milliseconds())
			if err != nil {
				logs.CtxError(ctx, "[searchEntityAtLocalInternal] #Folder recall failed err is %v", err)
				recallEvent.AddTeaParam(ckg_metrics.TeaParamLocalEmbeddingFolderErr, true)
				return
			}
			if len(res) == 0 {
				logs.CtxWarn(ctx, "[searchEntityAtLocalInternal] #Folder return empty results")
				recallEvent.AddTeaParam(ckg_metrics.TeaParamLocalEmbeddingFolderEmpty, true)
				return
			}
			resCh <- res
		})
		wg.Wait()
		close(resCh)
	})
	res := make([]*model.EmbeddingQueryResult, 0)
	for r := range resCh {
		res = append(res, r...)
	}
	sort.Slice(res, func(i, j int) bool { return res[i].Score > res[j].Score })
	res = lo.Slice(res, 0, entityNum)

	// 根据 VectorID 查找 vector_to_entity -> entity 表中实体信息。
	vectorScoreMap := lo.SliceToMap(res, func(eqr *model.EmbeddingQueryResult) (string, float32) { return eqr.ID, eqr.Score })
	{ // chromem-go
		vtes, err := storage.SearchVectorToEntityByVector(ctx, storage.GetConn(), lo.Keys(vectorScoreMap))
		if err != nil {
			logs.CtxError(ctx, "[searchEntityAtLocalInternal] SearchEntityIDsByVectorIDs failed err is %s", err.Error())
			recallEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, LocalRecallErrStepGetEntities)
			return results, resp.EmbeddingModel, err
		}
		for _, vte := range vtes {
			entity, err := storage.GetEntityByEntityID(ctx, storage.GetConn(), vte.EntityID)
			if err != nil {
				continue
			}
			results = append(results, &entitySearchResult{
				Entity:       entity,
				Score:        vectorScoreMap[vte.VectorID],
				RecallOption: &recallOption{Type: searchTypeLocal, EmbeddingTag: recallTag},
			})
		}
	}
	{ // sqlite-vec
		entities, err := storage.GetEntitiesByEntityIDs(ctx, storage.GetConn(), lo.Keys(vectorScoreMap))
		if err != nil {
			logs.CtxWarn(ctx, "[searchEntityAtLocalInternal] GetEntitiesByEntityIDs failed, err: %v", err)
			recallEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, LocalRecallErrStepGetEntities)
			return results, resp.EmbeddingModel, err
		}
		for _, entity := range entities {
			results = append(results, &entitySearchResult{
				Entity:       entity,
				Score:        vectorScoreMap[entity.ID],
				RecallOption: &recallOption{Type: searchTypeLocal, EmbeddingTag: recallTag},
			})
		}
	}
	if len(results) == 0 {
		recallEvent.AddTeaParam(ckg_metrics.TeaParamLocalEmbeddingEmptySearchResult, true)
		logs.CtxWarn(ctx, "[searchEntityAtLocalInternal] empty entity search results")
	}

	// #Folder: 根据 relFolderPath，100% 召回对应 Folder 实体
	// TODO: 当 expectTypes 不为空且不包含 folder，还需要返回这个实体吗？
	if len(absFolderPaths) > 0 {
		entities, err := storage.GetEntitiesByEntityIDs(ctx, storage.GetConn(), absFolderPaths)
		if err != nil {
			logs.CtxWarn(ctx, "[searchEntityAtLocalInternal] GetEntitiesByEntityIDs failed, err: %v", err)
			recallEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, LocalRecallErrStepFolderEntityNotFoundInDB)
			return results, resp.EmbeddingModel, nil
		}
		for _, folderEntity := range entities {
			if folderEntity.Type != model.Folder {
				logs.CtxDebug(ctx, "[searchEntityAtLocalInternal] GetEntitiesByEntityIDs return non-folder entity: %v, type: %v", folderEntity.URI, folderEntity.Type)
				continue
			}
			results = append(results, &entitySearchResult{
				Entity:       folderEntity,
				Score:        1.0,
				RecallOption: &recallOption{Type: searchTypeLocal, EmbeddingTag: recallTag},
			})
		}
	}

	return results, resp.EmbeddingModel, nil
}

// searchWorkspaceEntityAtLocalInternal 在整个向量库纬度召回向量
func (s *service) searchWorkspaceEntityAtLocalInternal(ctx context.Context, embedding *model.Embedding,
	embeddingStorage data_storage.EmbeddingStorage, expectTypes []model.EntityType, entityNum int) ([]*model.EmbeddingQueryResult, error) {
	whereMetadata := map[string]any{string(model.EMK_EntityType): expectTypes}
	option := data_storage.NewEmbeddingQueryOptions().
		SetWhereMetadata(whereMetadata).
		Build()
	res, err := embeddingStorage.QueryByEmbedding(ctx, embedding, entityNum, option)
	if err != nil {
		logs.CtxError(ctx, "[searchWorkspaceEntityAtLocalInternal] QueryByEmbedding failed err is %s", err.Error())
		return make([]*model.EmbeddingQueryResult, 0), err
	}
	return res, nil
}

// searchFolderEntityAtLocalInternal 根据不同 folder 召回
func (s *service) searchFolderEntityAtLocalInternal(ctx context.Context, embedding *model.Embedding, storage data_storage.Storage,
	embeddingStorage data_storage.EmbeddingStorage, relFolderPaths []string, expectTypes []model.EntityType, entityNum int) ([]*model.EmbeddingQueryResult, error) {
	whereMetadata := map[string]any{
		string(model.EMK_RelPath):    relFolderPaths,
		string(model.EMK_EntityType): expectTypes,
	}
	option := data_storage.NewEmbeddingQueryOptions().
		SetWhereMetadata(whereMetadata).
		Build()
	embeddingQueryRes, err := embeddingStorage.QueryByEmbedding(ctx, embedding, entityNum, option)
	if err != nil {
		logs.CtxError(ctx, "QueryByEmbedding failed err is %s", err.Error())
		return []*model.EmbeddingQueryResult{}, err
	}
	if len(embeddingQueryRes) == 0 {
		cnt := embeddingStorage.EmbeddingDocumentCount(ctx)
		logs.CtxWarn(ctx, "[searchFolderEntityAtLocalInternal] embeddingQueryRes is empty, folders might be not existed (%v)", relFolderPaths)
		logs.CtxWarn(ctx, "[searchFolderEntityAtLocalInternal] embedding count: %d, storage id: %s, embedding storage is deleted: %v, storage is deleted: %v",
			cnt, storage.IsDeleted(), embeddingStorage.IsDeleted(), storage.IsDeleted())
	}
	return embeddingQueryRes, nil
}

func (s *service) searchEntityByAlias(ctx context.Context, storage data_storage.Storage,
	userMessage string, expectTypes []model.EntityType) ([]*entitySearchResult, error) {
	recallEvent := ckg_metrics.GetEvent(ctx)
	startTime := time.Now()

	rawSearchResults, err := storage.SearchEntityByQueryContainsName(ctx, storage.GetConn(), userMessage, 2)
	if err != nil {
		logs.CtxError(ctx, "SearchEntityByQueryContainsName err is %s", err)
		return nil, err
	}

	rawAliasResults, err := storage.SearchEntityByQueryContainsAlias(ctx, storage.GetConn(), userMessage, 2)
	if err != nil {
		logs.CtxError(ctx, "SearchEntityByQueryContainsAlias err is %s", err)
		return nil, err
	}

	searchResults := lo.Map(rawSearchResults, func(item *model.Entity, index int) *entitySearchResult {
		return &entitySearchResult{
			Entity: item,
			Score:  1,
			RecallOption: &recallOption{
				Type:         searchTypeAlias,
				NERTag:       -1,
				AliasTag:     knowledgebase.ContainContent,
				EmbeddingTag: -1,
			},
		}
	})

	aliasResults := lo.Map(rawAliasResults, func(item *model.Entity, index int) *entitySearchResult {
		return &entitySearchResult{
			Entity: item,
			Score:  1,
			RecallOption: &recallOption{
				Type:         searchTypeAlias,
				NERTag:       -1,
				AliasTag:     knowledgebase.ContainAlias,
				EmbeddingTag: -1,
			},
		}
	})

	recallEvent.AddParam(ckg_metrics.ParamAliasRecallResultEntities, map[string]interface{}{
		"query_contains_name_result_entities":  searchResults,
		"query_contains_alias_result_entities": aliasResults,
	})

	finalResult := lo.Filter(lo.Union(searchResults, aliasResults), func(item *entitySearchResult, index int) bool {
		return len(expectTypes) == 0 || lo.Contains(expectTypes, item.Entity.Type)
	})
	recallEvent.AddTeaParam(ckg_metrics.TeaParamAliasRecallNum, len(finalResult))
	recallEvent.AddTeaParam(ckg_metrics.TeaParamAliasRecallCost, time.Since(startTime).Milliseconds())

	return finalResult, nil
}

func matchEntityType(value string) (model.EntityType, bool) {
	switch value {
	case string(model.Clazz), "class":
		return model.Clazz, true
	case string(model.Method), "func":
		return model.Method, true
	case string(model.File):
		return model.File, true
	case string(model.Folder):
		return model.Folder, true
	default:
		return "", false
	}
}

func getNERRecallTag(entityType model.EntityType) knowledgebase.NerRecallTag {
	switch entityType {
	case model.File:
		return knowledgebase.NERFile
	case model.Clazz:
		return knowledgebase.NERClazz
	case model.Method:
		return knowledgebase.NERMethod
	case model.Folder:
		return knowledgebase.NERFolder
	case model.CodeChunk:
		return knowledgebase.NERCodeChunk
	default:
		return knowledgebase.NEROther
	}
}

func getNERCurRecallTag(entityType model.EntityType) knowledgebase.NerRecallTag {
	switch entityType {
	case model.File:
		return knowledgebase.NERCurFile
	case model.Clazz:
		return knowledgebase.NERCurClazz
	case model.Method:
		return knowledgebase.NERCurMethod
	case model.Folder:
		return knowledgebase.NERCurFolder
	case model.CodeChunk:
		return knowledgebase.NERCurCodeChunk
	default:
		return knowledgebase.NEROther
	}
}

func (s *service) searchEntityByNER(ctx context.Context, cli knowledgebase.Client, storage data_storage.Storage,
	userID, userMessage string, editorInfo *protocol.CurrentEditorInfo, expectTypes []model.EntityType) ([]*entitySearchResult, error) {
	recallEvent := ckg_metrics.GetEvent(ctx)
	startTime := time.Now()

	if userMessage == "" {
		return []*entitySearchResult{}, nil
	}

	response, err := cli.EntityDetect(ctx, env.GetToken(userID), &knowledgebase.EntityDetectRequest{
		UserInput: userMessage,
	})
	if err != nil {
		logs.CtxError(ctx, "EntityDetect err is %s", err)
		if util.JudgeNewWorkError(err) {
			recallEvent.AddTeaParam(ckg_metrics.TeaParamNERRecallErrCode, KnowledgebaseAPINetWorkErrCode)
		}
		return nil, errors.WithMessagef(err, "EntityDetect err is %s", err)
	}
	recallEvent.AddParam(ckg_metrics.ParamNERRecallRawResult, response)

	normalResult := make([]*entitySearchResult, 0)
	currentCandidates := make(map[model.EntityType]struct{}, 0)
	for _, item := range response.ResultEntities {
		name := item.Mention
		if name == "" {
			continue
		}

		typeString := item.Type
		entityType, ok := matchEntityType(typeString)
		if !ok {
			return nil, errors.WithMessagef(bizErr.ErrNotMatchEntityType, "matchEntityType err")
		}

		// 如果 ner 识别为当前文件/类/函数，特殊处理
		if name == nerCurrentName {
			if entityType == model.Folder {
				name = "."
			} else {
				currentCandidates[entityType] = struct{}{}
				continue
			}
		}

		searchResults, err := storage.SearchEntityByNameAndTypes(ctx, storage.GetConn(), name, []model.EntityType{entityType}, 5)
		if err != nil {
			logs.CtxError(ctx, "SearchEntityByNameAndType err")
			return nil, err
		}

		if len(searchResults) > 0 {
			for _, searchResult := range searchResults {
				normalResult = append(normalResult, &entitySearchResult{
					Entity: searchResult,
					RecallOption: &recallOption{
						Type:         searchTypeNER,
						NERTag:       getNERRecallTag(searchResult.Type),
						AliasTag:     -1,
						EmbeddingTag: -1,
					},
					Score: 1.0,
				})
			}

			continue
		}

		// 如果 id 和 name 没有匹配到，使用 alias 匹配
		aliasResults, err := storage.GetAllAliasEqualKeyWord(ctx, storage.GetConn(), name)
		if err != nil {
			logs.CtxError(ctx, "GetAllAliasEqualKeyWord err is %s", err)
			return nil, err
		}

		var selectedEntityIDs = make(map[string]bool)
		var selectedAlias = make(map[string]bool)
		for _, aliasResult := range aliasResults {
			if selectedEntityIDs[aliasResult.EntityID] {
				selectedAlias[aliasResult.Alias] = true
				continue
			}

			aliasIsContained := lo.ContainsBy(lo.Keys(selectedAlias), func(item string) bool {
				return strings.Contains(item, aliasResult.Alias)
			})
			if !aliasIsContained {
				selectedEntityIDs[aliasResult.EntityID] = true
				selectedAlias[aliasResult.Alias] = true
			}
		}

		var toEntity []*model.Entity
		for key, value := range selectedEntityIDs {
			if value {
				entity, err := storage.GetEntityByEntityID(ctx, storage.GetConn(), key)
				if err != nil {
					logs.CtxError(ctx, "GetEntityByEntityID err is %s", err)
					return nil, err
				}
				toEntity = append(toEntity, entity)
			}
		}

		for _, entity := range toEntity {
			normalResult = append(normalResult, &entitySearchResult{
				Entity: entity,
				RecallOption: &recallOption{
					Type:         searchTypeNER,
					NERTag:       getNERRecallTag(entity.Type),
					AliasTag:     -1,
					EmbeddingTag: -1,
				},
				Score: 1.0,
			})
		}
	}

	recallEvent.AddParam(ckg_metrics.ParamNERRecallNormalResultEntities, normalResult)

	logs.CtxInfo(ctx, "currentCandidates is %+v", currentCandidates)
	currentResult := make([]*entitySearchResult, 0)
	if editorInfo != nil {
		uriMeta, err := storage.GetURIMetaFromURIWithoutContent(ctx, storage.GetConn(), &model.URIStatus{
			AbsPath: editorInfo.FilePath,
		})
		if err == nil {
			allEntities, err := storage.GetAllEntityByURI(ctx, storage.GetConn(), uriMeta.ID)
			if err != nil {
				logs.CtxError(ctx, "GetAllEntityByURI err is %s", err)
				return nil, err
			}

			var startLine int32 = 0
			var endLine int32 = 0
			if editorInfo.SelectCodeRange != nil && editorInfo.SelectCodeRange.StartLine > 0 {
				startLine = editorInfo.SelectCodeRange.StartLine
				endLine = editorInfo.SelectCodeRange.EndLine
			} else if editorInfo.VisibleCodeRange != nil && editorInfo.VisibleCodeRange.StartLine > 0 {
				startLine = editorInfo.VisibleCodeRange.StartLine
				endLine = editorInfo.VisibleCodeRange.EndLine
			} else if editorInfo.CursorLine > 0 {
				startLine = editorInfo.CursorLine
				endLine = editorInfo.CursorLine
			}

			if uriMeta != nil {
				lo.ForEach(lo.Keys(currentCandidates), func(t model.EntityType, _ int) {
					switch t {
					case model.Clazz:
						if startLine > 0 && endLine >= startLine {
							currentResult = append(currentResult, searchSelectedEntities(lo.Filter(allEntities, func(item *model.Entity, _ int) bool {
								return item.Type == model.Clazz
							}), startLine, endLine)...)
						}
					case model.Method:
						if startLine > 0 && endLine >= startLine {
							currentResult = append(currentResult, searchSelectedEntities(lo.Filter(allEntities, func(item *model.Entity, _ int) bool {
								return item.Type == model.Method
							}), startLine, endLine)...)
						}
					case model.File:
						fileEntities := lo.Filter(allEntities, func(item *model.Entity, _ int) bool {
							return item.Type == model.File
						})
						currentResult = append(currentResult, lo.Map(fileEntities, func(item *model.Entity, _ int) *entitySearchResult {
							item.ContainsSelectedCode = true
							return &entitySearchResult{
								Entity: item,
								Score:  1.0,
								RecallOption: &recallOption{
									Type:         searchTypeNER,
									NERTag:       getNERCurRecallTag(item.Type),
									AliasTag:     -1,
									EmbeddingTag: -1,
								},
							}
						})...)
					}
				})
			}
		} else {
			logs.CtxInfo(ctx, "failed to get uri for current editor, err: %s", err)
		}
	}

	recallEvent.AddParam(ckg_metrics.ParamNERRecallCurrentResultEntities, currentResult)

	finalResult := lo.Filter(lo.Union(normalResult, currentResult), func(item *entitySearchResult, index int) bool {
		return len(expectTypes) == 0 || lo.Contains(expectTypes, item.Entity.Type)
	})
	recallEvent.AddTeaParam(ckg_metrics.TeaParamNERRecallNum, len(finalResult))
	recallEvent.AddTeaParam(ckg_metrics.TeaParamNERRecallCost, time.Since(startTime).Milliseconds())

	return finalResult, nil
}

func (s *service) uploadReasonWhyEmptyRecall(ctx context.Context, cli knowledgebase.Client, userID string, storage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage, absFolderPaths []string) string {
	recallEvent := ckg_metrics.GetEvent(ctx)
	// 空召回时，上报当前索引状态。
	projectId := storage.GetProjectID()
	var buildProgress float32 = 0.0
	if status, ok := s.dm.GetProjectsBuildStatus(ctx, &protocol.GetBuildStatusRequest{ProjectIds: []string{projectId}})[projectId]; ok {
		recallEvent.AddTeaParam(ckg_metrics.TeaParamIndexProgressWhenEmptyRecall, status.Progress)
		recallEvent.AddTeaParam(ckg_metrics.TeaParamEmptyRecallIndexStatus, status.Status.Number())
		buildProgress = status.Progress
	} else {
		const buildStatusNotFound = -1
		recallEvent.AddTeaParam(ckg_metrics.TeaParamIndexProgressWhenEmptyRecall, float32(buildStatusNotFound))
		recallEvent.AddTeaParam(ckg_metrics.TeaParamEmptyRecallIndexStatus, int32(buildStatusNotFound))
		buildProgress = buildStatusNotFound
	}
	vectorCnt := embeddingStorage.EmbeddingDocumentCount(ctx)
	entityCnt := storage.GetEntityCount(ctx, storage.GetConn())
	folderCnt := storage.GetFolderEntityCount(ctx, storage.GetConn())
	hashFolder := len(absFolderPaths) != 0
	if hashFolder && embeddingStorage.GetType() == consts.EmbeddingStorage_ChormemGo {
		recallEvent.AddTeaParam(ckg_metrics.TeaParamHashFolderButDBUnsupported, true)
		finalMsg := fmt.Sprintf("[uploadReasonWhyEmptyRecall] %s, progress: %v", EmptyRecallUseChromemGoFolder, buildProgress)
		logs.CtxError(ctx, finalMsg)
		return finalMsg
	}

	projectID := storage.GetProjectID()
	if s.dm.GetProjectType(ctx, projectID) != model.ProjectTypeVirtual {
		if emptyProject := s.is.IsNotEmptyFolder(ctx, projectID); emptyProject {
			recallEvent.AddTeaParam(ckg_metrics.TeaParamEmptyRecallSinceProjectEmpty, true)
			errMsg := fmt.Sprintf(EmptyRecallWithEmptyProject, projectID)
			finalMsg := fmt.Sprintf("[uploadReasonWhyEmptyRecall] %s, progress: %v", errMsg, buildProgress)
			logs.CtxError(ctx, finalMsg)
			return errMsg
		}
	}
	// 没有一个 folder 是 not empty => 所有 folder 都是 empty
	allFolderAreEmpty := lo.NoneBy(absFolderPaths, func(p string) bool { return s.is.IsNotEmptyFolder(ctx, p) })
	if hashFolder && allFolderAreEmpty {
		recallEvent.AddTeaParam(ckg_metrics.TeaParamHashFolderButAllFolderEmpty, true)
		errMsg := fmt.Sprintf(EmptyRecallWithAllEmptyFolder, absFolderPaths)
		finalMsg := fmt.Sprintf("[uploadReasonWhyEmptyRecall] %s, progress: %v", errMsg, buildProgress)
		logs.CtxError(ctx, finalMsg)
		return errMsg
	}
	if vectorCnt == 0 || entityCnt == 0 {
		if vectorCnt == 0 {
			// 仅向量库为空
			recallEvent.AddTeaParam(ckg_metrics.TeaParamEmptyRecallWithEmptyVecDB, true)
			finalMsg := fmt.Sprintf("[uploadReasonWhyEmptyRecall] %s, progress: %v", EmptyRecallWithEmptyVecDB, buildProgress)
			logs.CtxError(ctx, finalMsg)
			return finalMsg
		}
		if entityCnt == 0 {
			// 实体库为空
			recallEvent.AddTeaParam(ckg_metrics.TeaParamEmptyRecallWithEmptyEntityDB, true)
			finalMsg := fmt.Sprintf("[uploadReasonWhyEmptyRecall] %s, progress: %v", EmptyRecallWithEmptyEntityDB, buildProgress)
			logs.CtxError(ctx, finalMsg)
			return finalMsg
		}
	} else if folderCnt == entityCnt {
		// 实体库中全部实体都是 folder 类型实体，说明文件相关实体都未能成功写入
		recallEvent.AddTeaParam(ckg_metrics.TeaParamEmptyRecallSinceAllFolderEntity, true)
		finalMsg := fmt.Sprintf("[uploadReasonWhyEmptyRecall] %s, progress: %v", EmptyRecallAllEntityAreFolder, buildProgress)
		logs.CtxError(ctx, finalMsg)
		return finalMsg
	}
	// 当上面的条件都不成立时，说明其实是召回条件太苛刻导致的，比如：
	// #Folder 一个空文件夹，因此没有文件相关实体
	recallEvent.AddTeaParam(ckg_metrics.TeaParamEmptyRecallWithNarrowCondition, true)
	finalMsg := fmt.Sprintf("[uploadReasonWhyEmptyRecall] %s, progress: %v", EmptyRecallNarrowCondition, buildProgress)
	logs.CtxError(ctx, finalMsg)
	return finalMsg
}

func searchSelectedEntities(entities []*model.Entity, startLine, endLine int32) []*entitySearchResult {
	// 首先看有没有直接被选中的实体
	selected := lo.Filter(entities, func(item *model.Entity, _ int) bool {
		return startLine <= item.GetStartLine() && item.GetEndLine() <= endLine
	})
	if len(selected) > 0 {
		return lo.Map(selected, func(item *model.Entity, _ int) *entitySearchResult {
			return &entitySearchResult{
				Entity: item,
				RecallOption: &recallOption{
					Type:         searchTypeNER,
					NERTag:       getNERCurRecallTag(item.Type),
					AliasTag:     -1,
					EmbeddingTag: -1,
				},
				Score: 1.0,
			}
		})
	}

	// 然后是部分选中
	partialSelected := lo.Filter(entities, func(item *model.Entity, _ int) bool {
		coverStart := startLine <= item.GetStartLine() && item.GetStartLine() <= endLine
		coverEnd := startLine <= item.GetEndLine() && item.GetEndLine() <= endLine
		return coverStart || coverEnd
	})
	if len(partialSelected) > 0 {
		return lo.Map(partialSelected, func(item *model.Entity, _ int) *entitySearchResult {
			return &entitySearchResult{
				Entity: item,
				RecallOption: &recallOption{
					Type:         searchTypeNER,
					NERTag:       getNERCurRecallTag(item.Type),
					AliasTag:     -1,
					EmbeddingTag: -1,
				},
				Score: 1.0,
			}
		})
	}

	// 最后是包含选中代码的实体
	allSelected := lo.Filter(entities, func(item *model.Entity, _ int) bool {
		return item.GetStartLine() <= startLine && endLine <= item.GetEndLine()
	})
	if len(allSelected) > 0 {
		return lo.Map(allSelected, func(item *model.Entity, _ int) *entitySearchResult {
			return &entitySearchResult{
				Entity: item,
				RecallOption: &recallOption{
					Type:         searchTypeNER,
					NERTag:       getNERCurRecallTag(item.Type),
					AliasTag:     -1,
					EmbeddingTag: -1,
				},
				Score: 1.0,
			}
		})
	}

	return make([]*entitySearchResult, 0)
}
