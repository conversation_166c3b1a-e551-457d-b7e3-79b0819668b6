package env

import (
	"context"
	"encoding/json"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"ide/ckg/codekg/components/data_storage/consts"
	"ide/ckg/codekg/components/data_storage/user_storage"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/model"
)

const LocalSetUpEnvFile = "local_env.json"

var (
	LocalEmbedding    bool
	EmbeddingStorageT consts.EmbeddingStorageType

	// debug env
	DumpEmbeddingRequest            bool
	KeepVirtualProjectEntityContent bool
)

type setupEnv struct {
	Host     string `json:"host"`
	DeviceID string `json:"device_id"`
}

type env struct {
	setupEnv

	knowledgebaseID  string
	appID            string
	storagePath      string
	port             string
	limitCPU         int
	ideID            string
	ideVersion       string
	extensionVersion string
	versionCode      int64
	region           string
	isMerlin         bool
	// 风控参数
	deviceCPU   string
	deviceBrand string
	deviceType  string
	osVersion   string
	machineID   string

	sourceProduct        model.SourceProduct
	userMap              map[string]*model.TokenValue
	userKnowledgebaseMap map[string]string
	envStorage           *user_storage.UserStorage
	mu                   sync.RWMutex
}

var e = &env{
	setupEnv: setupEnv{
		Host:     "",
		DeviceID: "",
	},

	knowledgebaseID:      "", // 历史兼容
	appID:                "",
	storagePath:          "",
	port:                 "",
	limitCPU:             0,
	ideID:                "",
	ideVersion:           "",
	extensionVersion:     "",
	versionCode:          0,
	region:               "",
	isMerlin:             false,
	deviceCPU:            "",
	deviceBrand:          "",
	deviceType:           "",
	osVersion:            "",
	machineID:            "",
	sourceProduct:        model.SourceProductUnknown,
	userMap:              make(map[string]*model.TokenValue),
	userKnowledgebaseMap: make(map[string]string),
	mu:                   sync.RWMutex{},
}

func SetEnvStorage(envStorage *user_storage.UserStorage) {
	e.envStorage = envStorage
}

type Options struct {
	Host             string
	AppID            string
	StoragePath      string
	Port             string
	IdeVersion       string
	ExtensionVersion string
	VersionCode      int64
	Region           string
	LimitCPU         int
	SourceProduct    model.SourceProduct
	IdeID            string
	UserID           string
	IsMerlin         bool
}

func GetSourceProduct() model.SourceProduct {
	e.mu.RLock()
	defer e.mu.RUnlock()

	return e.sourceProduct
}

func SetSourceProduct(sourceProduct model.SourceProduct) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.sourceProduct = sourceProduct
}

func InitEnvValue(ctx context.Context, envParam Options) {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.Host = envParam.Host
	e.appID = envParam.AppID
	e.storagePath = envParam.StoragePath
	e.port = envParam.Port
	e.ideVersion = envParam.IdeVersion
	e.extensionVersion = envParam.ExtensionVersion
	e.versionCode = envParam.VersionCode
	e.region = envParam.Region
	e.limitCPU = envParam.LimitCPU
	e.sourceProduct = envParam.SourceProduct
	e.ideID = envParam.IdeID
	e.isMerlin = envParam.IsMerlin

	logs.CtxDebug(ctx, "init env value %+v", e)
}

func RefreshToken(ctx context.Context, tokenValue *model.TokenValue, userID string) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	logs.CtxInfo(ctx, "user token is updated, user is %s, token: %s", userID, tokenValue)
	e.userMap[userID] = tokenValue

	if e.envStorage == nil {
		envDB, err := user_storage.CreateEnvStorage(ctx, e.storagePath)
		if err != nil {
			logs.CtxError(ctx, "create env storage error: %s", err.Error())
			return err
		}
		e.envStorage = envDB
	}
	err := e.envStorage.UpsertUserInformation(ctx, e.envStorage.GetConn(), userID, &user_storage.StorageUserInformation{
		Token:  tokenValue.Scheme + " " + tokenValue.Token,
		UserID: userID,
	})
	if err != nil {
		logs.CtxWarn(ctx, "InsertUserIDAndToken err is %v", err)
		return err
	}
	return nil
}

func TokenIsOutdated(ctx context.Context, token *model.TokenValue) bool {
	if token == nil {
		return true
	}

	tokenObj, _, err := new(jwt.Parser).ParseUnverified(token.Token, jwt.MapClaims{})
	if err != nil {
		logs.CtxWarn(ctx, "ParseUnverified err is %v", err)
		return true
	}

	// 断言 MapClaims 部分
	if tokenClaims, ok := tokenObj.Claims.(jwt.MapClaims); ok {
		// 从 MapClaims 中获取 "exp" 字段作为过期时间，再与当前时间比较
		if v, ok := tokenClaims["exp"]; ok {
			exp := int64(v.(float64))
			if exp < time.Now().Add(time.Minute).Unix() {
				return true
			} else {
				return false
			}
		}

		if v, ok := tokenClaims["expireTime"]; ok {
			exp := int64(v.(float64))
			if exp < time.Now().Add(time.Minute).Unix() {
				return true
			} else {
				return false
			}
		}
		return true
	} else {
		logs.CtxWarn(ctx, "error tokenClaims")
		return true
	}
}

// 随机获取一个 ckg 用户，如果有的话
func GetUser() string {
	e.mu.RLock()
	defer e.mu.RUnlock()

	users := lo.Keys(e.userMap)
	if len(users) == 0 {
		return ""
	}

	return users[0]
}

func GetToken(userID string) *model.TokenValue {
	e.mu.RLock()
	defer e.mu.RUnlock()

	return e.userMap[userID]
}

func GetKnowledgebaseID(ctx context.Context, userID string) (string, error) {
	e.mu.Lock()
	defer e.mu.Unlock()

	if e.userKnowledgebaseMap[userID] != "" {
		return e.userKnowledgebaseMap[userID], nil
	}

	// e.envStorage 现在可能是 nil，懒加载
	if e.envStorage == nil {
		envDB, err := user_storage.CreateEnvStorage(ctx, e.storagePath)
		if err != nil {
			logs.CtxError(ctx, "create env storage error: %s", err.Error())
			return "", err
		}
		e.envStorage = envDB
	}
	// 读取数据库，获取 knowledgebase id 时已经有数据了
	info, err := e.envStorage.GetUserInformation(ctx, e.envStorage.GetConn(), userID)
	if err != nil {
		return "", errors.WithMessagef(err, "error get user information from db")
	}

	if info.KnowledgebaseID != "" {
		e.userKnowledgebaseMap[userID] = info.KnowledgebaseID
		return info.KnowledgebaseID, nil
	}

	knowledgebaseID, err := uuid.NewV7()
	if err != nil {
		return "", errors.WithMessagef(err, "error generate knowledgebase id")
	}

	err = e.envStorage.UpsertUserInformation(ctx, e.envStorage.GetConn(), userID, &user_storage.StorageUserInformation{
		UserID:          userID,
		KnowledgebaseID: knowledgebaseID.String(),
	})
	if err != nil {
		return "", errors.WithMessagef(err, "error update knowledgebase id")
	}

	e.userKnowledgebaseMap[userID] = knowledgebaseID.String()
	return knowledgebaseID.String(), nil
}

func GetIDEVersion() string {
	e.mu.RLock()
	defer e.mu.RUnlock()

	return e.ideVersion
}

func GetHost() string {
	e.mu.RLock()
	defer e.mu.RUnlock()

	if e.Host == "" {
		_ = loadLocalSetupEnv()
	}

	return e.Host
}

func SetHost(host string) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.Host = host
}

func GetAppID() string {
	e.mu.RLock()
	defer e.mu.RUnlock()

	return e.appID
}

func GetExtensionVersion() string {
	e.mu.RLock()
	defer e.mu.RUnlock()

	return e.extensionVersion
}

func GetVersionCode() int64 {
	e.mu.RLock()
	defer e.mu.RUnlock()

	return e.versionCode
}

func GetRegion() string {
	e.mu.RLock()
	defer e.mu.RUnlock()

	return e.region
}

func SetRegion(region string) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.region = region
}

func GetIDEID() string {
	e.mu.RLock()
	defer e.mu.RUnlock()

	return e.ideID
}

func GetEnvStorage(ctx context.Context) (*user_storage.UserStorage, error) {
	e.mu.Lock()
	defer e.mu.Unlock()
	if e.envStorage == nil {
		envDB, err := user_storage.CreateEnvStorage(ctx, e.storagePath)
		if err != nil {
			logs.CtxError(ctx, "create env storage error: %s", err.Error())
			return nil, errors.WithMessagef(err, "create env storage")
		}
		e.envStorage = envDB
	}
	return e.envStorage, nil
}

func GetStoragePath() string {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.storagePath
}

func SetDeviceCPU(deviceCPU string) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.deviceCPU = deviceCPU
}

func GetDeviceCPU() string {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.deviceCPU
}

func SetDeviceID(deviceID string) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.DeviceID = deviceID
}

func GetDeviceID() string {
	e.mu.RLock()
	defer e.mu.RUnlock()
	if e.DeviceID == "" {
		_ = loadLocalSetupEnv()
	}
	return e.DeviceID
}

func SetDeviceBrand(deviceBrand string) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.deviceBrand = deviceBrand
}

func GetDeviceBrand() string {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.deviceBrand
}

func SetDeviceType(deviceType string) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.deviceType = deviceType
}

func GetDeviceType() string {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.deviceType
}

func SetOSVersion(osVersion string) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.osVersion = osVersion
}

func GetOSVersion() string {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.osVersion
}

func SetMachineID(machineID string) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.machineID = machineID
}

func GetMachineID() string {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.machineID
}

func IsMerlin() bool {
	e.mu.Lock()
	defer e.mu.Unlock()
	return e.isMerlin
}

func SaveLocalSetupEnv() error {
	setupEnvFile := filepath.Join(e.storagePath, LocalSetUpEnvFile)
	originalSetupEnvData, err := os.ReadFile(setupEnvFile)
	if err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			return err
		}
	}
	var originalSetupEnv setupEnv
	_ = json.Unmarshal(originalSetupEnvData, &originalSetupEnv)

	envToSave := setupEnv{
		Host:     lo.Ternary(e.setupEnv.Host != "", e.setupEnv.Host, originalSetupEnv.Host),
		DeviceID: lo.Ternary(e.setupEnv.DeviceID != "", e.setupEnv.DeviceID, originalSetupEnv.DeviceID),
	}

	setupEnvData, err := json.Marshal(envToSave)
	if err != nil {
		return err
	}
	if err = os.WriteFile(setupEnvFile, setupEnvData, 0644); err != nil {
		return err
	}
	return nil
}

func loadLocalSetupEnv() error {
	setupEnvFile := filepath.Join(e.storagePath, LocalSetUpEnvFile)
	setupEnvData, err := os.ReadFile(setupEnvFile)
	if err != nil {
		return err
	}
	var setupEnv setupEnv
	if err = json.Unmarshal(setupEnvData, &setupEnv); err != nil {
		return err
	}
	e.setupEnv = setupEnv
	return nil
}
