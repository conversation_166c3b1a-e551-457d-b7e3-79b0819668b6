package version_feature

import (
	"fmt"
	"github.com/hashicorp/go-version"
	"testing"
)

func TestFeatureManager(t *testing.T) {
	fm := NewFeatureManager()

	withoutLocalEmbedding, _ := version.NewVersion("0.0.8")
	withLocalEmbedding, _ := version.NewVersion("0.0.11")
	withLocalEmbedding2, _ := version.NewVersion("0.0.13")
	withoutHashFolder, _ := version.NewVersion("0.0.17")
	withHashFolder, _ := version.NewVersion("0.0.24")
	withHashFolder2, _ := version.NewVersion("0.0.26")

	// 测试 GetRequiredAction
	testData := []struct {
		currVersion, originVersion *version.Version
		expectedActions            []VersionUpdateAction
	}{
		{
			originVersion:   withLocalEmbedding,
			currVersion:     withHashFolder,
			expectedActions: []VersionUpdateAction{ReIndexAction},
		},
		{
			originVersion:   withLocalEmbedding2,
			currVersion:     withHashFolder,
			expectedActions: []VersionUpdateAction{ReIndexAction},
		},
		{
			originVersion:   withLocalEmbedding,
			currVersion:     withoutHashFolder,
			expectedActions: []VersionUpdateAction{NoAction},
		},
		{
			originVersion:   withoutLocalEmbedding,
			currVersion:     withHashFolder,
			expectedActions: []VersionUpdateAction{ReIndexAction},
		},
		{
			originVersion:   withLocalEmbedding,
			currVersion:     withHashFolder2,
			expectedActions: []VersionUpdateAction{ReIndexAction},
		},
	}

	for _, data := range testData {
		fmt.Printf("%s -> %s\n", data.originVersion.String(), data.currVersion.String())
		actions := fm.GetRequiredAction(data.currVersion, data.originVersion)
		if len(actions) != len(data.expectedActions) {
			t.Errorf("Expected actions: %v, got: %v", data.expectedActions, actions)
		}
		if actions[0] != data.expectedActions[0] {
			t.Errorf("Expected actions: %v, got: %v", data.expectedActions, actions)
		}
	}
}
