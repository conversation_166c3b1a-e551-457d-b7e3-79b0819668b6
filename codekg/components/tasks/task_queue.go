package tasks

import (
	"ide/ckg/codekg/components/logs"
	"context"
	"github.com/adrianbrad/queue"
	"sync"
)

type taskWithPriority struct {
	priority int
	task     Task
}

type TaskQueue struct {
	impl   *queue.Priority[taskWithPriority]
	uriMap *sync.Map
	mu     *sync.Mutex
}

func NewTaskQueue() *TaskQueue {
	return &TaskQueue{
		impl: queue.NewPriority([]taskWithPriority{}, func(a, b taskWithPriority) bool {
			return a.priority > b.priority
		}),
		uriMap: new(sync.Map),
		mu:     new(sync.Mutex),
	}
}

func (q *TaskQueue) Len() int {
	q.mu.Lock()
	defer q.mu.Unlock()

	return q.impl.Size()
}

func (q *TaskQueue) Push(ctx context.Context, task Task, priority int) bool {
	q.mu.Lock()
	defer q.mu.Unlock()

	value, ok := q.uriMap.Load(task.ID())
	if ok && value.(bool) {
		return false
	}

	err := q.impl.Offer(taskWithPriority{
		priority: priority,
		task:     task,
	})
	if err != nil {
		logs.CtxWarn(ctx, "failed to push task to queue, err: %v", err)
		return false
	}

	q.uriMap.Store(task.ID(), true)
	return true
}

func (q *TaskQueue) Pop() Task {
	q.mu.Lock()
	defer q.mu.Unlock()

	if q.impl.IsEmpty() {
		return nil
	}

	task, err := q.impl.Get()
	if err != nil {
		logs.Warn("failed to get task from queue, err: %v", err)
		return nil
	}

	q.uriMap.Delete(task.task.ID())
	return task.task
}

func (q *TaskQueue) RemoveIf(predicate func(t Task) bool) (int, error) {
	q.mu.Lock()
	defer q.mu.Unlock()

	count := 0
	tasks := make([]taskWithPriority, 0)
	for !q.impl.IsEmpty() {
		task, _ := q.impl.Get()
		if predicate(task.task) {
			// this task is removed
			task.task.IsRemoved()
			q.uriMap.Delete(task.task.ID())
			count++
			continue
		}

		tasks = append(tasks, task)
	}

	for _, task := range tasks {
		_ = q.impl.Offer(task)
	}

	return count, nil
}
