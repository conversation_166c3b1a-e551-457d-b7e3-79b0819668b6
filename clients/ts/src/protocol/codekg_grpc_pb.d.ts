// GENERATED CODE -- DO NOT EDIT!

// package: protocol
// file: codekg.proto

import * as codekg_pb from "./codekg_pb";
import * as grpc from "@grpc/grpc-js";

interface ICodeKGService extends grpc.ServiceDefinition<grpc.UntypedServiceImplementation> {
  ping: grpc.MethodDefinition<codekg_pb.Empty, codekg_pb.Empty>;
  setUp: grpc.MethodDefinition<codekg_pb.SetUpRequest, codekg_pb.SetUpResponse>;
  init: grpc.MethodDefinition<codekg_pb.InitRequest, codekg_pb.InitResponse>;
  initVirtualProjects: grpc.MethodDefinition<codekg_pb.InitVirtualProjectsRequest, codekg_pb.InitVirtualProjectsResponse>;
  documentCreate: grpc.MethodDefinition<codekg_pb.DocumentRequest, codekg_pb.DocumentResponse>;
  documentChange: grpc.MethodDefinition<codekg_pb.DocumentRequest, codekg_pb.DocumentResponse>;
  documentDelete: grpc.MethodDefinition<codekg_pb.DocumentRequest, codekg_pb.DocumentResponse>;
  documentSelect: grpc.MethodDefinition<codekg_pb.DocumentRequest, codekg_pb.DocumentResponse>;
  cursorMove: grpc.MethodDefinition<codekg_pb.CursorMoveRequest, codekg_pb.Empty>;
  getBuildStatus: grpc.MethodDefinition<codekg_pb.GetBuildStatusRequest, codekg_pb.GetBuildStatusResponse>;
  getDocumentsIndexStatus: grpc.MethodDefinition<codekg_pb.GetDocumentsIndexStatusRequest, codekg_pb.GetDocumentsIndexStatusResponse>;
  cancelIndex: grpc.MethodDefinition<codekg_pb.CancelIndexRequest, codekg_pb.CancelIndexResponse>;
  deleteIndex: grpc.MethodDefinition<codekg_pb.DeleteIndexRequest, codekg_pb.DeleteIndexResponse>;
  retrieveRelation: grpc.MethodDefinition<codekg_pb.RetrieveRelationRequest, codekg_pb.RetrieveRelationResponse>;
  retrieveEntity: grpc.MethodDefinition<codekg_pb.RetrieveEntityRequest, codekg_pb.RetrieveEntityResponse>;
  retrieveRelevantSnippet: grpc.MethodDefinition<codekg_pb.RetrieveRelevantSnippetRequest, codekg_pb.RetrieveRelevantSnippetResponse>;
  rerankSnippet: grpc.MethodDefinition<codekg_pb.RerankSnippetRequest, codekg_pb.RerankSnippetResponse>;
  refreshToken: grpc.MethodDefinition<codekg_pb.RefreshTokenRequest, codekg_pb.RefreshTokenResponse>;
  isVersionMatched: grpc.MethodDefinition<codekg_pb.IsVersionMatchedRequest, codekg_pb.IsVersionMatchedResponse>;
  importAnalysis: grpc.MethodDefinition<codekg_pb.ImportAnalysisRequest, codekg_pb.ImportAnalysisResponse>;
  filesImportAnalysis: grpc.MethodDefinition<codekg_pb.FilesImportAnalysisRequest, codekg_pb.ImportAnalysisResponse>;
  searchCKGDB: grpc.MethodDefinition<codekg_pb.SearchCKGDBRequest, codekg_pb.SearchCKGDBResponse>;
  isCKGEnabledForNonWorkspaceScenario: grpc.MethodDefinition<codekg_pb.IsCKGEnabledForNonWorkspaceScenarioRequest, codekg_pb.IsCKGEnabledForNonWorkspaceScenarioResponse>;
}

export const CodeKGService: ICodeKGService;

export interface ICodeKGServer extends grpc.UntypedServiceImplementation {
  ping: grpc.handleUnaryCall<codekg_pb.Empty, codekg_pb.Empty>;
  setUp: grpc.handleUnaryCall<codekg_pb.SetUpRequest, codekg_pb.SetUpResponse>;
  init: grpc.handleUnaryCall<codekg_pb.InitRequest, codekg_pb.InitResponse>;
  initVirtualProjects: grpc.handleUnaryCall<codekg_pb.InitVirtualProjectsRequest, codekg_pb.InitVirtualProjectsResponse>;
  documentCreate: grpc.handleUnaryCall<codekg_pb.DocumentRequest, codekg_pb.DocumentResponse>;
  documentChange: grpc.handleUnaryCall<codekg_pb.DocumentRequest, codekg_pb.DocumentResponse>;
  documentDelete: grpc.handleUnaryCall<codekg_pb.DocumentRequest, codekg_pb.DocumentResponse>;
  documentSelect: grpc.handleUnaryCall<codekg_pb.DocumentRequest, codekg_pb.DocumentResponse>;
  cursorMove: grpc.handleUnaryCall<codekg_pb.CursorMoveRequest, codekg_pb.Empty>;
  getBuildStatus: grpc.handleUnaryCall<codekg_pb.GetBuildStatusRequest, codekg_pb.GetBuildStatusResponse>;
  getDocumentsIndexStatus: grpc.handleUnaryCall<codekg_pb.GetDocumentsIndexStatusRequest, codekg_pb.GetDocumentsIndexStatusResponse>;
  cancelIndex: grpc.handleUnaryCall<codekg_pb.CancelIndexRequest, codekg_pb.CancelIndexResponse>;
  deleteIndex: grpc.handleUnaryCall<codekg_pb.DeleteIndexRequest, codekg_pb.DeleteIndexResponse>;
  retrieveRelation: grpc.handleUnaryCall<codekg_pb.RetrieveRelationRequest, codekg_pb.RetrieveRelationResponse>;
  retrieveEntity: grpc.handleUnaryCall<codekg_pb.RetrieveEntityRequest, codekg_pb.RetrieveEntityResponse>;
  retrieveRelevantSnippet: grpc.handleUnaryCall<codekg_pb.RetrieveRelevantSnippetRequest, codekg_pb.RetrieveRelevantSnippetResponse>;
  rerankSnippet: grpc.handleUnaryCall<codekg_pb.RerankSnippetRequest, codekg_pb.RerankSnippetResponse>;
  refreshToken: grpc.handleUnaryCall<codekg_pb.RefreshTokenRequest, codekg_pb.RefreshTokenResponse>;
  isVersionMatched: grpc.handleUnaryCall<codekg_pb.IsVersionMatchedRequest, codekg_pb.IsVersionMatchedResponse>;
  importAnalysis: grpc.handleUnaryCall<codekg_pb.ImportAnalysisRequest, codekg_pb.ImportAnalysisResponse>;
  filesImportAnalysis: grpc.handleUnaryCall<codekg_pb.FilesImportAnalysisRequest, codekg_pb.ImportAnalysisResponse>;
  searchCKGDB: grpc.handleUnaryCall<codekg_pb.SearchCKGDBRequest, codekg_pb.SearchCKGDBResponse>;
  isCKGEnabledForNonWorkspaceScenario: grpc.handleUnaryCall<codekg_pb.IsCKGEnabledForNonWorkspaceScenarioRequest, codekg_pb.IsCKGEnabledForNonWorkspaceScenarioResponse>;
}

export class CodeKGClient extends grpc.Client {
  constructor(address: string, credentials: grpc.ChannelCredentials, options?: object);
  ping(argument: codekg_pb.Empty, callback: grpc.requestCallback<codekg_pb.Empty>): grpc.ClientUnaryCall;
  ping(argument: codekg_pb.Empty, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.Empty>): grpc.ClientUnaryCall;
  ping(argument: codekg_pb.Empty, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.Empty>): grpc.ClientUnaryCall;
  setUp(argument: codekg_pb.SetUpRequest, callback: grpc.requestCallback<codekg_pb.SetUpResponse>): grpc.ClientUnaryCall;
  setUp(argument: codekg_pb.SetUpRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.SetUpResponse>): grpc.ClientUnaryCall;
  setUp(argument: codekg_pb.SetUpRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.SetUpResponse>): grpc.ClientUnaryCall;
  init(argument: codekg_pb.InitRequest, callback: grpc.requestCallback<codekg_pb.InitResponse>): grpc.ClientUnaryCall;
  init(argument: codekg_pb.InitRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.InitResponse>): grpc.ClientUnaryCall;
  init(argument: codekg_pb.InitRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.InitResponse>): grpc.ClientUnaryCall;
  initVirtualProjects(argument: codekg_pb.InitVirtualProjectsRequest, callback: grpc.requestCallback<codekg_pb.InitVirtualProjectsResponse>): grpc.ClientUnaryCall;
  initVirtualProjects(argument: codekg_pb.InitVirtualProjectsRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.InitVirtualProjectsResponse>): grpc.ClientUnaryCall;
  initVirtualProjects(argument: codekg_pb.InitVirtualProjectsRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.InitVirtualProjectsResponse>): grpc.ClientUnaryCall;
  documentCreate(argument: codekg_pb.DocumentRequest, callback: grpc.requestCallback<codekg_pb.DocumentResponse>): grpc.ClientUnaryCall;
  documentCreate(argument: codekg_pb.DocumentRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.DocumentResponse>): grpc.ClientUnaryCall;
  documentCreate(argument: codekg_pb.DocumentRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.DocumentResponse>): grpc.ClientUnaryCall;
  documentChange(argument: codekg_pb.DocumentRequest, callback: grpc.requestCallback<codekg_pb.DocumentResponse>): grpc.ClientUnaryCall;
  documentChange(argument: codekg_pb.DocumentRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.DocumentResponse>): grpc.ClientUnaryCall;
  documentChange(argument: codekg_pb.DocumentRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.DocumentResponse>): grpc.ClientUnaryCall;
  documentDelete(argument: codekg_pb.DocumentRequest, callback: grpc.requestCallback<codekg_pb.DocumentResponse>): grpc.ClientUnaryCall;
  documentDelete(argument: codekg_pb.DocumentRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.DocumentResponse>): grpc.ClientUnaryCall;
  documentDelete(argument: codekg_pb.DocumentRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.DocumentResponse>): grpc.ClientUnaryCall;
  documentSelect(argument: codekg_pb.DocumentRequest, callback: grpc.requestCallback<codekg_pb.DocumentResponse>): grpc.ClientUnaryCall;
  documentSelect(argument: codekg_pb.DocumentRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.DocumentResponse>): grpc.ClientUnaryCall;
  documentSelect(argument: codekg_pb.DocumentRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.DocumentResponse>): grpc.ClientUnaryCall;
  cursorMove(argument: codekg_pb.CursorMoveRequest, callback: grpc.requestCallback<codekg_pb.Empty>): grpc.ClientUnaryCall;
  cursorMove(argument: codekg_pb.CursorMoveRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.Empty>): grpc.ClientUnaryCall;
  cursorMove(argument: codekg_pb.CursorMoveRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.Empty>): grpc.ClientUnaryCall;
  getBuildStatus(argument: codekg_pb.GetBuildStatusRequest, callback: grpc.requestCallback<codekg_pb.GetBuildStatusResponse>): grpc.ClientUnaryCall;
  getBuildStatus(argument: codekg_pb.GetBuildStatusRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.GetBuildStatusResponse>): grpc.ClientUnaryCall;
  getBuildStatus(argument: codekg_pb.GetBuildStatusRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.GetBuildStatusResponse>): grpc.ClientUnaryCall;
  getDocumentsIndexStatus(argument: codekg_pb.GetDocumentsIndexStatusRequest, callback: grpc.requestCallback<codekg_pb.GetDocumentsIndexStatusResponse>): grpc.ClientUnaryCall;
  getDocumentsIndexStatus(argument: codekg_pb.GetDocumentsIndexStatusRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.GetDocumentsIndexStatusResponse>): grpc.ClientUnaryCall;
  getDocumentsIndexStatus(argument: codekg_pb.GetDocumentsIndexStatusRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.GetDocumentsIndexStatusResponse>): grpc.ClientUnaryCall;
  cancelIndex(argument: codekg_pb.CancelIndexRequest, callback: grpc.requestCallback<codekg_pb.CancelIndexResponse>): grpc.ClientUnaryCall;
  cancelIndex(argument: codekg_pb.CancelIndexRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.CancelIndexResponse>): grpc.ClientUnaryCall;
  cancelIndex(argument: codekg_pb.CancelIndexRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.CancelIndexResponse>): grpc.ClientUnaryCall;
  deleteIndex(argument: codekg_pb.DeleteIndexRequest, callback: grpc.requestCallback<codekg_pb.DeleteIndexResponse>): grpc.ClientUnaryCall;
  deleteIndex(argument: codekg_pb.DeleteIndexRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.DeleteIndexResponse>): grpc.ClientUnaryCall;
  deleteIndex(argument: codekg_pb.DeleteIndexRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.DeleteIndexResponse>): grpc.ClientUnaryCall;
  retrieveRelation(argument: codekg_pb.RetrieveRelationRequest, callback: grpc.requestCallback<codekg_pb.RetrieveRelationResponse>): grpc.ClientUnaryCall;
  retrieveRelation(argument: codekg_pb.RetrieveRelationRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.RetrieveRelationResponse>): grpc.ClientUnaryCall;
  retrieveRelation(argument: codekg_pb.RetrieveRelationRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.RetrieveRelationResponse>): grpc.ClientUnaryCall;
  retrieveEntity(argument: codekg_pb.RetrieveEntityRequest, callback: grpc.requestCallback<codekg_pb.RetrieveEntityResponse>): grpc.ClientUnaryCall;
  retrieveEntity(argument: codekg_pb.RetrieveEntityRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.RetrieveEntityResponse>): grpc.ClientUnaryCall;
  retrieveEntity(argument: codekg_pb.RetrieveEntityRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.RetrieveEntityResponse>): grpc.ClientUnaryCall;
  retrieveRelevantSnippet(argument: codekg_pb.RetrieveRelevantSnippetRequest, callback: grpc.requestCallback<codekg_pb.RetrieveRelevantSnippetResponse>): grpc.ClientUnaryCall;
  retrieveRelevantSnippet(argument: codekg_pb.RetrieveRelevantSnippetRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.RetrieveRelevantSnippetResponse>): grpc.ClientUnaryCall;
  retrieveRelevantSnippet(argument: codekg_pb.RetrieveRelevantSnippetRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.RetrieveRelevantSnippetResponse>): grpc.ClientUnaryCall;
  rerankSnippet(argument: codekg_pb.RerankSnippetRequest, callback: grpc.requestCallback<codekg_pb.RerankSnippetResponse>): grpc.ClientUnaryCall;
  rerankSnippet(argument: codekg_pb.RerankSnippetRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.RerankSnippetResponse>): grpc.ClientUnaryCall;
  rerankSnippet(argument: codekg_pb.RerankSnippetRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.RerankSnippetResponse>): grpc.ClientUnaryCall;
  refreshToken(argument: codekg_pb.RefreshTokenRequest, callback: grpc.requestCallback<codekg_pb.RefreshTokenResponse>): grpc.ClientUnaryCall;
  refreshToken(argument: codekg_pb.RefreshTokenRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.RefreshTokenResponse>): grpc.ClientUnaryCall;
  refreshToken(argument: codekg_pb.RefreshTokenRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.RefreshTokenResponse>): grpc.ClientUnaryCall;
  isVersionMatched(argument: codekg_pb.IsVersionMatchedRequest, callback: grpc.requestCallback<codekg_pb.IsVersionMatchedResponse>): grpc.ClientUnaryCall;
  isVersionMatched(argument: codekg_pb.IsVersionMatchedRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.IsVersionMatchedResponse>): grpc.ClientUnaryCall;
  isVersionMatched(argument: codekg_pb.IsVersionMatchedRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.IsVersionMatchedResponse>): grpc.ClientUnaryCall;
  importAnalysis(argument: codekg_pb.ImportAnalysisRequest, callback: grpc.requestCallback<codekg_pb.ImportAnalysisResponse>): grpc.ClientUnaryCall;
  importAnalysis(argument: codekg_pb.ImportAnalysisRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.ImportAnalysisResponse>): grpc.ClientUnaryCall;
  importAnalysis(argument: codekg_pb.ImportAnalysisRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.ImportAnalysisResponse>): grpc.ClientUnaryCall;
  filesImportAnalysis(argument: codekg_pb.FilesImportAnalysisRequest, callback: grpc.requestCallback<codekg_pb.ImportAnalysisResponse>): grpc.ClientUnaryCall;
  filesImportAnalysis(argument: codekg_pb.FilesImportAnalysisRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.ImportAnalysisResponse>): grpc.ClientUnaryCall;
  filesImportAnalysis(argument: codekg_pb.FilesImportAnalysisRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.ImportAnalysisResponse>): grpc.ClientUnaryCall;
  searchCKGDB(argument: codekg_pb.SearchCKGDBRequest, callback: grpc.requestCallback<codekg_pb.SearchCKGDBResponse>): grpc.ClientUnaryCall;
  searchCKGDB(argument: codekg_pb.SearchCKGDBRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.SearchCKGDBResponse>): grpc.ClientUnaryCall;
  searchCKGDB(argument: codekg_pb.SearchCKGDBRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.SearchCKGDBResponse>): grpc.ClientUnaryCall;
  isCKGEnabledForNonWorkspaceScenario(argument: codekg_pb.IsCKGEnabledForNonWorkspaceScenarioRequest, callback: grpc.requestCallback<codekg_pb.IsCKGEnabledForNonWorkspaceScenarioResponse>): grpc.ClientUnaryCall;
  isCKGEnabledForNonWorkspaceScenario(argument: codekg_pb.IsCKGEnabledForNonWorkspaceScenarioRequest, metadataOrOptions: grpc.Metadata | grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.IsCKGEnabledForNonWorkspaceScenarioResponse>): grpc.ClientUnaryCall;
  isCKGEnabledForNonWorkspaceScenario(argument: codekg_pb.IsCKGEnabledForNonWorkspaceScenarioRequest, metadata: grpc.Metadata | null, options: grpc.CallOptions | null, callback: grpc.requestCallback<codekg_pb.IsCKGEnabledForNonWorkspaceScenarioResponse>): grpc.ClientUnaryCall;
}
