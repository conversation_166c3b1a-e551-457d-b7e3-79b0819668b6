import express, {Request, Response} from 'express';
import {
  handleGetDefinitionForRange,
  handleGetDefinitionNodeRange,
  handleInitAnalyzer,
  handleInitTreeSitterParser,
  updateInitInfo,
} from "./handler";
import {localLogger} from "./log";

const app = express();
app.use(express.json());

app.post('/init_analyzer', async (req: Request, res: Response): Promise<void> => {
  const {workspaceFolder, lspPort, language} = req.body;
  if (!workspaceFolder || !lspPort || !language) {
    res.status(400).send('(workspaceFolder | lspPort | language) is required');
    return;
  }

  updateInitInfo(lspPort, language);

  await handleInitAnalyzer(workspaceFolder);
  res.sendStatus(200);
});

app.post('/init_tree_sitter_parser', async (req: Request, res: Response): Promise<void> => {
  await handleInitTreeSitterParser();
  res.sendStatus(200);
});

app.post('/get_definition_for_range', async (req: Request, res: Response): Promise<void> => {
  const {file, language, start, end} = req.body;
  if (!file || !language) {
    res.status(400).send('file and language is required');
    return;
  }

  const result = await handleGetDefinitionForRange(file, language, start, end);
  res.status(200).send(result);
});

app.post('/get_definition_node_range', async (req: Request, res: Response): Promise<void> => {
  const {file, language, range} = req.body;
  if (!file || !language || !range) {
    res.status(400).send('file, language and range is required');
    return;
  }

  const result = await handleGetDefinitionNodeRange(file, language, range);
  res.status(200).send(result);
});

export function startServer(port: number): void {
  app.listen(port, () => {
    localLogger.info(`Server listening on port ${port}`);
  });
}