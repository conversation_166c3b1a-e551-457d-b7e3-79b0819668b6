package main

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	"ide/ckg/clients/ts"
	"ide/ckg/codekg/components/analysis"
	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/data_manager"
	"ide/ckg/codekg/components/data_storage/user_storage"
	"ide/ckg/codekg/components/env"
	bizErr "ide/ckg/codekg/components/error"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/query_service"
	"ide/ckg/codekg/components/validator"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/protocol"
	"ide/ckg/codekg/util"

	"ide/ckg/codekg/components/logs"

	"github.com/gogo/protobuf/jsonpb"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type server struct {
	protocol.UnimplementedCodeKGServer

	queryService query_service.QueryService
	dataManager  data_manager.DataManager
	packageInfo  *ts.Package
	config       *ckg_config.Config

	requestChan chan string
}

func newCodeKGServer(dm data_manager.DataManager, qs query_service.QueryService, p *ts.Package, config *ckg_config.Config, userID string) *server {
	requestChan := make(chan string, 10)

	ctx := util.NewBackgroundContext("")
	// 单独启一个 goroutine，避免同步请求 bytegate API 超时对 CKG 启动速度造成影响
	util.SafeGo(ctx, func() {
		if !config.IsFeatureEnabled(ctx, userID, ckg_config.ExitWhenPPIDChanged) {
			return
		}
		logs.CtxInfo(ctx, "exit when ppid changed is enabled")
		checkPPIDPeriod := time.Second * 30
		tick := time.NewTicker(checkPPIDPeriod)

		ppid := os.Getppid()
		if runtime.GOOS == "windows" {
			ideManagerPPIDStr := os.Getenv("ICUBE_MANAGER_PID")
			if ideManagerPPIDStr != "" {
				ideManagerPPID, err := strconv.Atoi(ideManagerPPIDStr)
				if err == nil {
					cmd := exec.Command("tasklist", "/NH", "/FI", fmt.Sprintf("PID eq %d", ideManagerPPID))
					output, err := cmd.Output()
					if err == nil && strings.Contains(string(output), ideManagerPPIDStr) {
						ppid = ideManagerPPID
						logs.Info("windows ppid is: %d", ppid)
					}
				}
			}
		}

		for {
			select {
			case <-tick.C:
				logs.Info("start to check if ckg needs to exit")
				shouldExit := false
				if runtime.GOOS == "windows" {
					cmd := exec.Command("tasklist", "/NH", "/FI", fmt.Sprintf("PID eq %d", ppid))
					output, err := cmd.Output()
					if err != nil {
						logs.CtxError(ctx, "failed to get ppid, err: %v", err)
					} else {
						shouldExit = !strings.Contains(string(output), fmt.Sprintf("%d", ppid))
					}
				} else {
					currentPPID := os.Getppid()
					shouldExit = currentPPID != ppid
				}
				if shouldExit {
					logs.Info("ppid changed or killed, exit ckg process")
					logs.Flush()
					event := ckg_metrics.NewEvent(
						ckg_metrics.EventNameCKGExit,
						userID,
						"",
						string(env.GetSourceProduct()),
					)
					event.AddTeaParam(ckg_metrics.TeaParamExitPPID, ppid)
					event.ReportSync(ctx, false)
					os.Exit(0)
				}
				tick.Stop()
				tick = time.NewTicker(checkPPIDPeriod)
			case <-requestChan:
				tick.Reset(checkPPIDPeriod)
			}
		}
	})

	return &server{
		dataManager:  dm,
		queryService: qs,
		packageInfo:  p,
		config:       config,
		requestChan:  requestChan,
	}
}

func (s *server) requestReceived(request string) {
	go func() {
		s.requestChan <- request
	}()
}

func (s *server) IsVersionMatched(ctx context.Context, in *protocol.IsVersionMatchedRequest) (response *protocol.IsVersionMatchedResponse, resultErr error) {
	logs.Info("Check version code start, req: %v", in)
	s.requestReceived("IsVersionMatched")

	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "IsVersionMatched")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "IsVersionMatched panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.IsVersionMatchedResponse{Code: protocol.Code_panic, Error: err}
		}
	}()

	if in.GetVersion() != s.packageInfo.GetVersionCode() {
		return &protocol.IsVersionMatchedResponse{
			Matched: false,
		}, nil
	}

	return &protocol.IsVersionMatchedResponse{
		Matched: true,
	}, nil
}

func (s *server) Ping(ctx context.Context, in *protocol.Empty) (*protocol.Empty, error) {
	s.requestReceived("Ping")
	return &protocol.Empty{}, nil
}

func migrateIndexData(ctx context.Context, projects []*protocol.Project) {
	projectStorages := make([]*protocol.Project, 0)
	for _, p := range projects {
		if p.StoragePath == "" || p.StoragePath == env.GetStoragePath() {
			continue
		}
		projectStorages = append(projectStorages, p)
	}
	envStorage, err := env.GetEnvStorage(ctx)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	for _, p := range projectStorages {
		// 如果 env db 可打开 && 可获取所有 projects，再写入到新 db 中。
		// 但 upsert 前需注意：要写入的 project 是否已存在于新 db 中。
		oldEnvStorage, storageFilePath, ok := user_storage.TryOpenEnvStorage(ctx, p.StoragePath)
		if ok {
			projectInfos, err := oldEnvStorage.ListAllProjects(ctx, oldEnvStorage.GetConn())
			if err == nil {
				for _, info := range projectInfos {
					_, err = envStorage.GetProjectIDByProjectPath(ctx, envStorage.GetConn(), p.ProjectId)
					if err != nil {
						// First 报错，说明不存在，写入
						_ = envStorage.UpsertProjectInfo(ctx, envStorage.GetConn(), info)
					}
				}
			}
		}

		// Windows 上可能不一定能删掉，也可保留，但后续不再使用该 db。
		if err := os.Remove(storageFilePath); err != nil {
			logs.CtxError(ctx, "[migrateIndexData] remove file err: %+v, file storage: %s", err, storageFilePath)
		}
	}
}

func (s *server) Init(ctx context.Context, req *protocol.InitRequest) (response *protocol.InitResponse, resultErr error) {
	logs.Info("Init repo request start, req: %v", req)
	s.requestReceived("Init")
	defer func() {
		if rec := recover(); rec != nil {
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "Init panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			logs.Info("init is panic, err is %+v", err)
			response = &protocol.InitResponse{Code: protocol.Code_panic, Error: err}
		}
	}()

	if len(req.UserId) == 0 {
		return &protocol.InitResponse{
			Code: protocol.Code_user_id_is_missing,
			Error: &protocol.Error{
				Message: "empty user id",
			}}, nil
	}

	cli, err := knowledgebase.GetClient()
	if err != nil {
		return &protocol.InitResponse{
			Code: protocol.Code_nil_client,
			Error: &protocol.Error{
				Message: err.Error(),
			},
		}, nil
	}

	initReqEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGInitReq, req.UserId, "", string(env.GetSourceProduct()))
	defer initReqEvent.Report(ctx, false)
	initReqEvent.AddTeaParam(ckg_metrics.TeaParamInitProjectNum, len(req.ProjectIds))
	initReqEvent.AddTeaParam(ckg_metrics.TeaParamInitIgnoreFileLimit, false)

	projects := make([]*protocol.Project, 0)
	projects = append(projects, lo.Map(req.ProjectIds, func(item string, _ int) *protocol.Project {
		return &protocol.Project{
			ProjectId:   item,
			StoragePath: env.GetStoragePath(),
			IgnoreFile:  "",
		}
	})...)
	projects = append(projects, req.Projects...)
	// 兼容老数据，storage_path 中如果有 codekg_env.db，将数据迁移到全局的 codekg_env.db 中
	migrateIndexData(ctx, projects)

	for _, p := range projects {
		err := s.dataManager.InitProject(ctx, cli, &data_manager.InitProjectOption{
			ProjectID:            p.ProjectId,
			StoragePath:          p.StoragePath,
			UserID:               req.UserId,
			IgnoreFileLimit:      false,
			CustomizedIgnoreFile: p.IgnoreFile,
			ProjectType:          int32(model.ProjectTypeDefault),
			ProjectURI:           util.GetCanonicalURIFromURIOrPath(p.ProjectId, false),
		})
		if err != nil {
			if errors.Is(err, bizErr.ErrFileCountExceed) {
				// 不需日志，InitProject 抛出该 err 的 basic block 已经输出具备特征的日志
				return &protocol.InitResponse{
					Code: protocol.Code_file_limit_exceed,
				}, nil
			}

			logs.CtxError(ctx, "[Init] InitProject failed project is %s err is %+v", p.ProjectId, err)
			respErr := &protocol.Error{
				Message: err.Error(),
				Stack:   "",
			}
			return &protocol.InitResponse{Code: protocol.Code_unknown_error, Error: respErr}, nil
		}
	}

	return &protocol.InitResponse{Code: protocol.Code_succeed, Error: nil}, nil
}

func (s *server) InitVirtualProjects(ctx context.Context, req *protocol.InitVirtualProjectsRequest) (response *protocol.InitVirtualProjectsResponse, resultErr error) {
	logs.Info("InitVirtualProjects request start, req: %v", req)
	s.requestReceived("InitVirtualProjects")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "InitVirtualProjects")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "InitVirtualProjects panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.InitVirtualProjectsResponse{Code: protocol.Code_panic, Error: err}
		}
	}()

	if len(req.UserId) == 0 {
		return &protocol.InitVirtualProjectsResponse{
			Code: protocol.Code_user_id_is_missing,
			Error: &protocol.Error{
				Message: "empty user id",
			}}, nil
	}

	cli, err := knowledgebase.GetClient()
	if err != nil {
		return &protocol.InitVirtualProjectsResponse{
			Code: protocol.Code_nil_client,
			Error: &protocol.Error{
				Message: err.Error(),
			},
		}, nil
	}
	for _, p := range req.Projects {
		err := s.dataManager.InitVirtualProject(ctx, cli, p.ProjectId, p.Uri, req.UserId, req.LoadFilesFromFs, p.RelativeGlobsToLoad)
		if err != nil {
			logs.CtxError(ctx, "InitVirtualProject failed project is %s err is %+v", p.ProjectId, err)
		}
	}
	return &protocol.InitVirtualProjectsResponse{Code: protocol.Code_succeed, Error: nil}, nil
}

func (s *server) DocumentCreate(ctx context.Context, req *protocol.DocumentRequest) (response *protocol.DocumentResponse, resultErr error) {
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "DocumentCreate")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "DocumentCreate panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.DocumentResponse{Code: protocol.Code_panic, Error: err}
		}
	}()

	documentsToPrint := make([]model.Document, 0, len(req.Documents))
	for _, doc := range req.Documents {
		documentsToPrint = append(documentsToPrint, model.Document{
			Uri:       doc.Uri,
			Name:      doc.Name,
			Content:   fmt.Sprintf("(%d) chars", len(doc.Content)),
			ProjectID: doc.ProjectId,
		})
	}

	logs.Info("DocumentCreate request start, documents: %v, userID: %s, req filePaths size: %d, files: %v", documentsToPrint, req.UserID, len(req.GetFilePaths()), validator.LogFilePath(req.FilePaths))
	s.requestReceived("DocumentCreate")

	if len(req.UserID) == 0 {
		return &protocol.DocumentResponse{
			Code: protocol.Code_user_id_is_missing,
			Error: &protocol.Error{
				Message: "empty user id",
			}}, nil
	}

	cli, err := knowledgebase.GetClient()
	if err != nil {
		return &protocol.DocumentResponse{
			Code: protocol.Code_nil_client,
			Error: &protocol.Error{
				Message: err.Error(),
			},
		}, nil
	}

	err = s.dataManager.FileCreated(ctx, cli, lo.Map(req.FilePaths, func(item string, index int) string {
		return filepath.Clean(item)
	}), req.UserID, lo.Map(req.Documents, func(item *protocol.Document, _ int) model.Document {
		return model.Document{
			Uri:       item.Uri,
			Name:      item.Name,
			Content:   item.Content,
			ProjectID: item.ProjectId,
		}
	}))
	if err != nil {
		logs.CtxError(ctx, "FileCreated err is %v", err)
		respErr := &protocol.Error{
			Message: err.Error(),
			Stack:   "",
		}
		return &protocol.DocumentResponse{Code: protocol.Code_unknown_error, Error: respErr}, nil
	}
	return &protocol.DocumentResponse{Code: protocol.Code_succeed, Error: nil}, nil
}

func (s *server) DocumentChange(ctx context.Context, req *protocol.DocumentRequest) (response *protocol.DocumentResponse, resultErr error) {
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "DocumentChange")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "DocumentChange panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.DocumentResponse{Code: protocol.Code_panic, Error: err}
		}
	}()

	documentsToPrint := make([]model.Document, 0, len(req.Documents))
	for _, doc := range req.Documents {
		documentsToPrint = append(documentsToPrint, model.Document{
			Uri:       doc.Uri,
			Name:      doc.Name,
			Content:   fmt.Sprintf("(%d) chars", len(doc.Content)),
			ProjectID: doc.ProjectId,
		})
	}

	logs.Info("DocumentChange request start, documents: %v, userID: %s, req filePaths size: %d, files: %v", documentsToPrint, req.UserID, len(req.FilePaths), validator.LogFilePath(req.FilePaths))
	s.requestReceived("DocumentChange")

	if len(req.UserID) == 0 {
		return &protocol.DocumentResponse{
			Code: protocol.Code_user_id_is_missing,
			Error: &protocol.Error{
				Message: "empty user id",
			}}, nil
	}

	cli, err := knowledgebase.GetClient()
	if err != nil {
		return &protocol.DocumentResponse{
			Code: protocol.Code_nil_client,
			Error: &protocol.Error{
				Message: err.Error(),
			},
		}, nil
	}

	logs.CtxInfo(ctx, "before clean: %+v", req.FilePaths)
	err = s.dataManager.FileChanged(ctx, cli, lo.Map(req.FilePaths, func(item string, index int) string {
		return filepath.Clean(item)
	}), req.UserID)
	if err != nil {
		logs.CtxError(ctx, "InitProject failed err is %v", err)
		resultErr := &protocol.Error{
			Message: err.Error(),
			Stack:   "",
		}
		return &protocol.DocumentResponse{Code: protocol.Code_unknown_error, Error: resultErr}, nil
	}

	return &protocol.DocumentResponse{Code: protocol.Code_succeed, Error: nil}, nil
}

func (s *server) DocumentDelete(ctx context.Context, req *protocol.DocumentRequest) (response *protocol.DocumentResponse, resultErr error) {
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "DocumentDelete")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "DocumentDelete panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.DocumentResponse{Code: protocol.Code_panic, Error: err}
		}
	}()

	documentsToPrint := make([]model.Document, 0, len(req.Documents))
	for _, doc := range req.Documents {
		documentsToPrint = append(documentsToPrint, model.Document{
			Uri:       doc.Uri,
			Name:      doc.Name,
			Content:   fmt.Sprintf("(%d) chars", len(doc.Content)),
			ProjectID: doc.ProjectId,
		})
	}

	logs.Info("DocumentDelete request start, documents: %v, userID: %s, req filePaths size: %d, files: %v", documentsToPrint, req.UserID, len(req.FilePaths), validator.LogFilePath(req.FilePaths))
	s.requestReceived("DocumentDelete")

	if len(req.UserID) == 0 {
		return &protocol.DocumentResponse{
			Code: protocol.Code_user_id_is_missing,
			Error: &protocol.Error{
				Message: "empty user id",
			}}, nil
	}

	cli, err := knowledgebase.GetClient()
	if err != nil {
		return &protocol.DocumentResponse{
			Code: protocol.Code_nil_client,
			Error: &protocol.Error{
				Message: err.Error(),
			},
		}, nil
	}

	err = s.dataManager.FileDeleted(ctx, cli, lo.Map(req.FilePaths, func(item string, index int) string {
		return filepath.Clean(item)
	}), req.UserID, lo.Map(req.Documents, func(item *protocol.Document, _ int) model.Document {
		return model.Document{
			Uri:       item.Uri,
			Name:      item.Name,
			Content:   item.Content,
			ProjectID: item.ProjectId,
		}
	}))
	if err != nil {
		logs.CtxError(ctx, "FileDeleted failed err is %v", err)
		respErr := &protocol.Error{
			Message: err.Error(),
			Stack:   "",
		}
		return &protocol.DocumentResponse{Code: protocol.Code_unknown_error, Error: respErr}, nil
	}

	return &protocol.DocumentResponse{Code: protocol.Code_succeed, Error: nil}, nil
}

func (s *server) DocumentSelect(ctx context.Context, req *protocol.DocumentRequest) (response *protocol.DocumentResponse, resultErr error) {
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "DocumentSelect")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "DocumentSelect panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.DocumentResponse{Code: protocol.Code_panic, Error: err}
		}
	}()

	documentsToPrint := make([]model.Document, 0, len(req.Documents))
	for _, doc := range req.Documents {
		documentsToPrint = append(documentsToPrint, model.Document{
			Uri:       doc.Uri,
			Name:      doc.Name,
			Content:   fmt.Sprintf("(%d) chars", len(doc.Content)),
			ProjectID: doc.ProjectId,
		})
	}

	logs.Info("DocumentSelect request start, documents: %v, userID: %s, req filePaths size: %d, files: %v", documentsToPrint, req.UserID, len(req.FilePaths), validator.LogFilePath(req.FilePaths))
	s.requestReceived("DocumentSelect")

	return nil, nil
}

func (s *server) GetBuildStatus(ctx context.Context, req *protocol.GetBuildStatusRequest) (response *protocol.GetBuildStatusResponse, resultErr error) {
	logs.Info("GetBuildStatus request start: %v", req)
	s.requestReceived("GetBuildStatus")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "GetBuildStatus")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "GetBuildStatus panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.GetBuildStatusResponse{Code: protocol.Code_panic, Error: err}
		}
	}()

	allStatus := s.dataManager.GetProjectsBuildStatus(ctx, req)

	result := make(map[string]*protocol.ProjectBuildStatus)
	result["__codekg_placeholder"] = &protocol.ProjectBuildStatus{
		Status:   protocol.BuildStatus_finished,
		Progress: 1,
	}
	for project, buildStatus := range allStatus {
		result[project] = buildStatus
	}

	return &protocol.GetBuildStatusResponse{
		Code:   protocol.Code_succeed,
		Status: result,
	}, nil
}

func (s *server) GetDocumentsIndexStatus(ctx context.Context, req *protocol.GetDocumentsIndexStatusRequest) (response *protocol.GetDocumentsIndexStatusResponse, resultErr error) {
	logs.Info("GetDocumentsIndexStatus request start: %v", req)
	s.requestReceived("GetDocumentsIndexStatus")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "GetDocumentsIndexStatus")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "GetDocumentsIndexStatus panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.GetDocumentsIndexStatusResponse{Code: protocol.Code_panic, Error: err}
		}
	}()

	results := s.dataManager.GetDocumentsIndexStatus(ctx, req)

	logs.Info("GetDocumentsIndexStatus got results with %d projects", len(results))

	// 打印结果的详细信息，以便调试
	for projectID, status := range results {
		logs.Info("GetDocumentsIndexStatus project %s has %d document statuses", projectID, len(status.DocumentsStatus))
	}

	response = &protocol.GetDocumentsIndexStatusResponse{
		Code:           protocol.Code_succeed,
		ProjectsStatus: results,
	}

	// 打印最终响应的详细信息
	logs.Info("GetDocumentsIndexStatus response has ProjectsStatus with %d entries", len(response.ProjectsStatus))

	return response, nil
}

func (s *server) DeleteIndex(ctx context.Context, req *protocol.DeleteIndexRequest) (response *protocol.DeleteIndexResponse, resultErr error) {
	logs.Info("DeleteProject request: %v", req)
	s.requestReceived("DeleteIndex")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "DeleteIndex")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "DeleteProject panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.DeleteIndexResponse{Code: protocol.Code_panic, Error: err}
		}
	}()

	err := s.dataManager.DeleteIndex(ctx, req.ProjectId)
	if err != nil {
		logs.CtxError(ctx, "ProjectDeleted failed err is %s projectID is %v", err, req.ProjectId)
		respErr := &protocol.Error{
			Message: err.Error(),
			Stack:   "",
		}
		return &protocol.DeleteIndexResponse{Code: protocol.Code_unknown_error, Error: respErr}, nil
	}
	return &protocol.DeleteIndexResponse{
		Code:    protocol.Code_succeed,
		Succeed: true,
	}, nil
}

func (s *server) RetrieveRelation(ctx context.Context, req *protocol.RetrieveRelationRequest) (response *protocol.RetrieveRelationResponse, resultErr error) {
	logs.Info("RetrieveRelation request start, req: %v", req)
	s.requestReceived("RetrieveRelation")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "RetrieveRelation")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "RetrieveRelation panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.RetrieveRelationResponse{Code: protocol.Code_panic, Error: err}
		}
	}()

	if len(req.UserId) == 0 {
		return &protocol.RetrieveRelationResponse{
			Code: protocol.Code_user_id_is_missing,
			Error: &protocol.Error{
				Message: "empty user id",
			}}, nil
	}

	resp, err := s.queryService.RetrieveRelation(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "RetrieveRelation failed err is %v", err)
		return &protocol.RetrieveRelationResponse{
			Code: protocol.Code_unknown_error,
			Error: &protocol.Error{
				Message: err.Error(),
			},
		}, nil
	}

	jsonData, err := (&jsonpb.Marshaler{
		EmitDefaults: true,
	}).MarshalToString(&protocol.RetrieveRelationResponse{
		CurrentEditor: resp.CurrentEditor,
		Variables:     resp.Variables,
	})
	if err != nil {
		logs.CtxWarn(ctx, "error marshal retrieve relation result, err: %v", err)
	} else {
		resp.JsonResult = jsonData
	}

	resp.Code = protocol.Code_succeed
	return resp, nil
}

func (s *server) RetrieveEntity(ctx context.Context, req *protocol.RetrieveEntityRequest) (response *protocol.RetrieveEntityResponse, resultErr error) {
	logs.Info("RetrieveEntity request start, req: %v", req)
	s.requestReceived("RetrieveEntity")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "RetrieveEntity")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "RetrieveEntity panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.RetrieveEntityResponse{
				Code:  protocol.Code_panic,
				Error: err,
			}
		}
	}()

	if len(req.UserId) == 0 {
		return &protocol.RetrieveEntityResponse{
			Code: protocol.Code_user_id_is_missing,
			Error: &protocol.Error{
				Message: "empty user id",
			}}, nil
	}

	cli, err := knowledgebase.GetClient()
	if err != nil {
		return &protocol.RetrieveEntityResponse{
			Code: protocol.Code_nil_client,
			Error: &protocol.Error{
				Message: err.Error(),
			},
		}, nil
	}

	response, err = s.queryService.RetrieveEntity(ctx, cli, req)
	if err != nil {
		logs.CtxError(ctx, "RetrieveEntity err is %v", err)
		return &protocol.RetrieveEntityResponse{
			Code: protocol.Code_unknown_error,
			Error: &protocol.Error{
				Message: err.Error(),
			},
		}, nil
	}

	return response, nil
}

func (s *server) RefreshToken(ctx context.Context, req *protocol.RefreshTokenRequest) (response *protocol.RefreshTokenResponse, resultErr error) {
	// Token 格式为 Cloud-IDE-JWT xxxx / Cloud-IDE-JWT xxxx / GDPR-JWT xxxx
	// ref: https://bytedance.larkoffice.com/wiki/FqfMwibDBi3SkNk1fYmcaLZanxb
	logs.Info("RefreshToken request, req: %v", req)
	s.requestReceived("RefreshToken")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "RefreshToken")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "RefreshToken panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.RefreshTokenResponse{Code: protocol.Code_panic, Error: err}
		}
	}()

	parts := strings.SplitN(req.Token, " ", -1)
	if len(parts) != 2 {
		logs.CtxWarn(ctx, "invalid user token: %v", req.Token)
		return &protocol.RefreshTokenResponse{
			Code: protocol.Code_invalid_token,
			Error: &protocol.Error{
				Message: "invalid user token",
			}}, nil
	}

	err := env.RefreshToken(ctx, &model.TokenValue{
		Scheme: parts[0],
		Token:  parts[1],
		UserID: req.UserId,
	}, req.UserId)
	if err != nil {
		return &protocol.RefreshTokenResponse{Code: protocol.Code_unknown_error, Error: &protocol.Error{
			Message: errors.WithMessagef(bizErr.ErrNotMatchStorage, "RefreshToken err is %v", err).Error(),
		}}, nil
	}
	return &protocol.RefreshTokenResponse{Code: protocol.Code_succeed, Error: nil}, nil
}

func (s *server) CancelIndex(ctx context.Context, req *protocol.CancelIndexRequest) (response *protocol.CancelIndexResponse, resultErr error) {
	logs.Info("CancelIndex request: %v", req)
	s.requestReceived("CancelIndex")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "CancelIndex")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "CancelIndex panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.CancelIndexResponse{Code: protocol.Code_panic, Error: err}
		}
	}()

	err := s.dataManager.CancelIndex(ctx, req.ProjectId)
	if err != nil {
		return &protocol.CancelIndexResponse{
			Succeed: false,
			Code:    protocol.Code_unknown_error,
			Error: &protocol.Error{
				Message: err.Error(),
			}}, nil
	}

	return &protocol.CancelIndexResponse{
		Code:    protocol.Code_succeed,
		Succeed: true,
	}, nil
}

func (s *server) ImportAnalysis(ctx context.Context, req *protocol.ImportAnalysisRequest) (response *protocol.ImportAnalysisResponse, resultErr error) {
	s.requestReceived("ImportAnalysis")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "ImportAnalysis")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "ImportAnalysis panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.ImportAnalysisResponse{
				Code:  protocol.Code_panic,
				Error: err,
			}
		}
	}()

	analyzer := analysis.NewAnalyzer(ctx, s.dataManager)
	result, err := analyzer.ImportAnalysis(req.ProjectId, req.File, req.ImportStatement)
	if err != nil {
		return &protocol.ImportAnalysisResponse{
			Code: protocol.Code_unknown_error,
			Error: &protocol.Error{
				Message: err.Error(),
				Stack:   "",
			},
			Result: nil,
		}, fmt.Errorf("failed to ImportAnalysis: %s", err.Error())
	}
	return &protocol.ImportAnalysisResponse{
		Code:   protocol.Code_succeed,
		Error:  nil,
		Result: result,
	}, nil
}

func (s *server) FilesImportAnalysis(ctx context.Context, req *protocol.FilesImportAnalysisRequest) (response *protocol.ImportAnalysisResponse, resultErr error) {
	s.requestReceived("FilesImportAnalysis")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "FilesImportAnalysis")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "FilesImportAnalysis panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.ImportAnalysisResponse{
				Code:  protocol.Code_panic,
				Error: err,
			}
		}
	}()

	analyzer := analysis.NewAnalyzer(ctx, s.dataManager)
	result, err := analyzer.FilesImportAnalysis(req.ProjectId, req.RelaFiles)
	if err != nil {
		return &protocol.ImportAnalysisResponse{
			Code: protocol.Code_unknown_error,
			Error: &protocol.Error{
				Message: err.Error(),
				Stack:   "",
			},
			Result: nil,
		}, fmt.Errorf("failed to FilesImportAnalysis: %s", err.Error())
	}
	return &protocol.ImportAnalysisResponse{
		Code:   protocol.Code_succeed,
		Error:  nil,
		Result: result,
	}, nil
}

func (s *server) SearchCKGDB(ctx context.Context, req *protocol.SearchCKGDBRequest) (response *protocol.SearchCKGDBResponse, resultErr error) {
	logs.Info("SearchCKGDB request start, req: %v", req)
	s.requestReceived("SearchCKGDB")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "SearchCKGDB")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "SearchCKGDB panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.SearchCKGDBResponse{
				Code:  protocol.Code_panic,
				Error: err,
			}
		}
	}()

	result, err := s.queryService.SearchCKGDB(ctx, req)
	if err != nil {
		return &protocol.SearchCKGDBResponse{
			Code: protocol.Code_unknown_error,
			Error: &protocol.Error{
				Message: err.Error(),
			},
		}, nil
	}

	result.Code = protocol.Code_succeed
	return result, nil
}

func (s *server) CursorMove(ctx context.Context, req *protocol.CursorMoveRequest) (response *protocol.Empty, resultErr error) {
	s.requestReceived("CursorMove")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "CursorMove")
			logs.Error("[CursorMove] is panic, err is %+v", rec, string(debug.Stack()))
			resultErr = errors.WithMessagef(bizErr.ErrPanic, "CursorMove panic is %v", rec)
		}
	}()

	err := s.dataManager.CursorMove(ctx, req.ProjectId, filepath.Clean(req.File), req.UserId, req.Line, req.Version)
	if err != nil {
		logs.CtxTrace(ctx, "failed to record cursor move, err: %v", err)
	}
	return &protocol.Empty{}, nil
}

func (s *server) RetrieveRelevantSnippet(ctx context.Context, req *protocol.RetrieveRelevantSnippetRequest) (response *protocol.RetrieveRelevantSnippetResponse, resultErr error) {
	logs.Info("RetrieveRelevantSnippet request start, user_query: %v, project_id: %v, version: %v", req.UserQuery, req.ProjectId, req.Version)
	if req.CurrentEditor != nil {
		logs.Info("RetrieveRelevantSnippet request start, current_editor file_path: %v", req.CurrentEditor.FilePath)
	}
	s.requestReceived("RetrieveRelevantSnippet")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "RetrieveRelevantSnippet")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "RetrieveRelevantSnippet panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.RetrieveRelevantSnippetResponse{
				Code:  protocol.Code_panic,
				Error: err,
			}
		}
	}()

	cli, err := knowledgebase.GetClient()
	if err != nil {
		return &protocol.RetrieveRelevantSnippetResponse{
			Code: protocol.Code_nil_client,
			Error: &protocol.Error{
				Message: err.Error(),
			},
		}, nil
	}

	snippets, err := s.queryService.RetrieveRelevantSnippet(ctx, cli, req)
	if err != nil {
		logs.CtxError(ctx, "RetrieveRelevantSnippet err is %v", err)
		return &protocol.RetrieveRelevantSnippetResponse{
			Code: protocol.Code_unknown_error,
			Error: &protocol.Error{
				Message: err.Error(),
			},
		}, nil
	}

	return &protocol.RetrieveRelevantSnippetResponse{
		SnippetsByWorkspace:   snippets.WorkspaceSnippets,
		SnippetsByFolder:      snippets.SelectedFolderSnippets,
		SnippetsByFile:        snippets.SelectedFileSnippets,
		SnippetsByCode:        snippets.SelectedCodeSnippets,
		SnippetsByCurrentFile: snippets.CurrentEditorSnippets,

		SnippetsByInteraction: snippets.UserInteractionSnippets,
		Code:                  protocol.Code_succeed,
		Error:                 nil,
	}, nil
}

func (s *server) RerankSnippet(ctx context.Context, req *protocol.RerankSnippetRequest) (response *protocol.RerankSnippetResponse, resultErr error) {
	s.requestReceived("RerankSnippet")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "RerankSnippet")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "RerankSnippet panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.RerankSnippetResponse{Code: protocol.Code_panic, Error: err}
		}
	}()
	return nil, nil
}

func (s *server) IsCKGEnabledForNonWorkspaceScenario(ctx context.Context, req *protocol.IsCKGEnabledForNonWorkspaceScenarioRequest) (response *protocol.IsCKGEnabledForNonWorkspaceScenarioResponse, resultErr error) {
	s.requestReceived("IsCKGEnabledForNonWorkspaceScenario")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "IsCKGEnabledForNonWorkspaceScenario")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "IsCKGEnabledForNonWorkspaceScenario panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.IsCKGEnabledForNonWorkspaceScenarioResponse{Code: protocol.Code_panic, Error: err}
		}
	}()

	userInteractionVersion := ""

	cli, err := knowledgebase.GetClient()
	if err != nil {
		logs.CtxError(ctx, "client is nil, IsCKGEnabledForNonWorkspaceScenario returns false", err)
		return &protocol.IsCKGEnabledForNonWorkspaceScenarioResponse{
			IsEnabled: false,
			Version:   userInteractionVersion,
			Code:      protocol.Code_nil_client,
		}, nil
	}

	// 先看 AB 是不是命中实验组，如果命中实验，返回 libra 上的字段
	userInteractionVersion, ok := s.config.GetABConfig(ctx, cli, req.UserId).GetUserInteractionVersion()
	if ok {
		return &protocol.IsCKGEnabledForNonWorkspaceScenarioResponse{
			IsEnabled: true,
			Version:   userInteractionVersion,
			Code:      protocol.Code_succeed,
		}, nil
	}

	// 否则看 FeatureGate 是否开启，如果命中灰度，返回 default 版本
	isFeatureEnabled := s.config.IsFeatureEnabled(ctx, req.UserId, ckg_config.EnableInteractionGraph)
	if isFeatureEnabled {
		return &protocol.IsCKGEnabledForNonWorkspaceScenarioResponse{
			IsEnabled: true,
			Version:   "default",
			Code:      protocol.Code_succeed,
		}, nil
	}

	// 没有命中任何条件，返回 false，version = ""
	return &protocol.IsCKGEnabledForNonWorkspaceScenarioResponse{
		IsEnabled: false,
		Version:   userInteractionVersion,
		Code:      protocol.Code_succeed,
	}, nil
}

func (s *server) SetUp(ctx context.Context, req *protocol.SetUpRequest) (response *protocol.SetUpResponse, resultErr error) {
	logs.Info("SetUp request start, req: %v", req)
	s.requestReceived("SetUp")
	defer func() {
		if rec := recover(); rec != nil {
			reportRecoverEvent(ctx, "SetUp")
			err := &protocol.Error{
				Message: errors.WithMessagef(bizErr.ErrPanic, "SetUp panic is %v", rec).Error(),
				Stack:   string(debug.Stack()),
			}
			response = &protocol.SetUpResponse{Code: protocol.Code_panic, Error: err}
		}
	}()

	if req.Host != "" {
		env.SetHost(req.Host)
	}
	if req.Region != "" {
		env.SetRegion(req.Region)
	}
	if req.SourceProduct != "" {
		env.SetSourceProduct(model.GetSourceProduct(req.SourceProduct))
	}
	if req.DeviceCpu != "" {
		env.SetDeviceCPU(req.DeviceCpu)
	}
	if req.DeviceId != "" {
		env.SetDeviceID(req.DeviceId)
	}
	if req.DeviceBrand != "" {
		env.SetDeviceBrand(req.DeviceBrand)
	}
	if req.DeviceType != "" {
		env.SetDeviceType(req.DeviceType)
	}
	if req.OsVersion != "" {
		env.SetOSVersion(req.OsVersion)
	}
	if req.MachineId != "" {
		env.SetMachineID(req.MachineId)
	}

	if err := env.SaveLocalSetupEnv(); err != nil {
		logs.CtxError(ctx, "save setup env to local failed, err: %v", err)
	}

	return &protocol.SetUpResponse{
		Code: protocol.Code_succeed,
	}, nil
}

func reportRecoverEvent(ctx context.Context, apiName string) {
	recoverEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGRecover, env.GetDeviceID(), "", string(env.GetSourceProduct()))
	ctx = ckg_metrics.SetEvent(ctx, recoverEvent)
	recoverEvent.AddTeaParam(ckg_metrics.TeaCKGCrash, apiName)
	recoverEvent.Report(ctx, false)
}
