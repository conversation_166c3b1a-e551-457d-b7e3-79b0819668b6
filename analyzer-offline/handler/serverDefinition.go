package handler

import (
	"byted-ckg/analyzer-offline/manager"
	"code.byted.org/gopkg/logs/v2"
	"context"
	"encoding/json"
	"fmt"
	"go.lsp.dev/protocol"
	"io"
	"net/http"
)

type ServerDefinitionRequestData struct {
	File      string `json:"file"`
	Line      int    `json:"line"`
	Character int    `json:"character"`
	Language  string `json:"language"`
}

func ServerDefinitionHandler(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	if r.Method != http.MethodPost {
		msg := fmt.Sprintf("unsupported request method, required POST, got %s", r.Method)
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		msg := fmt.Sprintf("failed to read request body: %s", err.Error())
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	var data ServerDefinitionRequestData
	if err = json.Unmarshal(body, &data); err != nil {
		msg := fmt.Sprintf("failed to read body data: %s", err.Error())
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusBadRequest)
		return
	}

	if !manager.GetLanguageServerManager().IsSupportedLanguage(data.Language) {
		msg := fmt.Sprintf("unsupported language: %s", data.Language)
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusBadRequest)
		return
	}

	result, err := manager.GetLanguageServerManager().ExecuteDefinition(data.Language, protocol.DocumentURI(fmt.Sprintf("file://%s", data.File)), protocol.Position{
		Line:      uint32(data.Line),
		Character: uint32(data.Character),
	})
	if err != nil {
		msg := fmt.Sprintf("failed to execute definition: %s", err.Error())
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusInternalServerError)
		return
	}

	logs.CtxInfo(ctx, "LSP definition success, result: %+v", result)
}
