package model

import (
	"errors"
	"strings"

	kb "ide/ckg/codekg/components/knowledgebase"
)

// SplitterType 定义了分割器的类型
type SplitterType string

// ContentType 定义了内容的类型
type ContentType string

// LocalSubtype 定义了本地分割器的子类型
type LocalSubtype string

// LangchainType 定义了Langchain分割器的类型
type LangchainType string

// SplitterType 常量
const (
	// SplitterTypeLocal 使用本地分割器
	SplitterTypeLocal SplitterType = "local"
	// SplitterTypeKB 使用知识库分割器
	SplitterTypeKB SplitterType = "kb"
)

func GetSplitterType(chunkingMethod string) SplitterType {
	if strings.HasPrefix(chunkingMethod, "local.") {
		return SplitterTypeLocal
	}
	return SplitterTypeKB
}

// ContentType 常量
const (
	// ContentTypeText 用于文本内容
	ContentTypeText ContentType = "text"
	// ContentTypeCode 用于代码内容
	ContentTypeCode ContentType = "code"
)

func GetContentType(chunkingMethod string) ContentType {
	if strings.HasPrefix(chunkingMethod, "local.text.") {
		return ContentTypeText
	}
	if strings.HasPrefix(chunkingMethod, "local.code.") {
		return ContentTypeCode
	}
	return ContentTypeText
}

// LocalSubtype 常量
const (
	// LocalSubtypeBasicText 使用基础文本分割器
	LocalSubtypeBasicText LocalSubtype = "basic_text"
	// LocalSubtypeLangchainText 使用Langchain风格文本分割器
	LocalSubtypeLangchainText LocalSubtype = "langchain_text"
)

func GetLocalSubtype(chunkingMethod string) LocalSubtype {
	// // 注册本地文本分割器
	// factory.RegisterCreator("local.text.basic", createBasicTextSplitter)

	// // 注册Langchain分割器
	// factory.RegisterCreator("local.text.langchain.recursive", createRecursiveSplitter)
	// factory.RegisterCreator("local.text.langchain.markdown", createMarkdownSplitter)
	// factory.RegisterCreator("local.text.langchain.token", createTokenSplitter)

	if strings.HasPrefix(chunkingMethod, "local.text.langchain.") {
		return LocalSubtypeLangchainText
	}
	return LocalSubtypeBasicText
}

// LangchainType 常量
const (
	// LangchainTypeRecursive 使用递归字符分割器
	LangchainTypeRecursive LangchainType = "recursive"
	// LangchainTypeMarkdown 使用Markdown分割器
	LangchainTypeMarkdown LangchainType = "markdown"
	// LangchainTypeToken 使用Token分割器
	LangchainTypeToken LangchainType = "token"
)

func GetLangchainType(chunkingMethod string) LangchainType {
	switch chunkingMethod {
	case "local.text.langchain.recursive":
		return LangchainTypeRecursive
	case "local.text.langchain.markdown":
		return LangchainTypeMarkdown
	case "local.text.langchain.token":
		return LangchainTypeToken
	}
	return LangchainTypeMarkdown
}

// IsValid 检查SplitterType是否有效
func (s SplitterType) IsValid() bool {
	switch s {
	case SplitterTypeLocal, SplitterTypeKB:
		return true
	default:
		return false
	}
}

// IsValid 检查ContentType是否有效
func (c ContentType) IsValid() bool {
	switch c {
	case ContentTypeText, ContentTypeCode:
		return true
	default:
		return false
	}
}

// IsValid 检查LocalSubtype是否有效
func (l LocalSubtype) IsValid() bool {
	switch l {
	case LocalSubtypeBasicText, LocalSubtypeLangchainText:
		return true
	default:
		return false
	}
}

// IsValid 检查LangchainType是否有效
func (l LangchainType) IsValid() bool {
	switch l {
	case LangchainTypeRecursive, LangchainTypeMarkdown, LangchainTypeToken:
		return true
	default:
		return false
	}
}

// String 返回SplitterType的字符串表示
func (s SplitterType) String() string {
	return string(s)
}

// String 返回ContentType的字符串表示
func (c ContentType) String() string {
	return string(c)
}

// String 返回LocalSubtype的字符串表示
func (l LocalSubtype) String() string {
	return string(l)
}

// String 返回LangchainType的字符串表示
func (l LangchainType) String() string {
	return string(l)
}

// SplitOption 定义了分割文件的选项
type SplitOption struct {
	// 分割器类型的选择策略
	PreferLocalSplit bool

	// 顶级分割器类型选择 (local or kb)
	SplitterType SplitterType

	// 向后兼容字段
	AsPlainText bool // 将所有内容视为纯文本，向后兼容

	// 特定分割器的选项
	// 只需要设置与SplitterType对应的选项
	LocalOpt *LocalSplitOption // 本地分割器选项，当SplitterType为SplitterTypeLocal时使用
	KBOpt    *KBSplitOption    // 知识库分割器选项，当SplitterType为SplitterTypeKB时使用
}

// LocalSplitOption 定义了本地分割器的选项
type LocalSplitOption struct {
	// 内容类型选择
	ContentType ContentType // 内容类型 (text or code)

	// 本地分割器子类型选择
	LocalSubtype LocalSubtype // 本地分割器子类型 (basic_text, langchain_text)

	// Langchain特定选项，当LocalSubtype为LocalSubtypeLangchainText时使用
	LangchainOpt *LangchainSplitOption
}

// KBSplitOption 定义了知识库分割器的选项
type KBSplitOption struct {
	KBClient       kb.Client // 知识库客户端
	DatastoreName  string    // 数据存储名称
	ChunkingMethod string    // 分块方法
	UserID         string    // 用户ID
}

// LangchainSplitOption 定义了Langchain分割器的选项
type LangchainSplitOption struct {
	// Langchain分割器类型选择
	SplitterType LangchainType // Langchain分割器类型 (recursive, markdown, token)

	// 基本选项
	ChunkSize      int  // 分块大小
	ChunkOverlap   int  // 分块重叠大小
	AllSplitAsText bool // 是否将所有文件都作为文本处理

	// Token分割器特定选项，当SplitterType为LangchainTypeToken时使用
	TokenModel string // token分割器使用的模型名称
}

// 常见错误定义
var (
	ErrUnsupportedLanguage = errors.New("unsupported language")
	ErrInvalidArguments    = errors.New("invalid arguments")
	ErrSplitterNotFound    = errors.New("splitter not found")
)
