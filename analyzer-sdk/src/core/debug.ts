import {EditorSelectionInfo} from "../utils/common";
import {Logger} from "../utils/logger";
import {getCachedFileSymbolDef} from "./cache/symbolDefCache";
import {getCachedDefinitionInfoByKey} from "./cache/definitionCache";
import {getActiveFileCachedTreeAndDocument} from "./cache/astCache";
import {doGetDefinitionsForRange} from "./sync/definition";
import {doExtractUnitTestContext} from "./feat/utgen/common";

export async function debugDefinitionAsync(selectionInfo: EditorSelectionInfo): Promise<void> {
  const startIndex = selectionInfo.document.offsetAt(selectionInfo.selectionRange.start);
  const endIndex = selectionInfo.document.offsetAt(selectionInfo.selectionRange.end);
  if (startIndex === endIndex) {
    return;
  }

  Logger.info(`debugDefinitionAsync: ${selectionInfo.document.uri} (${selectionInfo.selectionRange.start.line}, ${selectionInfo.selectionRange.start.character}) - (${selectionInfo.selectionRange.end.line}, ${selectionInfo.selectionRange.end.character})`);

  const cachedFileSymbolDef = getCachedFileSymbolDef(selectionInfo.document, false);
  if (!cachedFileSymbolDef) {
    Logger.warn(`debugDefinitionAsync: cachedFileSymbolDef is null`);
    return;
  }

  const cachedTree = getActiveFileCachedTreeAndDocument(selectionInfo.document)?.tree;
  if (!cachedTree) {
    Logger.warn(`debugDefinitionAsync: cachedTree is null`);
    return;
  }

  const symbolDefInfo = cachedFileSymbolDef.getSymbolDefInfo(startIndex);
  if (symbolDefInfo) {
    const symbolName = symbolDefInfo.name;
    const definitionKey = symbolDefInfo.definitionKey;

    const cachedDefinitionInfo = getCachedDefinitionInfoByKey(selectionInfo.document.languageId, definitionKey);
    if (cachedDefinitionInfo) {
      Logger.info(`[DEBUG-LJR]: (${selectionInfo.selectionRange.start.line}, ${selectionInfo.selectionRange.start.character}, ${startIndex})\nSymbol: ${symbolName}\nTypeInfo:\n${JSON.stringify(cachedDefinitionInfo, null, 2)}`);
    } else {
      Logger.info(`[DEBUG-LJR]: (${selectionInfo.selectionRange.start.line}, ${selectionInfo.selectionRange.start.character}, ${startIndex}) NO definition found - cachedDefinitionInfo (definitionKey: ${definitionKey}) is null`);
    }
  } else {
    Logger.info(`[DEBUG-LJR]: (${selectionInfo.selectionRange.start.line}, ${selectionInfo.selectionRange.start.character}, ${startIndex}) NO definition found - symbolDefInfo is null`);
  }
}

export async function debugDefinitionSync(selectionInfo: EditorSelectionInfo): Promise<void> {
  if (selectionInfo.document.offsetAt(selectionInfo.selectionRange.start) === selectionInfo.document.offsetAt(selectionInfo.selectionRange.end)) {
    return;
  }

  Logger.info(`debugDefinitionSync: ${selectionInfo.document.uri} (${selectionInfo.selectionRange.start.line}, ${selectionInfo.selectionRange.start.character}) - (${selectionInfo.selectionRange.end.line}, ${selectionInfo.selectionRange.end.character})`);

  const result = await doGetDefinitionsForRange(selectionInfo.document, selectionInfo.selectionRange.start, selectionInfo.selectionRange.end);

  Logger.info(`[DEBUG-LJR]:\n${result}`);
}

export async function debugUnitTestContext(selectionInfo: EditorSelectionInfo): Promise<void> {
  const startIndex = selectionInfo.document.offsetAt(selectionInfo.selectionRange.start);
  const endIndex = selectionInfo.document.offsetAt(selectionInfo.selectionRange.end);
  if (startIndex === endIndex) {
    return;
  }

  Logger.info(`debugUnitTestContext: ${selectionInfo.document.uri} (${selectionInfo.selectionRange.start.line}, ${selectionInfo.selectionRange.start.character}) - (${selectionInfo.selectionRange.end.line}, ${selectionInfo.selectionRange.end.character})`);

  const result = await doExtractUnitTestContext(selectionInfo.document, startIndex, endIndex);

  Logger.info(`[DEBUG-UT]:\n${result}`);
}