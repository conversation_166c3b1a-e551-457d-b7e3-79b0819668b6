package model

type CodeKGTCCConfig struct {
	// Remote configurations
	FileCountLimit              int     `json:"file_count_limit"`
	FileSizeThreshold           int64   `json:"file_size_threshold"`
	SingleFileLineThreshold     int     `json:"single_file_line_threshold"`
	DatastoreName               string  `json:"datastore_name"`
	EnableRerank                bool    `json:"enable_rerank"`
	DefaultRecallNum            int     `json:"default_recall_num"`
	ForceIndexFileCountLimit    int     `json:"force_index_file_count_limit"`
	IndexFileLineLimit          int     `json:"index_file_line_limit"`
	DefaultIgnoreRules          string  `json:"default_ignore_rules"`
	DefaultOmitProjectRoots     string  `json:"default_omit_project_roots"`
	DebounceTime                int     `json:"debounce_time"`
	BinaryContentCheckThreshold int     `json:"binary_content_check_threshold"`
	RecentCursorRelationNum     int     `json:"recent_cursor_relation_num"`
	TaskSleepTimeFactor         float64 `json:"task_sleep_time_factor"`

	// ignore rule
	// 1. black list rule
	ExcludeFolderRule  string `json:"exclude_folder_rule"`
	ExcludeFileRule    string `json:"exclude_file_rule"`
	ExcludeExtFileRule string `json:"exclude_ext_file_rule"`
	// 2. white list rule
	IncludeExtFileRule string `json:"include_ext_file_rule"`

	VirtualProjectConfig *VirtualProjectConfig `json:"virtual_project_config"`
}

type VirtualProjectConfig struct {
	DisableVirtualProjectIndexing bool   `json:"disable_virtual_project_indexing"`
	FileCountLimit                int    `json:"file_count_limit"`
	FileSizeLimit                 int64  `json:"file_size_limit"`
	TotalFileSizeLimit            int64  `json:"total_file_size_limit"`
	RecallNum                     int    `json:"recall_num"`
	ChunkingMethodBaseline        string `json:"chunking_method_baseline"`
	ChunkingSize                  int    `json:"chunking_size"`
	ChunkingOverlap               int    `json:"chunking_overlap"`
	RemoveEntityContent           bool   `json:"remove_entity_content"`
}

type CodeKGABConfig struct {
	QueryRewrite   map[string]interface{} `json:"query_rewrite"`
	Rerank         map[string]interface{} `json:"rerank"`
	Embedding      map[string]interface{} `json:"embedding"`
	Chunking       map[string]interface{} `json:"chunking"`
	RecallStrategy map[string]interface{} `json:"recall_strategy"`
	Implementation map[string]interface{} `json:"implementation"`
}

func (c *CodeKGABConfig) IsRerankEnabled() bool {
	if c.Rerank == nil {
		return false
	}

	_, ok := c.Rerank["enabled"]
	if !ok {
		return false
	}

	enabled, ok := c.Rerank["enabled"].(bool)
	if !ok {
		return false
	}

	return enabled
}

func (c *CodeKGABConfig) GetWorkspaceRecallNum() int {
	if c.RecallStrategy == nil {
		return 0
	}

	recallNum, ok := c.RecallStrategy["workspace_recall_num"].(float64)
	if !ok {
		return 0
	}

	return int(recallNum)
}

// GetEmbeddingModel 返回的 bool 表明是否命中 a/b，string 表示对应的 embedding model
func (c *CodeKGABConfig) GetEmbeddingModel() (string, bool) {
	if c.Embedding == nil {
		return "", false
	}
	_, ok := c.Embedding["enabled"]
	if !ok {
		return "", false
	}
	model, ok := c.Embedding["model"].(string)
	if !ok || model == "" {
		return "", true
	}
	return model, true
}

func (c *CodeKGABConfig) GetChunkingMethod() (string, bool) {
	if c.Chunking == nil {
		return "", false
	}
	_, ok := c.Chunking["enabled"]
	if !ok {
		return "", false
	}
	method, ok := c.Chunking["method"].(string)
	if !ok || method == "" {
		return "", true
	}
	return method, true
}

func (c *CodeKGABConfig) GetUserInteractionVersion() (string, bool) {
	if c.RecallStrategy == nil {
		return "", false
	}

	_, ok := c.RecallStrategy["non_workspace_scenario"]
	if !ok {
		return "", false
	}

	version, ok := c.RecallStrategy["non_workspace_scenario"].(string)
	if !ok {
		return "", false
	}

	return version, true
}
