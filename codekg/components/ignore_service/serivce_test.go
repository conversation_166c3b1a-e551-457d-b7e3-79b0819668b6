package ignore_service

import (
	"github.com/stretchr/testify/assert"
	"os"
	"testing"
)

func TestSolveGitignore(t *testing.T) {
	// 调用 SolveGitignore 函数
	is := NewIgnoreService(".testignore")
	err := is.CreateOrChangeURI("test_data")
	if err != nil {
		t.<PERSON>rf("Error:%s", err)
	}
	result, err := is.IsIgnored("test_data/.idea/test.txt")
	if err != nil {
		t.Errorf("Error:%s", err)
	}
	assert.Equal(t, result, true)
	result, err = is.IsIgnored("test_data/dev/apitest/http-client.private.env.json")
	if err != nil {
		t.Errorf("Error:%s", err)
	}
	assert.Equal(t, result, true)
	result, err = is.IsIgnored("test_data/dev/local/test111.txt")
	assert.Equal(t, result, true)
	assert.Equal(t, err, nil)
	result, err = is.IsIgnored("test_data/dev/local/test1.txt")
	assert.Equal(t, result, true)
	assert.Equal(t, err, nil)
	_, err = os.Create("./test_data/.idea/kiwis121.iml")
	if err != nil {
		t.Errorf("Error:%s", err)
	}
	is.CreateOrChangeURI("test_data/.idea/kiwis121.iml")
	result, err = is.IsIgnored("test_data/.idea/kiwis121.iml")
	assert.Equal(t, result, true)
	assert.Equal(t, err, nil)
	// 关闭文件
	err = os.Remove("./test_data/.idea/kiwis121.iml")
	if err != nil {
		t.Errorf("Error:%s", err)
	}
	is.DeleteURI("test_data/.idea/kiwis121.iml")
	result, err = is.IsIgnored("test_data/.idea/kiwis121.iml")
	assert.Equal(t, result, false)
	assert.True(t, err == nil)
	is.DeleteURI("test_data")
}

func TestDefaultIgnore(t *testing.T) {
	s := NewIgnoreService(".testignore")

	var (
		isIgnored bool
		err       error
	)

	isIgnored, err = s.IsIgnored("test_data/build/abc.txt")
	assert.NoError(t, err)
	assert.Equal(t, true, isIgnored)

	isIgnored, err = s.IsIgnored("test_data/dev/a.log")
	assert.NoError(t, err)
	assert.Equal(t, true, isIgnored)
}
