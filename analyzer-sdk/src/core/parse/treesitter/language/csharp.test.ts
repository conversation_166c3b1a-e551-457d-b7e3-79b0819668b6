import {beforeAll, describe, expect, it} from "vitest";
import {TreeSitterParser} from "../parser";
import {LanguageIdEnum} from "../../share";
import {CSharpTreeSitterHandler} from "./csharp";

describe("test_getFuncParentNodeForRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
using System;

namespace MathOperations
{
    public class Calculator
    {
        public int Add(int a, int b)
        {
            int result = a + b;
            return result;
        }
        
        public int Subtract(int a, int b)
        {
            return a - b;
        }
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.CSharp, docText);
    const result = CSharpTreeSitterHandler.INSTANCE.getFuncParentNodeForRange(tree.rootNode, {
      start: {
        line: 9,
        character: 12
      }, 
      end: {
        line: 9, 
        character: 18
      }
    });
    
    expect(result.text).toEqual(`public int Add(int a, int b)
        {
            int result = a + b;
            return result;
        }`);
  });

  it("case2_constructor", async () => {
    const docText = `
using System;

namespace MathOperations
{
    public class Calculator
    {
        private int _value;
        
        public Calculator()
        {
            _value = 0;
        }
        
        public Calculator(int initialValue)
        {
            _value = initialValue;
        }
        
        public int Add(int a, int b)
        {
            int result = a + b;
            return result;
        }
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.CSharp, docText);
    const result = CSharpTreeSitterHandler.INSTANCE.getFuncParentNodeForRange(tree.rootNode, {
      start: {
        line: 10,
        character: 12
      }, 
      end: {
        line: 10, 
        character: 18
      }
    });
    
    expect(result.text).toEqual(`public Calculator()
        {
            _value = 0;
        }`);
  });
});

describe("test_getTypeParentNodeForRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_class", async () => {
    const docText = `
using System;

namespace MathOperations
{
    public class Calculator
    {
        public int Add(int a, int b)
        {
            int result = a + b;
            return result;
        }
    }
    
    public interface IMathOperation
    {
        int Execute(int a, int b);
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.CSharp, docText);
    const result = CSharpTreeSitterHandler.INSTANCE.getTypeParentNodeForRange(tree.rootNode, {
      start: {
        line: 16,
        character: 8
      }, 
      end: {
        line: 16, 
        character: 15
      }
    });
    
    expect(result.text).toEqual(`public interface IMathOperation
    {
        int Execute(int a, int b);
    }`);
  });

  it("case2_enum", async () => {
    const docText = `
using System;

namespace MathOperations
{
    public enum Operation
    {
        Add,
        Subtract,
        Multiply,
        Divide
    }
    
    public class Calculator
    {
        public int Calculate(int a, int b, Operation operation)
        {
            switch (operation)
            {
                case Operation.Add:
                    return a + b;
                case Operation.Subtract:
                    return a - b;
                case Operation.Multiply:
                    return a * b;
                case Operation.Divide:
                    return a / b;
                default:
                    throw new ArgumentException("Invalid operation");
            }
        }
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.CSharp, docText);
    const result = CSharpTreeSitterHandler.INSTANCE.getTypeParentNodeForRange(tree.rootNode, {
      start: {
        line: 8,
        character: 8
      }, 
      end: {
        line: 8, 
        character: 16
      }
    });
    
    expect(result.text).toEqual(`public enum Operation
    {
        Add,
        Subtract,
        Multiply,
        Divide
    }`);
  });
});

describe("test_getMethodInRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
using System;

namespace MathOperations
{
    public class Calculator
    {
        public int Add(int a, int b)
        {
            int result = a + b;
            return result;
        }
        
        public int Subtract(int a, int b)
        {
            return a - b;
        }
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.CSharp, docText);
    const range = {
      start: {
        line: 7,
        character: 8
      },
      end: {
        line: 11,
        character: 9
      }
    };
    
    const result = CSharpTreeSitterHandler.INSTANCE.getMethodInRange(tree.rootNode, range, 150, 250, false);
    
    expect(result.map(node => node.text)).toEqual([
      `public int Add(int a, int b)
        {
            int result = a + b;
            return result;
        }`
    ]);
  });
}); 