package ckg_metrics

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"ide/ckg/clients/ts"
	"ide/ckg/codekg/components/env"
	bizErr "ide/ckg/codekg/components/error"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/util"
	"net/http"
	"runtime"
	"sync"
	"time"

	"code.byted.org/data/http_mario_collector"
	"code.byted.org/data/http_mario_collector/pb_event"
	"code.byted.org/gopkg/ctxvalues"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"google.golang.org/protobuf/proto"
	"ide/ckg/codekg/components/logs"
)

const (
	caller      = "ckg.client.process"
	appID       = 604550
	appName     = "CKG"
	internalURL = "https://mcs.bytedance.net/v2/event/json"
)

var collector *http_mario_collector.MarioCollector
var teaHeader = &pb_event.Header{
	AppId:   proto.Uint32(appID),
	AppName: proto.String(appName),
}

func newCollector() {
	if env.IsMerlin() {
		logs.Info("tea report using internal url: %s", internalURL)
		collector = http_mario_collector.NewMarioCollectorWithUrl(internalURL, "")
	} else {
		logs.Info("tea report using default url")
		collector = http_mario_collector.NewMarioCollector()
	}
}

type Event interface {
	AddParam(key string, value interface{})
	AddTeaParam(key string, value interface{})
	NewSubEvent(name string) Event
	AllSubEvents() []Event
	Finish()
	ToTeaEvent() *pb_event.Event
	Report(ctx context.Context, needReport bool)
	ReportSync(ctx context.Context, needReport bool)
	GetStartTime() time.Time
}

type null struct{}

func (n *null) AddParam(key string, value interface{})          {}
func (n *null) AddTeaParam(key string, value interface{})       {}
func (n *null) NewSubEvent(name string) Event                   { return &null{} }
func (n *null) AllSubEvents() []Event                           { return []Event{} }
func (n *null) Finish()                                         {}
func (n *null) ToTeaEvent() *pb_event.Event                     { return &pb_event.Event{} }
func (n *null) Report(ctx context.Context, needReport bool)     {}
func (n *null) ReportSync(ctx context.Context, needReport bool) {}
func (n *null) GetStartTime() time.Time                         { return time.Time{} }

type event struct {
	ID            string                 `json:"id"`
	ParentID      string                 `json:"parent_id"`
	Name          string                 `json:"name"`
	UserID        string                 `json:"user_id"`
	StartTime     time.Time              `json:"-"`
	TeaParams     map[string]interface{} `json:"-"`
	TotalParams   map[string]interface{} `json:"total_params"`
	SubEvents     []Event                `json:"sub_events"`
	SourceProduct string                 `json:"source_product"`

	mu *sync.Mutex
}

func NewEvent(name, userID, parentID, sourceProduct string) Event {
	return &event{
		ID:            uuid.New().String(),
		ParentID:      parentID,
		Name:          name,
		UserID:        userID,
		SourceProduct: sourceProduct,
		StartTime:     time.Now(),
		TeaParams:     make(map[string]interface{}),
		TotalParams:   make(map[string]interface{}),
		SubEvents:     make([]Event, 0),
		mu:            new(sync.Mutex),
	}
}

const eventKey = "__ckg_event"

func SetEvent(ctx context.Context, event Event) context.Context {
	return context.WithValue(ctx, eventKey, event)
}

func GetEvent(ctx context.Context) Event {
	e := ctx.Value(eventKey)
	if e != nil {
		return e.(*event)
	}
	return &null{}
}

func (e *event) AddParam(key string, value interface{}) {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.TotalParams[key] = value
}

func (e *event) GetStartTime() time.Time {
	return e.StartTime
}

func (e *event) AddTeaParam(key string, value interface{}) {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.TeaParams[key] = value
	e.TotalParams[key] = value
}

func (e *event) NewSubEvent(name string) Event {
	e.mu.Lock()
	defer e.mu.Unlock()

	subEvent := NewEvent(name, e.UserID, e.ID, e.SourceProduct)
	e.SubEvents = append(e.SubEvents, subEvent)
	return subEvent
}

func (e *event) AllSubEvents() []Event {
	e.mu.Lock()
	defer e.mu.Unlock()

	result := make([]Event, 0)
	result = append(result, e.SubEvents...)
	for _, sub := range e.SubEvents {
		result = append(result, sub.AllSubEvents()...)
	}
	return result
}

func (e *event) Finish() {
	e.AddTeaParam(TeaParamDuration, time.Since(e.StartTime).Milliseconds())
}

func (e *event) ToTeaEvent() *pb_event.Event {
	e.mu.Lock()
	defer e.mu.Unlock()

	params := make(map[string]interface{})
	for k, v := range e.TeaParams {
		params[k] = v
	}
	params[TeaParamEventID] = e.ID
	params[TeaParamParentEventID] = e.ParentID
	params[TeaParamStartTime] = e.StartTime.UnixMilli()
	params[TeaParamProcessID] = model.GetProcessID()
	params[TeaParamSourceProduct] = e.SourceProduct
	params[TeaParamOsType] = runtime.GOOS
	params[TeaParamAIBackendHost] = env.GetHost()
	params[TeaParamExtensionVersion] = env.GetExtensionVersion()
	params[TeaParamIDEVersion] = env.GetIDEVersion()
	params[TeaParamRegion] = env.GetRegion()

	params[TeaParamCKGVersion] = ts.Version
	params[TeaParamCKGUserID] = env.GetUser()

	paramsBytes, _ := json.Marshal(params)
	return &pb_event.Event{
		Event:  proto.String(e.Name),
		Time:   proto.Uint32(uint32(time.Now().Unix())),
		Params: proto.String(string(paramsBytes)),
	}
}

var once sync.Once

// Report sends the event data to HDFS and report tea data
func (e *event) Report(ctx context.Context, needReport bool) {
	once.Do(newCollector)
	util.SafeGo(ctx, func() {
		e.ReportSync(ctx, needReport)
	})
}

func (e *event) ReportSync(ctx context.Context, needReport bool) {
	once.Do(newCollector)
	e.Finish()

	allEvents := make([]Event, 0)
	allEvents = append(allEvents, e)
	allEvents = append(allEvents, e.AllSubEvents()...)

	ctx = util.NewBackgroundContext(ctxvalues.LogIDDefault(ctx))
	// 上报数据到 tea
	err := collector.CollectEvents(caller, &pb_event.User{
		UserUniqueId: proto.String(e.UserID),
	}, teaHeader, lo.Map(allEvents, func(item Event, _ int) *pb_event.Event {
		return item.ToTeaEvent()
	}))
	if err != nil {
		logs.CtxDebug(ctx, "failed to collect tea events, err: %v", err)
	}

	if needReport { // TODO: 服务端 Report 接口适配后开启
		eventData, _ := json.Marshal(e)
		resp, err := reportData(ctx, e.UserID, env.GetToken(e.UserID), []ReportDataRequest{
			{
				Event:   e.Name,
				Time:    int(time.Now().Unix()),
				Payload: string(eventData),
			},
		})
		if err != nil || resp.Code == -1 {
			logs.CtxDebug(ctx, "failed to report event data to hdfs err: %s", err)
		}
	}
}

func reportData(ctx context.Context, userID string, token *model.TokenValue, request []ReportDataRequest) (*ReportDataResponse, error) {
	if env.TokenIsOutdated(ctx, token) {
		return nil, bizErr.ErrTokenOutOfDate
	}
	metadata := Metadata{
		Channel:          string(env.GetSourceProduct()),
		IdeVersion:       env.GetIDEVersion(),
		ExtensionVersion: env.GetExtensionVersion(),
		VersionCode:      env.GetVersionCode(),
		Region:           env.GetRegion(),
		AppID:            env.GetAppID(),
		UserID:           userID,
	}

	formData := FormData{
		EnvMetadata: metadata,
		Events:      request,
	}

	jsonData, err := json.Marshal(formData)
	if err != nil {
		return nil, errors.WithMessagef(bizErr.ErrMarshal, "Marshal formData err")
	}

	cli := resty.New().SetHostURL(env.GetHost()).SetHeader("X-APP-ID", env.GetAppID())
	req := cli.R().SetContext(ctx).SetHeader("Authorization", token.Scheme+" "+token.Token)
	resp, err := req.SetResult(&ReportDataResponse{}).
		SetMultipartFormData(map[string]string{
			"body": string(jsonData),
		}).
		Post(fmt.Sprintf("/api/ide/v1/report/clients"))
	if err != nil {
		return nil, &util.HTTPError{
			Code:       resp.StatusCode(),
			Message:    err.Error(),
			Extra:      "failed post Report",
			StatusCode: resp.StatusCode(),
		}
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, &util.HTTPError{
			Code:       resp.StatusCode(),
			Message:    resp.String(),
			Extra:      "failed post Report",
			StatusCode: resp.StatusCode(),
		}
	}

	return resp.Result().(*ReportDataResponse), nil
}
