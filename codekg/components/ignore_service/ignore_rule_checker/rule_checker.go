package ignore_rule_checker

import (
	"context"
	"errors"
	"github.com/samber/lo"
	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/ignore_service"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/logs"
	"os"
	"path/filepath"
	"slices"
	"strings"
)

type IgnoreRule string
type CheckerName string
type CheckerKind string

const (
	Folder CheckerKind = "folder"
	File   CheckerKind = "file"
	All    CheckerKind = "all"
)

type IgnoreRuleChecker interface {
	Name() CheckerName
	Rank() int
	Kind() CheckerKind
	SetIgnoreService(v2 IgnoreServiceV2)
	AddRules(ctx context.Context, userId string, base string, rules []IgnoreRule) error
	UpdateRulesFromTCC(ctx context.Context, userId string, base string) error
	IsIgnore(ctx context.Context, userId string, base string, path string, info os.FileInfo) (ignored bool, skip error, err error)
}

type UserIgnoreRuleChecker interface {
	UpdateRules(ctx context.Context, userId string, base string, rules []IgnoreRule) error
}

type IncludeRuleChecker interface {
	Name() CheckerName
	SetIgnoreService(v2 IgnoreServiceV2)
	AddRules(ctx context.Context, userId string, base string, rules []IgnoreRule) error
	UpdateRulesFromTCC(ctx context.Context, userId string, base string) error
	IsInclude(ctx context.Context, path string, info os.FileInfo) (bool, error)
}

type IgnoreServiceV2 interface {
	Register(ctx context.Context, ignoreRuleChecker IgnoreRuleChecker) error
	Unregister(ctx context.Context, checker CheckerName) error
	UpdateRules(ctx context.Context, checker CheckerName, userId string, base string, rules []IgnoreRule) error
	IsIgnore(ctx context.Context, userId string, base string, path string, info os.FileInfo, checkers ...CheckerName) (ignored bool, skip error, err error)

	IgnoreServiceV1Compatible

	GetConfig() *ckg_config.Config
	GetClient() knowledgebase.Client
}

type IgnoreServiceV1Compatible interface {
	InitRulesForProjectPath(ctx context.Context, userId string, projectAbsPath string, defaultIgnoreRules string, customizedIgnoreRules string) error
	UpdateGitIgnoreRulesWhenUpsert(ctx context.Context, userId string, projectAbsPath string, relPath string) error
	UpdateGitIgnoreRulesWhenDelete(ctx context.Context, userId string, projectAbsPath string, relPath string) error
	IsIgnored(ctx context.Context, userId string, base string, path string, info os.FileInfo) (ignored bool, err error)
	GetGitignoreService(ctx context.Context, userId string) ignore_service.IgnoreService
}

type service struct {
	config              *ckg_config.Config
	cli                 knowledgebase.Client
	ignoreRuleCheckers  map[CheckerName]IgnoreRuleChecker
	includeRuleCheckers map[CheckerName]IncludeRuleChecker
}

func NewIgnoreServiceV2(config *ckg_config.Config, cli knowledgebase.Client) IgnoreServiceV2 {
	return &service{
		config:              config,
		cli:                 cli,
		ignoreRuleCheckers:  make(map[CheckerName]IgnoreRuleChecker),
		includeRuleCheckers: make(map[CheckerName]IncludeRuleChecker),
	}
}

// Register 新增一个 ignore rule checker，或覆盖原有的同名 checker
func (s *service) Register(ctx context.Context, ignoreRuleChecker IgnoreRuleChecker) error {
	if ignoreRuleChecker == nil {
		logs.CtxWarn(ctx, "[Register] ignoreRuleChecker must not be nil")
		return errors.New("IgnoreRuleChecker is nil")
	}
	ignoreRuleChecker.SetIgnoreService(s)
	s.ignoreRuleCheckers[ignoreRuleChecker.Name()] = ignoreRuleChecker
	return nil
}

func (s *service) Unregister(ctx context.Context, checker CheckerName) error {
	delete(s.ignoreRuleCheckers, checker)
	return nil
}

func (s *service) UpdateRules(ctx context.Context, checker CheckerName, userId string, base string, rules []IgnoreRule) error {
	var ignoreRuleChecker IgnoreRuleChecker
	var ok bool
	if ignoreRuleChecker, ok = s.ignoreRuleCheckers[checker]; !ok || ignoreRuleChecker == nil {
		logs.CtxWarn(ctx, "[AddRules] update failed, cannot find %s, ignoreRuleChecker: %+v", checker, s.ignoreRuleCheckers)
		return nil
	}
	return ignoreRuleChecker.AddRules(ctx, userId, base, rules)
}

func (s *service) IsIgnore(ctx context.Context, userId string, base string, path string, info os.FileInfo, checkers ...CheckerName) (ignored bool, skip error, err error) {
	var cs []IgnoreRuleChecker
	if len(checkers) == 0 {
		// 如果传入的 checkers 为空，则默认检查所有的 checker
		cs = lo.Filter(lo.Values(s.ignoreRuleCheckers), func(checker IgnoreRuleChecker, _ int) bool {
			return checker != nil
		})
	} else {
		cs = make([]IgnoreRuleChecker, 0)
		for _, checker := range checkers {
			if ignoreRuleChecker, ok := s.ignoreRuleCheckers[checker]; ok && ignoreRuleChecker != nil {
				cs = append(cs, ignoreRuleChecker)
			}
		}
	}
	// 按照 rank 顺序，从低到高排列，低 rank 的先执行
	// [0, 300): 不读取文件内容，内存压力小
	// [300, 500): 不读取文件内容，用户自定义的规则
	// [500, +\inf): 读取文件内容，内存压力可能较大，因此放在最后
	slices.SortFunc(cs, func(a, b IgnoreRuleChecker) int { return a.Rank() - b.Rank() })
	for _, checker := range cs {
		if info.IsDir() && checker.Kind() == File {
			// 如果该 path 为 folder，跳过所有仅作用于 File 的 checker
			continue
		}
		if err = checker.UpdateRulesFromTCC(ctx, userId, base); err != nil {
			logs.CtxWarn(ctx, "[IsIgnore] update checker's (%s) rules from tcc failed, userId: %s, err: %+v", checker.Name(), userId, err)
			continue
		}
		if ignored, skip, err = checker.IsIgnore(ctx, userId, base, path, info); err != nil {
			logs.CtxWarn(ctx, "[IsIgnore] check path %s failed, err: %+v", path, err)
			return
		} else if ignored {
			return
		}
	}
	return false, nil, nil
}

func (s *service) GetConfig() *ckg_config.Config { return s.config }

// GetClient 旨在获得服务端 client。
// 由于 CKG 启动时不再传入 host，其需要在 SetUp 时传入，因此 main 中无法完全初始化 IgnoreServiceV2，于是在 GetClient 时懒创建服务端 client。
// 只要确保在各 Handler 中使用 IgnoreServiceV2，且之前正确调用 SetUp，就不会返回 nil。
// 如果 GetClient 返回空，表示尚未正确调用 SetUp。
func (s *service) GetClient() knowledgebase.Client {
	if s.cli == nil {
		cli, err := knowledgebase.GetClient()
		if cli == nil || err != nil {
			return nil
		}
		s.cli = cli
	}
	return s.cli
}

// InitRulesForProjectPath 只是为了兼容 V1 接口在 InitProject 上的使用。
// 和 IgnoreService 的 AddProject 实现几乎一样：
// 1. CKG 默认忽略的内容 (From TCC)；
// 2. 用户自定义的忽略内容；
// 3. gitignore 的忽略内容。
func (s *service) InitRulesForProjectPath(ctx context.Context, userId string, projectAbsPath string, defaultIgnoreRules string, customizedIgnoreRules string) error {
	if tccIgnoreChecker, ok := s.ignoreRuleCheckers[TCCIgnoreRule].(*TCCIgnoreRuleChecker); ok {
		rs := strings.Split(defaultIgnoreRules, "\n")
		rules := lo.Map(rs, func(rule string, _ int) IgnoreRule { return IgnoreRule(rule) })
		err := tccIgnoreChecker.UpdateRules(ctx, userId, projectAbsPath, rules)
		if err != nil {
			return err
		}
	}
	if traeIgnoreChecker, ok := s.ignoreRuleCheckers[TraeIgnoreRule].(*TraeIgnoreRuleChecker); ok {
		rs := strings.Split(customizedIgnoreRules, "\n")
		rules := lo.Map(rs, func(rule string, _ int) IgnoreRule { return IgnoreRule(rule) })
		err := traeIgnoreChecker.UpdateRules(ctx, userId, projectAbsPath, rules)
		if err != nil {
			return err
		}
	}
	if gitignoreChecker, ok := s.ignoreRuleCheckers[GitIgnoreRule].(*GitIgnoreRuleChecker); ok {
		err := gitignoreChecker.UpdateRules(ctx, userId, projectAbsPath, []IgnoreRule{})
		if err != nil {
			return err
		}
	}
	return nil
}

// UpdateGitIgnoreRulesWhenUpsert 只是为了兼容 V1 接口的 CreateOrChangeURI
func (s *service) UpdateGitIgnoreRulesWhenUpsert(ctx context.Context, userId string, projectAbsPath string, relPath string) error {
	if gitignoreChecker, ok := s.ignoreRuleCheckers[GitIgnoreRule].(*GitIgnoreRuleChecker); ok {
		path := filepath.Clean(filepath.Join(projectAbsPath, relPath))
		is := gitignoreChecker.GetIgnoreService(ctx, userId)
		return is.CreateOrChangeURI(ctx, path)
	}
	return nil
}

// UpdateGitIgnoreRulesWhenDelete 只是为了兼容 V1 接口的 DeleteURI
func (s *service) UpdateGitIgnoreRulesWhenDelete(ctx context.Context, userId string, projectAbsPath string, relPath string) error {
	if gitignoreChecker, ok := s.ignoreRuleCheckers[GitIgnoreRule].(*GitIgnoreRuleChecker); ok {
		path := filepath.Clean(filepath.Join(projectAbsPath, relPath))
		is := gitignoreChecker.GetIgnoreService(ctx, userId)
		return is.DeleteURI(ctx, path)
	}
	return nil
}

// IsIgnored 只是为了兼容 V1 接口的 IsIgnored
func (s *service) IsIgnored(ctx context.Context, userId string, base string, path string, info os.FileInfo) (ignored bool, err error) {
	if gitignoreChecker, ok := s.ignoreRuleCheckers[GitIgnoreRule].(*GitIgnoreRuleChecker); ok {
		is := gitignoreChecker.GetIgnoreService(ctx, userId)
		if ignored, err = is.IsIgnored(ctx, path); err == nil && ignored {
			return true, nil
		}
	}
	if tccIgnoreChecker, ok := s.ignoreRuleCheckers[TCCIgnoreRule].(*TCCIgnoreRuleChecker); ok {
		if ignored, _, err = tccIgnoreChecker.IsIgnore(ctx, userId, base, path, info); err == nil && ignored {
			return true, nil
		}
	}
	if traeIgnoreChecker, ok := s.ignoreRuleCheckers[TraeIgnoreRule].(*TraeIgnoreRuleChecker); ok {
		if ignored, _, err = traeIgnoreChecker.IsIgnore(ctx, userId, base, path, info); err == nil && ignored {
			return true, nil
		}
	}
	return
}

// GetGitignoreService 只是为了兼容 V1 接口，直接获得 V1 的 ignore service。
// 获取 ignore service v1，除非没有注册 gitignore rule，否则不可能返回 nil。
func (s *service) GetGitignoreService(ctx context.Context, userId string) ignore_service.IgnoreService {
	if gitignoreChecker, ok := s.ignoreRuleCheckers[GitIgnoreRule].(*GitIgnoreRuleChecker); ok {
		return gitignoreChecker.GetIgnoreService(ctx, userId)
	}
	return nil
}
