package data_manager

import (
	"context"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/protocol"
	"sync"

	"ide/ckg/codekg/components/logs"

	"github.com/samber/lo"
)

// GetProjectsBuildStatus 用于返回当前项目的构建进度
// 如果 projectInitProgress 和 bgProjectInitProgress 都存在，则返回 projectInitProgress 的进度
func (m *manager) GetProjectsBuildStatus(ctx context.Context, req *protocol.GetBuildStatusRequest) map[model.URI]*protocol.ProjectBuildStatus {
	var projectIdsToGetStatus map[string]bool
	if req.ProjectIds != nil {
		projectIdsToGetStatus = make(map[string]bool)
		for _, projectId := range req.ProjectIds {
			projectIdsToGetStatus[projectId] = true
		}
	}

	result := make(map[model.URI]*protocol.ProjectBuildStatus)
	m.getProjectsBuildStatus(ctx, m.bgProjectInitProgress, result, projectIdsToGetStatus)
	m.getProjectsBuildStatus(ctx, m.projectInitProgress, result, projectIdsToGetStatus)
	return result
}

func (m *manager) getProjectsBuildStatus(ctx context.Context, projectInitProgress *sync.Map, result map[model.URI]*protocol.ProjectBuildStatus, projectIdsToGetStatus map[string]bool) {
	projectInitProgress.Range(func(projectID, projectProgress any) bool {
		if projectIdsToGetStatus != nil && !projectIdsToGetStatus[projectID.(string)] {
			return true
		}

		emptyProject := false
		if m.GetProjectType(ctx, projectID.(string)) != model.ProjectTypeVirtual {
			emptyProject = !m.ignoreService.IsNotEmptyFolder(ctx, projectID.(model.URI))
		}
		if projectProgress.(*projectBuildProgress).totalFiles == 0 {
			result[projectID.(model.URI)] = &protocol.ProjectBuildStatus{
				Status:            protocol.BuildStatus_finished,
				Progress:          1,
				SuccessIndexFiles: projectProgress.(*projectBuildProgress).successIndexFiles.Load(),
				FailedIndexFiles:  projectProgress.(*projectBuildProgress).failedIndexFiles.Load(),
				TotalIndexFiles:   projectProgress.(*projectBuildProgress).builtFiles.Load(),
				EmptyProject:      emptyProject,
			}
		} else {
			result[projectID.(model.URI)] = &protocol.ProjectBuildStatus{
				Status: protocol.BuildStatus_building,
				Progress: float32(projectProgress.(*projectBuildProgress).builtFiles.Load()) /
					float32(projectProgress.(*projectBuildProgress).totalFiles),
				SuccessIndexFiles: projectProgress.(*projectBuildProgress).successIndexFiles.Load(),
				FailedIndexFiles:  projectProgress.(*projectBuildProgress).failedIndexFiles.Load(),
				TotalIndexFiles:   projectProgress.(*projectBuildProgress).builtFiles.Load(),
				EmptyProject:      emptyProject,
			}
		}
		return true
	})
}

func (m *manager) GetDocumentsIndexStatus(ctx context.Context, req *protocol.GetDocumentsIndexStatusRequest) map[model.URI]*protocol.ProjectDocumentsIndexStatus {
	results := make(map[model.URI]*protocol.ProjectDocumentsIndexStatus)
	for _, projectId := range req.ProjectIds {
		logs.CtxInfo(ctx, "[GetDocumentsIndexStatus] Processing project: %s", projectId)

		projEntStorage, err := m.GetEntityStorage(ctx, model.URI(projectId))
		if err != nil {
			logs.CtxError(ctx, "[GetDocumentsIndexStatus] Failed to get entity storage for project %s: %v", projectId, err)
			continue
		}

		logs.CtxInfo(ctx, "[GetDocumentsIndexStatus] Successfully got entity storage for project: %s", projectId)

		statuses, err := projEntStorage.GetAllURIDocumentIndexStatus(ctx, projEntStorage.GetConn())
		if err != nil {
			logs.CtxError(ctx, "[GetDocumentsIndexStatus] Failed to get document index status for project %s: %v", projectId, err)
			continue
		}

		logs.CtxInfo(ctx, "[GetDocumentsIndexStatus] Got %d document statuses for project: %s", len(statuses), projectId)

		// 1. 如果 uri_meta 表中存在正在构建的文档，则更新项目状态为索引中
		projStatus := model.BuildStatusFinished
		docsStatuses := lo.Map(statuses, func(item data_storage.DocumentIndexStatus, _ int) *protocol.DocumentIndexStatus {
			if item.Status == model.BuildStatusBuilding {
				projStatus = model.BuildStatusBuilding
			}
			return &protocol.DocumentIndexStatus{
				Status: protocol.BuildStatus(item.Status),
				Name:   item.Name,
				Uri:    model.URI(item.Uri),
			}
		})

		// 2. 否则，如果 process map 中项目显示正在构建，则更新项目状态为索引中
		if projStatus != model.BuildStatusBuilding {
			p, ok := m.projectInitProgress.Load(projectId)
			if ok && p.(*projectBuildProgress).building.Load() {
				projStatus = model.BuildStatusBuilding
			}
		}

		results[model.URI(projectId)] = &protocol.ProjectDocumentsIndexStatus{
			Status:          protocol.BuildStatus(projStatus),
			DocumentsStatus: docsStatuses,
		}

		logs.CtxInfo(ctx, "[GetDocumentsIndexStatus] Added project %s to results with %d document statuses", projectId, len(docsStatuses))
	}

	logs.CtxInfo(ctx, "[GetDocumentsIndexStatus] Final results contain %d projects", len(results))

	return results
}
