import {LanguageIdEnum, ParserEnum, SyntaxNodeWithUri, UsageNodeInfo} from "../../share";
import {DocumentUri, Location} from "vscode-languageserver-types";
import type {SyntaxNode} from "web-tree-sitter";
import {PSIParser} from "../../psi/parser";
import {PsiExecutor} from "../../psi/executor";
import {LSPParser} from "../../lsp/parser";
import {LspExecutor} from "../../lsp/executor";

export abstract class CommonFunctionalityExecutor {
  abstract languageId: LanguageIdEnum;

  async getDefinitionNode(parses: ParserEnum[], uri: DocumentUri, node: SyntaxNode, resultFilter: (location: Location) => boolean): Promise<SyntaxNodeWithUri[]> {
    for (const parse of parses) {
      switch (parse) {
        case ParserEnum.PSI_LSP:
          if (PSIParser.INSTANCE.initialized()) {
            return PsiExecutor.getDefinitionNode(this.languageId, uri, node, resultFilter);
          }
          if (LSPParser.INSTANCE.initialized()) {
            return LspExecutor.getDefinitionNode(this.languageId, uri, node, resultFilter);
          }
          break;
        default:
          return [];
      }
    }

    return [];
  }

  async getUsage(parses: ParserEnum[], uri: DocumentUri, node: SyntaxNode, resultFilter: (location: Location) => boolean): Promise<UsageNodeInfo[]> {
    for (const parse of parses) {
      switch (parse) {
        case ParserEnum.PSI_LSP:
          if (PSIParser.INSTANCE.initialized()) {
            return PsiExecutor.getUsage(this.languageId, uri, node, resultFilter);
          }
          if (LSPParser.INSTANCE.initialized()) {
            return LspExecutor.getUsage(this.languageId, uri, node, resultFilter);
          }
          break;
        default:
          return [];
      }
    }

    return [];
  }

  async getUsageInUri(parses: ParserEnum[], uri: DocumentUri, node: SyntaxNode, scopeUri: DocumentUri): Promise<UsageNodeInfo[]> {
    for (const parse of parses) {
      switch (parse) {
        case ParserEnum.PSI_LSP:
          if (PSIParser.INSTANCE.initialized()) {
            return PsiExecutor.getUsageInUri(this.languageId, uri, node, scopeUri);
          }
          if (LSPParser.INSTANCE.initialized()) {
            return LspExecutor.getUsageInUri(this.languageId, uri, node, scopeUri);
          }
          break;
        default:
          return [];
      }
    }

    return [];
  }

  getTypeDefinitionPreFilter(_parses: ParserEnum[], _importInfo: any): (node: SyntaxNode) => boolean {
    return null;
  }
}