import {TextDocument} from "vscode-languageserver-textdocument";
import {Range} from "vscode-languageserver-types";
import {TreeSitterDispatcher} from "../../parse/treesitter/dispatcher";
import {TreeSitterFunctionalityEnum} from "../../parse/treesitter/share";
import {Logger} from "../../../utils/logger";
import {TreeSitterParser} from "../../parse/treesitter/parser";
import {
  EVENT_CATEGORY_GENERAL,
  EVENT_NAME_FAILED, EVENT_NAME_TRIGGER, EVENT_VALUE_ANALYSIS_FAILED,
  EVENT_VALUE_PARSE_TREE_FAILED,
  reportEvent,
} from "../../../utils/event";
import {SyntaxNode} from "web-tree-sitter";

const EVENT_PROCESS_GET_METHODS_IN_RANGE = "get_methods_in_range";

export function doGetMethodInRange(document: TextDocument, range: Range, completelyIncluded: boolean): {text: string, range: Range}[] {
  Logger.info(`doGetMethodInRange: '${document.uri}' (${range.start.line}, ${range.start.character}) - (${range.end.line}, ${range.end.character})`);
  reportEvent(document.languageId, EVENT_CATEGORY_GENERAL, EVENT_PROCESS_GET_METHODS_IN_RANGE, EVENT_NAME_TRIGGER).then();

  const tree = TreeSitterParser.INSTANCE.parse(document.languageId, document.getText());
  if (!tree) {
    Logger.error(`doGetMethodInRange: failed to parse document '${document.uri}'`);
    reportEvent(document.languageId, EVENT_CATEGORY_GENERAL, EVENT_PROCESS_GET_METHODS_IN_RANGE, EVENT_NAME_FAILED, EVENT_VALUE_PARSE_TREE_FAILED).then();
    return [];
  }

  const result: SyntaxNode[] = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetMethodInRange, tree.rootNode, range, document.offsetAt(range.start), document.offsetAt(range.end), completelyIncluded);
  if (!result) {
    Logger.error(`doGetMethodInRange: result is null`);
    reportEvent(document.languageId, EVENT_CATEGORY_GENERAL, EVENT_PROCESS_GET_METHODS_IN_RANGE, EVENT_NAME_FAILED, EVENT_VALUE_ANALYSIS_FAILED).then();
    return [];
  }

  return result.map(node => {
    return {
      text: node.text,
      range: {
        start: {
          line: node.startPosition.row,
          character: node.startPosition.column,
        },
        end: {
          line: node.endPosition.row,
          character: node.endPosition.column,
        },
      },
    };
  });
}