package knowledgebase

import (
	"github.com/samber/lo"
	"ide/ckg/codekg/model"
)

type FileSegment struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Path       string                 `json:"path"`
	Attributes map[string]interface{} `json:"attributes"`
}

type CodeChunkSegment struct {
	ID        string `json:"id"`
	StartLine int    `json:"start_line"`
	EndLine   int    `json:"end_line"`
	Content   string `json:"content"`
}

type TextSegment struct {
	ID        string `json:"id"`
	StartLine int    `json:"start_line"`
	EndLine   int    `json:"end_line"`
	Content   string `json:"content"`
}

type MethodSegment struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	StartLine  int                    `json:"start_line"`
	EndLine    int                    `json:"end_line"`
	Content    []string               `json:"content"`
	Attributes map[string]interface{} `json:"attributes"`
}

type ClassTopLevelSegment struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	StartLine  int                    `json:"start_line"`
	EndLine    int                    `json:"end_line"`
	Content    []string               `json:"content"`
	Attributes map[string]interface{} `json:"attributes"`
}

type ClassSegment struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	StartLine  int                    `json:"start_line"`
	EndLine    int                    `json:"end_line"`
	Content    string                 `json:"content"`
	Attributes map[string]interface{} `json:"attributes"`
}

type FileTopLevelSegment struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	StartLine  int                    `json:"start_line"`
	EndLine    int                    `json:"end_line"`
	Content    []string               `json:"content"`
	Attributes map[string]interface{} `json:"attributes"`
}

type FolderSegment struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Path       string                 `json:"path"`
	Attributes map[string]interface{} `json:"attributes"`
}

type Relation struct {
	StartID    string                 `json:"start_id"`
	StartName  string                 `json:"start_name"`
	EndID      string                 `json:"end_id"`
	EndName    string                 `json:"end_name"`
	Type       string                 `json:"type"`
	Attributes map[string]interface{} `json:"attributes"`
}

type Segment struct {
	ID                   string                `json:"id"`
	FileID               string                `json:"file_id"`
	ProjectID            string                `json:"project_id"`
	Type                 string                `json:"type"`
	Score                float32               `json:"score"`
	FolderSegment        *FolderSegment        `json:"folder"`
	TextSegment          *TextSegment          `json:"text"`
	CodeChunkSegment     *CodeChunkSegment     `json:"code_chunk"`
	FileSegment          *FileSegment          `json:"file"`
	FileTopLevelSegment  *FileTopLevelSegment  `json:"file_top_level"`
	ClassSegment         *ClassSegment         `json:"class"`
	ClassTopLevelSegment *ClassTopLevelSegment `json:"class_top_level"`
	MethodSegment        *MethodSegment        `json:"method"`
	Relation             *Relation             `json:"relation"`
}

// GetEntityType 仿照下面的 ToEntityOrRelation 中的逻辑，根据 Segment 生成对应的 EntityType。
// 其中，SegTypeRelation 返回 "" 空字符串，表示不对应任何 entity 类型。
func (s *Segment) GetEntityType() model.EntityType {
	switch s.Type {
	case model.SegTypeCodeChunk:
		return model.CodeChunk
	case model.SegTypeTextChunk:
		return model.Text
	case model.SegTypeFile:
		return model.File
	case model.SegTypeClass:
		return model.Clazz
	case model.SegTypeMethod:
		return model.Method
	case model.SegTypeCLassTopLevel:
		return model.ClassTopLevel
	case model.SegTypeFileTopLevel:
		return model.FileTopLevel
	default:
		return ""
	}
}

func (s *Segment) ToEntityOrRelation(uri *model.URIStatus) (*model.Entity, *model.Relation, []*model.AliasEntity) {
	var entity *model.Entity
	var relation *model.Relation
	aliasEntity := make([]*model.AliasEntity, 0)

	switch s.Type {
	case model.SegTypeCodeChunk:
		attribute := make(map[string]interface{})
		attribute[model.AttributeLabelContent] = s.CodeChunkSegment.Content
		attribute[model.AttributeLabelStartLine] = s.CodeChunkSegment.StartLine
		attribute[model.AttributeLabelEndLine] = s.CodeChunkSegment.EndLine
		entity = model.CreateEntity(s.CodeChunkSegment.ID, s.CodeChunkSegment.ID, uri.AbsPath, model.CodeChunk, attribute)
	case model.SegTypeTextChunk:
		attribute := make(map[string]interface{})
		attribute[model.AttributeLabelContent] = s.TextSegment.Content
		attribute[model.AttributeLabelStartLine] = s.TextSegment.StartLine
		attribute[model.AttributeLabelEndLine] = s.TextSegment.EndLine
		entity = model.CreateEntity(s.TextSegment.ID, s.TextSegment.ID, uri.AbsPath, model.Text, attribute)
	case model.SegTypeRelation:
		relation = model.CreateRelation(
			s.Relation.StartName,
			s.Relation.StartID,
			uri.AbsPath, matchRelationType(s.Relation.Type),
			s.Relation.EndID, s.Relation.EndName,
			s.Relation.Attributes)
	case model.SegTypeFile:
		entity = model.CreateEntity(
			s.FileSegment.ID,
			s.FileSegment.Name,
			uri.AbsPath,
			model.File,
			s.FileSegment.Attributes)
		alias := createFileAliasEntity(entity.ID, uri.AbsPath)
		aliasEntity = append(aliasEntity, alias...)
	case model.SegTypeFileTopLevel:
		attribute := s.FileTopLevelSegment.Attributes
		attribute[model.AttributeLabelStartLine] = s.FileTopLevelSegment.StartLine
		attribute[model.AttributeLabelEndLine] = s.FileTopLevelSegment.EndLine
		attribute[model.AttributeLabelContent] = s.FileTopLevelSegment.Content
		entity = model.CreateEntity(
			s.FileTopLevelSegment.ID,
			s.FileTopLevelSegment.Name,
			uri.AbsPath,
			model.FileTopLevel,
			attribute)
	case model.SegTypeClass:
		attribute := s.ClassSegment.Attributes
		attribute[model.AttributeLabelStartLine] = s.ClassSegment.StartLine
		attribute[model.AttributeLabelEndLine] = s.ClassSegment.EndLine
		attribute[model.AttributeLabelContent] = s.ClassSegment.Content
		entity = model.CreateEntity(
			s.ClassSegment.ID,
			s.ClassSegment.Name,
			uri.AbsPath,
			model.Clazz,
			attribute)
		alias := getAliasEntity(attribute, entity.ID)
		aliasEntity = append(aliasEntity, alias...)
	case model.SegTypeCLassTopLevel:
		attribute := s.ClassTopLevelSegment.Attributes
		attribute[model.AttributeLabelStartLine] = s.ClassTopLevelSegment.StartLine
		attribute[model.AttributeLabelEndLine] = s.ClassTopLevelSegment.EndLine
		attribute[model.AttributeLabelContent] = s.ClassTopLevelSegment.Content
		entity = model.CreateEntity(
			s.ClassTopLevelSegment.ID,
			s.ClassTopLevelSegment.Name,
			uri.AbsPath,
			model.ClassTopLevel,
			attribute)
	case model.SegTypeMethod:
		attribute := s.MethodSegment.Attributes
		attribute[model.AttributeLabelStartLine] = s.MethodSegment.StartLine
		attribute[model.AttributeLabelEndLine] = s.MethodSegment.EndLine
		attribute[model.AttributeLabelContent] = s.MethodSegment.Content
		entity = model.CreateEntity(
			s.MethodSegment.ID,
			s.MethodSegment.Name,
			uri.AbsPath,
			model.Method,
			attribute)
		alias := getAliasEntity(attribute, entity.ID)
		aliasEntity = append(aliasEntity, alias...)
	default:
		break
	}
	return entity, relation, aliasEntity
}

type File struct {
	FileID      string     `json:"file_id"`
	Content     string     `json:"content"`
	ContentHash string     `json:"content_hash"`
	UniqueID    string     `json:"unique_id"`
	Segments    []*Segment `json:"segments"`
}

func (f *File) ToURIData(uri *model.URIStatus) *model.URIData {
	var entities = make([]*model.Entity, 0)
	var relations = make([]*model.Relation, 0)
	var aliasEntities = make([]*model.AliasEntity, 0)

	for _, seg := range f.Segments {
		entity, relation, alias := seg.ToEntityOrRelation(uri)
		if entity != nil {
			entities = append(entities, entity)
		}
		if relation != nil {
			relations = append(relations, relation)
		}
		if alias != nil {
			aliasEntities = append(aliasEntities, alias...)
		}
	}

	return &model.URIData{
		Status:      *uri,
		Entities:    entities,
		Relation:    relations,
		AliasEntity: aliasEntities,
	}
}

type ProjectFile struct {
	ProjectID string  `json:"project_id"`
	Files     []*File `json:"files"`
}

type Project struct {
	ProjectID string `json:"project_id"`
}

type GetOrCreateKnowledgebaseRequest struct {
	IDEID    string     `json:"ide_id"`
	Projects []*Project `json:"projects"`
}

type GetUnIndexedFilesRequest struct {
	ProjectFiles []*ProjectFile `json:"project_files"`
}

type GetUnIndexedFilesResponse struct {
	ProjectFiles []*ProjectFile `json:"project_files"`
}

type IndexFilesRequest struct {
	ProjectFiles []*ProjectFile `json:"project_files"`
}

type IndexFilesResponse struct {
	ProjectFiles []*ProjectFile `json:"project_files"`
}

func (r *IndexFilesResponse) ToURIData(uri *model.URIStatus) []*model.URIData {
	var result = make([]*model.URIData, 0)
	for _, projectFile := range r.ProjectFiles {
		for _, file := range projectFile.Files {
			result = append(result, file.ToURIData(uri))
		}
	}

	return result
}

type RetrieveRequest struct {
	ProjectIDs []string `json:"project_ids"`
	Content    string   `json:"content"`
	TopN       int      `json:"top_n"`
}

type RetrieveResponse struct {
	Segments []*Segment `json:"segments"`
}

type EntityDetectRequest struct {
	UserInput string `json:"user_input"`
}

type Entity struct {
	Mention string `json:"mention"`
	Type    string `json:"type"`
}

type EntityDetectResponse struct {
	ResultEntities []Entity `json:"entities"`
}

type EntitySearchEvent struct {
	SearchResult []*SearchResult `json:"search_result"`
	SessionId    string          `json:"session_id"`
}

type SearchResult struct {
	ID         string  `json:"id"`
	Score      float32 `json:"score"`
	RecallType string  `json:"recall_type"`
	IsInFinal  bool    `json:"is_in_final"`
	EntityType string  `json:"entity_type"`
	EntityInfo string  `json:"entity_info"`
}

type FeatureConfig struct {
	Name    string `json:"name"`
	Enabled bool   `json:"is_enabled"`
	Config  string `json:"config"`
}

type GetFeaturesConfigResponse struct {
	Features []*FeatureConfig `json:"features"`
}

type GetABConfigResponse struct {
	ABParams string `json:"ab_params"`
}

type DeleteFile struct {
	FileID string `json:"file_id"`
}

type DeleteProjectFile struct {
	ProjectID string        `json:"project_id"`
	Files     []*DeleteFile `json:"files"`
}

type DeleteFileRequest struct {
	DeleteFiles []*DeleteProjectFile `json:"project_files"`
}

type EmbeddingRecallTag int

const (
	Query    EmbeddingRecallTag = 0
	Selected EmbeddingRecallTag = 1
)

type AliasRecallTag int

const (
	ContainAlias   AliasRecallTag = 0
	ContainContent AliasRecallTag = 1
)

type IntentTag int

const (
	CodeIntent         IntentTag = 1
	ComplexIntent      IntentTag = 2
	DangerousIntent    IntentTag = 3
	DocIntent          IntentTag = 4
	LinesDocIntent     IntentTag = 5
	EditIntent         IntentTag = 6
	FixIntent          IntentTag = 7
	ExplainIntent      IntentTag = 8
	GeneralQaIntent    IntentTag = 9
	TestIntent         IntentTag = 10
	InvalidIntent      IntentTag = 11
	LintErrorFixIntent IntentTag = 12
	ProjectIntent      IntentTag = 13
	WebSearchIntent    IntentTag = 14
	DefaultIntent      IntentTag = 15
)

type NerRecallTag int

const (
	NERCurFolder    NerRecallTag = 0
	NERCurFile      NerRecallTag = 1
	NERCurClazz     NerRecallTag = 2
	NERCurMethod    NerRecallTag = 3
	NERCurCodeChunk NerRecallTag = 4
	NERFolder       NerRecallTag = 5
	NERFile         NerRecallTag = 6
	NERClazz        NerRecallTag = 7
	NERMethod       NerRecallTag = 8
	NERCodeChunk    NerRecallTag = 9
	NEROther        NerRecallTag = 10
)

type EntityType int

const (
	FolderType        EntityType = 1
	FileType          EntityType = 2
	ClazzType         EntityType = 3
	MethodType        EntityType = 4
	CodeChunkType     EntityType = 5
	TextType          EntityType = 6
	OtherType         EntityType = 7
	FileTopLevelType  EntityType = 8
	ClassTopLevelType EntityType = 9
)

type RecallType int

const (
	ALIAS      RecallType = 0
	NER        RecallType = 1
	EMBED      RecallType = 2
	AT         RecallType = 3
	LOCALEMBED RecallType = 2
)

type EmbeddingCandidate struct {
	RecallScore float64            `json:"recall_score"`
	RecallTag   EmbeddingRecallTag `json:"recall_tag"`
}

type AliasCandidate struct {
	RecallScore float64        `json:"recall_score"`
	RecallTag   AliasRecallTag `json:"recall_tag"`
}

type NerCandidate struct {
	RecallScore float64      `json:"recall_score"`
	RecallTag   NerRecallTag `json:"recall_tag"`
}

type Candidate struct {
	ID                 string              `json:"id"`
	Content            string              `json:"content"`
	EntityType         EntityType          `json:"entity_type"`
	RecallType         RecallType          `json:"recall_type"`
	NERCandidate       *NerCandidate       `json:"ner_candidate"`
	AliasCandidate     *AliasCandidate     `json:"alias_candidate"`
	EmbeddingCandidate *EmbeddingCandidate `json:"embed_candidate"`
	Attributes         map[string]string   `json:"attributes"`
}

type EntityRerankRequest struct {
	UserInput  string       `json:"user_input"`
	Intent     IntentTag    `json:"intent"`
	Candidates []*Candidate `json:"candidates"`
	SessionID  string       `json:"session_id"`
}

type CandidateResponse struct {
	ID            string       `json:"id"`
	Score         float64      `json:"score"`
	RecallType    []RecallType `json:"recall_type"`
	RecallTag     []string     `json:"recall_tag"`
	OriginalScore []float64    `json:"original_score"`
}

type EntityRerankResponse struct {
	Candidates []*CandidateResponse `json:"candidates"`
}

func matchRelationType(relationType string) model.EntityRelationType {
	switch relationType {
	case "fileToFile":
		return model.FileToFile
	case "classToFile":
		return model.ClassToFile
	case "classTopLevelToClass":
		return model.ClassTopLevelToClass
	case "fileTopLevelToFile":
		return model.FileTopLevelToFile
	case "methodToClass":
		return model.MethodToClass
	case "methodToFile":
		return model.MethodToFile
	case "callerToCallee":
		return model.CallerToCallee
	}
	return ""
}

type CachedFile struct {
	FileID   string `json:"file_id"`
	UniqueID string `json:"unique_id"`
}

// SplitFilesRequest is copied from kiwis/api/idl/kitex_gen/knowledgebase/entity.go
type SplitFilesRequest struct {
	DatastoreName  string       `thrift:"DatastoreName,1" frugal:"1,default,string" json:"datastore_name"`
	Files          []*SplitFile `thrift:"Files,2" frugal:"2,default,list<SplitFile>" json:"files"`
	ChunkingMethod string       `thrift:"ChunkingMethod,3" frugal:"3,default,string" json:"chunking_method"`
}

// SplitFilesRequestV2 is copied from kiwis/api/idl/kitex_gen/idecopilot/knowledgebase.go
type SplitFilesRequestV2 struct {
	Files          []*SplitFile `thrift:"Files,1" frugal:"1,default,list<SplitFile>" json:"files"`
	EmbeddingModel string       `thrift:"EmbeddingModel,2" frugal:"2,default,string" json:"embedding_model"`
	ChunkingMethod string       `thrift:"ChunkingMethod,3" frugal:"3,default,string" json:"chunking_method"`
}

// SplitFile is copied from kiwis/api/idl/kitex_gen/knowledgebase/entity.go
type SplitFile struct {
	Content   string    `thrift:"Content,1" frugal:"1,default,string" json:"content"`
	Path      string    `thrift:"Path,2" frugal:"2,default,string" json:"path"`
	ChunkSize int32     `thrift:"ChunkSize,3" frugal:"3,default,i32" json:"chunk_size"`
	ChunkType ChunkType `thrift:"ChunkType,4" frugal:"4,default,string" json:"chunk_type"`
	Title     string    `thrift:"Title,5" frugal:"5,default,string" json:"title"`
}

// SplitContentType is copied from kiwis/api/idl/kitex_gen/knowledgebase/entity.go
type SplitContentType int64

const (
	SplitContentType_Code SplitContentType = 0
	SplitContentType_Text SplitContentType = 1
)

// LanguageType is copied from kiwis/api/idl/kitex_gen/knowledgebase/entity.go
type LanguageType = string

// TextType_ is copied from kiwis/api/idl/kitex_gen/knowledgebase/entity.go
type TextType_ = string

// ChunkType is copied from kiwis/api/idl/kitex_gen/knowledgebase/entity.go
type ChunkType = string

// SplitFilesResponse is copied from kiwis/api/idl/kitex_gen/knowledgebase/entity.go
type SplitFilesResponse struct {
	Results     map[string]*SplitFileResult_ `thrift:"Results,1" frugal:"1,default,map<string:SplitFileResult_>" json:"results"`
	FailedFiles map[string]string            `thrift:"FailedFiles,2" frugal:"2,default,map<string:string>" json:"failed_files"`
}

func (f *SplitFilesResponse) ToURIData(uris []*model.URIStatus) []*model.URIData {
	var result = make([]*model.URIData, 0)
	for _, uri := range uris {
		if splitRes, ok := f.Results[uri.RelPath]; ok {
			result = append(result, splitRes.ToURIData(uri))
		}
	}
	return result
}

// SplitFileResult_ is copied from kiwis/api/idl/kitex_gen/knowledgebase/entity.go
type SplitFileResult_ struct {
	SegmentsWithEmbedding map[string]*SegmentWithEmbedding `thrift:"SegmentsWithEmbedding,1" frugal:"1,default,map<string:SegmentWithEmbedding>" json:"segments_with_embedding"`
}

func (f *SplitFileResult_) ToURIData(uri *model.URIStatus) *model.URIData {
	var entities = make([]*model.Entity, 0)
	var relations = make([]*model.Relation, 0)
	var aliasEntities = make([]*model.AliasEntity, 0)
	if len(f.SegmentsWithEmbedding) == 0 {
		return &model.URIData{
			Status:      *uri,
			Entities:    entities,
			Relation:    relations,
			AliasEntity: aliasEntities,
		}
	}
	for _, seg := range f.SegmentsWithEmbedding {
		entity, relation, alias := seg.Segment.ToEntityOrRelation(uri)
		if entity != nil {
			entities = append(entities, entity)
		}
		if relation != nil {
			relations = append(relations, relation)
		}
		if alias != nil {
			aliasEntities = append(aliasEntities, alias...)
		}
	}
	return &model.URIData{
		Status:      *uri,
		Entities:    entities,
		Relation:    relations,
		AliasEntity: aliasEntities,
	}
}

func (f *SplitFileResult_) ToEmbeddingData(fileRelPath string) []*model.EmbeddingData {
	return lo.Filter(lo.MapToSlice(f.SegmentsWithEmbedding, func(_ string, segment *SegmentWithEmbedding) *model.EmbeddingData {
		return segment.ToEmbeddingData(fileRelPath)
	}), func(e *model.EmbeddingData, _ int) bool {
		return e != nil
	})
}

type SplitFilesV2Result_ struct {
	SegmentsWithEmbeddingAndFile []*SegmentWithEmbeddingAndFile
}

func NewSplitFilesV2Result_(segments []*SegmentWithEmbeddingAndFile) *SplitFilesV2Result_ {
	return &SplitFilesV2Result_{SegmentsWithEmbeddingAndFile: segments}
}

func (f *SplitFilesV2Result_) ToURIData(uri *model.URIStatus) *model.URIData {
	var entities = make([]*model.Entity, 0)
	var relations = make([]*model.Relation, 0)
	var aliasEntities = make([]*model.AliasEntity, 0)
	if len(f.SegmentsWithEmbeddingAndFile) == 0 {
		return &model.URIData{
			Status:      *uri,
			Entities:    entities,
			Relation:    relations,
			AliasEntity: aliasEntities,
		}
	}
	for _, seg := range f.SegmentsWithEmbeddingAndFile {
		if seg == nil || seg.SegmentWithEmbedding == nil || seg.SegmentWithEmbedding.Segment == nil {
			continue
		}
		entity, relation, alias := seg.SegmentWithEmbedding.Segment.ToEntityOrRelation(uri)
		if entity != nil {
			entities = append(entities, entity)
		}
		if relation != nil {
			relations = append(relations, relation)
		}
		if alias != nil {
			aliasEntities = append(aliasEntities, alias...)
		}
	}
	return &model.URIData{
		Status:      *uri,
		Entities:    entities,
		Relation:    relations,
		AliasEntity: aliasEntities,
	}
}

func (f *SplitFilesV2Result_) ToEmbeddingData(fileRelPath string) []*model.EmbeddingData {
	return lo.Filter(lo.Map(lo.Filter(f.SegmentsWithEmbeddingAndFile, func(seg *SegmentWithEmbeddingAndFile, _ int) bool {
		return seg == nil || seg.SegmentWithEmbedding == nil
	}), func(seg *SegmentWithEmbeddingAndFile, _ int) *model.EmbeddingData {
		return seg.SegmentWithEmbedding.ToEmbeddingData(fileRelPath)
	}), func(e *model.EmbeddingData, _ int) bool { return e != nil })
}

// SegmentWithEmbeddingAndFile is copied from copilot_marscode/idecopilot/knowledgebase/kitex_handler/split_file.go
type SegmentWithEmbeddingAndFile struct {
	File                 string
	SegmentWithEmbedding *SegmentWithEmbedding
}

// SegmentWithEmbedding is copied from kiwis/api/idl/kitex_gen/knowledgebase/entity.go
type SegmentWithEmbedding struct {
	EntityID          string               `thrift:"EntityID,1" frugal:"1,default,string" json:"entity_id"`
	SegmentUniqueID   string               `thrift:"SegmentUniqueID,2" frugal:"2,default,string" json:"segment_unique_id"`
	Segment           *Segment             `thrift:"Segment,3" frugal:"3,default,Segment" json:"segment"`
	IsAllEmbedding    bool                 `thrift:"IsAllEmbedding,4" frugal:"4,default,bool" json:"is_all_embedding"`
	EmbeddingContents []string             `thrift:"EmbeddingContents,5" frugal:"5,default,list<string>" json:"embedding_contents"`
	Embeddings        map[string][]float64 `thrift:"Embeddings,6" frugal:"6,default,map<string:list<double>>" json:"embeddings"`
}

// ToEmbeddingData 的输出 Embeddings 和 Relations 的长度必定相等
func (f *SegmentWithEmbedding) ToEmbeddingData(fileRelPath string) *model.EmbeddingData {
	entityType := f.Segment.GetEntityType()
	if !f.IsAllEmbedding || len(f.EmbeddingContents) != len(f.Embeddings) || len(entityType) == 0 {
		return nil
	}
	porcelainMetadata := model.ToPorcelainMetadata(f.EntityID, fileRelPath, entityType)
	docs := lo.MapToSlice(f.Embeddings, func(embeddingContent string, v []float64) *model.EmbeddingDocument {
		v32 := lo.Map(v, func(num float64, _ int) float32 { return float32(num) })
		return model.NewEmbeddingDocument(embeddingContent, v32, porcelainMetadata)
	})
	relations := lo.Map(docs, func(doc *model.EmbeddingDocument, _ int) *model.VectorToEntity {
		return &model.VectorToEntity{EntityID: f.EntityID, VectorID: doc.ID}
	})
	return &model.EmbeddingData{Embeddings: docs, Relations: relations}
}

// EmbeddingRequest is copied from kiwis/api/idl/kitex_gen/knowledgebase/entity.go
type EmbeddingRequest struct {
	DatastoreName     string   `thrift:"DatastoreName,1" frugal:"1,default,string" json:"datastore_name"`
	EmbeddingContents []string `thrift:"EmbeddingContents,2" frugal:"2,default,list<string>" json:"embedding_contents"`
	OfflineCluster    bool     `thrift:"OfflineCluster,3" frugal:"3,default,bool" json:"offline_cluster"`
	Extra             *string  `thrift:"Extra,4,optional" frugal:"4,optional,string" json:"extra"`
}

// EmbeddingRequestV2 is copied from kiwis/api/idl/kitex_gen/idecopilot/knowledgebase.go
type EmbeddingRequestV2 struct {
	EmbeddingModel    string   `thrift:"EmbeddingModel,1" frugal:"1,default,string" json:"embedding_model"`
	EmbeddingContents []string `thrift:"EmbeddingContents,2" frugal:"2,default,list<string>" json:"embedding_contents"`
	OfflineCluster    bool     `thrift:"OfflineCluster,3" frugal:"3,default,bool" json:"offline_cluster"`
	Extra             *string  `thrift:"Extra,4,optional" frugal:"4,optional,string" json:"extra"`
}

// EmbeddingResponse is copied from kiwis/api/idl/kitex_gen/knowledgebase/entity.go
type EmbeddingResponse struct {
	Embeddings     []*Embedding `thrift:"Embeddings,1" frugal:"1,default,list<Embedding>" json:"embeddings"`
	EmbeddingModel string       `thrift:"EmbeddingModel,2" frugal:"2,default,string" json:"embedding_model"`
}

// Embedding is copied from kiwis/api/idl/kitex_gen/knowledgebase/entity.go
type Embedding struct {
	Embedding []float64 `thrift:"Embedding,1" frugal:"1,default,list<double>" json:"embedding"`
}
