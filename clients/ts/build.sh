npm i -g pnpm --registry=https://bnpm.byted.org
pnpm i --registry=https://bnpm.byted.org

cd clients/ts
if [ x"$CUSTOM_RELEASE" == x ]; then
    BUILD_NUMBER=$(echo "$BUILD_VERSION" | sed 's/^.*\.\([^.]*\)$/\1/')
    pnpm version 0.$BUILD_NUMBER.0-test
fi
pnpm install
pnpm build

npm config set "registry=https://bnpm.byted.org"
echo "//bnpm.byted.org/:_authToken=$CUSTOM_NPM_TOKEN" >> ~/.npmrc
pnpm publish --no-git-checks
