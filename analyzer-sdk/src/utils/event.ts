import {Logger} from "./logger";
import {isMainThread} from "worker_threads";
import {addWorkerEventTask} from "../worker/queue/event";
import {FeatureGateManager, FeatureName} from "../core/common/config";

export const EVENT_CATEGORY_PROXY = "proxy";
export const EVENT_CATEGORY_COMPLETION = "completion";
export const EVENT_CATEGORY_CACHE = "cache";
export const EVENT_CATEGORY_TYPE_ANALYSIS = "type_analysis";
export const EVENT_CATEGORY_CONTEXT_EXTRACTION = "context_extraction";
export const EVENT_CATEGORY_UT_CONTEXT = "ut_context";
export const EVENT_CATEGORY_GENERAL = "general";

export const EVENT_NAME_TRIGGER = "trigger";
export const EVENT_NAME_FAILED = "failed";
export const EVENT_NAME_IGNORE = "ignore";

export const EVENT_VALUE_NO_CACHE = "no_cache";
export const EVENT_VALUE_TIMEOUT = "timeout";
export const EVENT_VALUE_EXCEPTION = "exception";
export const EVENT_VALUE_PARSE_TREE_FAILED = "parse_tree_failed";
export const EVENT_VALUE_ANALYSIS_FAILED = "analysis_failed";

export interface EventData {
  programming_language: string
  category: string
  process: string
  name: string
  string_value?: string
  integer_value?: number
  boolean_value?: boolean
}

let eventConsumer: ((data: EventData) => void) = null;
let mainThreadInitialized = false;

export function setEventConsumer(consumer: (data: EventData) => void): void {
  eventConsumer = consumer;
}

export function eventConsumerSetMainThreadInitialized(value: boolean): void {
  mainThreadInitialized = value;
}

export function eventConsumerInitialized(): boolean {
  if (isMainThread) {
    return eventConsumer !== null;
  } else {
    return mainThreadInitialized;
  }
}

export async function reportEvent(languageId: string, category: string, process: string, name: string, value?: any): Promise<void> {
  if (eventConsumerInitialized() && !FeatureGateManager.INSTANCE.isFeatureOpen(FeatureName.DISABLE_EVENT)) {
    if (isMainThread) {
      if (typeof value === "string") {
        eventConsumer({programming_language: languageId, category, process, name, string_value: value});
      } else if (Number.isInteger(value)) {
        eventConsumer({programming_language: languageId, category, process, name, integer_value: value});
      } else if (typeof value === "boolean") {
        eventConsumer({programming_language: languageId, category, process, name, boolean_value: value});
      } else if (value === undefined) {
        eventConsumer({programming_language: languageId, category, process, name});
      } else {
        Logger.error(`reportEvent: unsupported value type '${typeof value}: ${value}'`);
      }
    } else {
      addWorkerEventTask({languageId, category, process, name, value});
    }
  }
}