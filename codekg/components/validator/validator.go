package validator

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"io"
	"os"
	"path"
	"strings"
	"unicode"

	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/components/ignore_service"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/model"

	"github.com/samber/lo"
)

var (
	excludedFiles = map[string]struct{}{
		"go.sum":            {},
		"yarn.lock":         {},
		"package-lock.json": {},
		"pnpm-lock.yaml":    {},
		"cargo.lock":        {},
	}

	// 隐藏文件夹已经忽略
	excludedFolders = map[string]struct{}{
		"node_modules": {},
		"kitex_gen":    {},
		"thrift_gen":   {},
		"hertz_gen":    {},
	}

	imageAndMediaExtensions = map[string]struct{}{
		".jpg":  {},
		".jpeg": {},
		".jpe":  {},
		".png":  {},
		".gif":  {},
		".bmp":  {},
		".tif":  {},
		".tiff": {},
		".tga":  {},
		".ico":  {},
		".webp": {},
		".svg":  {},
		".eps":  {},
		".heif": {},
		".heic": {},
		".pdf":  {},
		".raw":  {},
		".mp4":  {},
		".m4v":  {},
		".mkv":  {},
		".webm": {},
		".mov":  {},
		".avi":  {},
		".wmv":  {},
		".flv":  {},
		".mp3":  {},
		".wav":  {},
		".m4a":  {},
		".flac": {},
		".ogg":  {},
		".wma":  {},
		".weba": {},
		".aac":  {},
		".7z":   {},
		".bz2":  {},
		".gz":   {},
		".rar":  {},
		".tar":  {},
		".xz":   {},
		".zip":  {},
		".vsix": {},
		".db":   {},
		".bin":  {},
		".dat":  {},
		".hex":  {},
		".map":  {},
		".wasm": {},
		".pyc":  {},
		".pdb":  {},
		".sym":  {},
		".git":  {},
	}

	// 常见的二进制文件扩展名
	binaryExtensions = map[string]struct{}{
		".exe":   {},
		".dll":   {},
		".so":    {},
		".dylib": {},
	}

	supportedFileExtensionsToConvert = map[string]struct{}{
		// ".pdf":  {},
		// ".docx": {},
		// ".doc":  {},
	}
)

func IsValidForCursorMove(ctx context.Context, uri model.URI, fileSizeLimit int64,
	ignoreService ignore_service.IgnoreService, fileSystem file_system.FileSystem) (bool, error) {
	result, err := ignoreService.IsIgnored(ctx, uri)
	if err != nil {
		logs.CtxError(ctx, "file %v IsIgnored failed err is %v", EncryptPath(uri), err)
		return false, err
	}
	if result {
		return false, nil
	}
	fileInfo, err := fileSystem.Stat(uri)
	if err != nil {
		if os.IsNotExist(err) {
			return false, nil
		}
		return false, err
	}
	if fileInfo.IsDir() {
		return fileInfo.Mode()&os.ModeSymlink == 0, nil
	}

	if fileSizeLimit != 0 && fileInfo.Size() > fileSizeLimit {
		logs.CtxTrace(ctx, "file %s is too large, ignored for index", EncryptPath(uri))
		return false, nil
	}

	if _, ok := imageAndMediaExtensions[path.Ext(uri)]; ok {
		return false, nil
	}

	if _, ok := excludedFiles[strings.ToLower(fileInfo.Name())]; ok {
		return false, nil
	}

	if _, ok := binaryExtensions[path.Ext(uri)]; ok {
		return false, nil
	}

	return fileInfo.Mode()&os.ModeSymlink == 0, nil
}

func IsValidForKnowledgeGraph(ctx context.Context, projectId model.URI, uri model.URI, fileSizeLimit int64,
	ignoreService ignore_service.IgnoreService, fileSystem file_system.FileSystem) (bool, error) {
	if ignoreService == nil {
		return false, nil
	}
	result, err := ignoreService.IsIgnored(ctx, uri)
	if err != nil {
		logs.CtxError(ctx, "file %v IsIgnored failed err is %v", EncryptPath(uri), err)
		return false, err
	}

	if result {
		// since ignore rules, file uri ignored
		return false, nil
	}

	fileInfo, err := fileSystem.Lstat(uri)
	if err != nil {
		if os.IsNotExist(err) {
			return false, nil
		}
		return false, err
	}

	relPath, err := fileSystem.Rel(projectId, uri)
	if err != nil {
		logs.CtxTrace(ctx, "file %s is not under project %s", EncryptPath(uri), projectId)
		return false, err
	}

	segments := strings.Split(relPath, string(os.PathSeparator))
	// 仅忽略 project 下的隐藏目录或文件
	if len(segments) >= 1 {
		segments = lo.Slice(segments, 0, len(segments)-1)
	}
	for _, segment := range segments {
		// 忽略所有隐藏文件夹
		if strings.HasPrefix(segment, ".") {
			return false, nil
		}

		if _, ok := excludedFolders[strings.ToLower(segment)]; ok {
			return false, nil
		}
	}

	if fileInfo.IsDir() {
		return fileInfo.Mode()&os.ModeSymlink == 0, nil
	}

	// 忽略过大的文件
	if fileSizeLimit != 0 && fileInfo.Size() > fileSizeLimit {
		logs.CtxTrace(ctx, "file %s is too large, ignored for index", EncryptPath(uri))
		return false, nil
	}

	if _, ok := imageAndMediaExtensions[path.Ext(uri)]; ok {
		return false, nil
	}

	if _, ok := excludedFiles[strings.ToLower(fileInfo.Name())]; ok {
		return false, nil
	}

	return fileInfo.Mode()&os.ModeSymlink == 0, nil
}

// FileContainsNonPrintableChar 只索引不包含大量非可见字符的文件，过滤包含大量非可见字符的文件
func FileContainsNonPrintableChar(ctx context.Context, filePath, content string) bool {
	nonPrintable := 0
	length := lo.Ternary(len(content) > 1024, 1024, len(content))
	for i, r := range content {
		if i >= 1024 { // 只检查前1024个字符
			break
		}
		if !unicode.IsPrint(r) && r != '\n' && r != '\r' && r != '\t' {
			nonPrintable++
		}
	}
	return float64(nonPrintable)/float64(length) > 0.2
}

func IsSupportedFileFormatToConvert(ctx context.Context, filePath string) bool {
	if _, ok := supportedFileExtensionsToConvert[path.Ext(filePath)]; ok {
		return true
	}
	return false
}

func IsValidVirtualProjectDocumentContent(ctx context.Context, projectID string, uri model.URI, content string, documentSizeLimit int) (bool, error) {
	if len(content) > documentSizeLimit {
		logs.CtxWarn(ctx, "document size limit exceeded, uri: %s", EncryptPath(uri))
		return false, nil
	}

	return true, nil
}

func IsVFSPath(path string) bool {
	// 定义所有可能的远程开发路径协议前缀
	remotePrefixes := []string{
		"vscode-vfs",
		"vscode-remote",
		"vscode-ssh",
		"vscode-container",
		"vscode-wsl",
		"vscode-sftp",
		"jetbrains-ssh",
		"jetbrains-container",
		"jetbrains-vfs",
		"jetbrains-wsl",
	}

	// 检查路径是否以任意一个协议前缀开头
	for _, prefix := range remotePrefixes {
		if strings.HasPrefix(path, prefix) {
			return true
		}
	}

	return false
}

func IsValidGitFile(ctx context.Context, fullPath string, fileSizeLimit int64) bool {
	fileInfo, err := os.Stat(fullPath)
	if err != nil {
		if os.IsNotExist(err) {
			return false
		}
		return false
	}
	if fileInfo.IsDir() {
		return false
	}

	if fileSizeLimit != 0 && fileInfo.Size() > fileSizeLimit {
		logs.CtxTrace(ctx, "file %s is too large, ignored for index", EncryptPath(fullPath))
		return false
	}

	if _, ok := imageAndMediaExtensions[path.Ext(fullPath)]; ok {
		return false
	}

	if _, ok := excludedFiles[strings.ToLower(fileInfo.Name())]; ok {
		return false
	}

	if _, ok := binaryExtensions[path.Ext(fullPath)]; ok {
		return false
	}

	if isBinaryFile(fullPath) {
		return false
	}

	return fileInfo.Mode()&os.ModeSymlink == 0
}

func isBinaryFile(filename string) bool {
	file, err := os.Open(filename)
	if err != nil {
		return false
	}
	defer file.Close()

	// 读取文件头部进行判断
	buffer := make([]byte, 512)
	n, err := file.Read(buffer)
	if err != nil && err != io.EOF {
		return false
	}

	// 3. 统计非ASCII字符比例
	nonASCII := 0
	for i := 0; i < n; i++ {
		if buffer[i] > 127 {
			nonASCII++
		}
	}
	if float64(nonASCII)/float64(n) > 0.3 {
		return true
	}

	// 4. 检查是否包含大量控制字符
	controlChars := 0
	for i := 0; i < n; i++ {
		if buffer[i] < 32 && buffer[i] != 9 && buffer[i] != 10 && buffer[i] != 13 {
			controlChars++
		}
	}
	if float64(controlChars)/float64(n) > 0.2 {
		return true
	}

	return false
}

// 加密密钥，实际使用时应该从配置中读取
var encryptionKey = []byte{0xfb, 0xe6, 0x64, 0x9a, 0x0c, 0xd1, 0xdd, 0x77, 0xd1, 0xa6, 0x54, 0xd0, 0x94, 0x2f, 0xbd, 0xcd, 0x9c, 0xf3, 0x46, 0xb4, 0x6d, 0x3b, 0x6b, 0x0f, 0xf0, 0x54, 0x57, 0xbe, 0xa2, 0x1c, 0x2e, 0x71}

// EncryptPath 加密文件路径
func EncryptPath(path string) string {
	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return path
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return path
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return path
	}

	// 加密
	ciphertext := gcm.Seal(nonce, nonce, []byte(path), nil)
	return base64.StdEncoding.EncodeToString(ciphertext)
}

// DecryptPath 解密文件路径
func DecryptPath(encryptedPath string) string {
	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return encryptedPath
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return encryptedPath
	}

	ciphertext, err := base64.StdEncoding.DecodeString(encryptedPath)
	if err != nil {
		return encryptedPath
	}

	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return encryptedPath
	}

	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return encryptedPath
	}

	return string(plaintext)
}

func LogFilePath(filePaths []string) []string {
	var result []string
	for i, filePath := range filePaths {
		if i >= 100 {
			break
		}
		result = append(result, EncryptPath(filePath))
	}
	return result
}
