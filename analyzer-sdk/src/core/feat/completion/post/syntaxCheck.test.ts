import {describe, expect, it, beforeAll} from "vitest";
import {
  doSyntaxCheck,
} from './syntaxCheck';
import type {Position} from 'vscode-languageserver-textdocument';
import {incrementalUpdateCachedTree, completeUpdateCachedTree} from "../../../cache/astCache";
import {TreeSitterParser} from "../../../parse/treesitter/parser";
import {createDocument} from "../../../../utils/common";

describe("test_doSyntaxCheck", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_check_pass", async () => {
    const docText = `
func main() {
  if i > 0 {
    
}
`;
    const textDocument = createDocument(docText, "go", "file:///test.go");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 3, character: 4} as Position;

    const completionText = "println();\n  }\n";

    const result = doSyntaxCheck(textDocument, position, completionText, 20);

    expect(result).toEqual({
      syntaxCheckPass: true,
    });
  });

  it("case2_check_not_pass", async () => {
    const docText = `
func main() {
  if i > 0 {
    
}
`;
    const textDocument = createDocument(docText, "go", "file:///test.go");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 3, character: 4} as Position;

    const completionText = "println();\n";

    const result = doSyntaxCheck(textDocument, position, completionText, 20);

    expect(result).toEqual({
      syntaxCheckPass: false,
    });
  });

  it("case3_after_on_edit", async () => {
    const docText = `
func main() {
  if i > 0 
    
}
`;
    const textDocument = createDocument(docText, "go", "file:///test.go");
    await completeUpdateCachedTree(textDocument);

    const docText2 = `
func main() {
  if i > 0 {
    
}
`;
    const textDocument2 = createDocument(docText2, "go", "file:///test.go");
    await incrementalUpdateCachedTree({
      document: textDocument2,
      contentChanges: [{
        position: {line: 2, character: 11},
        removeLength: 1,
        text: "{",
      }],
    });

    const position = {line: 3, character: 4} as Position;

    const completionText = "println();\n  }\n";

    const result = doSyntaxCheck(textDocument2, position, completionText, 20);

    expect(result).toEqual({
      syntaxCheckPass: true,
    });
  })
  ;

  it("case4_end_with_bracket", async () => {
    const docText = `
func f1
`;
    const textDocument = createDocument(docText, "go", "file:///test.go");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 1, character: 7} as Position;

    const completionText = "(a, b, c int) int {\n";

    const result = doSyntaxCheck(textDocument, position, completionText, 20);

    expect(result).toEqual({
      syntaxCheckPass: false,
    });
  });

  it("case5_bracket_shift", async () => {
    const docText = `
func f1() {
  if i > 0 {
    i = 0
    if j > 0 {
      if k > 0 {
        if 
        fmt.Println()
      }
      j = 1
    }
  }
}
`;
    const textDocument = createDocument(docText, "go", "file:///test.go");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 6, character: 11} as Position;

    const completionText = "m > 0 {\n";

    const result = doSyntaxCheck(textDocument, position, completionText, 20);

    expect(result).toEqual({
      syntaxCheckPass: false,
    });
  });

  it("case6_ts_missing_identifier", async () => {
    const docText = `
function f(): void {
  for (let i = 0; i < 10; i++) {
    if (a.b.m(i) === true ) {
      return;
    }
  }
}
`;
    const textDocument = createDocument(docText, "typescript", "file:///test.ts");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 3, character: 26} as Position;

    const completionText = "||";

    const result = doSyntaxCheck(textDocument, position, completionText, 20);

    expect(result).toEqual({
      syntaxCheckPass: false,
    });
  });

  it("case7_break_block_scope_1", async () => {
    const docText = `
function f(
  a: number,
  
): void {
  return;
}
`;
    const textDocument = createDocument(docText, "typescript", "file:///test.ts");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 3, character: 0} as Position;

    const completionText = "): void {  return;\n}\n";

    const result = doSyntaxCheck(textDocument, position, completionText, 20);

    expect(result).toEqual({
      syntaxCheckPass: false,
    });
  });

  it("case8_break_block_scope_2", async () => {
    const docText = `
const m1 = {
  a: "a",
  b: "b",

}
`;
    const textDocument = createDocument(docText, "typescript", "file:///test.ts");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 4, character: 2} as Position;

    const completionText = "}\n";

    const result = doSyntaxCheck(textDocument, position, completionText, 20);

    expect(result).toEqual({
      syntaxCheckPass: false,
    });
  });

  it("case9_error_node_in_head", async () => {
    const docText = `
func f() {
  if i > 0 {
    
  }
}
`;
    const textDocument = createDocument(docText, "go", "file:///test.go");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 3, character: 4} as Position;

    const completionText = "if i > 0 {\n      if i > 0 {\n";

    const result = doSyntaxCheck(textDocument, position, completionText, 20);

    expect(result).toEqual({
      syntaxCheckPass: false,
    });
  });
});