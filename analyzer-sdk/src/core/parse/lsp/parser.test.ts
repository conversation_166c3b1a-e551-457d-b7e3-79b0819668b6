import {beforeAll, describe, expect, it, vi} from "vitest";
import {doSetConfigById, FeatureGateManager, FeatureName, getConfig} from "../../common/config";
import {setEnableLocalLogging} from "../../../utils/logger";
import * as utilsPath from "../../../utils/path";
import {wait} from "../../../utils/common";
import {LSPParser} from "./parser";
import {isInTimeoutInterval} from "../../../utils/control";
import {MainHandler} from "../../../worker/main";

describe("test_execute", () => {
  beforeAll(async () => {
    FeatureGateManager.INSTANCE.openFeature(FeatureName.ENABLE_PSI_LSP_REQUEST)
    FeatureGateManager.INSTANCE.openFeature(FeatureName.ENABLE_PSI_LSP_TIMEOUT_INTERVAL)
    setEnableLocalLogging(true)
  });

  it("case1_timeout_interval", async () => {
    MainHandler.INSTANCE.init_("./dist/resources", "/test", false, false, false);

    const originLspTimeoutMs = getConfig("parse.lsp.timeoutMs");
    const originLspTimeoutInterval = getConfig("parse.common.timeoutInterval");
    doSetConfigById("parse.lsp.timeoutMs", 5);
    doSetConfigById("parse.common.timeoutInterval", 100);

    const workspacePathMock = vi.spyOn(utilsPath, "getWorkspaceFolderPath").mockImplementation(() => {
      return "file:///";
    });

    const lspProxy = {
      execute: async (command: string, ..._args: any[]): Promise<any> => {
        if (command === "cmd1") {
          return [{
            uri: "file:///cmd1.go",
            range: {start: {line: 0, character: 0}, end: {line: 1, character: 1}},
          }];
        }
        if (command === "cmd2") {
          await wait(10);
          return [];
        }

        return [];
      },
    }

    LSPParser.INSTANCE.init(lspProxy);

    await LSPParser.INSTANCE.execute("cmd1", "", "file:///", {line: 0, character: 0});
    expect(isInTimeoutInterval()).toEqual(false);

    await LSPParser.INSTANCE.execute("cmd2", "", "file:///", {line: 0, character: 0});
    expect(isInTimeoutInterval()).toEqual(true);

    await LSPParser.INSTANCE.execute("cmd1", "", "file:///", {line: 0, character: 0});
    expect(isInTimeoutInterval()).toEqual(true);

    await wait(110);

    await LSPParser.INSTANCE.execute("cmd1", "", "file:///", {line: 0, character: 0});
    expect(isInTimeoutInterval()).toEqual(false);

    await LSPParser.INSTANCE.execute("cmd2", "", "file:///", {line: 0, character: 0});
    expect(isInTimeoutInterval()).toEqual(true);

    await LSPParser.INSTANCE.execute("cmd1", "", "file:///", {line: 0, character: 0});
    expect(isInTimeoutInterval()).toEqual(true);

    workspacePathMock.mockRestore();

    doSetConfigById("parse.lsp.timeoutMs", originLspTimeoutMs);
    doSetConfigById("parse.common.timeoutInterval", originLspTimeoutInterval);
  });
});