import {beforeAll, describe, expect, it} from "vitest";
import {TreeSitterParser} from "../../parse/treesitter/parser";
import {createDocument} from "../../../utils/common";
import {LanguageIdEnum} from "../../parse/share";
import {doGetMethodInRange} from "./methodInRange";
import {doGetSymbolPositionInRange} from "./symbolPosition";
import {setEnableLocalLogging} from "../../../utils/logger";

describe("test_doGetSymbolPositionInRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
    setEnableLocalLogging(true);
  });

  it("case1_normal", async () => {
    const docText = `
func (p *T) m1() {
  aaa := aaaa.fff()
  bbb := aaa.fff()
}
`;

    const document = createDocument(docText, LanguageIdEnum.Go, "file:///test.go");

    const range1 = {
      start: {
        line: 2,
        character: 2,
      },
      end: {
        line: 2,
        character: 18,
      },
    };

    const result1 = doGetSymbolPositionInRange(document, range1, "aaa");

    expect(result1).toEqual([
      {
        line: 2,
        character: 2,
      },
    ]);

    const range2 = {
      start: {
        line: 2,
        character: 2,
      },
      end: {
        line: 3,
        character: 18,
      },
    };

    const result2 = doGetSymbolPositionInRange(document, range2, "aaa");

    expect(result2).toEqual([
      {
        line: 2,
        character: 2,
      },
      {
        line: 3,
        character: 9,
      },
    ]);
  });
});