

// eslint-disable-next-line @typescript-eslint/no-var-requires
const getPort = require('get-port').default;
export const getPortByHost = async () : Promise<number> => {
  return getPort({
    host: '127.0.0.1',
  });
};

export function toSnakeCase(str: string): string {
  return str
    .replace(/([a-z0-9])([A-Z])/g, '$1_$2') // 在小写字母或数字与大写字母之间添加下划线
    .replace(/([A-Z]+)([A-Z][a-z0-9])/g, '$1_$2') // 在连续大写字母与小写字母或数字之间添加下划线
    .toLowerCase();
}

