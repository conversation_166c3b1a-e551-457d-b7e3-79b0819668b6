import {Position} from "vscode-languageserver-types";
import {TextDocument} from "vscode-languageserver-textdocument";
import {getFileSymbolDef, GetSymbolDefSource} from "../share/definition";
import {FileSymbolDef, ResolvedTypeInfo} from "../share/common";
import {getCacheFileAstTreeAllowCreate} from "../cache/astCache";
import {Logger} from "../../utils/logger";

export async function doGetDefinitionsForRange(document: TextDocument, startPosition?: Position, endPosition?: Position): Promise<string> {
  Logger.info(`doGetDefinitionsForRange: '${document.uri}' (${startPosition?.line ?? -1}, ${startPosition?.character ?? -1}) - (${endPosition?.line ?? -1}, ${endPosition?.character ?? -1})`);

  const fileSymbolDefResult = new FileSymbolDef(document);
  fileSymbolDefResult.indexDefInfo = new Array(document.getText().length).fill(null);

  const definitionMap = new Map<string, ResolvedTypeInfo>();

  const definitionHandler = (defInfo: ResolvedTypeInfo): string => {
    definitionMap.set(defInfo.uniqueKeyString(), defInfo);
    return defInfo.uniqueKeyString();
  };

  await getFileSymbolDef(GetSymbolDefSource.Direct, fileSymbolDefResult, document, getCacheFileAstTreeAllowCreate(document), [{
    start: startPosition ?? {line: 0, character: 0},
    end: endPosition ?? {line: document.lineCount, character: 0},
  }], definitionHandler);

  const definitionKeyMap = new Map<string, number>();

  interface resultTypeInfo {
    path: string,
    name: string,
    category: string,
    content: string,
    subTypeInfo: resultTypeInfo[],
  }
  interface resultInfo {
    typeInfo: resultTypeInfo,
    symbols: {
      name: string,
      startIndex: number,
      endIndex: number,
    }[],
  }
  const typeInfoConvert = (defInfo: ResolvedTypeInfo): resultTypeInfo => {
    return {
      path: defInfo.path,
      name: defInfo.name,
      category: defInfo.category,
      content: defInfo.content,
      subTypeInfo: defInfo.subTypeInfo?.map(item => typeInfoConvert(item)),
    };
  };

  const result: resultInfo[] = [];

  fileSymbolDefResult.indexDefInfo.forEach((defInfo, index) => {
    if (defInfo && (index === 0 || defInfo !== fileSymbolDefResult.indexDefInfo[index - 1])) {
      const key = defInfo.definitionKey;
      if (key) {
        if (!definitionKeyMap.has(key)) {
          const resolvedTypeInfo = definitionMap.get(key);
          definitionKeyMap.set(key, result.length);
          let typeInfo: resultTypeInfo = null;
          if (resolvedTypeInfo) {
            typeInfo = typeInfoConvert(resolvedTypeInfo);
          } else {
            Logger.warn(`doGetDefinitionsForRange: resolvedTypeInfo is null, key: ${key}`);
          }
          result.push({
            typeInfo,
            symbols: [],
          });
        }

        result[definitionKeyMap.get(key)].symbols.push({
          name: defInfo.name,
          startIndex: index,
          endIndex: index + defInfo.name.length,
        });
      }
    }
  });

  return JSON.stringify(result, null, 2);
}