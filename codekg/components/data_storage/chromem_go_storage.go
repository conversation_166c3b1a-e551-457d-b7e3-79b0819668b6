// ignore_security_alert_file SQL_INJECTION
package data_storage

import (
	"context"
	"crypto/sha256"
	"fmt"
	"github.com/philippgille/chromem-go"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/atomic"
	"gorm.io/gorm"
	"ide/ckg/codekg/components/data_storage/consts"
	"ide/ckg/codekg/components/data_storage/user_storage"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/util"
	"path/filepath"
	"sync"
)

// ChromemGoStorage is implemented for chromem-go.
type ChromemGoStorage struct {
	db             *chromem.DB
	cs             *sync.Map // string -> *chromem.Collection
	isDeleted      *atomic.Bool
	dbFilePath     string
	embeddingModel string
}

// CreateChromemGoStorage creates a new ChromemGoStorage.
// storagePath is the parent directory that holds the vector storage directory.
// projectID is the project i, which must be non-empty.
// forceNew 表示强制创建新的 db
func CreateChromemGoStorage(ctx context.Context, es *user_storage.UserStorage, storagePath model.URI, projectID model.URI,
	embeddingModel string, t consts.EmbeddingStorageType, forceNew bool) (*ChromemGoStorage, error) {
	var db *chromem.DB
	var cs = &sync.Map{}
	var c *chromem.Collection
	var err error
	var dbFilePath = ""
	if t != consts.EmbeddingStorage_ChormemGo {
		return nil, errors.Errorf("[CreateChromemGoStorage] invalid embedding storage type: %s", t)
	}
	if len(storagePath) == 0 {
		db = chromem.NewDB()
		if c, err = db.GetOrCreateCollection(Collection_Embedding, nil, nil); err != nil {
			logs.CtxError(ctx, "[CreateChromemGoStorage] get or create collection for memory db failed, projectID: %s", projectID)
			return nil, err
		}
		cs.Store(Collection_Embedding, c)
	} else {
		dbFilePath, err = es.GetEmbeddingStorageFromUserStorage(ctx, projectID, t)
		if forceNew || err != nil || len(dbFilePath) == 0 {
			projectName := filepath.Base(projectID)
			sum := sha256.Sum256([]byte(projectID))
			randStr := util.GetRandStr(6)
			dbFilePath = filepath.Join(storagePath, fmt.Sprintf("%s_%x_%s_embedding.db", projectName, sum[:7], randStr))
			logs.CtxInfo(ctx, "[CreateChromemGoStorage] embedding db not exist for project %s, new db path is %s", projectID, dbFilePath)
		}
		if db, err = chromem.NewPersistentDB(dbFilePath, false); err != nil {
			logs.CtxError(ctx, "[CreateChromemGoStorage] new persist db failed, storage: %s", dbFilePath)
			return nil, err
		}
		if c, err = db.GetOrCreateCollection(Collection_Embedding, nil, nil); err != nil {
			logs.CtxError(ctx, "[CreateChromemGoStorage] get or create collection for persist db  failed, storage: %s, projectID: %s", dbFilePath, projectID)
			return nil, err
		}
		cs.Store(Collection_Embedding, c)
	}
	return &ChromemGoStorage{
		db:             db,
		cs:             cs,
		isDeleted:      atomic.NewBool(false),
		dbFilePath:     dbFilePath,
		embeddingModel: embeddingModel,
	}, nil
}

func (cgs *ChromemGoStorage) IsExistsOnDisk() bool {
	_, err := file_system.RealFS.Stat(cgs.dbFilePath)
	return err == nil
}

func (cgs *ChromemGoStorage) IsDeleted() bool           { return cgs.isDeleted.Load() }
func (cgs *ChromemGoStorage) GetStoragePath() string    { return cgs.dbFilePath }
func (cgs *ChromemGoStorage) GetType() string           { return consts.EmbeddingStorage_ChormemGo }
func (cgs *ChromemGoStorage) GetEmbeddingModel() string { return cgs.embeddingModel }
func (cgs *ChromemGoStorage) GetVersion() consts.StorageVersion {
	return consts.StorageVersionV0
}

func (cgs *ChromemGoStorage) AddEmbeddingDocument(ctx context.Context, doc *model.EmbeddingDocument, option AddEmbeddingDocumentOptions) error {
	if doc == nil {
		logs.CtxError(ctx, "[AddEmbeddingDocument] doc is nil")
		return errors.New("doc is nil")
	}
	c, err := cgs.getOrCreateCollection(ctx, Collection_Embedding)
	if err != nil {
		logs.CtxError(ctx, "[AddEmbeddingDocument] getOrCreateCollection failed: ", err.Error())
		return errors.WithMessagef(err, "[AddEmbeddingDocument] getOrCreateCollection %s failed", Collection_Embedding)
	}
	if err = option.FillInMetadata(ctx, doc.Metadata); err != nil {
		return errors.WithMessage(err, "[AddEmbeddingDocument] FillInMetadata failed")
	}
	return cgs.addEmbeddingDocument(ctx, c, doc)
}

func (cgs *ChromemGoStorage) addEmbeddingDocument(ctx context.Context, c *chromem.Collection, doc *model.EmbeddingDocument) error {
	err := c.AddDocument(ctx, ConvertDocumentToChromemDocument(doc))
	if err != nil {
		logs.CtxError(ctx, "[addEmbeddingDocument] add documents failed, doc: %+v", doc)
		return err
	}
	return nil
}

func (cgs *ChromemGoStorage) AddEmbeddingDocuments(ctx context.Context, docs []*model.EmbeddingDocument, option AddEmbeddingDocumentOptions) error {
	if len(docs) == 0 {
		return nil
	}
	c, err := cgs.getOrCreateCollection(ctx, Collection_Embedding)
	if err != nil {
		logs.CtxError(ctx, "[AddEmbeddingDocument] getOrCreateCollection failed: ", err.Error())
		return errors.WithMessagef(err, "[AddEmbeddingDocument] getOrCreateCollection %s failed", Collection_Embedding)
	}
	if err = c.AddDocuments(ctx, lo.Map(docs, func(d *model.EmbeddingDocument, _ int) chromem.Document {
		return ConvertDocumentToChromemDocument(d)
	}), 6); err != nil {
		logs.CtxError(ctx, "[AddEmbeddingDocuments] add documents failed, docs: %+v", docs)
		return err
	}
	return nil
}

func (cgs *ChromemGoStorage) GetById(ctx context.Context, id, collectionName string) (*model.EmbeddingDocument, error) {
	c, err := cgs.getOrCreateCollection(ctx, collectionName)
	if err != nil {
		logs.CtxError(ctx, "[GetById] getOrCreateCollection failed: ", err.Error())
		return nil, errors.WithMessagef(err, "[GetById] getOrCreateCollection failed with %s", collectionName)
	}
	doc, err := c.GetByID(ctx, id)
	if err != nil {
		logs.CtxError(ctx, "[GetById] get by id failed, id: %s", id)
		return nil, err
	}
	return ConvertChromemDocumentToDocument(doc), nil
}

// QueryByEmbedding conducts a query on chromem-go.
func (cgs *ChromemGoStorage) QueryByEmbedding(ctx context.Context, query *model.Embedding, nResults int, option EmbeddingQueryOptions) ([]*model.EmbeddingQueryResult, error) {
	c, err := cgs.getOrCreateCollection(ctx, Collection_Embedding)
	if err != nil {
		logs.CtxError(ctx, "[QueryByEmbedding] getOrCreateCollection failed: ", err.Error())
		return []*model.EmbeddingQueryResult{}, errors.WithMessagef(err, "[QueryByEmbedding] getOrCreateCollection %s failed", Collection_Embedding)
	}
	// prepare query metadata
	finalMetadata := make(map[string]string)
	if _, ok := option.WhereMetadata[string(model.EMK_RelPath)]; ok {
		// chromem-go 不支持 #Folder
	}
	results, err := cgs.queryByEmbedding(ctx, c, query, nResults, finalMetadata)
	if err != nil {
		logs.CtxError(ctx, "[QueryByEmbedding] query by embedding failed: %s", err.Error())
		return []*model.EmbeddingQueryResult{}, errors.WithMessagef(err, "[QueryByEmbedding] query by embedding failed")
	}
	// 过滤掉比 score threshold 小的结果
	return lo.Filter(results, func(r *model.EmbeddingQueryResult, _ int) bool {
		return r.Score > option.ScoreThreshold
	}), nil
}

func (cgs *ChromemGoStorage) queryByEmbedding(ctx context.Context, c *chromem.Collection, query *model.Embedding, nResults int, whereMetadata map[string]string) ([]*model.EmbeddingQueryResult, error) {
	// chromem-go 会在目标召回数量 nResults 大于 collection 中 document 数量时报错，并且不返回任何结果
	// 这里避免该场景导致的召回失败。
	if nResults > c.Count() {
		logs.CtxInfo(ctx, "[QueryByEmbedding] current collection only has %d docs, but nResults=%d", c.Count(), nResults)
		nResults = c.Count()
	}
	// chromem-go 会在目标召回数量 nResults 等于 0 时报错。
	// 但此处可能由于外部传入配置 == 0 或者 c.Count() == 0 导致，early-return，0 也是预期内结果。
	if nResults == 0 {
		logs.CtxInfo(ctx, "[QueryByEmbedding] nResults is 0, count of embedding in collection is %d", c.Count())
		return []*model.EmbeddingQueryResult{}, nil
	}
	logs.CtxInfo(ctx, "[QueryByEmbedding] querying embedding, nResults: %d, whereMetadata: %+v", nResults, whereMetadata)
	// invoke query
	results, err := c.QueryEmbedding(ctx, query.Embedding, nResults, whereMetadata, nil)
	if err != nil {
		logs.CtxError(ctx, "[QueryByEmbedding] query embedding failed, err: %s", err.Error())
		return []*model.EmbeddingQueryResult{}, err
	}
	var docs []*model.EmbeddingQueryResult
	for _, result := range results {
		doc := ConvertChromemResultToEmbeddingQueryResult(result)
		docs = append(docs, doc)
	}
	return docs, nil
}

func (cgs *ChromemGoStorage) EmbeddingDocumentCount(ctx context.Context) int {
	return cgs.documentCount(ctx, Collection_Embedding)
}

func (cgs *ChromemGoStorage) documentCount(ctx context.Context, collectionName string) int {
	collection, ok := cgs.cs.Load(collectionName)
	if !ok {
		logs.CtxError(ctx, "[documentCount] there is no collection named %s", collectionName)
		return 0
	}
	c, ok := collection.(*chromem.Collection)
	if !ok {
		logs.CtxError(ctx, "[documentCount] collection named %s is not a chromem.Collection", collectionName)
		return 0
	}
	return c.Count()
}

func (cgs *ChromemGoStorage) DeleteDocumentsByIds(ctx context.Context, option DeleteDocumentOptions, ids ...string) error {
	if len(ids) == 0 {
		// 这里需要返回，chromem-go API 不接受传入 nil where, nil whereDocument 的同时再传入 “空 ids 列表”。
		// no ids to delete, early return
		return nil
	}
	c, err := cgs.getOrCreateCollection(ctx, Collection_Embedding)
	if err != nil {
		logs.CtxError(ctx, "[DeleteDocumentsByIds] getOrCreateCollection failed: ", err.Error())
		return errors.WithMessagef(err, "[DeleteDocumentsByIds] getOrCreateCollection %s failed", Collection_Embedding)
	}
	if err = c.Delete(ctx, nil, nil, ids...); err != nil {
		logs.CtxError(ctx, "[DeleteDocumentsByIds] delete by ids failed, ids: %+v", ids)
		return err
	}
	return nil
}

func (cgs *ChromemGoStorage) DeleteDocumentsByMetadata(ctx context.Context, whereMetadata map[string]any, option DeleteDocumentOptions) error {
	metadata := make(map[string]string)
	for k, v := range whereMetadata {
		if val, ok := v.(string); ok {
			metadata[k] = val
		} else {
			logs.CtxError(ctx, "[DeleteDocumentsByMetadata] metadata value is not string, key: %s, value: %+v", k, v)
		}
	}
	c, err := cgs.getOrCreateCollection(ctx, Collection_Embedding)
	if err != nil {
		logs.CtxError(ctx, "[DeleteDocumentsByMetadata] getOrCreateCollection failed: ", err.Error())
		return errors.WithMessagef(err, "[DeleteDocumentsByMetadata] getOrCreateCollection %s failed", Collection_Embedding)
	}
	if err = c.Delete(ctx, nil, metadata); err != nil {
		logs.CtxError(ctx, "[DeleteDocumentsByMetadata] delete by metadata failed, metadata: %+v", metadata)
		return err
	}
	return nil
}

func (cgs *ChromemGoStorage) DeleteDocumentsByEntity(ctx context.Context, entities []StorageEntity, storage Storage, tx *gorm.DB, option DeleteDocumentOptions) error {
	entityIds := lo.Map(entities, func(entity StorageEntity, _ int) string { return entity.EntityID })
	vectors, err := storage.SearchVectorIDsByEntityIDs(ctx, tx, entityIds)
	if err != nil {
		logs.CtxError(ctx, "SearchVectorIDsByEntityIDs err %v", err)
		return err
	}
	// 先在向量库中删除，再删除 EntityStorage 中的关系
	err = cgs.DeleteDocumentsByIds(ctx, option, vectors...)
	if err != nil {
		return err
	}
	return nil
}

func (cgs *ChromemGoStorage) getOrCreateCollection(ctx context.Context, name string) (*chromem.Collection, error) {
	if len(name) == 0 {
		errMsg := "[getOrCreateCollection] name cannot be empty"
		logs.CtxError(ctx, errMsg)
		return nil, errors.New(errMsg)
	}
	var c *chromem.Collection
	var err error
	if cAny, ok := cgs.cs.Load(name); ok {
		if c, ok = cAny.(*chromem.Collection); ok {
			return c, nil
		}
		// name 对应的 value 并不是 *chromem.Collection 类型，删掉
		cgs.cs.Delete(name)
	}
	if c, err = cgs.db.GetOrCreateCollection(name, make(map[string]string), nil); err != nil {
		logs.CtxError(ctx, "[getOrCreateCollection] get or create collection failed, name: %s", name)
		return nil, err
	}
	cgs.cs.Store(name, c)
	return c, nil
}

func (cgs *ChromemGoStorage) Delete() error {
	cgs.isDeleted.Store(true)
	if cgs.db == nil {
		return errors.New("db is nil")
	}
	return cgs.db.Reset()
}
