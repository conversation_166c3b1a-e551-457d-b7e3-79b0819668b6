// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v5.28.3
// source: codekg.proto

package protocol

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Code int32

const (
	Code_succeed            Code = 0
	Code_unknown_error      Code = 1
	Code_panic              Code = 100
	Code_user_id_is_missing Code = 101
	Code_file_limit_exceed  Code = 102
	Code_invalid_token      Code = 103
	Code_nil_client         Code = 104
)

// Enum value maps for Code.
var (
	Code_name = map[int32]string{
		0:   "succeed",
		1:   "unknown_error",
		100: "panic",
		101: "user_id_is_missing",
		102: "file_limit_exceed",
		103: "invalid_token",
		104: "nil_client",
	}
	Code_value = map[string]int32{
		"succeed":            0,
		"unknown_error":      1,
		"panic":              100,
		"user_id_is_missing": 101,
		"file_limit_exceed":  102,
		"invalid_token":      103,
		"nil_client":         104,
	}
)

func (x Code) Enum() *Code {
	p := new(Code)
	*p = x
	return p
}

func (x Code) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Code) Descriptor() protoreflect.EnumDescriptor {
	return file_codekg_proto_enumTypes[0].Descriptor()
}

func (Code) Type() protoreflect.EnumType {
	return &file_codekg_proto_enumTypes[0]
}

func (x Code) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Code.Descriptor instead.
func (Code) EnumDescriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{0}
}

type EntityType int32

const (
	EntityType_folder EntityType = 0
	EntityType_file   EntityType = 1
	EntityType_class  EntityType = 2
	EntityType_method EntityType = 3
	EntityType_text   EntityType = 4
)

// Enum value maps for EntityType.
var (
	EntityType_name = map[int32]string{
		0: "folder",
		1: "file",
		2: "class",
		3: "method",
		4: "text",
	}
	EntityType_value = map[string]int32{
		"folder": 0,
		"file":   1,
		"class":  2,
		"method": 3,
		"text":   4,
	}
)

func (x EntityType) Enum() *EntityType {
	p := new(EntityType)
	*p = x
	return p
}

func (x EntityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EntityType) Descriptor() protoreflect.EnumDescriptor {
	return file_codekg_proto_enumTypes[1].Descriptor()
}

func (EntityType) Type() protoreflect.EnumType {
	return &file_codekg_proto_enumTypes[1]
}

func (x EntityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EntityType.Descriptor instead.
func (EntityType) EnumDescriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{1}
}

type BuildStatus int32

const (
	BuildStatus_building BuildStatus = 0
	BuildStatus_finished BuildStatus = 1
	BuildStatus_failed   BuildStatus = 2
)

// Enum value maps for BuildStatus.
var (
	BuildStatus_name = map[int32]string{
		0: "building",
		1: "finished",
		2: "failed",
	}
	BuildStatus_value = map[string]int32{
		"building": 0,
		"finished": 1,
		"failed":   2,
	}
)

func (x BuildStatus) Enum() *BuildStatus {
	p := new(BuildStatus)
	*p = x
	return p
}

func (x BuildStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BuildStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_codekg_proto_enumTypes[2].Descriptor()
}

func (BuildStatus) Type() protoreflect.EnumType {
	return &file_codekg_proto_enumTypes[2]
}

func (x BuildStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BuildStatus.Descriptor instead.
func (BuildStatus) EnumDescriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{2}
}

type RecallType int32

const (
	RecallType_recall_type_user_specified                RecallType = 0
	RecallType_recall_type_embedding                     RecallType = 1
	RecallType_recall_type_ner                           RecallType = 2
	RecallType_recall_type_relation_by_user_action_trace RecallType = 20
	RecallType_recall_type_relation_by_git_relevance     RecallType = 30
)

// Enum value maps for RecallType.
var (
	RecallType_name = map[int32]string{
		0:  "recall_type_user_specified",
		1:  "recall_type_embedding",
		2:  "recall_type_ner",
		20: "recall_type_relation_by_user_action_trace",
		30: "recall_type_relation_by_git_relevance",
	}
	RecallType_value = map[string]int32{
		"recall_type_user_specified":                0,
		"recall_type_embedding":                     1,
		"recall_type_ner":                           2,
		"recall_type_relation_by_user_action_trace": 20,
		"recall_type_relation_by_git_relevance":     30,
	}
)

func (x RecallType) Enum() *RecallType {
	p := new(RecallType)
	*p = x
	return p
}

func (x RecallType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecallType) Descriptor() protoreflect.EnumDescriptor {
	return file_codekg_proto_enumTypes[3].Descriptor()
}

func (RecallType) Type() protoreflect.EnumType {
	return &file_codekg_proto_enumTypes[3]
}

func (x RecallType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecallType.Descriptor instead.
func (RecallType) EnumDescriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{3}
}

type SnippetType int32

const (
	SnippetType_snippet_type_code        SnippetType = 0
	SnippetType_snippet_type_folder_tree SnippetType = 1
	SnippetType_snippet_type_file        SnippetType = 2
)

// Enum value maps for SnippetType.
var (
	SnippetType_name = map[int32]string{
		0: "snippet_type_code",
		1: "snippet_type_folder_tree",
		2: "snippet_type_file",
	}
	SnippetType_value = map[string]int32{
		"snippet_type_code":        0,
		"snippet_type_folder_tree": 1,
		"snippet_type_file":        2,
	}
)

func (x SnippetType) Enum() *SnippetType {
	p := new(SnippetType)
	*p = x
	return p
}

func (x SnippetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SnippetType) Descriptor() protoreflect.EnumDescriptor {
	return file_codekg_proto_enumTypes[4].Descriptor()
}

func (SnippetType) Type() protoreflect.EnumType {
	return &file_codekg_proto_enumTypes[4]
}

func (x SnippetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SnippetType.Descriptor instead.
func (SnippetType) EnumDescriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{4}
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{0}
}

type Error struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Stack   string `protobuf:"bytes,2,opt,name=stack,proto3" json:"stack,omitempty"`
}

func (x *Error) Reset() {
	*x = Error{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Error) ProtoMessage() {}

func (x *Error) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Error.ProtoReflect.Descriptor instead.
func (*Error) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{1}
}

func (x *Error) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Error) GetStack() string {
	if x != nil {
		return x.Stack
	}
	return ""
}

type RefreshTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token  string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	UserId string `protobuf:"bytes,10,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *RefreshTokenRequest) Reset() {
	*x = RefreshTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenRequest) ProtoMessage() {}

func (x *RefreshTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenRequest.ProtoReflect.Descriptor instead.
func (*RefreshTokenRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{2}
}

func (x *RefreshTokenRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *RefreshTokenRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type RefreshTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  Code   `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error *Error `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *RefreshTokenResponse) Reset() {
	*x = RefreshTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenResponse) ProtoMessage() {}

func (x *RefreshTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenResponse.ProtoReflect.Descriptor instead.
func (*RefreshTokenResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{3}
}

func (x *RefreshTokenResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *RefreshTokenResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type Project struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId   string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	StoragePath string `protobuf:"bytes,2,opt,name=storage_path,json=storagePath,proto3" json:"storage_path,omitempty"`
	IgnoreFile  string `protobuf:"bytes,3,opt,name=ignore_file,json=ignoreFile,proto3" json:"ignore_file,omitempty"`
}

func (x *Project) Reset() {
	*x = Project{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Project) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Project) ProtoMessage() {}

func (x *Project) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Project.ProtoReflect.Descriptor instead.
func (*Project) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{4}
}

func (x *Project) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Project) GetStoragePath() string {
	if x != nil {
		return x.StoragePath
	}
	return ""
}

func (x *Project) GetIgnoreFile() string {
	if x != nil {
		return x.IgnoreFile
	}
	return ""
}

type InitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectIds      []string   `protobuf:"bytes,1,rep,name=project_ids,json=projectIds,proto3" json:"project_ids,omitempty"`                   // deprecated
	IgnoreFileLimit bool       `protobuf:"varint,2,opt,name=ignore_file_limit,json=ignoreFileLimit,proto3" json:"ignore_file_limit,omitempty"` // deprecated
	Projects        []*Project `protobuf:"bytes,3,rep,name=projects,proto3" json:"projects,omitempty"`
	UserId          string     `protobuf:"bytes,10,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *InitRequest) Reset() {
	*x = InitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitRequest) ProtoMessage() {}

func (x *InitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitRequest.ProtoReflect.Descriptor instead.
func (*InitRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{5}
}

func (x *InitRequest) GetProjectIds() []string {
	if x != nil {
		return x.ProjectIds
	}
	return nil
}

func (x *InitRequest) GetIgnoreFileLimit() bool {
	if x != nil {
		return x.IgnoreFileLimit
	}
	return false
}

func (x *InitRequest) GetProjects() []*Project {
	if x != nil {
		return x.Projects
	}
	return nil
}

func (x *InitRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type InitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  Code   `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error *Error `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *InitResponse) Reset() {
	*x = InitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitResponse) ProtoMessage() {}

func (x *InitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitResponse.ProtoReflect.Descriptor instead.
func (*InitResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{6}
}

func (x *InitResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *InitResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type VirtualProject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId           string   `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Uri                 string   `protobuf:"bytes,2,opt,name=uri,proto3" json:"uri,omitempty"`
	RelativeGlobsToLoad []string `protobuf:"bytes,3,rep,name=relative_globs_to_load,json=relativeGlobsToLoad,proto3" json:"relative_globs_to_load,omitempty"`
}

func (x *VirtualProject) Reset() {
	*x = VirtualProject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VirtualProject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirtualProject) ProtoMessage() {}

func (x *VirtualProject) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirtualProject.ProtoReflect.Descriptor instead.
func (*VirtualProject) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{7}
}

func (x *VirtualProject) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *VirtualProject) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *VirtualProject) GetRelativeGlobsToLoad() []string {
	if x != nil {
		return x.RelativeGlobsToLoad
	}
	return nil
}

type InitVirtualProjectsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Projects        []*VirtualProject `protobuf:"bytes,1,rep,name=projects,proto3" json:"projects,omitempty"`
	LoadFilesFromFs bool              `protobuf:"varint,2,opt,name=load_files_from_fs,json=loadFilesFromFs,proto3" json:"load_files_from_fs,omitempty"`
	UserId          string            `protobuf:"bytes,10,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *InitVirtualProjectsRequest) Reset() {
	*x = InitVirtualProjectsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitVirtualProjectsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitVirtualProjectsRequest) ProtoMessage() {}

func (x *InitVirtualProjectsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitVirtualProjectsRequest.ProtoReflect.Descriptor instead.
func (*InitVirtualProjectsRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{8}
}

func (x *InitVirtualProjectsRequest) GetProjects() []*VirtualProject {
	if x != nil {
		return x.Projects
	}
	return nil
}

func (x *InitVirtualProjectsRequest) GetLoadFilesFromFs() bool {
	if x != nil {
		return x.LoadFilesFromFs
	}
	return false
}

func (x *InitVirtualProjectsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type InitVirtualProjectsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  Code   `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error *Error `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *InitVirtualProjectsResponse) Reset() {
	*x = InitVirtualProjectsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitVirtualProjectsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitVirtualProjectsResponse) ProtoMessage() {}

func (x *InitVirtualProjectsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitVirtualProjectsResponse.ProtoReflect.Descriptor instead.
func (*InitVirtualProjectsResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{9}
}

func (x *InitVirtualProjectsResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *InitVirtualProjectsResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type Document struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uri       string `protobuf:"bytes,1,opt,name=uri,proto3" json:"uri,omitempty"`
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Content   string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	ProjectId string `protobuf:"bytes,4,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
}

func (x *Document) Reset() {
	*x = Document{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Document) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Document) ProtoMessage() {}

func (x *Document) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Document.ProtoReflect.Descriptor instead.
func (*Document) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{10}
}

func (x *Document) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *Document) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Document) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Document) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

type DocumentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePaths []string    `protobuf:"bytes,1,rep,name=file_paths,json=filePaths,proto3" json:"file_paths,omitempty"`
	Documents []*Document `protobuf:"bytes,2,rep,name=documents,proto3" json:"documents,omitempty"`
	UserID    string      `protobuf:"bytes,10,opt,name=userID,proto3" json:"userID,omitempty"`
}

func (x *DocumentRequest) Reset() {
	*x = DocumentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocumentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocumentRequest) ProtoMessage() {}

func (x *DocumentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocumentRequest.ProtoReflect.Descriptor instead.
func (*DocumentRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{11}
}

func (x *DocumentRequest) GetFilePaths() []string {
	if x != nil {
		return x.FilePaths
	}
	return nil
}

func (x *DocumentRequest) GetDocuments() []*Document {
	if x != nil {
		return x.Documents
	}
	return nil
}

func (x *DocumentRequest) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

type DocumentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrMsgs map[string]string `protobuf:"bytes,1,rep,name=err_msgs,json=errMsgs,proto3" json:"err_msgs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Code    Code              `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error   *Error            `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *DocumentResponse) Reset() {
	*x = DocumentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocumentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocumentResponse) ProtoMessage() {}

func (x *DocumentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocumentResponse.ProtoReflect.Descriptor instead.
func (*DocumentResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{12}
}

func (x *DocumentResponse) GetErrMsgs() map[string]string {
	if x != nil {
		return x.ErrMsgs
	}
	return nil
}

func (x *DocumentResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *DocumentResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type DeleteIndexRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
}

func (x *DeleteIndexRequest) Reset() {
	*x = DeleteIndexRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteIndexRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIndexRequest) ProtoMessage() {}

func (x *DeleteIndexRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIndexRequest.ProtoReflect.Descriptor instead.
func (*DeleteIndexRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteIndexRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

type DeleteIndexResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Succeed bool   `protobuf:"varint,1,opt,name=succeed,proto3" json:"succeed,omitempty"`
	Code    Code   `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error   *Error `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *DeleteIndexResponse) Reset() {
	*x = DeleteIndexResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteIndexResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIndexResponse) ProtoMessage() {}

func (x *DeleteIndexResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIndexResponse.ProtoReflect.Descriptor instead.
func (*DeleteIndexResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{14}
}

func (x *DeleteIndexResponse) GetSucceed() bool {
	if x != nil {
		return x.Succeed
	}
	return false
}

func (x *DeleteIndexResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *DeleteIndexResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type CurrentEditorInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath           string `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	ProjectId          string `protobuf:"bytes,2,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	CursorLine         int32  `protobuf:"varint,10,opt,name=cursor_line,json=cursorLine,proto3" json:"cursor_line,omitempty"`
	SelectCodeRange    *Range `protobuf:"bytes,20,opt,name=select_code_range,json=selectCodeRange,proto3" json:"select_code_range,omitempty"`
	SelectCodeContent  string `protobuf:"bytes,21,opt,name=select_code_content,json=selectCodeContent,proto3" json:"select_code_content,omitempty"`
	VisibleCodeRange   *Range `protobuf:"bytes,30,opt,name=visible_code_range,json=visibleCodeRange,proto3" json:"visible_code_range,omitempty"`
	VisibleCodeContent string `protobuf:"bytes,31,opt,name=visible_code_content,json=visibleCodeContent,proto3" json:"visible_code_content,omitempty"`
	UserFileContent    string `protobuf:"bytes,40,opt,name=user_file_content,json=userFileContent,proto3" json:"user_file_content,omitempty"`
}

func (x *CurrentEditorInfo) Reset() {
	*x = CurrentEditorInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrentEditorInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrentEditorInfo) ProtoMessage() {}

func (x *CurrentEditorInfo) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrentEditorInfo.ProtoReflect.Descriptor instead.
func (*CurrentEditorInfo) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{15}
}

func (x *CurrentEditorInfo) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *CurrentEditorInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *CurrentEditorInfo) GetCursorLine() int32 {
	if x != nil {
		return x.CursorLine
	}
	return 0
}

func (x *CurrentEditorInfo) GetSelectCodeRange() *Range {
	if x != nil {
		return x.SelectCodeRange
	}
	return nil
}

func (x *CurrentEditorInfo) GetSelectCodeContent() string {
	if x != nil {
		return x.SelectCodeContent
	}
	return ""
}

func (x *CurrentEditorInfo) GetVisibleCodeRange() *Range {
	if x != nil {
		return x.VisibleCodeRange
	}
	return nil
}

func (x *CurrentEditorInfo) GetVisibleCodeContent() string {
	if x != nil {
		return x.VisibleCodeContent
	}
	return ""
}

func (x *CurrentEditorInfo) GetUserFileContent() string {
	if x != nil {
		return x.UserFileContent
	}
	return ""
}

type RetrieveEntityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectIds        []string           `protobuf:"bytes,1,rep,name=project_ids,json=projectIds,proto3" json:"project_ids,omitempty"`
	UserMessage       string             `protobuf:"bytes,2,opt,name=user_message,json=userMessage,proto3" json:"user_message,omitempty"`
	EditorInfo        *CurrentEditorInfo `protobuf:"bytes,3,opt,name=editor_info,json=editorInfo,proto3" json:"editor_info,omitempty"`
	ExpectEntityTypes []EntityType       `protobuf:"varint,4,rep,packed,name=expect_entity_types,json=expectEntityTypes,proto3,enum=protocol.EntityType" json:"expect_entity_types,omitempty"`
	EntityNum         int32              `protobuf:"varint,5,opt,name=entity_num,json=entityNum,proto3" json:"entity_num,omitempty"`
	Intent            string             `protobuf:"bytes,6,opt,name=intent,proto3" json:"intent,omitempty"`
	FolderPaths       []string           `protobuf:"bytes,7,rep,name=folder_paths,json=folderPaths,proto3" json:"folder_paths,omitempty"`
	UserId            string             `protobuf:"bytes,10,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SessionId         string             `protobuf:"bytes,20,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
}

func (x *RetrieveEntityRequest) Reset() {
	*x = RetrieveEntityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveEntityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveEntityRequest) ProtoMessage() {}

func (x *RetrieveEntityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveEntityRequest.ProtoReflect.Descriptor instead.
func (*RetrieveEntityRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{16}
}

func (x *RetrieveEntityRequest) GetProjectIds() []string {
	if x != nil {
		return x.ProjectIds
	}
	return nil
}

func (x *RetrieveEntityRequest) GetUserMessage() string {
	if x != nil {
		return x.UserMessage
	}
	return ""
}

func (x *RetrieveEntityRequest) GetEditorInfo() *CurrentEditorInfo {
	if x != nil {
		return x.EditorInfo
	}
	return nil
}

func (x *RetrieveEntityRequest) GetExpectEntityTypes() []EntityType {
	if x != nil {
		return x.ExpectEntityTypes
	}
	return nil
}

func (x *RetrieveEntityRequest) GetEntityNum() int32 {
	if x != nil {
		return x.EntityNum
	}
	return 0
}

func (x *RetrieveEntityRequest) GetIntent() string {
	if x != nil {
		return x.Intent
	}
	return ""
}

func (x *RetrieveEntityRequest) GetFolderPaths() []string {
	if x != nil {
		return x.FolderPaths
	}
	return nil
}

func (x *RetrieveEntityRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RetrieveEntityRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

type Entity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	EntityId  string `protobuf:"bytes,2,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
}

func (x *Entity) Reset() {
	*x = Entity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Entity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Entity) ProtoMessage() {}

func (x *Entity) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Entity.ProtoReflect.Descriptor instead.
func (*Entity) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{17}
}

func (x *Entity) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Entity) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

type RetrieveEntityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EntitiesByUserMessage []*Entity `protobuf:"bytes,1,rep,name=entities_by_user_message,json=entitiesByUserMessage,proto3" json:"entities_by_user_message,omitempty"`
	QueryEmbeddingModels  []string  `protobuf:"bytes,2,rep,name=query_embedding_models,json=queryEmbeddingModels,proto3" json:"query_embedding_models,omitempty"`
	VectorEmbeddingModels []string  `protobuf:"bytes,3,rep,name=vector_embedding_models,json=vectorEmbeddingModels,proto3" json:"vector_embedding_models,omitempty"`
	EntityChunkingMethods []string  `protobuf:"bytes,4,rep,name=entity_chunking_methods,json=entityChunkingMethods,proto3" json:"entity_chunking_methods,omitempty"`
	EmptyRecallReason     string    `protobuf:"bytes,10,opt,name=empty_recall_reason,json=emptyRecallReason,proto3" json:"empty_recall_reason,omitempty"`
	Code                  Code      `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error                 *Error    `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *RetrieveEntityResponse) Reset() {
	*x = RetrieveEntityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveEntityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveEntityResponse) ProtoMessage() {}

func (x *RetrieveEntityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveEntityResponse.ProtoReflect.Descriptor instead.
func (*RetrieveEntityResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{18}
}

func (x *RetrieveEntityResponse) GetEntitiesByUserMessage() []*Entity {
	if x != nil {
		return x.EntitiesByUserMessage
	}
	return nil
}

func (x *RetrieveEntityResponse) GetQueryEmbeddingModels() []string {
	if x != nil {
		return x.QueryEmbeddingModels
	}
	return nil
}

func (x *RetrieveEntityResponse) GetVectorEmbeddingModels() []string {
	if x != nil {
		return x.VectorEmbeddingModels
	}
	return nil
}

func (x *RetrieveEntityResponse) GetEntityChunkingMethods() []string {
	if x != nil {
		return x.EntityChunkingMethods
	}
	return nil
}

func (x *RetrieveEntityResponse) GetEmptyRecallReason() string {
	if x != nil {
		return x.EmptyRecallReason
	}
	return ""
}

func (x *RetrieveEntityResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *RetrieveEntityResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type RetrieveRelationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EntitiesByUserMessage []*Entity          `protobuf:"bytes,1,rep,name=entities_by_user_message,json=entitiesByUserMessage,proto3" json:"entities_by_user_message,omitempty"`
	EditorInfo            *CurrentEditorInfo `protobuf:"bytes,2,opt,name=editor_info,json=editorInfo,proto3" json:"editor_info,omitempty"`
	UserId                string             `protobuf:"bytes,10,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *RetrieveRelationRequest) Reset() {
	*x = RetrieveRelationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveRelationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveRelationRequest) ProtoMessage() {}

func (x *RetrieveRelationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveRelationRequest.ProtoReflect.Descriptor instead.
func (*RetrieveRelationRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{19}
}

func (x *RetrieveRelationRequest) GetEntitiesByUserMessage() []*Entity {
	if x != nil {
		return x.EntitiesByUserMessage
	}
	return nil
}

func (x *RetrieveRelationRequest) GetEditorInfo() *CurrentEditorInfo {
	if x != nil {
		return x.EditorInfo
	}
	return nil
}

func (x *RetrieveRelationRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type CodeChunkVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath  string `protobuf:"bytes,1,opt,name=file_path,proto3" json:"file_path,omitempty"`
	StartLine int32  `protobuf:"varint,2,opt,name=start_line,proto3" json:"start_line,omitempty"`
	EndLine   int32  `protobuf:"varint,3,opt,name=end_line,proto3" json:"end_line,omitempty"`
	Content   string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Provider  string `protobuf:"bytes,30,opt,name=provider,proto3" json:"provider,omitempty"`
}

func (x *CodeChunkVariable) Reset() {
	*x = CodeChunkVariable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CodeChunkVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeChunkVariable) ProtoMessage() {}

func (x *CodeChunkVariable) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeChunkVariable.ProtoReflect.Descriptor instead.
func (*CodeChunkVariable) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{20}
}

func (x *CodeChunkVariable) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *CodeChunkVariable) GetStartLine() int32 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *CodeChunkVariable) GetEndLine() int32 {
	if x != nil {
		return x.EndLine
	}
	return 0
}

func (x *CodeChunkVariable) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CodeChunkVariable) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

type TextVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath  string `protobuf:"bytes,1,opt,name=file_path,proto3" json:"file_path,omitempty"`
	StartLine int32  `protobuf:"varint,2,opt,name=start_line,proto3" json:"start_line,omitempty"`
	EndLine   int32  `protobuf:"varint,3,opt,name=end_line,proto3" json:"end_line,omitempty"`
	Content   string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Provider  string `protobuf:"bytes,30,opt,name=provider,proto3" json:"provider,omitempty"`
}

func (x *TextVariable) Reset() {
	*x = TextVariable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextVariable) ProtoMessage() {}

func (x *TextVariable) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextVariable.ProtoReflect.Descriptor instead.
func (*TextVariable) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{21}
}

func (x *TextVariable) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *TextVariable) GetStartLine() int32 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *TextVariable) GetEndLine() int32 {
	if x != nil {
		return x.EndLine
	}
	return 0
}

func (x *TextVariable) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *TextVariable) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

type UsefulFileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath string `protobuf:"bytes,1,opt,name=file_path,proto3" json:"file_path,omitempty"`
	Content  string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *UsefulFileInfo) Reset() {
	*x = UsefulFileInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsefulFileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsefulFileInfo) ProtoMessage() {}

func (x *UsefulFileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsefulFileInfo.ProtoReflect.Descriptor instead.
func (*UsefulFileInfo) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{22}
}

func (x *UsefulFileInfo) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *UsefulFileInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type FolderVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath    string            `protobuf:"bytes,1,opt,name=file_path,proto3" json:"file_path,omitempty"`
	FolderTree  string            `protobuf:"bytes,2,opt,name=folder_tree,proto3" json:"folder_tree,omitempty"`
	UsefulFiles []*UsefulFileInfo `protobuf:"bytes,3,rep,name=useful_files,proto3" json:"useful_files,omitempty"`
	Provider    string            `protobuf:"bytes,30,opt,name=provider,proto3" json:"provider,omitempty"`
}

func (x *FolderVariable) Reset() {
	*x = FolderVariable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FolderVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FolderVariable) ProtoMessage() {}

func (x *FolderVariable) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FolderVariable.ProtoReflect.Descriptor instead.
func (*FolderVariable) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{23}
}

func (x *FolderVariable) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *FolderVariable) GetFolderTree() string {
	if x != nil {
		return x.FolderTree
	}
	return ""
}

func (x *FolderVariable) GetUsefulFiles() []*UsefulFileInfo {
	if x != nil {
		return x.UsefulFiles
	}
	return nil
}

func (x *FolderVariable) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

type FileVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath string `protobuf:"bytes,1,opt,name=file_path,proto3" json:"file_path,omitempty"`
	Content  string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Comment  string `protobuf:"bytes,3,opt,name=comment,proto3" json:"comment,omitempty"`
	Provider string `protobuf:"bytes,30,opt,name=provider,proto3" json:"provider,omitempty"`
}

func (x *FileVariable) Reset() {
	*x = FileVariable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileVariable) ProtoMessage() {}

func (x *FileVariable) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileVariable.ProtoReflect.Descriptor instead.
func (*FileVariable) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{24}
}

func (x *FileVariable) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *FileVariable) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *FileVariable) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *FileVariable) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

type FileTopLevelVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath  string `protobuf:"bytes,1,opt,name=file_path,proto3" json:"file_path,omitempty"`
	StartLine int32  `protobuf:"varint,2,opt,name=start_line,proto3" json:"start_line,omitempty"`
	EndLine   int32  `protobuf:"varint,3,opt,name=end_line,proto3" json:"end_line,omitempty"`
	Content   string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Provider  string `protobuf:"bytes,30,opt,name=provider,proto3" json:"provider,omitempty"`
}

func (x *FileTopLevelVariable) Reset() {
	*x = FileTopLevelVariable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FileTopLevelVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileTopLevelVariable) ProtoMessage() {}

func (x *FileTopLevelVariable) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileTopLevelVariable.ProtoReflect.Descriptor instead.
func (*FileTopLevelVariable) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{25}
}

func (x *FileTopLevelVariable) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *FileTopLevelVariable) GetStartLine() int32 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *FileTopLevelVariable) GetEndLine() int32 {
	if x != nil {
		return x.EndLine
	}
	return 0
}

func (x *FileTopLevelVariable) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *FileTopLevelVariable) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

type Member struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *Member) Reset() {
	*x = Member{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Member) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Member) ProtoMessage() {}

func (x *Member) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Member.ProtoReflect.Descriptor instead.
func (*Member) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{26}
}

func (x *Member) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type ClassVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath      string        `protobuf:"bytes,1,opt,name=file_path,proto3" json:"file_path,omitempty"`
	StartLine     int32         `protobuf:"varint,2,opt,name=start_line,proto3" json:"start_line,omitempty"`
	EndLine       int32         `protobuf:"varint,3,opt,name=end_line,proto3" json:"end_line,omitempty"`
	Name          string        `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Comment       string        `protobuf:"bytes,5,opt,name=comment,proto3" json:"comment,omitempty"`
	Content       string        `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	Members       []*Member     `protobuf:"bytes,7,rep,name=members,json=member,proto3" json:"members,omitempty"`
	Methods       []*MethodInfo `protobuf:"bytes,8,rep,name=methods,proto3" json:"methods,omitempty"`
	TestFunctions []*MethodInfo `protobuf:"bytes,9,rep,name=test_functions,proto3" json:"test_functions,omitempty"`
	Provider      string        `protobuf:"bytes,30,opt,name=provider,proto3" json:"provider,omitempty"`
}

func (x *ClassVariable) Reset() {
	*x = ClassVariable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClassVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassVariable) ProtoMessage() {}

func (x *ClassVariable) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassVariable.ProtoReflect.Descriptor instead.
func (*ClassVariable) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{27}
}

func (x *ClassVariable) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *ClassVariable) GetStartLine() int32 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *ClassVariable) GetEndLine() int32 {
	if x != nil {
		return x.EndLine
	}
	return 0
}

func (x *ClassVariable) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ClassVariable) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *ClassVariable) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ClassVariable) GetMembers() []*Member {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *ClassVariable) GetMethods() []*MethodInfo {
	if x != nil {
		return x.Methods
	}
	return nil
}

func (x *ClassVariable) GetTestFunctions() []*MethodInfo {
	if x != nil {
		return x.TestFunctions
	}
	return nil
}

func (x *ClassVariable) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

type ClassTopLevelVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath  string `protobuf:"bytes,1,opt,name=file_path,proto3" json:"file_path,omitempty"`
	StartLine int32  `protobuf:"varint,2,opt,name=start_line,proto3" json:"start_line,omitempty"`
	EndLine   int32  `protobuf:"varint,3,opt,name=end_line,proto3" json:"end_line,omitempty"`
	Content   string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Provider  string `protobuf:"bytes,30,opt,name=provider,proto3" json:"provider,omitempty"`
}

func (x *ClassTopLevelVariable) Reset() {
	*x = ClassTopLevelVariable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClassTopLevelVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassTopLevelVariable) ProtoMessage() {}

func (x *ClassTopLevelVariable) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassTopLevelVariable.ProtoReflect.Descriptor instead.
func (*ClassTopLevelVariable) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{28}
}

func (x *ClassTopLevelVariable) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *ClassTopLevelVariable) GetStartLine() int32 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *ClassTopLevelVariable) GetEndLine() int32 {
	if x != nil {
		return x.EndLine
	}
	return 0
}

func (x *ClassTopLevelVariable) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ClassTopLevelVariable) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

type MethodInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath  string `protobuf:"bytes,1,opt,name=file_path,proto3" json:"file_path,omitempty"`
	StartLine int32  `protobuf:"varint,2,opt,name=start_line,proto3" json:"start_line,omitempty"`
	EndLine   int32  `protobuf:"varint,3,opt,name=end_line,proto3" json:"end_line,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Signature string `protobuf:"bytes,5,opt,name=signature,proto3" json:"signature,omitempty"`
	Comment   string `protobuf:"bytes,6,opt,name=comment,proto3" json:"comment,omitempty"`
	Content   string `protobuf:"bytes,7,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *MethodInfo) Reset() {
	*x = MethodInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MethodInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MethodInfo) ProtoMessage() {}

func (x *MethodInfo) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MethodInfo.ProtoReflect.Descriptor instead.
func (*MethodInfo) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{29}
}

func (x *MethodInfo) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *MethodInfo) GetStartLine() int32 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *MethodInfo) GetEndLine() int32 {
	if x != nil {
		return x.EndLine
	}
	return 0
}

func (x *MethodInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MethodInfo) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *MethodInfo) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *MethodInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type MethodVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath      string        `protobuf:"bytes,1,opt,name=file_path,proto3" json:"file_path,omitempty"`
	StartLine     int32         `protobuf:"varint,2,opt,name=start_line,proto3" json:"start_line,omitempty"`
	EndLine       int32         `protobuf:"varint,3,opt,name=end_line,proto3" json:"end_line,omitempty"`
	Name          string        `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Signature     string        `protobuf:"bytes,5,opt,name=signature,proto3" json:"signature,omitempty"`
	Comment       string        `protobuf:"bytes,6,opt,name=comment,proto3" json:"comment,omitempty"`
	Content       string        `protobuf:"bytes,7,opt,name=content,proto3" json:"content,omitempty"`
	Callers       []*MethodInfo `protobuf:"bytes,8,rep,name=callers,proto3" json:"callers,omitempty"`
	Callees       []*MethodInfo `protobuf:"bytes,9,rep,name=callees,proto3" json:"callees,omitempty"`
	TestFunctions []*MethodInfo `protobuf:"bytes,10,rep,name=test_functions,proto3" json:"test_functions,omitempty"`
	Provider      string        `protobuf:"bytes,30,opt,name=provider,proto3" json:"provider,omitempty"`
}

func (x *MethodVariable) Reset() {
	*x = MethodVariable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MethodVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MethodVariable) ProtoMessage() {}

func (x *MethodVariable) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MethodVariable.ProtoReflect.Descriptor instead.
func (*MethodVariable) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{30}
}

func (x *MethodVariable) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *MethodVariable) GetStartLine() int32 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *MethodVariable) GetEndLine() int32 {
	if x != nil {
		return x.EndLine
	}
	return 0
}

func (x *MethodVariable) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MethodVariable) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *MethodVariable) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *MethodVariable) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MethodVariable) GetCallers() []*MethodInfo {
	if x != nil {
		return x.Callers
	}
	return nil
}

func (x *MethodVariable) GetCallees() []*MethodInfo {
	if x != nil {
		return x.Callees
	}
	return nil
}

func (x *MethodVariable) GetTestFunctions() []*MethodInfo {
	if x != nil {
		return x.TestFunctions
	}
	return nil
}

func (x *MethodVariable) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

type Variable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CodeChunk     *CodeChunkVariable     `protobuf:"bytes,1,opt,name=code_chunk,proto3" json:"code_chunk,omitempty"`
	Text          *TextVariable          `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Folder        *FolderVariable        `protobuf:"bytes,3,opt,name=folder,proto3" json:"folder,omitempty"`
	File          *FileVariable          `protobuf:"bytes,4,opt,name=file,proto3" json:"file,omitempty"`
	FileTopLevel  *FileTopLevelVariable  `protobuf:"bytes,5,opt,name=file_top_level,proto3" json:"file_top_level,omitempty"`
	Class         *ClassVariable         `protobuf:"bytes,6,opt,name=class,proto3" json:"class,omitempty"`
	ClassTopLevel *ClassTopLevelVariable `protobuf:"bytes,7,opt,name=class_top_level,proto3" json:"class_top_level,omitempty"`
	Method        *MethodVariable        `protobuf:"bytes,8,opt,name=method,proto3" json:"method,omitempty"`
}

func (x *Variable) Reset() {
	*x = Variable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Variable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Variable) ProtoMessage() {}

func (x *Variable) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Variable.ProtoReflect.Descriptor instead.
func (*Variable) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{31}
}

func (x *Variable) GetCodeChunk() *CodeChunkVariable {
	if x != nil {
		return x.CodeChunk
	}
	return nil
}

func (x *Variable) GetText() *TextVariable {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *Variable) GetFolder() *FolderVariable {
	if x != nil {
		return x.Folder
	}
	return nil
}

func (x *Variable) GetFile() *FileVariable {
	if x != nil {
		return x.File
	}
	return nil
}

func (x *Variable) GetFileTopLevel() *FileTopLevelVariable {
	if x != nil {
		return x.FileTopLevel
	}
	return nil
}

func (x *Variable) GetClass() *ClassVariable {
	if x != nil {
		return x.Class
	}
	return nil
}

func (x *Variable) GetClassTopLevel() *ClassTopLevelVariable {
	if x != nil {
		return x.ClassTopLevel
	}
	return nil
}

func (x *Variable) GetMethod() *MethodVariable {
	if x != nil {
		return x.Method
	}
	return nil
}

type SelectedMethodInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartLine int32  `protobuf:"varint,1,opt,name=start_line,proto3" json:"start_line,omitempty"`
	EndLine   int32  `protobuf:"varint,2,opt,name=end_line,proto3" json:"end_line,omitempty"`
	Name      string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Comment   string `protobuf:"bytes,4,opt,name=comment,proto3" json:"comment,omitempty"`
	Content   string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *SelectedMethodInfo) Reset() {
	*x = SelectedMethodInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelectedMethodInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelectedMethodInfo) ProtoMessage() {}

func (x *SelectedMethodInfo) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelectedMethodInfo.ProtoReflect.Descriptor instead.
func (*SelectedMethodInfo) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{32}
}

func (x *SelectedMethodInfo) GetStartLine() int32 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *SelectedMethodInfo) GetEndLine() int32 {
	if x != nil {
		return x.EndLine
	}
	return 0
}

func (x *SelectedMethodInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SelectedMethodInfo) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *SelectedMethodInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type SelectedClassInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartLine       int32                 `protobuf:"varint,1,opt,name=start_line,proto3" json:"start_line,omitempty"`
	EndLine         int32                 `protobuf:"varint,2,opt,name=end_line,proto3" json:"end_line,omitempty"`
	Name            string                `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Comment         string                `protobuf:"bytes,4,opt,name=comment,proto3" json:"comment,omitempty"`
	Content         string                `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	SelectedMethods []*SelectedMethodInfo `protobuf:"bytes,6,rep,name=selected_methods,proto3" json:"selected_methods,omitempty"`
}

func (x *SelectedClassInfo) Reset() {
	*x = SelectedClassInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelectedClassInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelectedClassInfo) ProtoMessage() {}

func (x *SelectedClassInfo) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelectedClassInfo.ProtoReflect.Descriptor instead.
func (*SelectedClassInfo) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{33}
}

func (x *SelectedClassInfo) GetStartLine() int32 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *SelectedClassInfo) GetEndLine() int32 {
	if x != nil {
		return x.EndLine
	}
	return 0
}

func (x *SelectedClassInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SelectedClassInfo) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *SelectedClassInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SelectedClassInfo) GetSelectedMethods() []*SelectedMethodInfo {
	if x != nil {
		return x.SelectedMethods
	}
	return nil
}

type CurrentEditorEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SelectedClasses []*SelectedClassInfo  `protobuf:"bytes,1,rep,name=selected_classes,proto3" json:"selected_classes,omitempty"`
	SelectedMethods []*SelectedMethodInfo `protobuf:"bytes,2,rep,name=selected_methods,proto3" json:"selected_methods,omitempty"`
}

func (x *CurrentEditorEntity) Reset() {
	*x = CurrentEditorEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrentEditorEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrentEditorEntity) ProtoMessage() {}

func (x *CurrentEditorEntity) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrentEditorEntity.ProtoReflect.Descriptor instead.
func (*CurrentEditorEntity) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{34}
}

func (x *CurrentEditorEntity) GetSelectedClasses() []*SelectedClassInfo {
	if x != nil {
		return x.SelectedClasses
	}
	return nil
}

func (x *CurrentEditorEntity) GetSelectedMethods() []*SelectedMethodInfo {
	if x != nil {
		return x.SelectedMethods
	}
	return nil
}

type Range struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartLine int32 `protobuf:"varint,1,opt,name=start_line,proto3" json:"start_line,omitempty"`
	EndLine   int32 `protobuf:"varint,2,opt,name=end_line,proto3" json:"end_line,omitempty"`
}

func (x *Range) Reset() {
	*x = Range{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Range) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Range) ProtoMessage() {}

func (x *Range) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Range.ProtoReflect.Descriptor instead.
func (*Range) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{35}
}

func (x *Range) GetStartLine() int32 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *Range) GetEndLine() int32 {
	if x != nil {
		return x.EndLine
	}
	return 0
}

type RefClassInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath  string `protobuf:"bytes,1,opt,name=file_path,proto3" json:"file_path,omitempty"`
	StartLine string `protobuf:"bytes,2,opt,name=start_line,proto3" json:"start_line,omitempty"`
	EndLine   string `protobuf:"bytes,3,opt,name=end_line,proto3" json:"end_line,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Content   string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *RefClassInfo) Reset() {
	*x = RefClassInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefClassInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefClassInfo) ProtoMessage() {}

func (x *RefClassInfo) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefClassInfo.ProtoReflect.Descriptor instead.
func (*RefClassInfo) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{36}
}

func (x *RefClassInfo) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *RefClassInfo) GetStartLine() string {
	if x != nil {
		return x.StartLine
	}
	return ""
}

func (x *RefClassInfo) GetEndLine() string {
	if x != nil {
		return x.EndLine
	}
	return ""
}

func (x *RefClassInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RefClassInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type RefTypeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath  string `protobuf:"bytes,1,opt,name=file_path,proto3" json:"file_path,omitempty"`
	StartLine string `protobuf:"bytes,2,opt,name=start_line,proto3" json:"start_line,omitempty"`
	EndLine   string `protobuf:"bytes,3,opt,name=end_line,proto3" json:"end_line,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Content   string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *RefTypeInfo) Reset() {
	*x = RefTypeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefTypeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefTypeInfo) ProtoMessage() {}

func (x *RefTypeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefTypeInfo.ProtoReflect.Descriptor instead.
func (*RefTypeInfo) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{37}
}

func (x *RefTypeInfo) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *RefTypeInfo) GetStartLine() string {
	if x != nil {
		return x.StartLine
	}
	return ""
}

func (x *RefTypeInfo) GetEndLine() string {
	if x != nil {
		return x.EndLine
	}
	return ""
}

func (x *RefTypeInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RefTypeInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type CurrentEditorVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath     string               `protobuf:"bytes,1,opt,name=file_path,proto3" json:"file_path,omitempty"`
	Content      string               `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	CursorLine   int32                `protobuf:"varint,3,opt,name=cursor_line,proto3" json:"cursor_line,omitempty"`
	SelectRange  *Range               `protobuf:"bytes,4,opt,name=select_range,proto3" json:"select_range,omitempty"`
	VisibleRange *Range               `protobuf:"bytes,5,opt,name=visible_range,proto3" json:"visible_range,omitempty"`
	Entity       *CurrentEditorEntity `protobuf:"bytes,6,opt,name=entity,proto3" json:"entity,omitempty"`
	Callees      []*MethodInfo        `protobuf:"bytes,7,rep,name=callees,proto3" json:"callees,omitempty"`
	RefClasses   []*RefClassInfo      `protobuf:"bytes,8,rep,name=ref_classes,proto3" json:"ref_classes,omitempty"`
	RefTypes     []*RefTypeInfo       `protobuf:"bytes,9,rep,name=ref_types,proto3" json:"ref_types,omitempty"`
}

func (x *CurrentEditorVariable) Reset() {
	*x = CurrentEditorVariable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CurrentEditorVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrentEditorVariable) ProtoMessage() {}

func (x *CurrentEditorVariable) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrentEditorVariable.ProtoReflect.Descriptor instead.
func (*CurrentEditorVariable) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{38}
}

func (x *CurrentEditorVariable) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *CurrentEditorVariable) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CurrentEditorVariable) GetCursorLine() int32 {
	if x != nil {
		return x.CursorLine
	}
	return 0
}

func (x *CurrentEditorVariable) GetSelectRange() *Range {
	if x != nil {
		return x.SelectRange
	}
	return nil
}

func (x *CurrentEditorVariable) GetVisibleRange() *Range {
	if x != nil {
		return x.VisibleRange
	}
	return nil
}

func (x *CurrentEditorVariable) GetEntity() *CurrentEditorEntity {
	if x != nil {
		return x.Entity
	}
	return nil
}

func (x *CurrentEditorVariable) GetCallees() []*MethodInfo {
	if x != nil {
		return x.Callees
	}
	return nil
}

func (x *CurrentEditorVariable) GetRefClasses() []*RefClassInfo {
	if x != nil {
		return x.RefClasses
	}
	return nil
}

func (x *CurrentEditorVariable) GetRefTypes() []*RefTypeInfo {
	if x != nil {
		return x.RefTypes
	}
	return nil
}

type Reference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath  string `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	StartLine int32  `protobuf:"varint,2,opt,name=start_line,json=startLine,proto3" json:"start_line,omitempty"`
	EndLine   int32  `protobuf:"varint,3,opt,name=end_line,json=endLine,proto3" json:"end_line,omitempty"`
	Uri       string `protobuf:"bytes,4,opt,name=uri,proto3" json:"uri,omitempty"`
	Name      string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Reference) Reset() {
	*x = Reference{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reference) ProtoMessage() {}

func (x *Reference) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reference.ProtoReflect.Descriptor instead.
func (*Reference) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{39}
}

func (x *Reference) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *Reference) GetStartLine() int32 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *Reference) GetEndLine() int32 {
	if x != nil {
		return x.EndLine
	}
	return 0
}

func (x *Reference) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *Reference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type RetrieveRelationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentEditor *CurrentEditorVariable `protobuf:"bytes,1,opt,name=current_editor,proto3" json:"current_editor,omitempty"`
	Variables     []*Variable            `protobuf:"bytes,2,rep,name=variables,json=user_message_entities,proto3" json:"variables,omitempty"`
	References    []*Reference           `protobuf:"bytes,10,rep,name=references,proto3" json:"references,omitempty"`
	JsonResult    string                 `protobuf:"bytes,50,opt,name=json_result,json=jsonResult,proto3" json:"json_result,omitempty"`
	ErrMetric     string                 `protobuf:"bytes,51,opt,name=err_metric,json=errMetric,proto3" json:"err_metric,omitempty"`
	Code          Code                   `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error         *Error                 `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *RetrieveRelationResponse) Reset() {
	*x = RetrieveRelationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveRelationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveRelationResponse) ProtoMessage() {}

func (x *RetrieveRelationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveRelationResponse.ProtoReflect.Descriptor instead.
func (*RetrieveRelationResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{40}
}

func (x *RetrieveRelationResponse) GetCurrentEditor() *CurrentEditorVariable {
	if x != nil {
		return x.CurrentEditor
	}
	return nil
}

func (x *RetrieveRelationResponse) GetVariables() []*Variable {
	if x != nil {
		return x.Variables
	}
	return nil
}

func (x *RetrieveRelationResponse) GetReferences() []*Reference {
	if x != nil {
		return x.References
	}
	return nil
}

func (x *RetrieveRelationResponse) GetJsonResult() string {
	if x != nil {
		return x.JsonResult
	}
	return ""
}

func (x *RetrieveRelationResponse) GetErrMetric() string {
	if x != nil {
		return x.ErrMetric
	}
	return ""
}

func (x *RetrieveRelationResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *RetrieveRelationResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type GetBuildStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectIds []string `protobuf:"bytes,1,rep,name=project_ids,json=projectIds,proto3" json:"project_ids,omitempty"`
}

func (x *GetBuildStatusRequest) Reset() {
	*x = GetBuildStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBuildStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBuildStatusRequest) ProtoMessage() {}

func (x *GetBuildStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBuildStatusRequest.ProtoReflect.Descriptor instead.
func (*GetBuildStatusRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{41}
}

func (x *GetBuildStatusRequest) GetProjectIds() []string {
	if x != nil {
		return x.ProjectIds
	}
	return nil
}

type DocumentBuildStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status BuildStatus `protobuf:"varint,1,opt,name=status,proto3,enum=protocol.BuildStatus" json:"status,omitempty"`
	Uri    string      `protobuf:"bytes,2,opt,name=uri,proto3" json:"uri,omitempty"`
	Name   string      `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DocumentBuildStatus) Reset() {
	*x = DocumentBuildStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocumentBuildStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocumentBuildStatus) ProtoMessage() {}

func (x *DocumentBuildStatus) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocumentBuildStatus.ProtoReflect.Descriptor instead.
func (*DocumentBuildStatus) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{42}
}

func (x *DocumentBuildStatus) GetStatus() BuildStatus {
	if x != nil {
		return x.Status
	}
	return BuildStatus_building
}

func (x *DocumentBuildStatus) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *DocumentBuildStatus) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ProjectBuildStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status            BuildStatus            `protobuf:"varint,1,opt,name=status,proto3,enum=protocol.BuildStatus" json:"status,omitempty"`
	Progress          float32                `protobuf:"fixed32,2,opt,name=progress,proto3" json:"progress,omitempty"`
	SuccessIndexFiles int64                  `protobuf:"varint,3,opt,name=success_index_files,json=successIndexFiles,proto3" json:"success_index_files,omitempty"`
	FailedIndexFiles  int64                  `protobuf:"varint,4,opt,name=failed_index_files,json=failedIndexFiles,proto3" json:"failed_index_files,omitempty"`
	TotalIndexFiles   int64                  `protobuf:"varint,5,opt,name=total_index_files,json=totalIndexFiles,proto3" json:"total_index_files,omitempty"`
	EmptyProject      bool                   `protobuf:"varint,6,opt,name=empty_project,json=emptyProject,proto3" json:"empty_project,omitempty"`
	DocumentsStatus   []*DocumentBuildStatus `protobuf:"bytes,7,rep,name=documents_status,json=documentsStatus,proto3" json:"documents_status,omitempty"`
}

func (x *ProjectBuildStatus) Reset() {
	*x = ProjectBuildStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectBuildStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectBuildStatus) ProtoMessage() {}

func (x *ProjectBuildStatus) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectBuildStatus.ProtoReflect.Descriptor instead.
func (*ProjectBuildStatus) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{43}
}

func (x *ProjectBuildStatus) GetStatus() BuildStatus {
	if x != nil {
		return x.Status
	}
	return BuildStatus_building
}

func (x *ProjectBuildStatus) GetProgress() float32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *ProjectBuildStatus) GetSuccessIndexFiles() int64 {
	if x != nil {
		return x.SuccessIndexFiles
	}
	return 0
}

func (x *ProjectBuildStatus) GetFailedIndexFiles() int64 {
	if x != nil {
		return x.FailedIndexFiles
	}
	return 0
}

func (x *ProjectBuildStatus) GetTotalIndexFiles() int64 {
	if x != nil {
		return x.TotalIndexFiles
	}
	return 0
}

func (x *ProjectBuildStatus) GetEmptyProject() bool {
	if x != nil {
		return x.EmptyProject
	}
	return false
}

func (x *ProjectBuildStatus) GetDocumentsStatus() []*DocumentBuildStatus {
	if x != nil {
		return x.DocumentsStatus
	}
	return nil
}

type GetBuildStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status map[string]*ProjectBuildStatus `protobuf:"bytes,1,rep,name=status,proto3" json:"status,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Code   Code                           `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error  *Error                         `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *GetBuildStatusResponse) Reset() {
	*x = GetBuildStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBuildStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBuildStatusResponse) ProtoMessage() {}

func (x *GetBuildStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBuildStatusResponse.ProtoReflect.Descriptor instead.
func (*GetBuildStatusResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{44}
}

func (x *GetBuildStatusResponse) GetStatus() map[string]*ProjectBuildStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetBuildStatusResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *GetBuildStatusResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type GetDocumentsIndexStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectIds []string `protobuf:"bytes,1,rep,name=project_ids,json=projectIds,proto3" json:"project_ids,omitempty"`
}

func (x *GetDocumentsIndexStatusRequest) Reset() {
	*x = GetDocumentsIndexStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDocumentsIndexStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDocumentsIndexStatusRequest) ProtoMessage() {}

func (x *GetDocumentsIndexStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDocumentsIndexStatusRequest.ProtoReflect.Descriptor instead.
func (*GetDocumentsIndexStatusRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{45}
}

func (x *GetDocumentsIndexStatusRequest) GetProjectIds() []string {
	if x != nil {
		return x.ProjectIds
	}
	return nil
}

type ProjectDocumentsIndexStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status          BuildStatus            `protobuf:"varint,1,opt,name=status,proto3,enum=protocol.BuildStatus" json:"status,omitempty"`
	DocumentsStatus []*DocumentIndexStatus `protobuf:"bytes,7,rep,name=documents_status,json=documentsStatus,proto3" json:"documents_status,omitempty"`
}

func (x *ProjectDocumentsIndexStatus) Reset() {
	*x = ProjectDocumentsIndexStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectDocumentsIndexStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectDocumentsIndexStatus) ProtoMessage() {}

func (x *ProjectDocumentsIndexStatus) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectDocumentsIndexStatus.ProtoReflect.Descriptor instead.
func (*ProjectDocumentsIndexStatus) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{46}
}

func (x *ProjectDocumentsIndexStatus) GetStatus() BuildStatus {
	if x != nil {
		return x.Status
	}
	return BuildStatus_building
}

func (x *ProjectDocumentsIndexStatus) GetDocumentsStatus() []*DocumentIndexStatus {
	if x != nil {
		return x.DocumentsStatus
	}
	return nil
}

type DocumentIndexStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status BuildStatus `protobuf:"varint,1,opt,name=status,proto3,enum=protocol.BuildStatus" json:"status,omitempty"`
	Uri    string      `protobuf:"bytes,2,opt,name=uri,proto3" json:"uri,omitempty"`
	Name   string      `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DocumentIndexStatus) Reset() {
	*x = DocumentIndexStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DocumentIndexStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocumentIndexStatus) ProtoMessage() {}

func (x *DocumentIndexStatus) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocumentIndexStatus.ProtoReflect.Descriptor instead.
func (*DocumentIndexStatus) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{47}
}

func (x *DocumentIndexStatus) GetStatus() BuildStatus {
	if x != nil {
		return x.Status
	}
	return BuildStatus_building
}

func (x *DocumentIndexStatus) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *DocumentIndexStatus) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetDocumentsIndexStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectsStatus map[string]*ProjectDocumentsIndexStatus `protobuf:"bytes,1,rep,name=projects_status,json=projectsStatus,proto3" json:"projects_status,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Code           Code                                    `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error          *Error                                  `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *GetDocumentsIndexStatusResponse) Reset() {
	*x = GetDocumentsIndexStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDocumentsIndexStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDocumentsIndexStatusResponse) ProtoMessage() {}

func (x *GetDocumentsIndexStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDocumentsIndexStatusResponse.ProtoReflect.Descriptor instead.
func (*GetDocumentsIndexStatusResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{48}
}

func (x *GetDocumentsIndexStatusResponse) GetProjectsStatus() map[string]*ProjectDocumentsIndexStatus {
	if x != nil {
		return x.ProjectsStatus
	}
	return nil
}

func (x *GetDocumentsIndexStatusResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *GetDocumentsIndexStatusResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type IsVersionMatchedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *IsVersionMatchedRequest) Reset() {
	*x = IsVersionMatchedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsVersionMatchedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsVersionMatchedRequest) ProtoMessage() {}

func (x *IsVersionMatchedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsVersionMatchedRequest.ProtoReflect.Descriptor instead.
func (*IsVersionMatchedRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{49}
}

func (x *IsVersionMatchedRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type IsVersionMatchedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Matched bool   `protobuf:"varint,1,opt,name=matched,proto3" json:"matched,omitempty"`
	Code    Code   `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error   *Error `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *IsVersionMatchedResponse) Reset() {
	*x = IsVersionMatchedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsVersionMatchedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsVersionMatchedResponse) ProtoMessage() {}

func (x *IsVersionMatchedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsVersionMatchedResponse.ProtoReflect.Descriptor instead.
func (*IsVersionMatchedResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{50}
}

func (x *IsVersionMatchedResponse) GetMatched() bool {
	if x != nil {
		return x.Matched
	}
	return false
}

func (x *IsVersionMatchedResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *IsVersionMatchedResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type CancelIndexRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
}

func (x *CancelIndexRequest) Reset() {
	*x = CancelIndexRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelIndexRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelIndexRequest) ProtoMessage() {}

func (x *CancelIndexRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelIndexRequest.ProtoReflect.Descriptor instead.
func (*CancelIndexRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{51}
}

func (x *CancelIndexRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

type CancelIndexResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Succeed bool   `protobuf:"varint,1,opt,name=succeed,proto3" json:"succeed,omitempty"`
	Code    Code   `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error   *Error `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *CancelIndexResponse) Reset() {
	*x = CancelIndexResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelIndexResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelIndexResponse) ProtoMessage() {}

func (x *CancelIndexResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelIndexResponse.ProtoReflect.Descriptor instead.
func (*CancelIndexResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{52}
}

func (x *CancelIndexResponse) GetSucceed() bool {
	if x != nil {
		return x.Succeed
	}
	return false
}

func (x *CancelIndexResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *CancelIndexResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type ImportAnalysisRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId       string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	File            string `protobuf:"bytes,5,opt,name=file,proto3" json:"file,omitempty"`
	ImportStatement string `protobuf:"bytes,6,opt,name=import_statement,json=importStatement,proto3" json:"import_statement,omitempty"`
}

func (x *ImportAnalysisRequest) Reset() {
	*x = ImportAnalysisRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportAnalysisRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportAnalysisRequest) ProtoMessage() {}

func (x *ImportAnalysisRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportAnalysisRequest.ProtoReflect.Descriptor instead.
func (*ImportAnalysisRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{53}
}

func (x *ImportAnalysisRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ImportAnalysisRequest) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

func (x *ImportAnalysisRequest) GetImportStatement() string {
	if x != nil {
		return x.ImportStatement
	}
	return ""
}

type FilesImportAnalysisRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId string   `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	RelaFiles []string `protobuf:"bytes,2,rep,name=rela_files,json=relaFiles,proto3" json:"rela_files,omitempty"`
}

func (x *FilesImportAnalysisRequest) Reset() {
	*x = FilesImportAnalysisRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilesImportAnalysisRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilesImportAnalysisRequest) ProtoMessage() {}

func (x *FilesImportAnalysisRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilesImportAnalysisRequest.ProtoReflect.Descriptor instead.
func (*FilesImportAnalysisRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{54}
}

func (x *FilesImportAnalysisRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *FilesImportAnalysisRequest) GetRelaFiles() []string {
	if x != nil {
		return x.RelaFiles
	}
	return nil
}

type ImportAnalysisResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   Code                  `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error  *Error                `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
	Result *ImportAnalysisResult `protobuf:"bytes,3,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *ImportAnalysisResponse) Reset() {
	*x = ImportAnalysisResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportAnalysisResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportAnalysisResponse) ProtoMessage() {}

func (x *ImportAnalysisResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportAnalysisResponse.ProtoReflect.Descriptor instead.
func (*ImportAnalysisResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{55}
}

func (x *ImportAnalysisResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *ImportAnalysisResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *ImportAnalysisResponse) GetResult() *ImportAnalysisResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type ImportAnalysisResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Data    string `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ImportAnalysisResult) Reset() {
	*x = ImportAnalysisResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportAnalysisResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportAnalysisResult) ProtoMessage() {}

func (x *ImportAnalysisResult) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportAnalysisResult.ProtoReflect.Descriptor instead.
func (*ImportAnalysisResult) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{56}
}

func (x *ImportAnalysisResult) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ImportAnalysisResult) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type Symbol struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	FilePath  string `protobuf:"bytes,2,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	StartLine int32  `protobuf:"varint,3,opt,name=start_line,json=startLine,proto3" json:"start_line,omitempty"`
	EndLine   int32  `protobuf:"varint,4,opt,name=end_line,json=endLine,proto3" json:"end_line,omitempty"`
}

func (x *Symbol) Reset() {
	*x = Symbol{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Symbol) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Symbol) ProtoMessage() {}

func (x *Symbol) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Symbol.ProtoReflect.Descriptor instead.
func (*Symbol) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{57}
}

func (x *Symbol) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Symbol) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *Symbol) GetStartLine() int32 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *Symbol) GetEndLine() int32 {
	if x != nil {
		return x.EndLine
	}
	return 0
}

type SearchCKGDBRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId        string       `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	SymbolIdentifier string       `protobuf:"bytes,2,opt,name=symbol_identifier,json=symbolIdentifier,proto3" json:"symbol_identifier,omitempty"`
	SymbolRegex      string       `protobuf:"bytes,3,opt,name=symbol_regex,json=symbolRegex,proto3" json:"symbol_regex,omitempty"`
	Types            []EntityType `protobuf:"varint,4,rep,packed,name=types,proto3,enum=protocol.EntityType" json:"types,omitempty"`
	EntityNum        int32        `protobuf:"varint,5,opt,name=entity_num,json=entityNum,proto3" json:"entity_num,omitempty"`
}

func (x *SearchCKGDBRequest) Reset() {
	*x = SearchCKGDBRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCKGDBRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCKGDBRequest) ProtoMessage() {}

func (x *SearchCKGDBRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCKGDBRequest.ProtoReflect.Descriptor instead.
func (*SearchCKGDBRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{58}
}

func (x *SearchCKGDBRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchCKGDBRequest) GetSymbolIdentifier() string {
	if x != nil {
		return x.SymbolIdentifier
	}
	return ""
}

func (x *SearchCKGDBRequest) GetSymbolRegex() string {
	if x != nil {
		return x.SymbolRegex
	}
	return ""
}

func (x *SearchCKGDBRequest) GetTypes() []EntityType {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *SearchCKGDBRequest) GetEntityNum() int32 {
	if x != nil {
		return x.EntityNum
	}
	return 0
}

type SearchCKGDBResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Variables  []*Variable  `protobuf:"bytes,2,rep,name=variables,proto3" json:"variables,omitempty"`
	References []*Reference `protobuf:"bytes,10,rep,name=references,proto3" json:"references,omitempty"`
	JsonResult string       `protobuf:"bytes,50,opt,name=json_result,json=jsonResult,proto3" json:"json_result,omitempty"`
	Code       Code         `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error      *Error       `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *SearchCKGDBResponse) Reset() {
	*x = SearchCKGDBResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCKGDBResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCKGDBResponse) ProtoMessage() {}

func (x *SearchCKGDBResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCKGDBResponse.ProtoReflect.Descriptor instead.
func (*SearchCKGDBResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{59}
}

func (x *SearchCKGDBResponse) GetVariables() []*Variable {
	if x != nil {
		return x.Variables
	}
	return nil
}

func (x *SearchCKGDBResponse) GetReferences() []*Reference {
	if x != nil {
		return x.References
	}
	return nil
}

func (x *SearchCKGDBResponse) GetJsonResult() string {
	if x != nil {
		return x.JsonResult
	}
	return ""
}

func (x *SearchCKGDBResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *SearchCKGDBResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type CodeSnippet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath  string `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	StartLine int32  `protobuf:"varint,2,opt,name=start_line,json=startLine,proto3" json:"start_line,omitempty"`
	EndLine   int32  `protobuf:"varint,3,opt,name=end_line,json=endLine,proto3" json:"end_line,omitempty"`
	Content   string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *CodeSnippet) Reset() {
	*x = CodeSnippet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CodeSnippet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeSnippet) ProtoMessage() {}

func (x *CodeSnippet) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeSnippet.ProtoReflect.Descriptor instead.
func (*CodeSnippet) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{60}
}

func (x *CodeSnippet) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *CodeSnippet) GetStartLine() int32 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *CodeSnippet) GetEndLine() int32 {
	if x != nil {
		return x.EndLine
	}
	return 0
}

func (x *CodeSnippet) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type RetrieveRelevantSnippetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	UserQuery string `protobuf:"bytes,2,opt,name=user_query,json=userQuery,proto3" json:"user_query,omitempty"`
	// 下面是用户选中的 context
	Workspace       bool               `protobuf:"varint,10,opt,name=workspace,proto3" json:"workspace,omitempty"`
	SelectedFolders []string           `protobuf:"bytes,11,rep,name=selected_folders,json=selectedFolders,proto3" json:"selected_folders,omitempty"`
	SelectedFiles   []string           `protobuf:"bytes,12,rep,name=selected_files,json=selectedFiles,proto3" json:"selected_files,omitempty"`
	SelectedCodes   []*CodeSnippet     `protobuf:"bytes,13,rep,name=selected_codes,json=selectedCodes,proto3" json:"selected_codes,omitempty"`
	CurrentEditor   *CurrentEditorInfo `protobuf:"bytes,14,opt,name=current_editor,json=currentEditor,proto3" json:"current_editor,omitempty"`
	// 版本号
	Version   string `protobuf:"bytes,30,opt,name=version,proto3" json:"version,omitempty"`
	UserId    string `protobuf:"bytes,100,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SessionId string `protobuf:"bytes,101,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
}

func (x *RetrieveRelevantSnippetRequest) Reset() {
	*x = RetrieveRelevantSnippetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveRelevantSnippetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveRelevantSnippetRequest) ProtoMessage() {}

func (x *RetrieveRelevantSnippetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveRelevantSnippetRequest.ProtoReflect.Descriptor instead.
func (*RetrieveRelevantSnippetRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{61}
}

func (x *RetrieveRelevantSnippetRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *RetrieveRelevantSnippetRequest) GetUserQuery() string {
	if x != nil {
		return x.UserQuery
	}
	return ""
}

func (x *RetrieveRelevantSnippetRequest) GetWorkspace() bool {
	if x != nil {
		return x.Workspace
	}
	return false
}

func (x *RetrieveRelevantSnippetRequest) GetSelectedFolders() []string {
	if x != nil {
		return x.SelectedFolders
	}
	return nil
}

func (x *RetrieveRelevantSnippetRequest) GetSelectedFiles() []string {
	if x != nil {
		return x.SelectedFiles
	}
	return nil
}

func (x *RetrieveRelevantSnippetRequest) GetSelectedCodes() []*CodeSnippet {
	if x != nil {
		return x.SelectedCodes
	}
	return nil
}

func (x *RetrieveRelevantSnippetRequest) GetCurrentEditor() *CurrentEditorInfo {
	if x != nil {
		return x.CurrentEditor
	}
	return nil
}

func (x *RetrieveRelevantSnippetRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *RetrieveRelevantSnippetRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RetrieveRelevantSnippetRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

type Snippet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId   string      `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Type        SnippetType `protobuf:"varint,2,opt,name=type,proto3,enum=protocol.SnippetType" json:"type,omitempty"`
	FilePath    string      `protobuf:"bytes,3,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	StartLine   int32       `protobuf:"varint,4,opt,name=start_line,json=startLine,proto3" json:"start_line,omitempty"`
	EndLine     int32       `protobuf:"varint,5,opt,name=end_line,json=endLine,proto3" json:"end_line,omitempty"`
	Content     string      `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	RecallType  RecallType  `protobuf:"varint,101,opt,name=recall_type,json=recallType,proto3,enum=protocol.RecallType" json:"recall_type,omitempty"`
	CkgEntityId string      `protobuf:"bytes,102,opt,name=ckg_entity_id,json=ckgEntityId,proto3" json:"ckg_entity_id,omitempty"`
}

func (x *Snippet) Reset() {
	*x = Snippet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Snippet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Snippet) ProtoMessage() {}

func (x *Snippet) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Snippet.ProtoReflect.Descriptor instead.
func (*Snippet) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{62}
}

func (x *Snippet) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Snippet) GetType() SnippetType {
	if x != nil {
		return x.Type
	}
	return SnippetType_snippet_type_code
}

func (x *Snippet) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *Snippet) GetStartLine() int32 {
	if x != nil {
		return x.StartLine
	}
	return 0
}

func (x *Snippet) GetEndLine() int32 {
	if x != nil {
		return x.EndLine
	}
	return 0
}

func (x *Snippet) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Snippet) GetRecallType() RecallType {
	if x != nil {
		return x.RecallType
	}
	return RecallType_recall_type_user_specified
}

func (x *Snippet) GetCkgEntityId() string {
	if x != nil {
		return x.CkgEntityId
	}
	return ""
}

type RetrieveRelevantSnippetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SnippetsByWorkspace   []*Snippet `protobuf:"bytes,1,rep,name=snippets_by_workspace,json=snippetsByWorkspace,proto3" json:"snippets_by_workspace,omitempty"`
	SnippetsByFolder      []*Snippet `protobuf:"bytes,2,rep,name=snippets_by_folder,json=snippetsByFolder,proto3" json:"snippets_by_folder,omitempty"`
	SnippetsByFile        []*Snippet `protobuf:"bytes,3,rep,name=snippets_by_file,json=snippetsByFile,proto3" json:"snippets_by_file,omitempty"`
	SnippetsByCode        []*Snippet `protobuf:"bytes,4,rep,name=snippets_by_code,json=snippetsByCode,proto3" json:"snippets_by_code,omitempty"`
	SnippetsByCurrentFile []*Snippet `protobuf:"bytes,5,rep,name=snippets_by_current_file,json=snippetsByCurrentFile,proto3" json:"snippets_by_current_file,omitempty"`
	SnippetsByInteraction []*Snippet `protobuf:"bytes,6,rep,name=snippets_by_interaction,json=snippetsByInteraction,proto3" json:"snippets_by_interaction,omitempty"`
	Code                  Code       `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error                 *Error     `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *RetrieveRelevantSnippetResponse) Reset() {
	*x = RetrieveRelevantSnippetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveRelevantSnippetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveRelevantSnippetResponse) ProtoMessage() {}

func (x *RetrieveRelevantSnippetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveRelevantSnippetResponse.ProtoReflect.Descriptor instead.
func (*RetrieveRelevantSnippetResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{63}
}

func (x *RetrieveRelevantSnippetResponse) GetSnippetsByWorkspace() []*Snippet {
	if x != nil {
		return x.SnippetsByWorkspace
	}
	return nil
}

func (x *RetrieveRelevantSnippetResponse) GetSnippetsByFolder() []*Snippet {
	if x != nil {
		return x.SnippetsByFolder
	}
	return nil
}

func (x *RetrieveRelevantSnippetResponse) GetSnippetsByFile() []*Snippet {
	if x != nil {
		return x.SnippetsByFile
	}
	return nil
}

func (x *RetrieveRelevantSnippetResponse) GetSnippetsByCode() []*Snippet {
	if x != nil {
		return x.SnippetsByCode
	}
	return nil
}

func (x *RetrieveRelevantSnippetResponse) GetSnippetsByCurrentFile() []*Snippet {
	if x != nil {
		return x.SnippetsByCurrentFile
	}
	return nil
}

func (x *RetrieveRelevantSnippetResponse) GetSnippetsByInteraction() []*Snippet {
	if x != nil {
		return x.SnippetsByInteraction
	}
	return nil
}

func (x *RetrieveRelevantSnippetResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *RetrieveRelevantSnippetResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type RerankSnippetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SnippetsByWorkspace   []*Snippet `protobuf:"bytes,1,rep,name=snippets_by_workspace,json=snippetsByWorkspace,proto3" json:"snippets_by_workspace,omitempty"`
	SnippetsByFolder      []*Snippet `protobuf:"bytes,2,rep,name=snippets_by_folder,json=snippetsByFolder,proto3" json:"snippets_by_folder,omitempty"`
	SnippetsByFile        []*Snippet `protobuf:"bytes,3,rep,name=snippets_by_file,json=snippetsByFile,proto3" json:"snippets_by_file,omitempty"`
	SnippetsByCode        []*Snippet `protobuf:"bytes,4,rep,name=snippets_by_code,json=snippetsByCode,proto3" json:"snippets_by_code,omitempty"`
	SnippetsByCurrentFile []*Snippet `protobuf:"bytes,5,rep,name=snippets_by_current_file,json=snippetsByCurrentFile,proto3" json:"snippets_by_current_file,omitempty"`
	Token                 string     `protobuf:"bytes,100,opt,name=token,proto3" json:"token,omitempty"`
	SessionId             string     `protobuf:"bytes,101,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
}

func (x *RerankSnippetRequest) Reset() {
	*x = RerankSnippetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RerankSnippetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RerankSnippetRequest) ProtoMessage() {}

func (x *RerankSnippetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RerankSnippetRequest.ProtoReflect.Descriptor instead.
func (*RerankSnippetRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{64}
}

func (x *RerankSnippetRequest) GetSnippetsByWorkspace() []*Snippet {
	if x != nil {
		return x.SnippetsByWorkspace
	}
	return nil
}

func (x *RerankSnippetRequest) GetSnippetsByFolder() []*Snippet {
	if x != nil {
		return x.SnippetsByFolder
	}
	return nil
}

func (x *RerankSnippetRequest) GetSnippetsByFile() []*Snippet {
	if x != nil {
		return x.SnippetsByFile
	}
	return nil
}

func (x *RerankSnippetRequest) GetSnippetsByCode() []*Snippet {
	if x != nil {
		return x.SnippetsByCode
	}
	return nil
}

func (x *RerankSnippetRequest) GetSnippetsByCurrentFile() []*Snippet {
	if x != nil {
		return x.SnippetsByCurrentFile
	}
	return nil
}

func (x *RerankSnippetRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *RerankSnippetRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

type SnippetWithScore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Snippet *Snippet `protobuf:"bytes,1,opt,name=snippet,proto3" json:"snippet,omitempty"`
	Score   float32  `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty"`
}

func (x *SnippetWithScore) Reset() {
	*x = SnippetWithScore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnippetWithScore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnippetWithScore) ProtoMessage() {}

func (x *SnippetWithScore) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnippetWithScore.ProtoReflect.Descriptor instead.
func (*SnippetWithScore) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{65}
}

func (x *SnippetWithScore) GetSnippet() *Snippet {
	if x != nil {
		return x.Snippet
	}
	return nil
}

func (x *SnippetWithScore) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

type RerankSnippetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Snippets []*SnippetWithScore `protobuf:"bytes,1,rep,name=snippets,proto3" json:"snippets,omitempty"`
	Code     Code                `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error    *Error              `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *RerankSnippetResponse) Reset() {
	*x = RerankSnippetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RerankSnippetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RerankSnippetResponse) ProtoMessage() {}

func (x *RerankSnippetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RerankSnippetResponse.ProtoReflect.Descriptor instead.
func (*RerankSnippetResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{66}
}

func (x *RerankSnippetResponse) GetSnippets() []*SnippetWithScore {
	if x != nil {
		return x.Snippets
	}
	return nil
}

func (x *RerankSnippetResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *RerankSnippetResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type CursorMoveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	File      string `protobuf:"bytes,2,opt,name=file,proto3" json:"file,omitempty"`
	Line      int32  `protobuf:"varint,3,opt,name=line,proto3" json:"line,omitempty"`
	UserId    string `protobuf:"bytes,9,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Version   string `protobuf:"bytes,10,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *CursorMoveRequest) Reset() {
	*x = CursorMoveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CursorMoveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CursorMoveRequest) ProtoMessage() {}

func (x *CursorMoveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CursorMoveRequest.ProtoReflect.Descriptor instead.
func (*CursorMoveRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{67}
}

func (x *CursorMoveRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *CursorMoveRequest) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

func (x *CursorMoveRequest) GetLine() int32 {
	if x != nil {
		return x.Line
	}
	return 0
}

func (x *CursorMoveRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CursorMoveRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type IsCKGEnabledForNonWorkspaceScenarioRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *IsCKGEnabledForNonWorkspaceScenarioRequest) Reset() {
	*x = IsCKGEnabledForNonWorkspaceScenarioRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsCKGEnabledForNonWorkspaceScenarioRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsCKGEnabledForNonWorkspaceScenarioRequest) ProtoMessage() {}

func (x *IsCKGEnabledForNonWorkspaceScenarioRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsCKGEnabledForNonWorkspaceScenarioRequest.ProtoReflect.Descriptor instead.
func (*IsCKGEnabledForNonWorkspaceScenarioRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{68}
}

func (x *IsCKGEnabledForNonWorkspaceScenarioRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type IsCKGEnabledForNonWorkspaceScenarioResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsEnabled bool   `protobuf:"varint,1,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	Version   string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Code      Code   `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error     *Error `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *IsCKGEnabledForNonWorkspaceScenarioResponse) Reset() {
	*x = IsCKGEnabledForNonWorkspaceScenarioResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsCKGEnabledForNonWorkspaceScenarioResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsCKGEnabledForNonWorkspaceScenarioResponse) ProtoMessage() {}

func (x *IsCKGEnabledForNonWorkspaceScenarioResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsCKGEnabledForNonWorkspaceScenarioResponse.ProtoReflect.Descriptor instead.
func (*IsCKGEnabledForNonWorkspaceScenarioResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{69}
}

func (x *IsCKGEnabledForNonWorkspaceScenarioResponse) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *IsCKGEnabledForNonWorkspaceScenarioResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *IsCKGEnabledForNonWorkspaceScenarioResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *IsCKGEnabledForNonWorkspaceScenarioResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type SetUpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host          string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	SourceProduct string `protobuf:"bytes,3,opt,name=source_product,json=sourceProduct,proto3" json:"source_product,omitempty"`
	DeviceCpu     string `protobuf:"bytes,4,opt,name=device_cpu,json=deviceCpu,proto3" json:"device_cpu,omitempty"`
	DeviceId      string `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	MachineId     string `protobuf:"bytes,6,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	DeviceBrand   string `protobuf:"bytes,7,opt,name=device_brand,json=deviceBrand,proto3" json:"device_brand,omitempty"`
	DeviceType    string `protobuf:"bytes,8,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	OsVersion     string `protobuf:"bytes,9,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`
}

func (x *SetUpRequest) Reset() {
	*x = SetUpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUpRequest) ProtoMessage() {}

func (x *SetUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUpRequest.ProtoReflect.Descriptor instead.
func (*SetUpRequest) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{70}
}

func (x *SetUpRequest) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *SetUpRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *SetUpRequest) GetSourceProduct() string {
	if x != nil {
		return x.SourceProduct
	}
	return ""
}

func (x *SetUpRequest) GetDeviceCpu() string {
	if x != nil {
		return x.DeviceCpu
	}
	return ""
}

func (x *SetUpRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *SetUpRequest) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *SetUpRequest) GetDeviceBrand() string {
	if x != nil {
		return x.DeviceBrand
	}
	return ""
}

func (x *SetUpRequest) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *SetUpRequest) GetOsVersion() string {
	if x != nil {
		return x.OsVersion
	}
	return ""
}

type SetUpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  Code   `protobuf:"varint,100,opt,name=code,proto3,enum=protocol.Code" json:"code,omitempty"`
	Error *Error `protobuf:"bytes,101,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *SetUpResponse) Reset() {
	*x = SetUpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_codekg_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUpResponse) ProtoMessage() {}

func (x *SetUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_codekg_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUpResponse.ProtoReflect.Descriptor instead.
func (*SetUpResponse) Descriptor() ([]byte, []int) {
	return file_codekg_proto_rawDescGZIP(), []int{71}
}

func (x *SetUpResponse) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_succeed
}

func (x *SetUpResponse) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_codekg_proto protoreflect.FileDescriptor

var file_codekg_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x63, 0x6f, 0x64, 0x65, 0x6b, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x37, 0x0a, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x22, 0x44, 0x0a, 0x13, 0x52, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x22, 0x61, 0x0a, 0x14, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x64, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x22, 0x6c, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x46, 0x69, 0x6c,
	0x65, 0x22, 0xa2, 0x01, 0x0a, 0x0b, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x5f, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x2d,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x59, 0x0a, 0x0c, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x64,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x22, 0x76, 0x0a, 0x0e, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x72, 0x69, 0x12, 0x33, 0x0a, 0x16, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x6c, 0x6f, 0x62, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x47, 0x6c,
	0x6f, 0x62, 0x73, 0x54, 0x6f, 0x4c, 0x6f, 0x61, 0x64, 0x22, 0x98, 0x01, 0x0a, 0x1a, 0x49, 0x6e,
	0x69, 0x74, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x2b,
	0x0a, 0x12, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x5f, 0x66, 0x72, 0x6f,
	0x6d, 0x5f, 0x66, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x6c, 0x6f, 0x61, 0x64,
	0x46, 0x69, 0x6c, 0x65, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x46, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x68, 0x0a, 0x1b, 0x49, 0x6e, 0x69, 0x74, 0x56, 0x69, 0x72, 0x74,
	0x75, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x64, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x69,
	0x0a, 0x08, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x69, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x69, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x7a, 0x0a, 0x0f, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x73, 0x12, 0x30, 0x0a, 0x09, 0x64,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x09, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x44, 0x22, 0xdd, 0x01, 0x0a, 0x10, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x08, 0x65, 0x72,
	0x72, 0x5f, 0x6d, 0x73, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x73, 0x12, 0x22,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x65, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x1a, 0x3a, 0x0a, 0x0c, 0x45, 0x72, 0x72,
	0x4d, 0x73, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x33, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x7a, 0x0a, 0x13, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x12, 0x22, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x25, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0xfa, 0x02, 0x0a, 0x11, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x45, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x72, 0x73,
	0x6f, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63,
	0x75, 0x72, 0x73, 0x6f, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x3b, 0x0a, 0x11, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x12, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x10, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x28, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x22, 0xf1, 0x02, 0x0a, 0x15, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x73, 0x12, 0x21,
	0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x3c, 0x0a, 0x0b, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x45, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x44, 0x0a, 0x13, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x11, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0b, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x50, 0x61, 0x74, 0x68, 0x73, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x44, 0x0a, 0x06, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x22, 0x84, 0x03,
	0x0a, 0x16, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x18, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x5f, 0x62, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x15, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x65, 0x6d, 0x62,
	0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x14, 0x71, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64,
	0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x76, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x76, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x12, 0x36, 0x0a, 0x17, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x15, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x5f, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x63,
	0x61, 0x6c, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x22, 0xbb, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x49, 0x0a, 0x18, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x5f, 0x62, 0x79, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x52, 0x15, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x42, 0x79,
	0x55, 0x73, 0x65, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3c, 0x0a, 0x0b, 0x65,
	0x64, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x45, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x65,
	0x64, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x22, 0xa3, 0x01, 0x0a, 0x11, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b,
	0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x22, 0x9e, 0x01, 0x0a, 0x0c, 0x54, 0x65, 0x78,
	0x74, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c,
	0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c,
	0x69, 0x6e, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x22, 0x48, 0x0a, 0x0e, 0x55, 0x73, 0x65,
	0x66, 0x75, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x22, 0xaa, 0x01, 0x0a, 0x0e, 0x46, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x56, 0x61,
	0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x74,
	0x72, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x5f, 0x74, 0x72, 0x65, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x66, 0x75, 0x6c,
	0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x55, 0x73, 0x65, 0x66, 0x75, 0x6c, 0x46, 0x69,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x22, 0x7c, 0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x65, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x1e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x22, 0xa6,
	0x01, 0x0a, 0x14, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x6f, 0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x56,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c,
	0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x22, 0x22, 0x0a, 0x06, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xe6, 0x02, 0x0a, 0x0d,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x29, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x07, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x12, 0x3c, 0x0a, 0x0e, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x66,
	0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x22, 0xa7, 0x01, 0x0a, 0x15, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x54, 0x6f,
	0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x1e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x22, 0xcc,
	0x01, 0x0a, 0x0a, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x8a, 0x03,
	0x0a, 0x0e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1e,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x2e, 0x0a, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x73,
	0x12, 0x2e, 0x0a, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x65, 0x73,
	0x12, 0x3c, 0x0a, 0x0e, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x22, 0xc5, 0x03, 0x0a, 0x08, 0x56,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x3b, 0x0a, 0x0a, 0x63, 0x6f, 0x64, 0x65, 0x5f,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x68, 0x75, 0x6e, 0x6b,
	0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x0a, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74,
	0x12, 0x30, 0x0a, 0x06, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x46, 0x6f, 0x6c, 0x64,
	0x65, 0x72, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x06, 0x66, 0x6f, 0x6c, 0x64,
	0x65, 0x72, 0x12, 0x2a, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x46, 0x69, 0x6c, 0x65,
	0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x46,
	0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x70, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x6f, 0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x56, 0x61,
	0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x70,
	0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x2d, 0x0a, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x05,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x49, 0x0a, 0x0f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x74,
	0x6f, 0x70, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x54,
	0x6f, 0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x52,
	0x0f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x70, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x30, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x22, 0x98, 0x01, 0x0a, 0x12, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xe1, 0x01,
	0x0a, 0x11, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c,
	0x69, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x48, 0x0a, 0x10, 0x73, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x10, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x73, 0x22, 0xa8, 0x01, 0x0a, 0x13, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x45, 0x64, 0x69,
	0x74, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x47, 0x0a, 0x10, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x10, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x65, 0x73, 0x12, 0x48, 0x0a, 0x10, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x22, 0x43, 0x0a, 0x05,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c,
	0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e,
	0x65, 0x22, 0x96, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x66, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x95, 0x01, 0x0a, 0x0b, 0x52,
	0x65, 0x66, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x6c, 0x69, 0x6e, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x22, 0xb3, 0x03, 0x0a, 0x15, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x45, 0x64,
	0x69, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x5f, 0x6c,
	0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x73, 0x6f,
	0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x33, 0x0a, 0x0c, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0c, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x35, 0x0a, 0x0d, 0x76,
	0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x72, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x35, 0x0a, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x45, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x52, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x2e, 0x0a, 0x07, 0x63, 0x61, 0x6c,
	0x6c, 0x65, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x0b, 0x72, 0x65, 0x66,
	0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x52, 0x65, 0x66, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x5f, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x2e, 0x52, 0x65, 0x66, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x72,
	0x65, 0x66, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0x88, 0x01, 0x0a, 0x09, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x69,
	0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x69, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x69, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0xe1, 0x02, 0x0a, 0x18, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x47, 0x0a, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x64, 0x69, 0x74,
	0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x45, 0x64, 0x69, 0x74, 0x6f,
	0x72, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x3c, 0x0a, 0x09, 0x76, 0x61, 0x72,
	0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x15, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x52, 0x0a, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x6a, 0x73, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x32, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6a, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x65, 0x72, 0x72, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x33, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x22, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x25, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x38, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x42, 0x75,
	0x69, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x73, 0x22, 0x6a, 0x0a, 0x13, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x75, 0x69,
	0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x2e, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x69, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x69, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xd8, 0x02,
	0x0a, 0x12, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e,
	0x42, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x2e, 0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12,
	0x2c, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x2a, 0x0a,
	0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x66, 0x69, 0x6c,
	0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0c, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x48,
	0x0a, 0x10, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x75, 0x69, 0x6c,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x82, 0x02, 0x0a, 0x16, 0x47, 0x65, 0x74,
	0x42, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x47,
	0x65, 0x74, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x1a, 0x57, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x41, 0x0a,
	0x1e, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x73,
	0x22, 0x96, 0x01, 0x0a, 0x1b, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x42, 0x75, 0x69, 0x6c,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x48, 0x0a, 0x10, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x6a, 0x0a, 0x13, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x42, 0x75, 0x69, 0x6c,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x72, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x69, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xbe, 0x02, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x0f, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x47, 0x65,
	0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x65,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x1a, 0x68, 0x0a, 0x13,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x33, 0x0a, 0x17, 0x49, 0x73, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x7f, 0x0a, 0x18, 0x49,
	0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x64, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x65,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x33, 0x0a, 0x12,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x22, 0x7a, 0x0a, 0x13, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x65, 0x64, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x75, 0x0a,
	0x15, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x22, 0x5a, 0x0a, 0x1a, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c, 0x61, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x73,
	0x22, 0x9b, 0x01, 0x0a, 0x16, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x25, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x36, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x44,
	0x0a, 0x14, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x73, 0x0a, 0x06, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x22, 0xce, 0x01, 0x0a, 0x12, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x43, 0x4b, 0x47, 0x44, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x2b, 0x0a, 0x11, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x79, 0x6d, 0x62,
	0x6f, 0x6c, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c,
	0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x5f, 0x72, 0x65, 0x67, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x52, 0x65, 0x67, 0x65, 0x78, 0x12,
	0x2a, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4e, 0x75, 0x6d, 0x22, 0xe8, 0x01, 0x0a, 0x13, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x4b, 0x47, 0x44, 0x42, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x30, 0x0a, 0x09, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x09, 0x76, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x2e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0a, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6a, 0x73, 0x6f,
	0x6e, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x32, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6a, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x7e, 0x0a, 0x0b, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x6e, 0x69,
	0x70, 0x70, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x69, 0x6e, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xa2, 0x03, 0x0a, 0x1e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x76, 0x61, 0x6e, 0x74, 0x53, 0x6e, 0x69, 0x70, 0x70, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65,
	0x72, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x12,
	0x25, 0x0a, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x65,
	0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x6e,
	0x69, 0x70, 0x70, 0x65, 0x74, 0x52, 0x0d, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x43,
	0x6f, 0x64, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x45,
	0x64, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x45, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x64, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x65, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x9f, 0x02, 0x0a, 0x07, 0x53,
	0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53,
	0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x35, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x65, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x2e, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65,
	0x63, 0x61, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6b, 0x67, 0x5f,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x66, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x6b, 0x67, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x22, 0x85, 0x04, 0x0a,
	0x1f, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x76, 0x61, 0x6e,
	0x74, 0x53, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x45, 0x0a, 0x15, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x62, 0x79, 0x5f,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53, 0x6e, 0x69, 0x70, 0x70,
	0x65, 0x74, 0x52, 0x13, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x42, 0x79, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x12, 0x73, 0x6e, 0x69, 0x70, 0x70,
	0x65, 0x74, 0x73, 0x5f, 0x62, 0x79, 0x5f, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53,
	0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x52, 0x10, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73,
	0x42, 0x79, 0x46, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x10, 0x73, 0x6e, 0x69, 0x70,
	0x70, 0x65, 0x74, 0x73, 0x5f, 0x62, 0x79, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53, 0x6e,
	0x69, 0x70, 0x70, 0x65, 0x74, 0x52, 0x0e, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x42,
	0x79, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x3b, 0x0a, 0x10, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74,
	0x73, 0x5f, 0x62, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53, 0x6e, 0x69, 0x70, 0x70,
	0x65, 0x74, 0x52, 0x0e, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x42, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x4a, 0x0a, 0x18, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x62,
	0x79, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e,
	0x53, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x52, 0x15, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74,
	0x73, 0x42, 0x79, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x49,
	0x0a, 0x17, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x62, 0x79, 0x5f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53, 0x6e, 0x69, 0x70, 0x70,
	0x65, 0x74, 0x52, 0x15, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x42, 0x79, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x22, 0x99, 0x03, 0x0a, 0x14, 0x52, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x53,
	0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x45, 0x0a,
	0x15, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x62, 0x79, 0x5f, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x52,
	0x13, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x12, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73,
	0x5f, 0x62, 0x79, 0x5f, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53, 0x6e, 0x69, 0x70,
	0x70, 0x65, 0x74, 0x52, 0x10, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x42, 0x79, 0x46,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x10, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74,
	0x73, 0x5f, 0x62, 0x79, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53, 0x6e, 0x69, 0x70, 0x70,
	0x65, 0x74, 0x52, 0x0e, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x42, 0x79, 0x46, 0x69,
	0x6c, 0x65, 0x12, 0x3b, 0x0a, 0x10, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x62,
	0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x52,
	0x0e, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x4a, 0x0a, 0x18, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x62, 0x79, 0x5f, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53, 0x6e, 0x69,
	0x70, 0x70, 0x65, 0x74, 0x52, 0x15, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x42, 0x79,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x64, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x65, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x22, 0x55, 0x0a, 0x10, 0x53, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x57, 0x69, 0x74, 0x68, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x2b, 0x0a, 0x07, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x2e, 0x53, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x52, 0x07, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x9a, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x72, 0x61,
	0x6e, 0x6b, 0x53, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x36, 0x0a, 0x08, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53,
	0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x57, 0x69, 0x74, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52,
	0x08, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x73, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x22, 0x8d, 0x01, 0x0a, 0x11, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x4d,
	0x6f, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6c, 0x69, 0x6e,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x45, 0x0a, 0x2a, 0x49, 0x73, 0x43, 0x4b, 0x47, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x4e, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x53, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xb1, 0x01, 0x0a, 0x2b,
	0x49, 0x73, 0x43, 0x4b, 0x47, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x4e,
	0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x63, 0x65, 0x6e, 0x61,
	0x72, 0x69, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x64, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22,
	0x9f, 0x02, 0x0a, 0x0c, 0x53, 0x65, 0x74, 0x55, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x68, 0x6f, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x70,
	0x75, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x70, 0x75, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x61, 0x6e,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x22, 0x5a, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x55, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2a, 0x83, 0x01,
	0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65,
	0x64, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x70, 0x61, 0x6e, 0x69, 0x63, 0x10,
	0x64, 0x12, 0x16, 0x0a, 0x12, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x69, 0x73, 0x5f,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x10, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x65, 0x78, 0x63, 0x65, 0x65, 0x64, 0x10, 0x66,
	0x12, 0x11, 0x0a, 0x0d, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x10, 0x67, 0x12, 0x0e, 0x0a, 0x0a, 0x6e, 0x69, 0x6c, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x10, 0x68, 0x2a, 0x43, 0x0a, 0x0a, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0a, 0x0a, 0x06, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x10, 0x00, 0x12, 0x08, 0x0a,
	0x04, 0x66, 0x69, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x10, 0x03, 0x12, 0x08,
	0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x10, 0x04, 0x2a, 0x35, 0x0a, 0x0b, 0x42, 0x75, 0x69, 0x6c,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0c, 0x0a, 0x08, 0x62, 0x75, 0x69, 0x6c, 0x64,
	0x69, 0x6e, 0x67, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65,
	0x64, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x02, 0x2a,
	0xb6, 0x01, 0x0a, 0x0a, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e,
	0x0a, 0x1a, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x19,
	0x0a, 0x15, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x65, 0x6d,
	0x62, 0x65, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x72, 0x65, 0x63,
	0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x65, 0x72, 0x10, 0x02, 0x12, 0x2d,
	0x0a, 0x29, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x10, 0x14, 0x12, 0x29, 0x0a,
	0x25, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x79, 0x5f, 0x67, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x6c,
	0x65, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x10, 0x1e, 0x2a, 0x59, 0x0a, 0x0b, 0x53, 0x6e, 0x69, 0x70,
	0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x73, 0x6e, 0x69, 0x70, 0x70,
	0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x10, 0x00, 0x12, 0x1c,
	0x0a, 0x18, 0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x66,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x72, 0x65, 0x65, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11,
	0x73, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x66, 0x69, 0x6c,
	0x65, 0x10, 0x02, 0x32, 0xa3, 0x0f, 0x0a, 0x06, 0x43, 0x6f, 0x64, 0x65, 0x4b, 0x47, 0x12, 0x2a,
	0x0a, 0x04, 0x50, 0x69, 0x6e, 0x67, 0x12, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x3a, 0x0a, 0x05, 0x53, 0x65,
	0x74, 0x55, 0x70, 0x12, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53,
	0x65, 0x74, 0x55, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53, 0x65, 0x74, 0x55, 0x70, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x37, 0x0a, 0x04, 0x49, 0x6e, 0x69, 0x74, 0x12, 0x15,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x2e, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x64, 0x0a, 0x13, 0x49, 0x6e, 0x69, 0x74, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x24, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x56, 0x69, 0x72, 0x74,
	0x75, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x0e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x49, 0x0a, 0x0e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x0e, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x19, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x0e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x12, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x3c, 0x0a, 0x0a, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x4d, 0x6f, 0x76, 0x65, 0x12,
	0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x43, 0x75, 0x72, 0x73, 0x6f,
	0x72, 0x4d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12,
	0x55, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x47, 0x65, 0x74,
	0x42, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x47, 0x65,
	0x74, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x28, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x47, 0x65, 0x74,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4c, 0x0a, 0x0b, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4c, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x10, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x55, 0x0a, 0x0e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x12, 0x1f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x52,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e,
	0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x17, 0x52, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x76, 0x61, 0x6e, 0x74, 0x53, 0x6e, 0x69, 0x70,
	0x70, 0x65, 0x74, 0x12, 0x28, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x52,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x76, 0x61, 0x6e, 0x74, 0x53,
	0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x65, 0x52, 0x65, 0x6c, 0x65, 0x76, 0x61, 0x6e, 0x74, 0x53, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x52, 0x0a, 0x0d, 0x52, 0x65,
	0x72, 0x61, 0x6e, 0x6b, 0x53, 0x6e, 0x69, 0x70, 0x70, 0x65, 0x74, 0x12, 0x1e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x52, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x53, 0x6e, 0x69,
	0x70, 0x70, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x52, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x53, 0x6e, 0x69,
	0x70, 0x70, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4f,
	0x0a, 0x0c, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73,
	0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x5b, 0x0a, 0x10, 0x49, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x64, 0x12, 0x21, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x49,
	0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x2e, 0x49, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x55, 0x0a, 0x0e,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x12, 0x1f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x5f, 0x0a, 0x13, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x49, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x12, 0x24, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x20, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x49, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x4c, 0x0a, 0x0b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x4b,
	0x47, 0x44, 0x42, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x4b, 0x47, 0x44, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x43, 0x4b, 0x47, 0x44, 0x42, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x94, 0x01, 0x0a, 0x23, 0x49, 0x73, 0x43, 0x4b, 0x47, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x4e, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x53, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x12, 0x34, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x49, 0x73, 0x43, 0x4b, 0x47, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x46, 0x6f, 0x72, 0x4e, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x53, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x35, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2e, 0x49, 0x73, 0x43, 0x4b,
	0x47, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x4e, 0x6f, 0x6e, 0x57, 0x6f,
	0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x0c, 0x5a, 0x0a, 0x2e, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_codekg_proto_rawDescOnce sync.Once
	file_codekg_proto_rawDescData = file_codekg_proto_rawDesc
)

func file_codekg_proto_rawDescGZIP() []byte {
	file_codekg_proto_rawDescOnce.Do(func() {
		file_codekg_proto_rawDescData = protoimpl.X.CompressGZIP(file_codekg_proto_rawDescData)
	})
	return file_codekg_proto_rawDescData
}

var file_codekg_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_codekg_proto_msgTypes = make([]protoimpl.MessageInfo, 75)
var file_codekg_proto_goTypes = []interface{}{
	(Code)(0),                                           // 0: protocol.Code
	(EntityType)(0),                                     // 1: protocol.EntityType
	(BuildStatus)(0),                                    // 2: protocol.BuildStatus
	(RecallType)(0),                                     // 3: protocol.RecallType
	(SnippetType)(0),                                    // 4: protocol.SnippetType
	(*Empty)(nil),                                       // 5: protocol.Empty
	(*Error)(nil),                                       // 6: protocol.Error
	(*RefreshTokenRequest)(nil),                         // 7: protocol.RefreshTokenRequest
	(*RefreshTokenResponse)(nil),                        // 8: protocol.RefreshTokenResponse
	(*Project)(nil),                                     // 9: protocol.Project
	(*InitRequest)(nil),                                 // 10: protocol.InitRequest
	(*InitResponse)(nil),                                // 11: protocol.InitResponse
	(*VirtualProject)(nil),                              // 12: protocol.VirtualProject
	(*InitVirtualProjectsRequest)(nil),                  // 13: protocol.InitVirtualProjectsRequest
	(*InitVirtualProjectsResponse)(nil),                 // 14: protocol.InitVirtualProjectsResponse
	(*Document)(nil),                                    // 15: protocol.Document
	(*DocumentRequest)(nil),                             // 16: protocol.DocumentRequest
	(*DocumentResponse)(nil),                            // 17: protocol.DocumentResponse
	(*DeleteIndexRequest)(nil),                          // 18: protocol.DeleteIndexRequest
	(*DeleteIndexResponse)(nil),                         // 19: protocol.DeleteIndexResponse
	(*CurrentEditorInfo)(nil),                           // 20: protocol.CurrentEditorInfo
	(*RetrieveEntityRequest)(nil),                       // 21: protocol.RetrieveEntityRequest
	(*Entity)(nil),                                      // 22: protocol.Entity
	(*RetrieveEntityResponse)(nil),                      // 23: protocol.RetrieveEntityResponse
	(*RetrieveRelationRequest)(nil),                     // 24: protocol.RetrieveRelationRequest
	(*CodeChunkVariable)(nil),                           // 25: protocol.CodeChunkVariable
	(*TextVariable)(nil),                                // 26: protocol.TextVariable
	(*UsefulFileInfo)(nil),                              // 27: protocol.UsefulFileInfo
	(*FolderVariable)(nil),                              // 28: protocol.FolderVariable
	(*FileVariable)(nil),                                // 29: protocol.FileVariable
	(*FileTopLevelVariable)(nil),                        // 30: protocol.FileTopLevelVariable
	(*Member)(nil),                                      // 31: protocol.Member
	(*ClassVariable)(nil),                               // 32: protocol.ClassVariable
	(*ClassTopLevelVariable)(nil),                       // 33: protocol.ClassTopLevelVariable
	(*MethodInfo)(nil),                                  // 34: protocol.MethodInfo
	(*MethodVariable)(nil),                              // 35: protocol.MethodVariable
	(*Variable)(nil),                                    // 36: protocol.Variable
	(*SelectedMethodInfo)(nil),                          // 37: protocol.SelectedMethodInfo
	(*SelectedClassInfo)(nil),                           // 38: protocol.SelectedClassInfo
	(*CurrentEditorEntity)(nil),                         // 39: protocol.CurrentEditorEntity
	(*Range)(nil),                                       // 40: protocol.Range
	(*RefClassInfo)(nil),                                // 41: protocol.RefClassInfo
	(*RefTypeInfo)(nil),                                 // 42: protocol.RefTypeInfo
	(*CurrentEditorVariable)(nil),                       // 43: protocol.CurrentEditorVariable
	(*Reference)(nil),                                   // 44: protocol.Reference
	(*RetrieveRelationResponse)(nil),                    // 45: protocol.RetrieveRelationResponse
	(*GetBuildStatusRequest)(nil),                       // 46: protocol.GetBuildStatusRequest
	(*DocumentBuildStatus)(nil),                         // 47: protocol.DocumentBuildStatus
	(*ProjectBuildStatus)(nil),                          // 48: protocol.ProjectBuildStatus
	(*GetBuildStatusResponse)(nil),                      // 49: protocol.GetBuildStatusResponse
	(*GetDocumentsIndexStatusRequest)(nil),              // 50: protocol.GetDocumentsIndexStatusRequest
	(*ProjectDocumentsIndexStatus)(nil),                 // 51: protocol.ProjectDocumentsIndexStatus
	(*DocumentIndexStatus)(nil),                         // 52: protocol.DocumentIndexStatus
	(*GetDocumentsIndexStatusResponse)(nil),             // 53: protocol.GetDocumentsIndexStatusResponse
	(*IsVersionMatchedRequest)(nil),                     // 54: protocol.IsVersionMatchedRequest
	(*IsVersionMatchedResponse)(nil),                    // 55: protocol.IsVersionMatchedResponse
	(*CancelIndexRequest)(nil),                          // 56: protocol.CancelIndexRequest
	(*CancelIndexResponse)(nil),                         // 57: protocol.CancelIndexResponse
	(*ImportAnalysisRequest)(nil),                       // 58: protocol.ImportAnalysisRequest
	(*FilesImportAnalysisRequest)(nil),                  // 59: protocol.FilesImportAnalysisRequest
	(*ImportAnalysisResponse)(nil),                      // 60: protocol.ImportAnalysisResponse
	(*ImportAnalysisResult)(nil),                        // 61: protocol.ImportAnalysisResult
	(*Symbol)(nil),                                      // 62: protocol.Symbol
	(*SearchCKGDBRequest)(nil),                          // 63: protocol.SearchCKGDBRequest
	(*SearchCKGDBResponse)(nil),                         // 64: protocol.SearchCKGDBResponse
	(*CodeSnippet)(nil),                                 // 65: protocol.CodeSnippet
	(*RetrieveRelevantSnippetRequest)(nil),              // 66: protocol.RetrieveRelevantSnippetRequest
	(*Snippet)(nil),                                     // 67: protocol.Snippet
	(*RetrieveRelevantSnippetResponse)(nil),             // 68: protocol.RetrieveRelevantSnippetResponse
	(*RerankSnippetRequest)(nil),                        // 69: protocol.RerankSnippetRequest
	(*SnippetWithScore)(nil),                            // 70: protocol.SnippetWithScore
	(*RerankSnippetResponse)(nil),                       // 71: protocol.RerankSnippetResponse
	(*CursorMoveRequest)(nil),                           // 72: protocol.CursorMoveRequest
	(*IsCKGEnabledForNonWorkspaceScenarioRequest)(nil),  // 73: protocol.IsCKGEnabledForNonWorkspaceScenarioRequest
	(*IsCKGEnabledForNonWorkspaceScenarioResponse)(nil), // 74: protocol.IsCKGEnabledForNonWorkspaceScenarioResponse
	(*SetUpRequest)(nil),                                // 75: protocol.SetUpRequest
	(*SetUpResponse)(nil),                               // 76: protocol.SetUpResponse
	nil,                                                 // 77: protocol.DocumentResponse.ErrMsgsEntry
	nil,                                                 // 78: protocol.GetBuildStatusResponse.StatusEntry
	nil,                                                 // 79: protocol.GetDocumentsIndexStatusResponse.ProjectsStatusEntry
}
var file_codekg_proto_depIdxs = []int32{
	0,   // 0: protocol.RefreshTokenResponse.code:type_name -> protocol.Code
	6,   // 1: protocol.RefreshTokenResponse.error:type_name -> protocol.Error
	9,   // 2: protocol.InitRequest.projects:type_name -> protocol.Project
	0,   // 3: protocol.InitResponse.code:type_name -> protocol.Code
	6,   // 4: protocol.InitResponse.error:type_name -> protocol.Error
	12,  // 5: protocol.InitVirtualProjectsRequest.projects:type_name -> protocol.VirtualProject
	0,   // 6: protocol.InitVirtualProjectsResponse.code:type_name -> protocol.Code
	6,   // 7: protocol.InitVirtualProjectsResponse.error:type_name -> protocol.Error
	15,  // 8: protocol.DocumentRequest.documents:type_name -> protocol.Document
	77,  // 9: protocol.DocumentResponse.err_msgs:type_name -> protocol.DocumentResponse.ErrMsgsEntry
	0,   // 10: protocol.DocumentResponse.code:type_name -> protocol.Code
	6,   // 11: protocol.DocumentResponse.error:type_name -> protocol.Error
	0,   // 12: protocol.DeleteIndexResponse.code:type_name -> protocol.Code
	6,   // 13: protocol.DeleteIndexResponse.error:type_name -> protocol.Error
	40,  // 14: protocol.CurrentEditorInfo.select_code_range:type_name -> protocol.Range
	40,  // 15: protocol.CurrentEditorInfo.visible_code_range:type_name -> protocol.Range
	20,  // 16: protocol.RetrieveEntityRequest.editor_info:type_name -> protocol.CurrentEditorInfo
	1,   // 17: protocol.RetrieveEntityRequest.expect_entity_types:type_name -> protocol.EntityType
	22,  // 18: protocol.RetrieveEntityResponse.entities_by_user_message:type_name -> protocol.Entity
	0,   // 19: protocol.RetrieveEntityResponse.code:type_name -> protocol.Code
	6,   // 20: protocol.RetrieveEntityResponse.error:type_name -> protocol.Error
	22,  // 21: protocol.RetrieveRelationRequest.entities_by_user_message:type_name -> protocol.Entity
	20,  // 22: protocol.RetrieveRelationRequest.editor_info:type_name -> protocol.CurrentEditorInfo
	27,  // 23: protocol.FolderVariable.useful_files:type_name -> protocol.UsefulFileInfo
	31,  // 24: protocol.ClassVariable.members:type_name -> protocol.Member
	34,  // 25: protocol.ClassVariable.methods:type_name -> protocol.MethodInfo
	34,  // 26: protocol.ClassVariable.test_functions:type_name -> protocol.MethodInfo
	34,  // 27: protocol.MethodVariable.callers:type_name -> protocol.MethodInfo
	34,  // 28: protocol.MethodVariable.callees:type_name -> protocol.MethodInfo
	34,  // 29: protocol.MethodVariable.test_functions:type_name -> protocol.MethodInfo
	25,  // 30: protocol.Variable.code_chunk:type_name -> protocol.CodeChunkVariable
	26,  // 31: protocol.Variable.text:type_name -> protocol.TextVariable
	28,  // 32: protocol.Variable.folder:type_name -> protocol.FolderVariable
	29,  // 33: protocol.Variable.file:type_name -> protocol.FileVariable
	30,  // 34: protocol.Variable.file_top_level:type_name -> protocol.FileTopLevelVariable
	32,  // 35: protocol.Variable.class:type_name -> protocol.ClassVariable
	33,  // 36: protocol.Variable.class_top_level:type_name -> protocol.ClassTopLevelVariable
	35,  // 37: protocol.Variable.method:type_name -> protocol.MethodVariable
	37,  // 38: protocol.SelectedClassInfo.selected_methods:type_name -> protocol.SelectedMethodInfo
	38,  // 39: protocol.CurrentEditorEntity.selected_classes:type_name -> protocol.SelectedClassInfo
	37,  // 40: protocol.CurrentEditorEntity.selected_methods:type_name -> protocol.SelectedMethodInfo
	40,  // 41: protocol.CurrentEditorVariable.select_range:type_name -> protocol.Range
	40,  // 42: protocol.CurrentEditorVariable.visible_range:type_name -> protocol.Range
	39,  // 43: protocol.CurrentEditorVariable.entity:type_name -> protocol.CurrentEditorEntity
	34,  // 44: protocol.CurrentEditorVariable.callees:type_name -> protocol.MethodInfo
	41,  // 45: protocol.CurrentEditorVariable.ref_classes:type_name -> protocol.RefClassInfo
	42,  // 46: protocol.CurrentEditorVariable.ref_types:type_name -> protocol.RefTypeInfo
	43,  // 47: protocol.RetrieveRelationResponse.current_editor:type_name -> protocol.CurrentEditorVariable
	36,  // 48: protocol.RetrieveRelationResponse.variables:type_name -> protocol.Variable
	44,  // 49: protocol.RetrieveRelationResponse.references:type_name -> protocol.Reference
	0,   // 50: protocol.RetrieveRelationResponse.code:type_name -> protocol.Code
	6,   // 51: protocol.RetrieveRelationResponse.error:type_name -> protocol.Error
	2,   // 52: protocol.DocumentBuildStatus.status:type_name -> protocol.BuildStatus
	2,   // 53: protocol.ProjectBuildStatus.status:type_name -> protocol.BuildStatus
	47,  // 54: protocol.ProjectBuildStatus.documents_status:type_name -> protocol.DocumentBuildStatus
	78,  // 55: protocol.GetBuildStatusResponse.status:type_name -> protocol.GetBuildStatusResponse.StatusEntry
	0,   // 56: protocol.GetBuildStatusResponse.code:type_name -> protocol.Code
	6,   // 57: protocol.GetBuildStatusResponse.error:type_name -> protocol.Error
	2,   // 58: protocol.ProjectDocumentsIndexStatus.status:type_name -> protocol.BuildStatus
	52,  // 59: protocol.ProjectDocumentsIndexStatus.documents_status:type_name -> protocol.DocumentIndexStatus
	2,   // 60: protocol.DocumentIndexStatus.status:type_name -> protocol.BuildStatus
	79,  // 61: protocol.GetDocumentsIndexStatusResponse.projects_status:type_name -> protocol.GetDocumentsIndexStatusResponse.ProjectsStatusEntry
	0,   // 62: protocol.GetDocumentsIndexStatusResponse.code:type_name -> protocol.Code
	6,   // 63: protocol.GetDocumentsIndexStatusResponse.error:type_name -> protocol.Error
	0,   // 64: protocol.IsVersionMatchedResponse.code:type_name -> protocol.Code
	6,   // 65: protocol.IsVersionMatchedResponse.error:type_name -> protocol.Error
	0,   // 66: protocol.CancelIndexResponse.code:type_name -> protocol.Code
	6,   // 67: protocol.CancelIndexResponse.error:type_name -> protocol.Error
	0,   // 68: protocol.ImportAnalysisResponse.code:type_name -> protocol.Code
	6,   // 69: protocol.ImportAnalysisResponse.error:type_name -> protocol.Error
	61,  // 70: protocol.ImportAnalysisResponse.result:type_name -> protocol.ImportAnalysisResult
	1,   // 71: protocol.SearchCKGDBRequest.types:type_name -> protocol.EntityType
	36,  // 72: protocol.SearchCKGDBResponse.variables:type_name -> protocol.Variable
	44,  // 73: protocol.SearchCKGDBResponse.references:type_name -> protocol.Reference
	0,   // 74: protocol.SearchCKGDBResponse.code:type_name -> protocol.Code
	6,   // 75: protocol.SearchCKGDBResponse.error:type_name -> protocol.Error
	65,  // 76: protocol.RetrieveRelevantSnippetRequest.selected_codes:type_name -> protocol.CodeSnippet
	20,  // 77: protocol.RetrieveRelevantSnippetRequest.current_editor:type_name -> protocol.CurrentEditorInfo
	4,   // 78: protocol.Snippet.type:type_name -> protocol.SnippetType
	3,   // 79: protocol.Snippet.recall_type:type_name -> protocol.RecallType
	67,  // 80: protocol.RetrieveRelevantSnippetResponse.snippets_by_workspace:type_name -> protocol.Snippet
	67,  // 81: protocol.RetrieveRelevantSnippetResponse.snippets_by_folder:type_name -> protocol.Snippet
	67,  // 82: protocol.RetrieveRelevantSnippetResponse.snippets_by_file:type_name -> protocol.Snippet
	67,  // 83: protocol.RetrieveRelevantSnippetResponse.snippets_by_code:type_name -> protocol.Snippet
	67,  // 84: protocol.RetrieveRelevantSnippetResponse.snippets_by_current_file:type_name -> protocol.Snippet
	67,  // 85: protocol.RetrieveRelevantSnippetResponse.snippets_by_interaction:type_name -> protocol.Snippet
	0,   // 86: protocol.RetrieveRelevantSnippetResponse.code:type_name -> protocol.Code
	6,   // 87: protocol.RetrieveRelevantSnippetResponse.error:type_name -> protocol.Error
	67,  // 88: protocol.RerankSnippetRequest.snippets_by_workspace:type_name -> protocol.Snippet
	67,  // 89: protocol.RerankSnippetRequest.snippets_by_folder:type_name -> protocol.Snippet
	67,  // 90: protocol.RerankSnippetRequest.snippets_by_file:type_name -> protocol.Snippet
	67,  // 91: protocol.RerankSnippetRequest.snippets_by_code:type_name -> protocol.Snippet
	67,  // 92: protocol.RerankSnippetRequest.snippets_by_current_file:type_name -> protocol.Snippet
	67,  // 93: protocol.SnippetWithScore.snippet:type_name -> protocol.Snippet
	70,  // 94: protocol.RerankSnippetResponse.snippets:type_name -> protocol.SnippetWithScore
	0,   // 95: protocol.RerankSnippetResponse.code:type_name -> protocol.Code
	6,   // 96: protocol.RerankSnippetResponse.error:type_name -> protocol.Error
	0,   // 97: protocol.IsCKGEnabledForNonWorkspaceScenarioResponse.code:type_name -> protocol.Code
	6,   // 98: protocol.IsCKGEnabledForNonWorkspaceScenarioResponse.error:type_name -> protocol.Error
	0,   // 99: protocol.SetUpResponse.code:type_name -> protocol.Code
	6,   // 100: protocol.SetUpResponse.error:type_name -> protocol.Error
	48,  // 101: protocol.GetBuildStatusResponse.StatusEntry.value:type_name -> protocol.ProjectBuildStatus
	51,  // 102: protocol.GetDocumentsIndexStatusResponse.ProjectsStatusEntry.value:type_name -> protocol.ProjectDocumentsIndexStatus
	5,   // 103: protocol.CodeKG.Ping:input_type -> protocol.Empty
	75,  // 104: protocol.CodeKG.SetUp:input_type -> protocol.SetUpRequest
	10,  // 105: protocol.CodeKG.Init:input_type -> protocol.InitRequest
	13,  // 106: protocol.CodeKG.InitVirtualProjects:input_type -> protocol.InitVirtualProjectsRequest
	16,  // 107: protocol.CodeKG.DocumentCreate:input_type -> protocol.DocumentRequest
	16,  // 108: protocol.CodeKG.DocumentChange:input_type -> protocol.DocumentRequest
	16,  // 109: protocol.CodeKG.DocumentDelete:input_type -> protocol.DocumentRequest
	16,  // 110: protocol.CodeKG.DocumentSelect:input_type -> protocol.DocumentRequest
	72,  // 111: protocol.CodeKG.CursorMove:input_type -> protocol.CursorMoveRequest
	46,  // 112: protocol.CodeKG.GetBuildStatus:input_type -> protocol.GetBuildStatusRequest
	50,  // 113: protocol.CodeKG.GetDocumentsIndexStatus:input_type -> protocol.GetDocumentsIndexStatusRequest
	56,  // 114: protocol.CodeKG.CancelIndex:input_type -> protocol.CancelIndexRequest
	18,  // 115: protocol.CodeKG.DeleteIndex:input_type -> protocol.DeleteIndexRequest
	24,  // 116: protocol.CodeKG.RetrieveRelation:input_type -> protocol.RetrieveRelationRequest
	21,  // 117: protocol.CodeKG.RetrieveEntity:input_type -> protocol.RetrieveEntityRequest
	66,  // 118: protocol.CodeKG.RetrieveRelevantSnippet:input_type -> protocol.RetrieveRelevantSnippetRequest
	69,  // 119: protocol.CodeKG.RerankSnippet:input_type -> protocol.RerankSnippetRequest
	7,   // 120: protocol.CodeKG.RefreshToken:input_type -> protocol.RefreshTokenRequest
	54,  // 121: protocol.CodeKG.IsVersionMatched:input_type -> protocol.IsVersionMatchedRequest
	58,  // 122: protocol.CodeKG.ImportAnalysis:input_type -> protocol.ImportAnalysisRequest
	59,  // 123: protocol.CodeKG.FilesImportAnalysis:input_type -> protocol.FilesImportAnalysisRequest
	63,  // 124: protocol.CodeKG.SearchCKGDB:input_type -> protocol.SearchCKGDBRequest
	73,  // 125: protocol.CodeKG.IsCKGEnabledForNonWorkspaceScenario:input_type -> protocol.IsCKGEnabledForNonWorkspaceScenarioRequest
	5,   // 126: protocol.CodeKG.Ping:output_type -> protocol.Empty
	76,  // 127: protocol.CodeKG.SetUp:output_type -> protocol.SetUpResponse
	11,  // 128: protocol.CodeKG.Init:output_type -> protocol.InitResponse
	14,  // 129: protocol.CodeKG.InitVirtualProjects:output_type -> protocol.InitVirtualProjectsResponse
	17,  // 130: protocol.CodeKG.DocumentCreate:output_type -> protocol.DocumentResponse
	17,  // 131: protocol.CodeKG.DocumentChange:output_type -> protocol.DocumentResponse
	17,  // 132: protocol.CodeKG.DocumentDelete:output_type -> protocol.DocumentResponse
	17,  // 133: protocol.CodeKG.DocumentSelect:output_type -> protocol.DocumentResponse
	5,   // 134: protocol.CodeKG.CursorMove:output_type -> protocol.Empty
	49,  // 135: protocol.CodeKG.GetBuildStatus:output_type -> protocol.GetBuildStatusResponse
	53,  // 136: protocol.CodeKG.GetDocumentsIndexStatus:output_type -> protocol.GetDocumentsIndexStatusResponse
	57,  // 137: protocol.CodeKG.CancelIndex:output_type -> protocol.CancelIndexResponse
	19,  // 138: protocol.CodeKG.DeleteIndex:output_type -> protocol.DeleteIndexResponse
	45,  // 139: protocol.CodeKG.RetrieveRelation:output_type -> protocol.RetrieveRelationResponse
	23,  // 140: protocol.CodeKG.RetrieveEntity:output_type -> protocol.RetrieveEntityResponse
	68,  // 141: protocol.CodeKG.RetrieveRelevantSnippet:output_type -> protocol.RetrieveRelevantSnippetResponse
	71,  // 142: protocol.CodeKG.RerankSnippet:output_type -> protocol.RerankSnippetResponse
	8,   // 143: protocol.CodeKG.RefreshToken:output_type -> protocol.RefreshTokenResponse
	55,  // 144: protocol.CodeKG.IsVersionMatched:output_type -> protocol.IsVersionMatchedResponse
	60,  // 145: protocol.CodeKG.ImportAnalysis:output_type -> protocol.ImportAnalysisResponse
	60,  // 146: protocol.CodeKG.FilesImportAnalysis:output_type -> protocol.ImportAnalysisResponse
	64,  // 147: protocol.CodeKG.SearchCKGDB:output_type -> protocol.SearchCKGDBResponse
	74,  // 148: protocol.CodeKG.IsCKGEnabledForNonWorkspaceScenario:output_type -> protocol.IsCKGEnabledForNonWorkspaceScenarioResponse
	126, // [126:149] is the sub-list for method output_type
	103, // [103:126] is the sub-list for method input_type
	103, // [103:103] is the sub-list for extension type_name
	103, // [103:103] is the sub-list for extension extendee
	0,   // [0:103] is the sub-list for field type_name
}

func init() { file_codekg_proto_init() }
func file_codekg_proto_init() {
	if File_codekg_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_codekg_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Error); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Project); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VirtualProject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitVirtualProjectsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitVirtualProjectsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Document); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocumentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocumentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteIndexRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteIndexResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CurrentEditorInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveEntityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Entity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveEntityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveRelationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CodeChunkVariable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextVariable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsefulFileInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FolderVariable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileVariable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FileTopLevelVariable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Member); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClassVariable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClassTopLevelVariable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MethodInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MethodVariable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Variable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SelectedMethodInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SelectedClassInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CurrentEditorEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Range); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefClassInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefTypeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CurrentEditorVariable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reference); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveRelationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBuildStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocumentBuildStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectBuildStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBuildStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDocumentsIndexStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectDocumentsIndexStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DocumentIndexStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDocumentsIndexStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsVersionMatchedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsVersionMatchedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelIndexRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelIndexResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportAnalysisRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilesImportAnalysisRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportAnalysisResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportAnalysisResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Symbol); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCKGDBRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCKGDBResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CodeSnippet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveRelevantSnippetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Snippet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveRelevantSnippetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RerankSnippetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnippetWithScore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RerankSnippetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CursorMoveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsCKGEnabledForNonWorkspaceScenarioRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsCKGEnabledForNonWorkspaceScenarioResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetUpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_codekg_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetUpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_codekg_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   75,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_codekg_proto_goTypes,
		DependencyIndexes: file_codekg_proto_depIdxs,
		EnumInfos:         file_codekg_proto_enumTypes,
		MessageInfos:      file_codekg_proto_msgTypes,
	}.Build()
	File_codekg_proto = out.File
	file_codekg_proto_rawDesc = nil
	file_codekg_proto_goTypes = nil
	file_codekg_proto_depIdxs = nil
}
