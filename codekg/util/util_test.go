package util

import (
	"runtime"
	"testing"
)

func TestGetCanonicalURIFromURIOrPath(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
		windows  bool // 是否仅在Windows下运行此测试
	}{
		{
			name:     "Already a file URI",
			input:    "file:///path/to/file",
			expected: "file:///path/to/file",
			windows:  false,
		},
		{
			name:     "Unix path",
			input:    "/path/to/file",
			expected: "file:///path/to/file",
			windows:  false,
		},
		{
			name:     "Windows path with drive letter",
			input:    "C:\\Users\\<USER>\\File.txt",
			expected: "file:///C%3A%5CUsers%5CAdmin%5CFile.txt",
			windows:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Skip tests that are Windows-specific if not on Windows
			if tt.windows && runtime.GOOS != "windows" {
				t.Skip("Skipping Windows-specific test on non-Windows platform")
			}

			result := GetCanonicalURIFromURIOrPath(tt.input, true)
			if result != tt.expected {
				t.<PERSON>("GetCanonicalURIFromURIOrPath(%q) = %q, expected %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestGetFilePathFromURI(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
		windows  bool // 是否仅在Windows下运行此测试
	}{
		{
			name:     "Unix file URI",
			input:    "file:///path/to/file",
			expected: "/path/to/file",
			windows:  false,
		},
		{
			name:     "Not a file URI",
			input:    "http://example.com",
			expected: "",
			windows:  false,
		},
		{
			name:     "Windows file URI with encoded drive letter",
			input:    "file:///C%3A/Users/<USER>/File.txt",
			expected: "C:\\Users\\<USER>\\File.txt",
			windows:  true,
		},
		{
			name:     "Windows file URI without encoding",
			input:    "file:///C:/Users/<USER>/File.txt",
			expected: "C:\\Users\\<USER>\\File.txt",
			windows:  true,
		},
		{
			name:     "Windows file URI with spaces",
			input:    "file:///C:/Users/<USER>/My%20Documents/File.txt",
			expected: "C:\\Users\\<USER>\\My Documents\\File.txt",
			windows:  true,
		},
		{
			name:     "Windows file URI with special characters",
			input:    "file:///C:/Users/<USER>/%E6%96%87%E6%A1%A3/File.txt",
			expected: "C:\\Users\\<USER>\\文档\\File.txt",
			windows:  true,
		},
		{
			name:     "Windows file URI with long path",
			input:    "file:///C:/Users/<USER>/Documents/Very/Long/Path/That/Could/Potentially/Cause/Issues/With/Windows/Path/Length/Limitations/File.txt",
			expected: `C:\Users\<USER>\Documents\Very\Long\Path\That\Could\Potentially\Cause\Issues\With\Windows\Path\Length\Limitations\File.txt`,
			windows:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Skip tests that are Windows-specific if not on Windows
			if tt.windows && runtime.GOOS != "windows" {
				t.Skip("Skipping Windows-specific test on non-Windows platform")
			}

			result := GetFilePathFromURI(tt.input, true)
			if result != tt.expected {
				t.Errorf("GetFilePathFromURI(%q) = %q, expected %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestGetCanonicalURIFromFilePath(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
		windows  bool
	}{
		{
			name:     "Already a file URI",
			input:    "file:///path/to/file",
			expected: "file:///path/to/file",
			windows:  false,
		},
		{
			name:     "Unix path",
			input:    "/path/to/file",
			expected: "file:///path/to/file",
			windows:  false,
		},
		{
			name:     "Windows path with drive letter",
			input:    "C:\\Users\\<USER>\\File.txt",
			expected: "file:///C%3A%5CUsers%5CAdmin%5CFile.txt",
			windows:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Skip tests that are Windows-specific if not on Windows
			if tt.windows && runtime.GOOS != "windows" {
				t.Skip("Skipping Windows-specific test on non-Windows platform")
			}

			result := GetCanonicalURIFromFilePath(tt.input, true)
			if result != tt.expected {
				t.Errorf("GetCanonicalURIFromFilePath(%q) = %q, expected %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestRoundTrip(t *testing.T) {
	// 测试路径转换的往返一致性
	paths := []struct {
		path    string
		windows bool
	}{
		{"/home/<USER>/file.txt", false},
		{"/usr/local/bin/program", false},
		{"C:\\Program Files\\App\\file.exe", true},
		{"C:\\Users\\<USER>\\My Documents\\file.txt", true},
		{"C:\\Users\\<USER>\\文档\\file.txt", true},
	}

	for _, p := range paths {
		t.Run(p.path, func(t *testing.T) {
			// 跳过不适用的测试
			if p.windows && runtime.GOOS != "windows" {
				t.Skip("Skipping Windows-specific test on non-Windows platform")
			}

			// 路径 -> URI -> 路径
			uri := GetCanonicalURIFromFilePath(p.path, true)
			path := GetFilePathFromURI(uri, true)

			// 在Windows上，我们需要标准化路径分隔符进行比较
			expectedPath := p.path
			//if p.windows {
			//	expectedPath = strings.ReplaceAll(expectedPath, "\\", "/")
			//}

			if path != expectedPath {
				t.Errorf("Round trip failed: %s -> %s -> %s", p.path, uri, path)
			}
		})
	}
}
