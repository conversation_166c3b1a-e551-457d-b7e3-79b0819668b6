package tasks

import "github.com/emirpasic/gods/trees/redblacktree"

// findMinKeyAboveValue 从一棵红黑树中找到 “大于或等于给定 v” 的 “最小 key”，及其对应的 queue
// e.g. 一棵树上 key set 为 [128, 256, 512]: 给定 511 得到 512，给定 127 得到 128，给定 513 但是要获得 512。
func findMinKeyAboveValue(tree *redblacktree.Tree, v int) (int, *EmbeddingContentQueue, bool) {
	it := tree.Iterator()
	if !it.First() { // 如果树为空，直接返回
		return 0, nil, false
	}
	// 也有可能比树上最小的数字小，返回最小的数字
	if key, ok := it.Key().(int); !ok {
		return 0, nil, false
	} else if key >= v {
		return key, it.Value().(*EmbeddingContentQueue), true
	}
	// 迭代寻找大于或等于 value 的最小键
	for it.Next() {
		key := it.Key().(int)
		if key >= v {
			return key, it.Value().(*EmbeddingContentQueue), true
		}
	}
	// 如果没有找到大于或等于 value 的键，返回最大键
	if it.Last() {
		return it.Key().(int), it.Value().(*EmbeddingContentQueue), true
	}
	return 0, nil, false
}
