import {CommonFunctionalityExecutor} from "./common";
import {DefNodeInfo, DefNodeKindEnum, LanguageIdEnum, ParserEnum, VarDeclNodeInfo} from "../../share";
import {DocumentUri, Location} from "vscode-languageserver-types";
import {SyntaxNode} from "web-tree-sitter";
import {TreeSitterDispatcher} from "../../treesitter/dispatcher";
import {TreeSitterFunctionalityEnum} from "../../treesitter/share";

export class GoFunctionalityExecutor extends CommonFunctionalityExecutor {
  static INSTANCE = new GoFunctionalityExecutor();

  languageId = LanguageIdEnum.Go;

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
    super();
  }

  async getInvokeDefinition(parses: ParserEnum[], uri: DocumentUri, invokeNameNode: SyntaxNode, resultFilter: (location: Location) => boolean): Promise<DefNodeInfo[]> {
    const result: DefNodeInfo[] = [];

    for (const node of await this.getDefinitionNode(parses, uri, invokeNameNode, resultFilter)) {
      const realDefNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(this.languageId, TreeSitterFunctionalityEnum.GetFuncParentNodeForNode, node.node);

      if (realDefNode) {
        result.push({
          languageId: this.languageId,
          uri: node.uri,
          kind: DefNodeKindEnum.Function,
          name: invokeNameNode.text,
          defNode: realDefNode,
          subDefNodeInfo: null,
        });
      }
    }

    return result;
  }

  async getTypeDefinition(parses: ParserEnum[], uri: DocumentUri, typeNameNode: SyntaxNode, preFilter: (node: SyntaxNode) => boolean, resultFilter: (location: Location) => boolean): Promise<DefNodeInfo[]> {
    if (preFilter && !preFilter(typeNameNode)) {
      return [];
    }

    const result: DefNodeInfo[] = [];

    for (const node of await this.getDefinitionNode(parses, uri, typeNameNode, resultFilter)) {
      const realDefNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(this.languageId, TreeSitterFunctionalityEnum.GetTypeParentNodeForNode, node.node);

      if (realDefNode) {
        result.push({
          languageId: this.languageId,
          uri: node.uri,
          kind: this.getTypeKind(realDefNode),
          name: typeNameNode.text,
          defNode: realDefNode,
          subDefNodeInfo: null,
        });
      }
    }

    return result;
  }

  override getTypeDefinitionPreFilter(_parses: ParserEnum[], importInfo: Map<string, string>): (node: SyntaxNode) => boolean {
    return (node: SyntaxNode): boolean => {
      if (importInfo) {
        const packageBlacklist = [
          "archive",
          "atomic",
          "bufio",
          "compress",
          "context",
          "crypto",
          "database/sql",
          "encoding",
          "errors",
          "flag",
          "fmt",
          "html",
          "image",
          "internal",
          "io",
          "io/fs",
          "io/ioutil",
          "json",
          "log",
          "math",
          "mime",
          "net",
          "net/http",
          "net/url",
          "os",
          "os/exec",
          "os/signal",
          "path",
          "path/filepath",
          "plugin",
          "reflect",
          "regexp",
          "runtime",
          "runtime/debug",
          "runtime/pprof",
          "sort",
          "strconv",
          "strings",
          "sync",
          "syscall",
          "testing",
          "text/template",
          "time",
          "trace",
          "unicode",
          "xml",
        ];

        if (node.parent && node.parent.type === "qualified_type") {
          const packageNode = node.parent.childForFieldName("package");
          if (packageNode) {
            const packageName = packageNode.text;
            if (importInfo.has(packageName) && packageBlacklist.includes(importInfo.get(packageName))) {
              return false;
            }
          }
        }
      }

      return true;
    };
  }

  async getVarDeclaration(parses: ParserEnum[], uri: DocumentUri, varNameNode: SyntaxNode, resultFilter: (location: Location) => boolean): Promise<VarDeclNodeInfo[]> {
    const nodes = await this.getDefinitionNode(parses, uri, varNameNode, resultFilter);

    return nodes.map(node => {
      const realDeclNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(this.languageId, TreeSitterFunctionalityEnum.GetVarParentNodeForNode, node.node);

      if (realDeclNode) {
        return {
          languageId: this.languageId,
          uri: node.uri,
          kind: DefNodeKindEnum.VarDecl,
          name: varNameNode.text,
          declNode: realDeclNode,
        };
      } else {
        return null;
      }
    }).filter(item => item !== null);
  }

  private getTypeKind(node: SyntaxNode): DefNodeKindEnum {
    const typeNode = node.namedChild(0)?.childForFieldName("type");
    if (typeNode) {
      switch (typeNode.type) {
        case "interface_type":
          return DefNodeKindEnum.Interface;
        case "struct_type":
          return DefNodeKindEnum.Struct;
        case "function_type":
          return DefNodeKindEnum.Function;
        case "type_identifier":
          return DefNodeKindEnum.Alias;
      }
    }

    return DefNodeKindEnum.Unknown;
  }
}