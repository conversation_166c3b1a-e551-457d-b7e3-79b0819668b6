package util

import (
	"bufio"
	"context"
	"crypto/sha256"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"strings"
	"time"

	"ide/ckg/codekg/components/logs"

	"github.com/pkg/errors"
	"github.com/samber/lo"
)

const (
	requestApiErr              = "request_server_api"
	indexFilePostUrl           = "/api/ide/v1/knowledgebase/:id/files"
	httpStatusAGWAdaptiveLimit = 515
	apiTimeOutMsg              = "i/o timeout"
	socketForbiddenMsg         = "An attempt was made to access a socket in a way forbidden by its access permissions"
	WSARecvConnectionErrMsg    = "A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond"
)

func DefaultAny[T any](v any) T {
	if res, ok := v.(T); ok {
		return res
	}
	var d T
	return d
}

func GetSubdirectory(filePath string) string {
	filePath = strings.Replace(filePath, "\\", "/", -1)
	pathSegments := strings.Split(filePath, "/")
	if len(pathSegments) >= 2 {
		return strings.Join(pathSegments[1:], "/")
	}
	return ""
}

// GetFileUniqueID 通过文件路径与文件内容计算文件的 unique id，改逻辑要与服务端对齐
func GetFileUniqueID(filepath string, fileContent string) string {
	data := []byte(filepath + fileContent + fmt.Sprintf("%d", len(fileContent)))
	hash := sha256.Sum256(data)
	return fmt.Sprintf("p%x", hash)
}

func SafeGo(ctx context.Context, f func()) {
	go func() {
		defer func() {
			if rec := recover(); rec != nil {
				logs.CtxInfo(ctx, "panic recovered, err: %v, %s", rec, string(debug.Stack()))
			}
		}()

		f()
	}()
}

func GetJwtToken(serviceKey string) string {
	req, _ := http.NewRequest("GET", "https://cloud.bytedance.net/auth/api/v1/jwt", nil)
	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", serviceKey))
	resp, _ := (&http.Client{}).Do(req)
	return fmt.Sprintf("Byte-Cloud-JWT %s", resp.Header["X-Jwt-Token"][0])
}

// GetFolderSegments 获取文件夹的所有父文件夹的相对路径 (包括自身)
// fileRelPath 是文件相对路径，不能传入文件夹路径，不能传入绝对路径。
func GetFolderSegments(fileRelPath string) []string {
	var segments []string
	cleanPath := filepath.Clean(fileRelPath)
	parts := strings.Split(cleanPath, string(os.PathSeparator))
	parts = lo.Filter(parts, func(part string, _ int) bool { return len(part) > 0 })
	// e.g. codekg/components/data_manager/manager.go ->
	//      ["codekg", "codekg/components", "codekg/components/data_manager"]
	// 不合法的 fileRelPath：/Users/<USER>/ckg/codekg/components/data_manager/manager.go
	// 不合法的 fileRelPath：ckg/codekg/components/data_manager
	for i := 1; i < len(parts); i++ {
		result := filepath.Join(parts[:i]...)
		segments = append(segments, result)
	}
	return segments
}

// GetRandStr 获取随机字符串，给定长度
func GetRandStr(n int) string {
	const letters = "abcdefghijklmnopqrstuvwxyz0123456789"
	rand.New(rand.NewSource(time.Now().UnixNano()))
	b := make([]byte, n)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}

func JudgeNewWorkError(err error) bool {
	var httpError *HTTPError
	// 515 状态码：AGW 自适应限流，1 秒后重试
	// 504 Gateway 超时
	return errors.As(err, &httpError) && (httpError.StatusCode == http.StatusTooManyRequests ||
		httpError.StatusCode == http.StatusExpectationFailed ||
		httpError.StatusCode == http.StatusGatewayTimeout ||
		httpError.StatusCode == httpStatusAGWAdaptiveLimit ||
		strings.Contains(httpError.Message, "broken pipe") ||
		strings.Contains(httpError.Message, "no such host") ||
		strings.Contains(httpError.Message, apiTimeOutMsg) ||
		strings.Contains(httpError.Message, socketForbiddenMsg) ||
		strings.Contains(httpError.Message, WSARecvConnectionErrMsg))
}

func GetCanonicalURIFromURIOrPath(uriOrPath string, forVirtualProject bool) string {
	if strings.HasPrefix(uriOrPath, "file://") || strings.HasPrefix(uriOrPath, "git://") || strings.HasPrefix(uriOrPath, "http://") || strings.HasPrefix(uriOrPath, "https://") {
		return uriOrPath
	}

	return GetCanonicalURIFromFilePath(uriOrPath, forVirtualProject)
}

func IsSubdirectory(child, parent string) bool {
	// compatible with windows
	a := strings.ReplaceAll(child, `\`, `/`)
	b := strings.ReplaceAll(parent, `\`, `/`)
	return strings.HasPrefix(a, b)
}

func GetFilePathFromURI(uri string, forVirtualProject bool) string {
	if !strings.HasPrefix(uri, "file://") {
		return ""
	}

	// Handle different file URI formats
	path := strings.TrimPrefix(uri, "file://")

	if forVirtualProject {
		// Try to unescape the path to handle encoded characters
		unescapedPath, err := url.PathUnescape(path)
		if err == nil {
			path = unescapedPath
		}

		// Handle Windows paths with triple slash: file:///c:/path or file:///c%3A/path
		if runtime.GOOS == "windows" {
			// Remove leading slash for Windows paths with drive letters
			if len(path) > 0 && path[0] == '/' {
				path = path[1:] // 去掉开头的斜杠
			}

			path = strings.ReplaceAll(path, `/`, `\`)
		}
	}

	return path
}

func GetURIFromFilePathForCursorMove(filePath string) string {
	if strings.HasPrefix(filePath, "file://") {
		return filePath
	}

	// Handle Windows paths with triple slash: file:///c:/path or file:///c%3A/path
	if runtime.GOOS == "windows" {
		// Remove leading slash for Windows paths with drive letters
		if len(filePath) > 0 && filePath[0] == '/' {
			filePath = filePath[1:] // 去掉开头的斜杠
		}

		filePath = strings.ReplaceAll(filePath, `/`, `\`)
	}

	return filePath
}

func GetCanonicalURIFromFilePath(filePath string, forVirtualProject bool) string {
	if strings.HasPrefix(filePath, "file://") {
		return filePath
	}

	if forVirtualProject {
		filePath = strings.ReplaceAll(filePath, `\`, `/`)

		// For Windows paths, handle the colon in drive letters (e.g., C:)
		if runtime.GOOS == "windows" && len(filePath) >= 2 && filePath[1] == ':' {
			// Use triple slash for Windows absolute paths and properly escape special characters
			filePath = `/` + strings.ReplaceAll(filePath, `\`, `/`)
		}

		return "file://" + strings.ReplaceAll(url.PathEscape(filePath), "%2F", "/")
	}

	return fmt.Sprintf("file://%s", filePath)
}

func ReadFirstCharFromFile(file *os.File, num int) (string, error) {
	reader := bufio.NewReader(file)
	buffer := make([]byte, num)
	n, err := reader.Read(buffer)
	if err != nil {
		if errors.Is(err, io.EOF) {
			return "", nil
		}
		return "", err
	}
	return string(buffer[:n]), nil
}

func CheckSystemResources(ctx context.Context, afterPanic bool) {
	// 检查内存使用和goroutine 数量
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	logs.CtxInfo(ctx, "afterPanic %s memory usage: %v, goroutine number:  %d", afterPanic, m, runtime.NumGoroutine())
}
