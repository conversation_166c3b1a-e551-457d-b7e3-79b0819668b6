{"name": "@byted-ckg/analyzer-sdk", "version": "0.5.1", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "exports": {".": {"types": "./dist/src/index.d.ts", "default": "./dist/src/index.js"}}, "license": "ISC", "files": ["dist/**/*"], "scripts": {"test": "pnpm build:debug && vitest", "clean": "tsc -b --clean && rm -rf ./dist", "build": "pnpm run clean && tsc && pnpm run copy:resources && node ./esbuild.worker.mjs", "build:debug": "export DEBUG_BUILD=true && pnpm run clean && tsc && pnpm run copy:resources && node ./esbuild.worker.mjs", "copy:resources": "cp -r ./resources dist/ && pnpm run copy:wasm", "copy:wasm": "cp ./node_modules/web-tree-sitter/tree-sitter.wasm dist/src/"}, "dependencies": {"@grpc/grpc-js": "1.10.9", "google-protobuf": "3.18.0-rc.2", "lru-cache": "^11.0.0", "tslib": "^2.6.2", "vscode-languageserver-textdocument": "^1.0.12", "vscode-languageserver-types": "^3.17.5", "web-tree-sitter": "^0.24.4", "winston": "^3.14.2"}, "peerDependencies": {"@byted-ckg/client": ">=0.0.58"}}