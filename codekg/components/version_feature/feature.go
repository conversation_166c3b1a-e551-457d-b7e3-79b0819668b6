package version_feature

const (
	FEAT_LOCAL_EMBEDDING = "LocalEmbedding"
	FEAT_HASH_FOLDER     = "HashFolder"
)

var (
	versionFeatureMap = map[string]*CKGFeature{
		FEAT_LOCAL_EMBEDDING: localEmbeddingFeature,
		//FEAT_HASH_FOLDER:     hashFolderFeature,
	}

	localEmbeddingFeature = &CKGFeature{
		Name:                FEAT_LOCAL_EMBEDDING,
		Description:         "Embedding local storage & local recall",
		FromVersion:         VersionLocalEmbedding,
		CompatLowestVersion: VersionLocalEmbeddingCompat,
		ExpectedActions:     []VersionUpdateAction{NoAction},
		Dependencies:        []*CKGFeature{},
		Deprecated:          false,
		Status:              StableFeature,
	}

	// TODO: 后续支持 #Folder 功能
	//hashFolderFeature = &CKGFeature{
	//	Name:                FEAT_HASH_FOLDER,
	//	Description:         "#Folder",
	//	FromVersion:         VersionHashFolder,
	//	CompatLowestVersion: VersionHashFolderCompat,
	//	ExpectedActions:     []VersionUpdateAction{ReIndexAction},
	//	Dependencies:        []*CKGFeature{localEmbeddingFeature},
	//	Deprecated:          false,
	//	Status:              AlphaFeature,
	//}
)
