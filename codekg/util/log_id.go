package util

import (
	"github.com/bytedance/gopkg/lang/fastrand"
	"strconv"
	"strings"
	"time"
)

const (
	// 目前版本为 02
	version    = "02"
	length     = 53
	maxRandNum = 1<<24 - 1<<20
)

func GenLogID() string {
	r := fastrand.Uint32n(maxRandNum) + 1<<20
	sb := strings.Builder{}
	sb.Grow(length)
	sb.WriteString(version)
	sb.WriteString(strconv.FormatUint(uint64(time.Now().UnixNano()/int64(time.Millisecond)), 10))
	sb.Write([]byte("00000000000000000000000000000000"))
	sb.WriteString(strconv.FormatUint(uint64(r), 16))
	return sb.String()
}
