import {beforeAll, describe, expect, it, vi} from "vitest";
import {TreeSitterParser} from "../../parse/treesitter/parser";
import {LanguageIdEnum} from "../../parse/share";
import {
  getAnonymousParameterNameNodes,
  getConstructorDefNodes,
  getFieldDefNodes,
  getFuncNameNode, getIncludingFuncDeclNode,
  getParameterNameNodes, getSuperClassNode, getUsageInfos,
  getUsedFuncNameNodes, getUsedOutsideVariableNameNodes, getVariableDeclNameNodes,
} from "./javascript";
import {createDocument} from "../../../utils/common";
import * as utilsPath from "../../../utils/path";
import {PSIParser} from "../../parse/psi/parser";
import type {DocumentUri, Position} from "vscode-languageserver-types";
import {completeUpdateCachedTree} from "../../cache/astCache";

describe("test_getFuncNameNode", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_function_declaration", () => {
    const docText = `
function myFunction(a, b) {
    return a + b;
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getFuncNameNode(tree.rootNode.namedChild(0));

    expect(result.text).toEqual("myFunction");
  });

  it("case2_method_definition", () => {
    const docText = `
class MyClass {
    myMethod(m, n) {
        return m - n;
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getFuncNameNode(tree.rootNode.namedChild(0).childForFieldName("body").namedChild(0));

    expect(result.text).toEqual("myMethod");
  });

  it("case3_generator_function_declaration", () => {
    const docText = `
function* myGeneratorFunction() {
    yield 'a';
    yield 'b';
    yield 'c';
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getFuncNameNode(tree.rootNode.namedChild(0));

    expect(result.text).toEqual("myGeneratorFunction");
  });

  it("case4_function_with_name", () => {
    const docText = `
const v1 = function f1() { 
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getFuncNameNode(tree.rootNode.namedChild(0).namedChild(0).childForFieldName("value"));

    expect(result.text).toEqual("f1");
  });

  it("case5_function_assignment", () => {
    const docText = `
const v1 = function() { 
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getFuncNameNode(tree.rootNode.namedChild(0).namedChild(0).childForFieldName("value"));

    expect(result.text).toEqual("v1");
  });

  it("case6_function_anonymous", () => {
    const docText = `
a.b = function() { 
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getFuncNameNode(tree.rootNode.namedChild(0).namedChild(0).childForFieldName("right"));

    expect(result).toEqual(null);
  });

  it("case7_arrow_function_assignment", () => {
    const docText = `
const v1 = () => { 
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getFuncNameNode(tree.rootNode.namedChild(0).namedChild(0).childForFieldName("value"));

    expect(result.text).toEqual("v1");
  });

  it("case8_arrow_function_anonymous", () => {
    const docText = `
a.b = () => { 
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getFuncNameNode(tree.rootNode.namedChild(0).namedChild(0).childForFieldName("right"));

    expect(result).toEqual(null);
  });
})

describe("test_getSuperClassNode", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
class T extends p.A {
  constructor() {
    super();
  }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getSuperClassNode(tree.rootNode.namedChild(0));

    expect(result.text).toEqual("p.A");
  });
})

describe("test_getFieldDefNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
class ExampleClass {
    publicVar = 'I am public';

    #privateVar = 'I am private';
    
    static #privateStaticVar = 'I am private and static';

    static staticVar = 'I am static';

    constructor() {
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getFieldDefNodes(tree.rootNode.namedChild(0));

    expect(result.map(item => item.text)).toEqual([
      "publicVar = 'I am public'",
      "#privateVar = 'I am private'",
      "static #privateStaticVar = 'I am private and static'",
      "static staticVar = 'I am static'",
    ]);
  });
})

describe("test_getConstructorDefNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
class ExampleClass {
    publicVar = 'I am public';
    
    constructor() {
    }
    
    constructor(p0) {
    }
    
    constructor(p0, p1) {
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getConstructorDefNodes(tree.rootNode.namedChild(0));

    expect(result.map(item => item.text)).toEqual([
      "constructor() {\n    }",
      "constructor(p0) {\n    }",
      "constructor(p0, p1) {\n    }",
    ]);
  });
})

describe("test_getUsedFuncNameNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
function f() {
    f1();
    a.b.f2();
    a.f2().b.f3()
    const c = new C()
    c.m();
    const obj = {
        f3: f3
    };
    obj.f3();
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getUsedFuncNameNodes(tree.rootNode.namedChild(0), LanguageIdEnum.Javascript);

    expect(result.map(item => item.text)).toEqual([
      "f1",
      "f2",
      "f3",
      "f2",
      "m",
      "f3",
    ]);
  });
})

describe("test_getParameterNameNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
function f(a, b, c) {
    f1();
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getParameterNameNodes(tree.rootNode.namedChild(0), LanguageIdEnum.Javascript);

    expect(result.map(item => item.text)).toEqual([
      "a",
      "b",
      "c",
    ]);
  });

  it("case2_object_pattern", () => {
    const docText = `
function f({a, b}, c = 1, {d, e = "e"}) {
    f1();
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getParameterNameNodes(tree.rootNode.namedChild(0), LanguageIdEnum.Javascript);

    expect(result.map(item => item.text)).toEqual([
      "a",
      "b",
      "c",
      "d",
      "e",
    ]);
  });

  it("case3_rest_pattern", () => {
    const docText = `
function f(a, ...b) {
    f1();
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getParameterNameNodes(tree.rootNode.namedChild(0), LanguageIdEnum.Javascript);

    expect(result.map(item => item.text)).toEqual([
      "a",
      "b",
    ]);
  });
})

describe("test_getVariableDeclNameNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
function variableDeclarations() {
    var varVariable = 'This is a var variable';
    let letVariable = 'This is a let variable';
    const constVariable = 'This is a const variable';
    const myClassInstance = new MyClass('JavaScript');
    const arrowFunction = () => {
        console.log('This is an arrow function');
    };
    const obj = {
        property: 'This is an object property',
        method() {
            console.log('This is an object method');
        }
    };
    const array = [1, 2, 3, 4, 5];
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getVariableDeclNameNodes(tree.rootNode.namedChild(0), LanguageIdEnum.Javascript);

    expect(result.map(item => item.text)).toEqual([
      "varVariable",
      "letVariable",
      "constVariable",
      "myClassInstance",
      "arrowFunction",
      "obj",
      "array",
    ]);
  });
})

describe("test_getAnonymousParameterNameNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
function demonstrateAnonymousFunctions() {
    const singleStringParamFunc = function(param1) {
    };

    const multipleParamsFunc = function(param2, {param3, param4}) {
    };

    const defaultParamsFunc = function(param5 = 'Default', param6 = false) {
    };

    const restParamsFunc = function(param7, ...param8) {
    };

    const singleStringParamArrowFunc = param9 => {
    };

    const multipleParamsArrowFunc = (param10, param11) => {
    };

    const defaultParamsArrowFunc = (param12 = 'Default', {param13 = false}) => {
    };

    const restParamsArrowFunc = (param14, ...param15) => {
    };

    (function(param16) {
    })(0);

    (function({param17 = 1, param18}) {
    })(0, 1);
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getAnonymousParameterNameNodes(tree.rootNode.namedChild(0), LanguageIdEnum.Javascript);

    expect(result.map(item => item.text)).toEqual([
      "param1",
      "param2",
      "param3",
      "param4",
      "param5",
      "param6",
      "param7",
      "param8",
      "param9",
      "param10",
      "param11",
      "param12",
      "param13",
      "param14",
      "param15",
      "param16",
      "param17",
      "param18",
    ]);
  });
})

describe("test_getUsedOutsideVariableNameNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
function complexFunction(param1, param2, param3) {
    // 多个变量声明
    const var1 = 'Local Variable 1';
    let var2 = 'Local Variable 2';
    var var3 = 'Local Variable 3';

    // 多个函数调用
    functionCall1(var1, param1);
    functionCall2(var2, param2);
    functionCall3(var3, param3);

    // 匿名函数作为函数表达式
    const anonFunc1 = function() {
        console.log('Anonymous function 1');
    };
    anonFunc1();

    // 带参数的匿名函数
    const anonFunc2 = function(param) {
        console.log('Anonymous function 2 with param:', param);
    };
    anonFunc2('Test Param');

    // 箭头函数
    const arrowFunc1 = () => {
        console.log('Arrow function 1');
    };
    arrowFunc1();

    // 带参数的箭头函数
    const arrowFunc2 = (param) => {
        console.log('Arrow function 2 with param:', param);
        console.log('References externalVar1:', externalVar1);
    };
    arrowFunc2('Test Param');

    // 立即调用函数表达式 (IIFE)
    (function(param) {
        console.log('IIFE with param:', param);
        console.log('References externalVar2:', externalVar2);
    })('IIFE Param');
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = getUsedOutsideVariableNameNodes(tree.rootNode.namedChild(0), LanguageIdEnum.Javascript);

    expect(result.map(item => item.text)).toEqual([
      "console",
      "console",
      "console",
      "console",
      "console",
      "externalVar1",
      "console",
      "console",
      "externalVar2",
    ]);
  });
})

describe("test_getIncludingFuncDeclNode", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
export const createTestdataDatasourceIfNotExists = (client) => {
  const payload = {
    access: 'proxy',
    isDefault: false,
    name: 'k6-testdata',
    type: 'testdata',
  };
  
  const f = function(): void {
  }

  let res = client.datasources.getByName(payload.name);
  if (res.status === 404) {
    res = client.datasources.create(payload);
  }

  return res.json().id;
};
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);
    const result = getIncludingFuncDeclNode(LanguageIdEnum.Javascript, tree.rootNode, {
      start: {line: 1, character: 0},
      end: {line: 18, character: 1},
    });

    const expectedResult = `
(client) => {
  const payload = {
    access: 'proxy',
    isDefault: false,
    name: 'k6-testdata',
    type: 'testdata',
  };
  
  const f = function(): void {
  }

  let res = client.datasources.getByName(payload.name);
  if (res.status === 404) {
    res = client.datasources.create(payload);
  }

  return res.json().id;
}
`
    expect(result.text).toEqual(expectedResult.trim());
  });
});

describe("test_getUsageInfos", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
function f() {
  f1();
}
`;

    const workspacePathMock = vi.spyOn(utilsPath, "getWorkspaceFolderPath").mockImplementation(() => {
      return "file:///";
    });
    const psiIns = PSIParser.INSTANCE
    const psiInitMock = vi.spyOn(psiIns, "initialized").mockImplementation(() => {
      return true;
    });
    const psiExecMock = vi.spyOn(psiIns, "execute").mockImplementation(async (_command: string, _languageId: string, _uri: DocumentUri, _position: Position) => {
      return [{
        uri: "file:///test.js",
        range: {start: {line: 2, character: 2}, end: {line: 2, character: 4}},
      }];
    });

    const document = createDocument(docText, LanguageIdEnum.Javascript, "file:///test.js");
    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);
    await completeUpdateCachedTree(document);

    const result = await getUsageInfos(document.uri, tree.rootNode, LanguageIdEnum.Javascript);

    expect(result.map(item => item.node.text)).toEqual([
      `
function f() {
  f1();
}
`.trim(),
    ]);

    workspacePathMock.mockRestore();
    psiInitMock.mockRestore();
    psiExecMock.mockRestore();
  });
});