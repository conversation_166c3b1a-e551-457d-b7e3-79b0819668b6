package main

import (
	"context"
	"fmt"
	"google.golang.org/grpc"
	"ide/ckg/codekg/protocol"
	"log"
	"time"
)

func main() {
	// 连接服务器
	conn, err := grpc.Dial("localhost:56789", grpc.WithInsecure())
	if err != nil {
		log.Fatalf("无法连接服务器: %v", err)
	}
	defer conn.Close()

	// 创建客户端
	client := protocol.NewCodeKGClient(conn)

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 测试用户列表
	testUsers := []string{
		"2522746474477433",
		"2689820513806084",
		"user_001",
		"user_002",
		"test_user",
		"jialiang.99",
		"2338022701411699",
	}

	// 循环测试不同用户
	for _, userId := range testUsers {
		fmt.Printf("\n测试用户 ID: %s\n", userId)

		req := &protocol.IsCKGEnabledForNonWorkspaceScenarioRequest{
			UserId: userId,
		}

		resp, err := client.IsCKGEnabledForNonWorkspaceScenario(ctx, req)
		if err != nil {
			fmt.Printf("调用失败: %v\n", err)
			continue
		}

		// 打印结果
		fmt.Printf("响应结果:\n")
		fmt.Printf("IsEnabled: %v\n", resp.IsEnabled)
		fmt.Printf("Code: %v\n", resp.Code)

		if resp.Error != nil {
			fmt.Printf("Error Message: %s\n", resp.Error.Message)
			fmt.Printf("Error Stack: %s\n", resp.Error.Stack)
		}
		// 等待一下，避免请求太快
		time.Sleep(time.Second)
	}
}
