package logs

import (
	"code.byted.org/gopkg/ctxvalues"
	"context"
	"fmt"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"ide/ckg/codekg/components/logs/custom_writer"
	"os"
	"path/filepath"
)

const logFileName = "codekg.log"

func Init(path string) {
	consoleEncoder := zapcore.NewConsoleEncoder(zap.NewDevelopmentEncoderConfig())
	consoleWriteSyncer := zapcore.Lock(os.Stdout)

	// Create write syncer for the file.
	fileWriteSyncer := zapcore.AddSync(custom_writer.NewFileWriter(filepath.Join(path, logFileName), custom_writer.Daily, custom_writer.SetKeepFiles(7)))

	// Combine write syncers using zapcore.NewTee.
	core := zapcore.NewTee(
		zapcore.NewCore(consoleEncoder, consoleWriteSyncer, zapcore.DebugLevel),
		zapcore.NewCore(zapcore.NewConsoleEncoder(zap.NewDevelopmentEncoderConfig()), fileWriteSyncer, zapcore.DebugLevel),
	)
	logger := zap.New(core, zap.AddCaller())
	zap.ReplaceGlobals(logger)
}

func Info(format string, args ...interface{}) {
	// 单行大日志内容进行缩减：单行日志超过2048个字符，则超过部分用 ...... 代替
	msg := getMessage(format, args)
	if len(msg) > 2*1024 {
		msg = msg[:2*1024] + "......"
	}
	zap.S().Infof(msg)
}

func Error(format string, args ...interface{}) {
	zap.S().Errorf(format, args...)
}

func Warn(format string, args ...interface{}) {
	zap.S().Warnf(format, args...)
}

func Trace(format string, args ...interface{}) {
	//zap.S().Infof(format, args...)
}

func Debug(format string, args ...interface{}) {
	//zap.S().Infof(format, args...)
}

func CtxInfo(ctx context.Context, format string, args ...interface{}) {
	argsList := make([]interface{}, 0)
	argsList = append(argsList, ctxvalues.LogIDDefault(ctx))
	argsList = append(argsList, args...)
	zap.S().Infof("%s "+format, argsList...)
}

func CtxError(ctx context.Context, format string, args ...interface{}) {
	argsList := make([]interface{}, 0)
	argsList = append(argsList, ctxvalues.LogIDDefault(ctx))
	argsList = append(argsList, args...)
	zap.S().Errorf("%s "+format, argsList...)
}

func CtxWarn(ctx context.Context, format string, args ...interface{}) {
	argsList := make([]interface{}, 0)
	argsList = append(argsList, ctxvalues.LogIDDefault(ctx))
	argsList = append(argsList, args...)
	zap.S().Warnf("%s "+format, argsList...)
}

func CtxTrace(ctx context.Context, format string, args ...interface{}) {
	//argsList := make([]interface{}, 0)
	//argsList = append(argsList, ctxvalues.LogIDDefault(ctx))
	//argsList = append(argsList, args...)
	//zap.S().Debugf("%s "+format, argsList...)
}

func CtxDebug(ctx context.Context, format string, args ...interface{}) {
	//argsList := make([]interface{}, 0)
	//argsList = append(argsList, ctxvalues.LogIDDefault(ctx))
	//argsList = append(argsList, args...)
	//zap.S().Debugf("%s "+format, argsList...)
}

func Flush() {
	_ = zap.S().Sync()
}

func getMessage(template string, fmtArgs []interface{}) string {
	if len(fmtArgs) == 0 {
		return template
	}

	if template != "" {
		return fmt.Sprintf(template, fmtArgs...)
	}

	if len(fmtArgs) == 1 {
		if str, ok := fmtArgs[0].(string); ok {
			return str
		}
	}
	return fmt.Sprint(fmtArgs...)
}
