const TAG = "[CKG]";
export interface Logger {
  info(msg: string): void
  warn(msg: string): void
  error(msg: string): void
}

let clientLogger: Logger | null = null;

export function setClientLogger(logger: Logger): void {
  clientLogger = logger;
}

export namespace Logger {
  export function info(msg: string) : void{
    if (clientLogger) {
      clientLogger.info(`${TAG} ${msg}`);
    }
  }

  export function warn(msg: string) : void{
    if (clientLogger) {
      clientLogger.warn(`${TAG} ${msg}`);
    }
  }

  export function error(msg: string) : void{
    if (clientLogger) {
      clientLogger.error(`${TAG} ${msg}`);
    }
  }
}
