package langchain

import (
	"fmt"
	"reflect"
	"strings"

	"ide/ckg/codekg/model"

	"gitlab.com/golang-commonmark/markdown"
)

// NewMarkdownTextSplitter creates a new Markdown text splitter.
func NewMarkdownTextSplitter(opts ...Option) *MarkdownTextSplitter {
	options := DefaultOptions()

	for _, o := range opts {
		o(&options)
	}

	sp := &MarkdownTextSplitter{
		ChunkSize:        options.ChunkSize,
		ChunkOverlap:     options.ChunkOverlap,
		SecondSplitter:   options.SecondSplitter,
		CodeBlocks:       options.CodeBlocks,
		ReferenceLinks:   options.ReferenceLinks,
		HeadingHierarchy: options.KeepHeadingHierarchy,
		JoinTableRows:    options.JoinTableRows,
		LenFunc:          options.LenFunc,
	}

	//if sp.SecondSplitter == nil {
	//	sp.SecondSplitter = NewRecursiveCharacter(
	//		WithChunkSize(options.ChunkSize),
	//		WithChunkOverlap(options.ChunkOverlap),
	//		WithSeparators([]string{
	//			"\n\n", // new line
	//			"\n",   // new line
	//			" ",    // space
	//		}),
	//		WithLenFunc(options.LenFunc),
	//	)
	//}

	return sp
}

var _ TextSplitter = (*MarkdownTextSplitter)(nil)

// MarkdownTextSplitter markdown header text splitter.
//
// If your origin document is HTML, you purify and convert to markdown,
// then split it.
type MarkdownTextSplitter struct {
	ChunkSize    int
	ChunkOverlap int
	// SecondSplitter splits paragraphs
	SecondSplitter   TextSplitter
	CodeBlocks       bool
	ReferenceLinks   bool
	HeadingHierarchy bool
	JoinTableRows    bool
	LenFunc          func(string) int
}

// SplitText splits a text into multiple chunks with line information.
func (sp MarkdownTextSplitter) SplitText(text string) ([]TextChunk, error) {
	// 按行分割文本
	lines := strings.Split(text, "\n")

	// 解析Markdown
	mdParser := markdown.New(markdown.XHTMLOutput(true))
	tokens := mdParser.Parse([]byte(text))

	// 创建上下文
	mc := &markdownContext{
		startAt:                0,
		endAt:                  len(tokens),
		tokens:                 tokens,
		chunkSize:              sp.ChunkSize,
		chunkOverlap:           sp.ChunkOverlap,
		secondSplitter:         sp.SecondSplitter,
		renderCodeBlocks:       sp.CodeBlocks,
		useInlineContent:       !sp.ReferenceLinks,
		joinTableRows:          sp.JoinTableRows,
		hTitleStack:            []string{},
		hTitlePrependHierarchy: sp.HeadingHierarchy,
		lenFunc:                sp.LenFunc,
		originalText:           text,
		lines:                  lines,
		currentLine:            1,
		lineIndices:            findLineIndices(text),
	}

	// 获取分割结果，现在直接返回带行号信息的TextChunk
	return mc.splitText(), nil
}

// markdownContext the helper.
type markdownContext struct {
	// startAt represents the start position of the cursor in tokens
	startAt int
	// endAt represents the end position of the cursor in tokens
	endAt int
	// tokens represents the markdown tokens
	tokens []markdown.Token

	// originalText is the original text being split
	originalText string
	// lines is the text split by lines
	lines []string
	// lineIndices contains the character positions of each line start
	lineIndices []int

	// currentLine tracks the current line number (1-indexed)
	currentLine int
	// lineMap maps token positions to line numbers
	lineMap map[int]int
	// textChunks stores the final chunks with line information
	textChunks []TextChunk

	// hTitle represents the current header(H1、H2 etc.) content
	hTitle string
	// hTitleStack represents the hierarchy of headers
	hTitleStack []string
	// hTitlePrepended represents whether hTitle has been appended to chunks
	hTitlePrepended bool
	// hTitlePrependHierarchy represents whether hTitle should contain the title hierarchy or only the last title
	hTitlePrependHierarchy bool

	// orderedList represents whether current list is ordered list
	orderedList bool
	// bulletList represents whether current list is bullet list
	bulletList bool
	// listOrder represents the current order number for ordered list
	listOrder int

	// indentLevel represents the current indent level for ordered、unordered lists
	indentLevel int

	// chunks represents the final chunks
	chunks []string
	// curSnippet represents the current short markdown-format chunk
	curSnippet string
	// chunkSize represents the max chunk size, when exceeds, it will be split again
	chunkSize int
	// chunkOverlap represents the overlap size for each chunk
	chunkOverlap int

	// secondSplitter re-split markdown single long paragraph into chunks
	secondSplitter TextSplitter

	// renderCodeBlocks determines whether indented and fenced code blocks should
	// be rendered
	renderCodeBlocks bool

	// useInlineContent determines whether the default inline content is rendered
	useInlineContent bool

	// joinTableRows determines whether a chunk should contain multiple table rows,
	// or if each row in a table should be split into a separate chunk.
	joinTableRows bool

	// lenFunc represents the function to calculate the length of a string.
	lenFunc func(string) int
}

// splitText splits Markdown text.
func (mc *markdownContext) splitText() []TextChunk {
	// 初始化行号跟踪
	mc.currentLine = 1
	mc.lineMap = make(map[int]int)
	mc.textChunks = make([]TextChunk, 0)

	// 处理Markdown标记
	for idx := mc.startAt; idx < mc.endAt; {
		token := mc.tokens[idx]

		// 记录当前标记的行号
		mc.lineMap[idx] = mc.currentLine

		switch token.(type) {
		case *markdown.HeadingOpen:
			mc.onMDHeader()
		case *markdown.TableOpen:
			mc.onMDTable()
		case *markdown.ParagraphOpen:
			mc.onMDParagraph()
		case *markdown.BlockquoteOpen:
			mc.onMDQuote()
		case *markdown.BulletListOpen:
			mc.onMDBulletList()
		case *markdown.OrderedListOpen:
			mc.onMDOrderedList()
		case *markdown.ListItemOpen:
			mc.onMDListItem()
		case *markdown.CodeBlock:
			mc.onMDCodeBlock()
		case *markdown.Fence:
			mc.onMDFence()
		case *markdown.Hr:
			mc.onMDHr()
		default:
			// 更新行号
			if text, ok := token.(*markdown.Text); ok {
				mc.currentLine += strings.Count(text.Content, "\n")
			}
			mc.startAt = indexOfCloseTag(mc.tokens, idx) + 1
		}
		idx = mc.startAt
	}

	// 应用最后一个块
	mc.applyToChunks()

	return mc.textChunks
}

// applyToChunks applies current snippet to chunks.
func (mc *markdownContext) applyToChunks() {
	defer func() {
		mc.curSnippet = ""
	}()

	var chunks []string
	if mc.curSnippet != "" {
		// 检查当前块是否超过ChunkSize，如果是，重新分割
		if mc.lenFunc(mc.curSnippet) <= mc.chunkSize+mc.chunkOverlap {
			chunks = []string{mc.curSnippet}
		} else {
			// 分割当前片段为多个块
			secondSplitterChunks, err := mc.secondSplitter.SplitText(mc.curSnippet)
			if err == nil {
				// 提取内容
				chunks = make([]string, len(secondSplitterChunks))
				for i, chunk := range secondSplitterChunks {
					chunks[i] = chunk.Content
				}
			} else {
				// 如果分割失败，使用整个片段
				chunks = []string{mc.curSnippet}
			}
		}
	}

	// 如果只有标题，将标题应用到块
	if len(chunks) == 0 && mc.hTitle != "" && !mc.hTitlePrepended {
		// 创建TextChunk
		startLine := mc.currentLine
		endLine := startLine

		textChunk := TextChunk{
			Content:   mc.hTitle,
			StartLine: startLine,
			EndLine:   endLine,
		}

		mc.textChunks = append(mc.textChunks, textChunk)
		mc.hTitlePrepended = true
		return
	}

	for _, chunk := range chunks {
		if chunk == "" {
			continue
		}

		mc.hTitlePrepended = true

		// 计算行号 - 使用基于行索引的方式
		startLine := mc.currentLine

		// 如果有标题且当前块不包含标题，将标题添加到块
		if mc.hTitle != "" && !strings.Contains(mc.curSnippet, mc.hTitle) {
			chunk = fmt.Sprintf("%s\n%s", mc.hTitle, chunk)
		}

		// 计算块中的换行符数量并更新行号
		lineCount := strings.Count(chunk, "\n")
		endLine := startLine + lineCount

		// 创建TextChunk
		textChunk := TextChunk{
			Content:   chunk,
			StartLine: startLine,
			EndLine:   endLine,
		}

		mc.textChunks = append(mc.textChunks, textChunk)

		// 更新当前行号
		mc.currentLine = endLine + 1 // 移动到下一行
	}
}

// clone clones the markdownContext with sub tokens.
func (mc *markdownContext) clone(startAt, endAt int) *markdownContext {
	subTokens := mc.tokens[startAt : endAt+1]
	return &markdownContext{
		endAt:  len(subTokens),
		tokens: subTokens,

		hTitle:          mc.hTitle,
		hTitleStack:     mc.hTitleStack,
		hTitlePrepended: mc.hTitlePrepended,

		orderedList: mc.orderedList,
		bulletList:  mc.bulletList,
		listOrder:   mc.listOrder,
		indentLevel: mc.indentLevel,

		chunkSize:      mc.chunkSize,
		chunkOverlap:   mc.chunkOverlap,
		secondSplitter: mc.secondSplitter,

		lenFunc:      mc.lenFunc,
		currentLine:  mc.currentLine,
		originalText: mc.originalText,
		lines:        mc.lines,
	}
}

// onMDHeader splits H1/H2/.../H6
//
// format: HeadingOpen/Inline/HeadingClose
func (mc *markdownContext) onMDHeader() {
	endAt := indexOfCloseTag(mc.tokens, mc.startAt)
	defer func() {
		mc.startAt = endAt + 1
	}()

	header, ok := mc.tokens[mc.startAt].(*markdown.HeadingOpen)
	if !ok {
		return
	}

	// check next token is Inline
	inline, ok := mc.tokens[mc.startAt+1].(*markdown.Inline)
	if !ok {
		return
	}

	mc.applyToChunks() // change header, apply to chunks

	hm := repeatString(header.HLevel, "#")
	mc.hTitle = fmt.Sprintf("%s %s", hm, inline.Content)

	// fill titlestack with empty strings up to the current level
	for len(mc.hTitleStack) < header.HLevel {
		mc.hTitleStack = append(mc.hTitleStack, "")
	}

	if mc.hTitlePrependHierarchy {
		// Build the new title from the title stack, joined by newlines, while ignoring empty entries
		mc.hTitleStack = append(mc.hTitleStack[:header.HLevel-1], mc.hTitle)
		mc.hTitle = ""
		for _, t := range mc.hTitleStack {
			if t != "" {
				mc.hTitle = strings.Join([]string{mc.hTitle, t}, "\n")
			}
		}
		mc.hTitle = strings.TrimLeft(mc.hTitle, "\n")
	}

	mc.hTitlePrepended = false
}

// onMDParagraph splits paragraph.
//
// format: ParagraphOpen/Inline/ParagraphClose
func (mc *markdownContext) onMDParagraph() {
	endAt := indexOfCloseTag(mc.tokens, mc.startAt)
	defer func() {
		mc.startAt = endAt + 1
	}()

	_, ok := mc.tokens[mc.startAt].(*markdown.ParagraphOpen)
	if !ok {
		return
	}

	inline, ok := mc.tokens[mc.startAt+1].(*markdown.Inline)
	if !ok {
		return
	}

	// 处理内联内容
	content := mc.splitInline(inline)

	// 更新行号
	mc.currentLine += strings.Count(content, "\n")

	// 添加到当前片段
	mc.joinSnippet(content)
}

// onMDQuote splits blockquote
//
// format: BlockquoteOpen/[Any]*/BlockquoteClose
func (mc *markdownContext) onMDQuote() {
	endAt := indexOfCloseTag(mc.tokens, mc.startAt)
	defer func() {
		mc.startAt = endAt + 1
	}()

	_, ok := mc.tokens[mc.startAt].(*markdown.BlockquoteOpen)
	if !ok {
		return
	}

	tmpMC := mc.clone(mc.startAt+1, endAt-1)
	tmpMC.hTitle = ""
	chunks := tmpMC.splitText()

	for _, chunk := range chunks {
		// 使用chunk的内容进行格式化
		formattedContent := formatWithIndent(chunk.Content, "> ")
		mc.joinSnippet(formattedContent)
	}

	mc.applyToChunks()
}

// onMDBulletList splits bullet list
//
// format: BulletListOpen/[ListItem]*/BulletListClose
func (mc *markdownContext) onMDBulletList() {
	mc.bulletList = true
	mc.orderedList = false

	mc.onMDList()
}

// onMDOrderedList splits ordered list
//
// format: BulletListOpen/[ListItem]*/BulletListClose
func (mc *markdownContext) onMDOrderedList() {
	mc.orderedList = true
	mc.bulletList = false
	mc.listOrder = 0

	mc.onMDList()
}

// onMDList splits ordered list or unordered list.
func (mc *markdownContext) onMDList() {
	endAt := indexOfCloseTag(mc.tokens, mc.startAt)
	defer func() {
		mc.startAt = endAt + 1
		mc.indentLevel--
	}()

	mc.indentLevel++

	// try move to ListItemOpen
	mc.startAt++

	// split list item with recursive
	tempMD := mc.clone(mc.startAt, endAt-1)
	tempChunks := tempMD.splitText()
	for _, chunk := range tempChunks {
		content := chunk.Content
		if tempMD.indentLevel > 1 {
			content = formatWithIndent(content, "  ")
		}
		mc.joinSnippet(content)
	}
}

// onMDListItem splits list item.
//
// format: ListItemOpen/[Any]*/ListItemClose
func (mc *markdownContext) onMDListItem() {
	endAt := indexOfCloseTag(mc.tokens, mc.startAt)
	defer func() {
		mc.startAt = endAt + 1
	}()

	_, ok := mc.tokens[mc.startAt].(*markdown.ListItemOpen)
	if !ok {
		return
	}

	// try move to ParagraphOpen
	mc.startAt++

	var prefix string
	if mc.orderedList {
		prefix = fmt.Sprintf("%d. ", mc.listOrder)
		mc.listOrder++
	} else if mc.bulletList {
		prefix = "- "
	}

	// split list item with recursive
	tempMD := mc.clone(mc.startAt, endAt-1)
	tempChunks := tempMD.splitText()
	for i, chunk := range tempChunks {
		content := chunk.Content
		if i == 0 {
			content = prefix + content
		} else {
			content = formatWithIndent(content, strings.Repeat(" ", len(prefix)))
		}
		mc.joinSnippet(content)
	}
}

// onMDTable splits table.
//
// format: TableOpen/THead/TBody/TableClose
func (mc *markdownContext) onMDTable() {
	endAt := indexOfCloseTag(mc.tokens, mc.startAt)
	defer func() {
		mc.startAt = endAt + 1
	}()

	_, ok := mc.tokens[mc.startAt].(*markdown.TableOpen)
	if !ok {
		return
	}

	// move to THead
	mc.startAt++
	headers := mc.onTableHead()

	// move to TBody
	mc.startAt++
	rows := mc.onTableBody()

	// 渲染表格内容
	tableContent := mc.renderTable(headers, rows)

	// 计算表格内容的行数
	tableLines := strings.Count(tableContent, "\n") + 1

	// 添加到当前片段
	if mc.joinTableRows {
		mc.joinSnippet(tableContent)
		// 更新行号，表格的每一行对应一行
		mc.currentLine += tableLines - 1 // -1 是因为当前行已经计算在内
	} else {
		// 分别处理每一行
		lines := strings.Split(tableContent, "\n")
		for _, line := range lines {
			if line != "" {
				mc.joinSnippet(line)
				// 每添加一行，行号加1
				mc.currentLine++
			}
		}
		// 因为每行都已经更新了行号，所以这里减去1，避免重复计算
		mc.currentLine--
	}
}

// onTableHead splits table head
//
// format: TheadOpen/TrOpen/[ThOpen/Inline/ThClose]*/TrClose/TheadClose
func (mc *markdownContext) onTableHead() []string {
	endAt := indexOfCloseTag(mc.tokens, mc.startAt)
	defer func() {
		mc.startAt = endAt + 1
	}()

	_, ok := mc.tokens[mc.startAt].(*markdown.TheadOpen)
	if !ok {
		return nil
	}

	// check TrOpen
	if _, ok := mc.tokens[mc.startAt+1].(*markdown.TrOpen); !ok {
		return nil
	}

	// move to TrOpen
	mc.startAt++

	var headers []string
	for {
		// check ThOpen
		if _, ok := mc.tokens[mc.startAt+1].(*markdown.ThOpen); !ok {
			break
		}

		// move to ThOpen
		mc.startAt++

		// move to Inline
		mc.startAt++
		inline, ok := mc.tokens[mc.startAt].(*markdown.Inline)
		if !ok {
			break
		}

		headers = append(headers, inline.Content)

		// move to ThClose
		mc.startAt++
	}

	return headers
}

// onTableBody splits table body
//
// format: TBodyOpen/TrOpen/[TdOpen/Inline/TdClose]*/TrClose/TBodyClose
func (mc *markdownContext) onTableBody() [][]string {
	endAt := indexOfCloseTag(mc.tokens, mc.startAt)
	defer func() {
		mc.startAt = endAt + 1
	}()

	var rows [][]string

	for {
		// check TrOpen
		if _, ok := mc.tokens[mc.startAt+1].(*markdown.TrOpen); !ok {
			return rows
		}

		var row []string
		// move to TrOpen
		mc.startAt++
		colIdx := 0
		for {
			// check TdOpen
			if _, ok := mc.tokens[mc.startAt+1].(*markdown.TdOpen); !ok {
				break
			}

			// move to TdOpen
			mc.startAt++

			// move to Inline
			mc.startAt++
			inline, ok := mc.tokens[mc.startAt].(*markdown.Inline)
			if !ok {
				break
			}

			row = append(row, inline.Content)

			// move to TdClose
			mc.startAt++
			colIdx++
		}

		rows = append(rows, row)
		// move to TrClose
		mc.startAt++
	}
}

// onMDCodeBlock splits indented code block.
func (mc *markdownContext) onMDCodeBlock() {
	defer func() {
		mc.startAt++
	}()

	if !mc.renderCodeBlocks {
		return
	}

	codeblock, ok := mc.tokens[mc.startAt].(*markdown.CodeBlock)
	if !ok {
		return
	}

	// CommonMark Spec 4.4: Indented Code Blocks
	// An indented code block is composed of one or more indented chunks
	// separated by blank lines. An indented chunk is a sequence of
	// non-blank lines, each preceded by four or more spaces of indentation.

	//nolint:gomnd
	codeblockMD := "\n" + formatWithIndent(codeblock.Content, strings.Repeat(" ", 4))

	// 更新行号，代码块的每一行对应一行
	mc.currentLine += strings.Count(codeblockMD, "\n")

	// adding this as a single snippet means that long codeblocks will be split
	// as text, i.e. they won't be properly wrapped. This is not ideal, but
	// matches was python langchain does.
	mc.joinSnippet(codeblockMD)
}

// onMDFence splits fenced code block.
func (mc *markdownContext) onMDFence() {
	defer func() {
		mc.startAt++
	}()

	if !mc.renderCodeBlocks {
		return
	}

	fence, ok := mc.tokens[mc.startAt].(*markdown.Fence)
	if !ok {
		return
	}

	fenceMD := fmt.Sprintf("\n```%s\n%s```\n", fence.Params, fence.Content)

	// 更新行号，代码块的每一行对应一行
	mc.currentLine += strings.Count(fenceMD, "\n")

	// adding this as a single snippet means that long fenced blocks will be split
	// as text, i.e. they won't be properly wrapped. This is not ideal, but matches
	// was python langchain does.
	mc.joinSnippet(fenceMD)
}

// onMDHr splits thematic break.
func (mc *markdownContext) onMDHr() {
	defer func() {
		mc.startAt++
	}()

	if _, ok := mc.tokens[mc.startAt].(*markdown.Hr); !ok {
		return
	}

	mc.joinSnippet("\n---")
}

// joinSnippet join sub snippet to current total snippet.
func (mc *markdownContext) joinSnippet(snippet string) {
	if mc.curSnippet == "" {
		mc.curSnippet = snippet
		return
	}

	// check whether current chunk exceeds chunk size, if so, apply to chunks
	if mc.lenFunc(mc.curSnippet+snippet) >= mc.chunkSize {
		mc.applyToChunks()
		mc.curSnippet = snippet
	} else {
		mc.curSnippet = fmt.Sprintf("%s\n%s", mc.curSnippet, snippet)
	}
}

// splitInline splits inline
//
// format: Link/Image/Text
//
//nolint:cyclop
func (mc *markdownContext) splitInline(inline *markdown.Inline) string {
	if len(inline.Children) == 0 || mc.useInlineContent {
		return inline.Content
	}

	var content string

	var currentLink *markdown.LinkOpen

	// CommonMark Spec 6: Inlines
	// - Soft linebreaks
	// - Hard linebreaks
	// - Emphasis and strong emphasis
	// - Text
	// - Raw HTML
	// - Code spans
	// - Links
	// - Images
	// - Autolinks
	for _, child := range inline.Children {
		switch token := child.(type) {
		case *markdown.Softbreak:
			content += "\n"
		case *markdown.Hardbreak:
			// CommonMark Spec 6.7: Hard line breaks
			// For a more visible alternative, a backslash before the line
			// ending may be used instead of two or more spaces
			content += "\\\n"
		case *markdown.StrongOpen, *markdown.StrongClose:
			content += "**"
		case *markdown.EmphasisOpen, *markdown.EmphasisClose:
			content += "*"
		case *markdown.StrikethroughOpen, *markdown.StrikethroughClose:
			content += "~~"
		case *markdown.Text:
			content += token.Content
		case *markdown.HTMLInline:
			content += token.Content
		case *markdown.CodeInline:
			content += fmt.Sprintf("`%s`", token.Content)
		case *markdown.LinkOpen:
			content += "["
			// CommonMark Spec 6.3:
			// Links may not contain other links, at any level of nesting.
			// If multiple otherwise valid link definitions appear nested
			// inside each other, the inner-most definition is used.
			currentLink = token
		case *markdown.LinkClose:
			content += mc.inlineOnLinkClose(currentLink)
		case *markdown.Image:
			content += mc.inlineOnImage(token)
		}
	}

	return content
}

func (mc *markdownContext) inlineOnLinkClose(link *markdown.LinkOpen) string {
	//switch {
	//case link.Href == "":
	//	return "]()"
	//case link.Title != "":
	//	return fmt.Sprintf(`](%s "%s")`, link.Href, link.Title)
	//default:
	//	return fmt.Sprintf(`](%s)`, link.Href)
	//}

	label := "image"
	if link.Title != "" {
		label = link.Title
	}
	return fmt.Sprintf(`%s]`, label)
}

func (mc *markdownContext) inlineOnImage(image *markdown.Image) string {
	//var label string
	//
	//// CommonMark spec 6.4: Images
	//// Though this spec is concerned with parsing, not rendering, it is
	//// recommended that in rendering to HTML, only the plain string content
	//// of the image description be used.
	//for _, token := range image.Tokens {
	//	if text, ok := token.(*markdown.Text); ok {
	//		label += text.Content
	//	}
	//}
	//
	//if image.Title == "" {
	//	return fmt.Sprintf(`![%s](%s)`, label, image.Src)
	//}
	//
	//return fmt.Sprintf(`![%s](%s "%s")`, label, image.Src, image.Title)

	label := "image"
	if image.Title != "" {
		label = image.Title
	}
	return fmt.Sprintf(`[%s]`, label)
}

// closeTypes represents the close operation type for each open operation type.
var closeTypes = map[reflect.Type]reflect.Type{ //nolint:gochecknoglobals
	reflect.TypeOf(&markdown.HeadingOpen{}):     reflect.TypeOf(&markdown.HeadingClose{}),
	reflect.TypeOf(&markdown.BulletListOpen{}):  reflect.TypeOf(&markdown.BulletListClose{}),
	reflect.TypeOf(&markdown.OrderedListOpen{}): reflect.TypeOf(&markdown.OrderedListClose{}),
	reflect.TypeOf(&markdown.ParagraphOpen{}):   reflect.TypeOf(&markdown.ParagraphClose{}),
	reflect.TypeOf(&markdown.BlockquoteOpen{}):  reflect.TypeOf(&markdown.BlockquoteClose{}),
	reflect.TypeOf(&markdown.ListItemOpen{}):    reflect.TypeOf(&markdown.ListItemClose{}),
	reflect.TypeOf(&markdown.TableOpen{}):       reflect.TypeOf(&markdown.TableClose{}),
	reflect.TypeOf(&markdown.TheadOpen{}):       reflect.TypeOf(&markdown.TheadClose{}),
	reflect.TypeOf(&markdown.TbodyOpen{}):       reflect.TypeOf(&markdown.TbodyClose{}),
}

// indexOfCloseTag returns the index of the close tag for the open tag at startAt.
func indexOfCloseTag(tokens []markdown.Token, startAt int) int {
	sameCount := 0
	openType := reflect.ValueOf(tokens[startAt]).Type()
	closeType := closeTypes[openType]

	// some tokens (like Hr or Fence) are singular, i.e. they don't have a close type.
	if closeType == nil {
		return startAt
	}

	idx := startAt + 1
	for ; idx < len(tokens); idx++ {
		cur := reflect.ValueOf(tokens[idx]).Type()

		if openType == cur {
			sameCount++
		}

		if closeType == cur {
			if sameCount == 0 {
				break
			}
			sameCount--
		}
	}

	return idx
}

// repeatString repeats the initChar for count times.
func repeatString(count int, initChar string) string {
	var s string
	for i := 0; i < count; i++ {
		s += initChar
	}
	return s
}

// formatWithIndent.
func formatWithIndent(value, mark string) string {
	lines := strings.Split(value, "\n")
	for i, line := range lines {
		lines[i] = fmt.Sprintf("%s%s", mark, line)
	}
	return strings.Join(lines, "\n")
}

// tableHeaderInMarkdown represents the Markdown format for table header.
func tableHeaderInMarkdown(header []string) string {
	headerMD := tableRowInMarkdown(header)

	// add separator
	var separators []string
	for i := 0; i < len(header); i++ {
		separators = append(separators, "---")
	}

	headerMD += "\n" // add new line
	headerMD += tableRowInMarkdown(separators)

	return headerMD
}

// tableRowInMarkdown represents the Markdown format for table row.
func tableRowInMarkdown(row []string) string {
	var line string
	for i := range row {
		line += fmt.Sprintf("| %s ", row[i])
		if i == len(row)-1 {
			line += "|"
		}
	}

	return line
}

// renderTable renders a table in Markdown format
func (mc *markdownContext) renderTable(headers []string, rows [][]string) string {
	var sb strings.Builder

	// 检查是否有标题
	hasHeaders := len(headers) > 0
	if !hasHeaders && len(rows) > 0 {
		// 如果没有标题但有行，使用第一行作为标题
		headers = rows[0]
		rows = rows[1:]
	}

	// 渲染标题行
	if len(headers) > 0 {
		sb.WriteString("| ")
		for _, header := range headers {
			sb.WriteString(header)
			sb.WriteString(" | ")
		}
		sb.WriteString("\n")

		// 渲染分隔行
		sb.WriteString("| ")
		for range headers {
			sb.WriteString("--- | ")
		}
		sb.WriteString("\n")
	}

	// 渲染数据行
	for _, row := range rows {
		sb.WriteString("| ")
		for _, cell := range row {
			sb.WriteString(cell)
			sb.WriteString(" | ")
		}
		sb.WriteString("\n")
	}

	return sb.String()
}

// NewMarkdownSplitter creates a new markdown splitter adapter
func NewMarkdownSplitter(chunkSize, chunkOverlap int) LocalSplitterAdapter {
	secondSplitter := NewRecursiveCharacter(WithChunkSize(chunkSize), WithChunkOverlap(chunkOverlap))

	// 创建MarkdownTextSplitter
	splitter := NewMarkdownTextSplitter(
		WithChunkSize(chunkSize),
		WithChunkOverlap(chunkOverlap),
		WithSecondSplitter(secondSplitter),
		WithHeadingHierarchy(true),
	)

	// 创建适配器
	return NewAdapter(splitter, map[model.LanguageType]bool{
		model.LanguageMarkdown: true,
	})
}
