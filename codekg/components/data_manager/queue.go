package data_manager

import (
	"context"
	"sync"

	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/tasks"
	"ide/ckg/codekg/model"

	"ide/ckg/codekg/components/logs"
)

// submitIndexVirtualTaskToQueue 提交索引任务到队列，按配置优先提交到批量索引队列，否则提交到单文件索引队列
func (m *manager) submitIndexVirtualTaskToQueue(ctx context.Context, wg *sync.WaitGroup, userID string, storage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage, projectID string, cli knowledgebase.Client, uriStatus *model.URIStatus, progress *projectBuildProgress, virtualProjectConfig *model.VirtualProjectConfig) {
	var pushed bool
	var fallbackToSingleIndex = false

	indexTaskAttribute := &model.IndexTaskAttribute{
		ChunkingMethod:      virtualProjectConfig.ChunkingMethodBaseline,
		ChunkingSize:        virtualProjectConfig.ChunkingSize,
		ChunkingOverlap:     virtualProjectConfig.ChunkingOverlap,
		RemoveEntityContent: virtualProjectConfig.RemoveEntityContent,
	}

	useV2Ignore := m.config.IsFeatureEnabled(ctx, userID, ckg_config.UseV2Ignore)
	wg.Add(1)
	// 检查是否启用了批量索引功能
	if m.config.IsIndexFeatureEnabled(ctx, userID, ckg_config.EnableBatchIndex) {
		attr := model.IndexAttribute{
			ChunkingMethod: storage.GetChunkingMethod(),
			EmbeddingModel: embeddingStorage.GetEmbeddingModel(),
		}

		// 获取或创建项目嵌入队列管理器
		mgr, _ := m.batchEmbeddingMgr.GetOrCreateProjectEmbeddingQueueMgr(
			userID,
			projectID,
			m.config,
			storage,
			embeddingStorage,
			cli,
			attr,
			m.batchIndexVirtualEmbeddingTaskQueue,
			tasks.EmbeddingQueueMgrSetting{},
		)

		// 推送批量索引分割任务
		pushed = m.batchIndexVirtualSplitTaskQueue.Push(ctx, tasks.NewBatchLocalIndexSplitFileTask(
			userID,
			projectID,
			model.ProjectTypeVirtual,
			m.config,
			uriStatus,
			attr,
			indexTaskAttribute,
			cli,
			useV2Ignore,
			m.ignoreService,
			m.ignoreServiceV2,
			m.fs,
			func(ctx context.Context, uriData []*model.URIData, embeddingData []*model.EmbeddingData, todos []*model.BatchEmbeddingMsg) {
				defer func() {
					wg.Done()
					if progress != nil {
						progress.builtFiles.Add(1)
					}
				}()

				err := data_storage.WriteChunkingAndEmbeddingData(ctx, storage, embeddingStorage, uriStatus, uriData, embeddingData)
				if err != nil {
					// 日志已经在 WriteChunkingAndEmbeddingData 妥善输出了
					if progress != nil {
						progress.failedIndexFiles.Add(1)
					}
					return
				}

				if progress != nil {
					progress.successIndexFiles.Add(1)
				}
				logs.CtxDebug(ctx, "virtual project file %s split succeed", uriStatus.AbsPath)

				// 切分结束，切分结果 & Abase 中 Embedding 缓存已分别写入本地实体库 & 向量库。
				// 将未命中缓存的 Embedding Content 写入到队列
				for _, todo := range todos {
					mgr.Push(ctx, todo)
				}
			}), 1)

		if !pushed {
			fallbackToSingleIndex = true
		}
	}

	// 如果批量索引未启用或推送失败，则回退到单文件索引
	if fallbackToSingleIndex || !m.config.IsIndexFeatureEnabled(ctx, userID, ckg_config.EnableBatchIndex) {
		pushed = m.localIndexFileTaskQueue.Push(ctx, tasks.NewLocalIndexFileTasks(
			storage,
			embeddingStorage,
			userID,
			ckg_metrics.EventNameCKGInit,
			uriStatus,
			func(ctx context.Context, uriData []*model.URIData, embeddingData []*model.EmbeddingData) {
				defer func() {
					wg.Done()
					if progress != nil {
						progress.builtFiles.Add(1)
					}
				}()

				localIndexEvent := ckg_metrics.GetEvent(ctx)
				if len(uriData) == 0 || storage.IsDeleted() || embeddingStorage.IsDeleted() {
					localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmptyUriData, len(uriData) == 0)
					localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEntityStorageDeleted, storage.IsDeleted())
					localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbeddingStorageDeleted, embeddingStorage.IsDeleted())
					logs.CtxWarn(ctx, "virtual project file init failed %s, uriData size: %d, storage: %v, embedding storage: %v",
						uriStatus.AbsPath, len(uriData), storage.IsDeleted(), embeddingStorage.IsDeleted())
					if progress != nil {
						progress.failedIndexFiles.Add(1)
					}
					return
				}

				if !storage.IsExistsOnDisk() || !embeddingStorage.IsExistsOnDisk() {
					localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEntityStorageNotOnDisk, !storage.IsExistsOnDisk())
					localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbeddingStorageNotOnDisk, !embeddingStorage.IsExistsOnDisk())
					logs.CtxWarn(ctx, "virtual project file init failed, storage is not on disk: %v, embedding storage is not on disk: %v",
						!storage.IsExistsOnDisk(), !embeddingStorage.IsExistsOnDisk())
					return
				}

				// 插入实体数据
				for _, data := range uriData {
					err := data_storage.InsertStorageWithEmbedding(ctx, storage, embeddingStorage, data)
					if err != nil {
						// localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexInsertStorageErr, true)
						logs.CtxError(ctx, "insertStorage err is %v path is %v", err, uriStatus.AbsPath)
						if progress != nil {
							progress.failedIndexFiles.Add(1)
						}
						return
					}
				}

				for _, data := range uriData {
					if err := data_storage.MarkFileAsEmbedded(ctx, storage, data); err != nil {
						// localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexMarkFileAsEmbeddedErr, true)
						logs.CtxError(ctx, "updateFileEmbeddingStatus err is %v path is %s", err, uriStatus.AbsPath)
						if progress != nil {
							progress.failedIndexFiles.Add(1)
						}
						return
					}
				}

				// 写入 embedding to entity 的映射关系
				if len(embeddingData) == 0 {
					// localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmptyEmbeddingData, true)
					if progress != nil {
						progress.failedIndexFiles.Add(1)
					}
					return
				}
				for _, eData := range embeddingData {
					if err := data_storage.InsertEmbeddingStorage(ctx, storage, embeddingStorage, eData); err != nil {
						localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbeddingInsertFailed, true)
						if progress != nil {
							progress.failedIndexFiles.Add(1)
						}
						logs.CtxError(ctx, "insertEmbeddingStorage err is %v path is %v", err, uriStatus.AbsPath)
						return
					}
				}

				for _, data := range uriData {
					if err := storage.UpdateURIMetaStatus(ctx, storage.GetConn(), data.Status.AbsPath, int(model.BuildStatusFinished)); err != nil {
						// localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexMarkFileAsEmbeddedErr, true)
						logs.CtxError(ctx, "MarkURIMetaAsIndexedAndClearContent err is %v path is %s", err, uriStatus.AbsPath)
						if progress != nil {
							progress.failedIndexFiles.Add(1)
						}
					}
				}

				if progress != nil {
					progress.successIndexFiles.Add(1)
				}
				logs.CtxTrace(ctx, "virtual project file %s init succeed", uriStatus.AbsPath)
				localIndexEvent.Report(ctx, true)
			},
			cli,
			projectID,
			model.ProjectTypeVirtual,
			useV2Ignore,
			m.ignoreService,
			m.ignoreServiceV2,
			m.config,
			m.fs,
			indexTaskAttribute,
		), 1)
	}

	if !pushed {
		wg.Done()
		if progress != nil {
			progress.builtFiles.Add(1)
		}
	}
}
