import {beforeAll, describe, expect, it} from "vitest";
import {TreeSitterParser} from "./treesitter/parser";
import {findDescendantNodeForRange, LanguageIdEnum} from "./share";
import {createDocument} from "../../utils/common";

describe("test_findDescendantNodeForRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
func main() {
  if i > 0 {
    if i > 0 {
      fmt.Println()
    }
  }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);
    const document = createDocument(docText, LanguageIdEnum.Go, "file:///test.go");
    const range = {
      start: {line: 4, character: 6},
      end: {line: 4, character: 17}
    };

    const result = findDescendantNodeForRange(tree.rootNode, document, range);

    expect(result.text).toEqual("fmt.Println");
  });
});