package user_storage

import (
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/multierr"
	"ide/ckg/codekg/components/data_storage/cipher"
	"ide/ckg/codekg/components/data_storage/consts"
	bizErr "ide/ckg/codekg/components/error"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/model"
	"os"
	"path/filepath"
	"strings"
	"time"

	"code.byted.org/gopkg/lang/slices"
	sqlite3 "code.byted.org/ide/go-sqlcipher"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
	gormLog "gorm.io/gorm/logger"
	"ide/ckg/codekg/components/logs"
)

type UserStorage struct {
	db *gorm.DB
}

func CreateEnvStorage(ctx context.Context, storagePath model.URI) (*UserStorage, error) {
	if len(storagePath) == 0 {
		errMsg := "CreateEnvStorage storagePath is empty"
		logs.CtxError(ctx, errMsg)
		return nil, errors.New(errMsg)
	}

	projectStorage := filepath.Join(storagePath, fmt.Sprintf("env_codekg.db?_pragma_key=%s", consts.PragmaKey))
	projectStorageWithoutCipher := filepath.Join(storagePath, "env_codekg.db")
	var finalErr error
	var retry = 3
	for i := 0; i < retry; i++ {
		// try open a ciphered db first
		projectDB, err := gorm.Open(cipher.Open(projectStorage), &gorm.Config{Logger: gormLog.Default.LogMode(gormLog.Silent)})
		// 如果创建成功，就退出
		if err == nil {
			if err = projectDB.AutoMigrate(&StorageProjectID{}, &StorageUserInformation{}); err != nil {
				logs.CtxError(ctx, "[CreateEnvStorage] auto migrate error: %v", err)
				finalErr = multierr.Combine(finalErr, err)
				continue
			}
			return &UserStorage{db: projectDB}, nil
		}
		sqliteError := new(sqlite3.Error)
		if errors.As(err, sqliteError) && sqliteError.Code == sqlite3.ErrNotADB {
			// 如果没有 cipher，尝试无 cipher 打开
			projectDB, err = gorm.Open(cipher.Open(projectStorageWithoutCipher), &gorm.Config{Logger: gormLog.Default.LogMode(gormLog.Silent)})
			if err == nil {
				if err = projectDB.AutoMigrate(&StorageProjectID{}, &StorageUserInformation{}); err != nil {
					logs.CtxError(ctx, "[CreateEnvStorage] no cipher auto migrate error: %v", err)
					finalErr = multierr.Combine(finalErr, err)
					continue
				}
				return &UserStorage{db: projectDB}, nil
			}
			logs.CtxError(ctx, "[CreateEnvStorage] open db still error: %v", err)
			finalErr = multierr.Append(finalErr, errors.WithMessagef(err, "[CreateEnvStorage] open db still error: %s", storagePath))
		}
		if strings.Contains(err.Error(), "file is not a database") || strings.Contains(err.Error(), "is a directory") {
			var fileInfo os.FileInfo
			if fileInfo, err = file_system.RealFS.Stat(projectStorageWithoutCipher); err != nil {
				logs.CtxError(ctx, "[CreateEnvStorage] stat error: %v", err)
				if os.IsNotExist(err) {
					finalErr = multierr.Append(finalErr, errors.WithMessagef(err, "[CreateEnvStorage] file %s does not exist", projectStorageWithoutCipher))
				} else {
					finalErr = multierr.Append(finalErr, errors.WithMessagef(err, "[CreateEnvStorage] stat file %s error", projectStorageWithoutCipher))
				}
				continue
			}
			if fileInfo.IsDir() {
				if err = file_system.RealFS.RemoveAll(projectStorageWithoutCipher); err != nil {
					logs.CtxError(ctx, "[CreateEnvStorage] RemoveAll error: %v", err)
					finalErr = multierr.Append(finalErr, errors.WithMessagef(err, "[CreateEnvStorage] db (dir) %s remove error", projectStorageWithoutCipher))
					continue
				}
			} else {
				if err = file_system.RealFS.Remove(projectStorageWithoutCipher); err != nil {
					logs.CtxError(ctx, "[CreateEnvStorage] Remove error: %v", err)
					finalErr = multierr.Append(finalErr, errors.WithMessagef(err, "[CreateEnvStorage] db (file) %s remove error", projectStorageWithoutCipher))
					continue
				}
			}
		} else {
			logs.CtxError(ctx, "[CreateEnvStorage] open db failed, path: %s, err: %v", storagePath, err)
			finalErr = multierr.Append(finalErr, errors.WithMessagef(err, "[CreateEnvStorage] open db failed, path: %s", storagePath))
		}
	}
	return nil, finalErr
}

func TryOpenEnvStorage(ctx context.Context, storagePath string) (*UserStorage, string, bool) {
	dbFilePath := filepath.Join(storagePath, "env_codekg.db")
	_, err := os.Stat(dbFilePath)
	if err != nil {
		if errors.Is(err, os.ErrNotExist) {
			return nil, dbFilePath, false
		}
		logs.CtxInfo(ctx, "[TryOpenEnvStorage] dbFilePath %s, err: %v", dbFilePath, err)
		return nil, dbFilePath, false
	}

	db, err := gorm.Open(cipher.Open(fmt.Sprintf("%s?_pragma_key=%s", dbFilePath, consts.PragmaKey)), &gorm.Config{
		Logger: gormLog.Default.LogMode(gormLog.Silent),
	})
	if err != nil {
		logs.CtxInfo(ctx, "[TryOpenEnvStorage] dbFilePath %s, err: %v", dbFilePath, err)
		return nil, dbFilePath, false
	}

	err = db.AutoMigrate(&StorageProjectID{}, &StorageUserInformation{})
	if err != nil {
		logs.CtxInfo(ctx, "[TryOpenEnvStorage] dbFilePath %s, err: %v", dbFilePath, err)
		return nil, dbFilePath, false
	}

	return &UserStorage{
		db: db,
	}, dbFilePath, true
}

func (es *UserStorage) GetConn() *gorm.DB {
	return es.db
}

func (es *UserStorage) GetAllUserMeta(ctx context.Context, conn *gorm.DB) ([]StorageUserInformation, error) {
	var result []StorageUserInformation
	if err := conn.WithContext(ctx).WithContext(ctx).Find(&result).Error; err != nil {
		return nil, errors.WithMessagef(err, "error get all user meta")
	}

	return result, nil
}

func (es *UserStorage) GetUserInformation(ctx context.Context, conn *gorm.DB, userID string) (*StorageUserInformation, error) {
	var userValue StorageUserInformation
	if err := conn.WithContext(ctx).WithContext(ctx).First(&userValue, "user_id =?", userID).Error; err != nil {
		return nil, err
	}
	return &userValue, nil
}

func (es *UserStorage) GetProjectPathByID(ctx context.Context, conn *gorm.DB, projectID int) (error, string) {
	var projectValue StorageProjectID
	if err := conn.WithContext(ctx).WithContext(ctx).First(&projectValue, "id = ?", projectID).Error; err != nil {
		return err, ""
	}
	return nil, projectValue.ProjectPath
}

func (es *UserStorage) GetProjectIDByProjectPath(ctx context.Context, conn *gorm.DB, projectPath string) (*StorageProjectID, error) {
	var projectValue StorageProjectID
	if err := conn.WithContext(ctx).WithContext(ctx).First(&projectValue, "project_id = ?", projectPath).Error; err != nil {
		return nil, err
	}
	return &projectValue, nil
}

func (es *UserStorage) DeleteProjectID(ctx context.Context, conn *gorm.DB, projectID string, userID string) error {
	err := conn.WithContext(ctx).WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		projectObj := StorageProjectID{}
		if err := tx.First(&projectObj, "project_id = ?", projectID).Error; err != nil {
			return err
		}
		if err := tx.Where("project_id = ?", projectID).Delete(&projectObj).Error; err != nil {
			return err
		}
		userInfo := StorageUserInformation{}
		if err := tx.First(&userInfo, "user_id = ?", userID).Error; err != nil {
			return err
		}
		var projectIDs []int
		err := json.Unmarshal([]byte(userInfo.ProjectIDs), &projectIDs)
		if err != nil {
			return errors.WithMessage(bizErr.ErrUnmarshal, "Unmarshal err")
		}
		_, index, _ := lo.FindIndexOf(projectIDs, func(n int) bool {
			return n == projectObj.ID
		})
		if index != -1 {
			projectIDs = append(projectIDs[:index], projectIDs[index+1:]...)
		}
		jsonNumbers, err := json.Marshal(projectIDs)
		if err != nil {
			return bizErr.ErrMarshal
		}
		if err = tx.Model(&StorageUserInformation{}).Where("user_id = ?", userID).Update("project_ids", jsonNumbers).Error; err != nil {
			logs.CtxError(ctx, "Update userInfo err is %v", err)
			return err
		}
		return nil
	})
	return err
}

func (es *UserStorage) UpsertProjectID(ctx context.Context, conn *gorm.DB, projectID string, userID string,
	codekgDBPath, embeddingDBPath, chunkingMethod, embeddingModel string,
	ckgVersion string, embeddingStorageT consts.EmbeddingStorageType, storageVersion consts.StorageVersion, projectType model.ProjectType, projectURI string) error {
	err := conn.WithContext(ctx).WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var projectObj StorageProjectID
		if err := tx.Where("project_id = ?", projectID).First(&projectObj).Error; err != nil {
			// 说明没找到
			now := time.Now()
			projectObj = StorageProjectID{
				ProjectPath:     projectID,
				CodeKGDBPath:    codekgDBPath,
				EmbeddingDBPath: embeddingDBPath,
				EmbeddingDBType: embeddingStorageT.Int(),
				CKGVersion:      ckgVersion,
				ChunkingMethod:  chunkingMethod,
				EmbeddingModel:  embeddingModel,
				DBVersion:       int(storageVersion),
				ProjectURI:      projectURI,
				ProjectType:     int32(projectType),
				CreateTime:      &now,
			}
			logs.CtxInfo(ctx, "[UpsertProjectID] insert new project id record: %+v", projectObj)
			if err := tx.Where("project_id = ?", projectID).Assign(&projectObj).FirstOrCreate(&projectObj).Error; err != nil {
				return err
			}
			if err := tx.Where("project_id = ?", projectID).First(&projectObj).Error; err != nil {
				logs.CtxError(ctx, "First err is %v", err)
				return err
			}
		} else {
			// 找到了，更新
			logs.CtxInfo(ctx, "[UpsertProjectID] update project id record. codekg_db_path: %s, embedding_db_path: %s, embedding_storage_type: %v, ckg_version: %s",
				codekgDBPath, embeddingDBPath, embeddingStorageT.Int(), ckgVersion)
			if err := tx.Model(&StorageProjectID{}).Where("project_id = ?", projectID).
				Update("codekg_db_path", codekgDBPath).
				Update("embedding_db_path", embeddingDBPath).
				Update("embedding_db_type", embeddingStorageT.Int()).
				Update("chunking_method", chunkingMethod).
				Update("embedding_model", embeddingModel).
				Update("ckg_version", ckgVersion).
				Update("db_version", int(storageVersion)).
				Update("project_type", int32(projectType)).
				Update("project_uri", projectURI).Error; err != nil {
				logs.CtxError(ctx, "Updates err is %v", err)
				return err
			}
		}
		var userInfo StorageUserInformation
		if err := tx.Where("user_id = ?", userID).First(&userInfo).Error; err != nil {
			logs.CtxError(ctx, "First err is %v", err)
			return err
		}
		var projectIDs []int
		if len(userInfo.ProjectIDs) != 0 {
			err := json.Unmarshal([]byte(userInfo.ProjectIDs), &projectIDs)
			if err != nil {
				return errors.WithMessage(bizErr.ErrUnmarshal, "Unmarshal err")
			}
		}
		if lo.Contains(projectIDs, projectObj.ID) {
			return nil
		}
		projectIDs = append(projectIDs, projectObj.ID)
		jsonNumbers, err := json.Marshal(projectIDs)
		if err != nil {
			return bizErr.ErrMarshal
		}
		if err = tx.Model(&StorageUserInformation{}).Where("user_id = ?", userID).Update("project_ids", jsonNumbers).Error; err != nil {
			logs.CtxError(ctx, "Update userInformation err is %v", err)
			return err
		}
		return nil
	})
	return err
}

func (es *UserStorage) UpsertUserInformation(ctx context.Context, conn *gorm.DB, userID string, data *StorageUserInformation) error {
	result := StorageUserInformation{}
	if err := conn.WithContext(ctx).Where("user_id = ?", userID).Assign(data).FirstOrCreate(&result).Error; err != nil {
		return err
	}
	return nil
}

func (es *UserStorage) UpsertProjectInfo(ctx context.Context, conn *gorm.DB, project *StorageProjectID) error {
	result := StorageProjectID{}
	// 确保CreateTime字段已设置
	if project.CreateTime == nil {
		now := time.Now()
		project.CreateTime = &now
	}
	if err := conn.WithContext(ctx).Where("project_id =?", project.ProjectPath).Assign(&StorageProjectID{
		ProjectPath:     project.ProjectPath,
		CodeKGDBPath:    project.CodeKGDBPath,
		EmbeddingDBPath: project.EmbeddingDBPath,
		EmbeddingDBType: project.EmbeddingDBType,
		ChunkingMethod:  project.ChunkingMethod,
		EmbeddingModel:  project.EmbeddingModel,
		CKGVersion:      project.CKGVersion,
		DBVersion:       project.DBVersion,
		CreateTime:      project.CreateTime,
		ProjectURI:      project.ProjectURI,
		ProjectType:     project.ProjectType,
	}).FirstOrCreate(&result).Error; err != nil {
		return err
	}
	return nil
}

func (es *UserStorage) GetStorageFromUserStorage(ctx context.Context, projectId model.URI) (string, error) {
	storageProjectId, err := es.GetProjectIDByProjectPath(ctx, es.GetConn(), projectId)
	if err != nil {
		logs.CtxInfo(ctx, "[GetStorageFromUserStorage] GetProjectIDByProjectPath err is %v", err)
		return "", errors.WithMessagef(err, "[GetStorageFromUserStorage] get project id from user storage failed")
	}
	if len(storageProjectId.CodeKGDBPath) != 0 {
		if _, err := file_system.RealFS.Stat(storageProjectId.CodeKGDBPath); err != nil {
			if os.IsNotExist(err) {
				logs.CtxWarn(ctx, "[GetStorageFromUserStorage] codekg db not existed at %s, err: %v", storageProjectId.CodeKGDBPath, err)
			}
			return "", errors.WithMessagef(err, "[GetStorageFromUserStorage] codekg db stat err")
		}
	}
	return storageProjectId.CodeKGDBPath, nil
}

// GetEmbeddingStorageFromUserStorage 传入的 t 如果和数据库中不一致，则返回错误，表示数据库需要强制更新
// 此处 t 最终来源于 command arg，可由 feature gate 从插件 / IDE 侧控制。
func (es *UserStorage) GetEmbeddingStorageFromUserStorage(ctx context.Context, projectId model.URI, t consts.EmbeddingStorageType) (string, error) {
	storageProjectId, err := es.GetProjectIDByProjectPath(ctx, es.GetConn(), projectId)
	if err != nil {
		logs.CtxInfo(ctx, "[GetEmbeddingStorageFromUserStorage] GetProjectIDByProjectPath err is %v", err)
		return "", errors.WithMessagef(err, "[GetEmbeddingStorageFromUserStorage] get project id from user storage failed")
	}
	logs.CtxInfo(ctx, "[GetEmbeddingStorageFromUserStorage] embedding db type is %d", storageProjectId.EmbeddingDBType)
	if t.Int() != storageProjectId.EmbeddingDBType {
		logs.CtxInfo(ctx, "[GetEmbeddingStorageFromUserStorage] embedding db type not matched, type in env storage: %d, curr type: %s", storageProjectId.EmbeddingDBType, t)
		return "", errors.Errorf("[GetEmbeddingStorageFromUserStorage] embedding db type not matched, type in env storage: %d, curr type: %s", storageProjectId.EmbeddingDBType, t)
	}
	if len(storageProjectId.EmbeddingDBPath) != 0 {
		if _, err := file_system.RealFS.Stat(storageProjectId.EmbeddingDBPath); err != nil {
			if os.IsNotExist(err) {
				logs.CtxWarn(ctx, "[GetEmbeddingStorageFromUserStorage] embedding db not existed at %s, err: %v", storageProjectId.EmbeddingDBPath, err)
			}
			return "", errors.WithMessagef(err, "[GetEmbeddingStorageFromUserStorage] embedding db stat err")
		}
	}
	return storageProjectId.EmbeddingDBPath, nil
}

// RemoveRedundantProjectByStoragePath 用于从 env_codekg.db 中清除 “project_id 为 projectId 中的那些数据库路径对不上的 project_id 记录”
// 方便后续调用 RemoveRedundantStorage 在磁盘上清除对应 db
func (es *UserStorage) RemoveRedundantProjectByStoragePath(ctx context.Context, conn *gorm.DB, userId, projectId, entityStoragePath, embeddingStoragePath model.URI) error {
	var projects []StorageProjectID
	err := conn.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 删除 project_id 表中信息
		if err := tx.Find(&projects, "project_id = ?", projectId).Error; err != nil {
			logs.CtxWarn(ctx, "[RemoveRedundantProjectByStoragePath] find project err is %v", err)
			return err
		}
		var idOfProjectToBeDeleted []int
		for _, project := range projects {
			if project.CodeKGDBPath != entityStoragePath && project.EmbeddingDBPath != embeddingStoragePath {
				idOfProjectToBeDeleted = append(idOfProjectToBeDeleted, project.ID)
			}
		}
		if err := tx.Where("project_id IN (?)", idOfProjectToBeDeleted).Delete(&StorageProjectID{}).Error; err != nil {
			logs.CtxWarn(ctx, "[RemoveRedundantProjectByStoragePath] delete redundant project record (%v) err is %v", idOfProjectToBeDeleted, err)
			return err
		}

		// 同步 user_information 中信息
		var userInfo StorageUserInformation
		if err := tx.Where("user_id = ?", userId).First(&userInfo).Error; err != nil {
			logs.CtxError(ctx, "First err is %v", err)
			return err
		}
		var projectIDs []int
		if len(userInfo.ProjectIDs) != 0 {
			err := json.Unmarshal([]byte(userInfo.ProjectIDs), &projectIDs)
			if err != nil {
				return errors.WithMessage(bizErr.ErrUnmarshal, "Unmarshal err")
			}
		}
		newProjectIds := slices.RemoveAll(projectIDs, idOfProjectToBeDeleted)
		jsonNumbers, err := json.Marshal(newProjectIds)
		if err != nil {
			return bizErr.ErrMarshal
		}
		if err = tx.Model(&StorageUserInformation{}).Where("user_id = ?", userId).Update("project_ids", jsonNumbers).Error; err != nil {
			logs.CtxError(ctx, "Update userInformation err is %v", err)
			return err
		}
		return nil
	})
	return err
}

func (es *UserStorage) ListAllProjects(ctx context.Context, conn *gorm.DB) ([]*StorageProjectID, error) {
	projects := make([]*StorageProjectID, 0)
	if err := conn.WithContext(ctx).Model(&StorageProjectID{}).Find(&projects).Error; err != nil {
		logs.CtxInfo(ctx, "[ListAllProjects] get project err is %v", err)
		return nil, errors.WithMessagef(err, "[ListAllProjects] get project err")
	}
	return projects, nil
}

func (es *UserStorage) RemoveRedundantStorage(ctx context.Context, conn *gorm.DB, storagePath model.URI) error {
	entries, err := file_system.RealFS.ReadDir(storagePath)
	if err != nil {
		return errors.WithMessagef(err, "[RemoveRedundantStorage] walk err, path: %s", storagePath)
	}
	allDBPaths := lo.Map( // 获取所有以 .db 结尾的文件，只遍历一层。排除 env_codekg.db。
		lo.Filter(entries, func(e os.DirEntry, _ int) bool {
			return e.Name() != "env_codekg.db" && strings.HasSuffix(e.Name(), ".db")
		}),
		func(e os.DirEntry, _ int) string { return filepath.Join(storagePath, e.Name()) })

	var projects []StorageProjectID
	if err := conn.WithContext(ctx).Model(&StorageProjectID{}).Find(&projects).Error; err != nil {
		logs.CtxInfo(ctx, "[RemoveRedundantStorage] get project err is %v", err)
		return errors.WithMessagef(err, "[RemoveRedundantStorage] get project err")
	}

	// 后续逻辑不报错，不影响外部逻辑
	dbInUse := make(map[string]bool)
	for _, proj := range projects {
		dbInUse[proj.CodeKGDBPath] = true
		dbInUse[proj.EmbeddingDBPath] = true
	}
	deletedDBPaths := make([]string, 0)
	for _, path := range allDBPaths {
		if _, ok := dbInUse[path]; ok {
			logs.CtxDebug(ctx, "[RemoveRedundantStorage] db is in use %s", path)
			continue
		}
		// 再次校验是否存在该文件
		var fileInfo os.FileInfo
		if fileInfo, err = file_system.RealFS.Stat(path); err != nil {
			if os.IsNotExist(err) {
				logs.CtxWarn(ctx, "[RemoveRedundantStorage] db not existed %s", path)
			} else {
				logs.CtxError(ctx, "[RemoveRedundantStorage] db stat unknown err %v", err)
			}
			continue
		}
		if fileInfo.IsDir() {
			// chromem-go 的 db 是目录
			if err = file_system.RealFS.RemoveAll(path); err != nil {
				logs.CtxError(ctx, "[RemoveRedundantStorage] db dir remove err %v", err)
				continue
			}
		} else {
			if err = file_system.RealFS.Remove(path); err != nil {
				logs.CtxError(ctx, "[RemoveRedundantStorage] db remove err %v", err)
				continue
			}
		}
		deletedDBPaths = append(deletedDBPaths, path)
	}
	logs.CtxInfo(ctx, "[RemoveRedundantStorage] auto clean, delete db paths: %v", deletedDBPaths)
	return nil
}
