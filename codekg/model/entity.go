package model

import (
	"ide/ckg/codekg/util"

	"github.com/samber/lo"
)

type EntityType string

const (
	File          EntityType = "file"
	Folder        EntityType = "folder"
	FileTopLevel  EntityType = "fileTopLevel"
	Clazz         EntityType = "clazz"
	ClassTopLevel EntityType = "classTopLevel"
	Method        EntityType = "method"
	CodeChunk     EntityType = "codeChunk"
	Text          EntityType = "text"

	AttributeLabelContent           = "content"
	AttributeLabelSimplifiedContent = "simplified_content"
	AttributeLabelComment           = "comment"
	AttributeLabelSignature         = "signature"
	AttributeLabelStartLine         = "start_line"
	AttributeLabelEndLine           = "end_line"
	AttributeLabelIsTest            = "is_test"
	AttributeLabelExternalInfo      = "external_info"
	AttributeLabelAliasName         = "alias_name"
	SegTypeCodeChunk                = "code_chunk"
	SegTypeTextChunk                = "text"
	SegTypeFile                     = "file"
	SegTypeFileTopLevel             = "file_top_level"
	SegTypeRelation                 = "relation"
	SegTypeClass                    = "class"
	SegTypeCLassTopLevel            = "class_top_level"
	SegTypeMethod                   = "method"

	SimplifiedContentReplaceMarker = "{$0x233REPLACE_ME332x0$}"
)

type Entity struct {
	ID                   string                 `json:"id"`
	Name                 string                 `json:"name"`
	URI                  URI                    `json:"uri"`
	Type                 EntityType             `json:"type"`
	ContainsSelectedCode bool                   `json:"-"`
	Attributes           map[string]interface{} `json:"-"`
}

func CreateEntity(id string, name string, uri URI, entityType EntityType, Attributes map[string]interface{}) *Entity {
	if Attributes == nil {
		Attributes = make(map[string]interface{})
	}
	return &Entity{
		ID:                   id,
		Name:                 name,
		URI:                  uri,
		Type:                 entityType,
		ContainsSelectedCode: false,
		Attributes:           Attributes,
	}
}

func (e *Entity) GetAttributes() map[string]interface{} {
	return e.Attributes
}

func (e *Entity) GetAliasName() []string {
	aliasName := make([]string, 0)
	if v1, ok1 := e.Attributes[AttributeLabelAliasName]; ok1 {
		if v2, ok2 := v1.([]interface{}); ok2 {
			for _, alias := range v2 {
				aliasName = append(aliasName, util.DefaultAny[string](alias))
			}
		}
	}
	return aliasName
}

func (e *Entity) GetContent() []string {
	result := make([]string, 0)
	if v1, ok1 := e.Attributes[AttributeLabelContent]; ok1 {
		if v2, ok2 := v1.([]interface{}); ok2 {
			for _, content := range v2 {
				result = append(result, util.DefaultAny[string](content))
			}
		}

		if v2, ok2 := v1.(string); ok2 {
			result = append(result, v2)
		}
	}

	return lo.Filter(result, func(content string, _ int) bool {
		return content != ""
	})
}

func (e *Entity) GetSimplifiedContent() string {
	return util.DefaultAny[string](e.Attributes[AttributeLabelSimplifiedContent])
}

func (e *Entity) GetComment() string {
	return util.DefaultAny[string](e.Attributes[AttributeLabelComment])
}

func (e *Entity) GetSignature() string {
	return util.DefaultAny[string](e.Attributes[AttributeLabelSignature])
}

func (e *Entity) GetStartLine() int32 {
	// 如果是 int 类型，直接转成 int32
	if v, ok := e.Attributes[AttributeLabelStartLine].(int); ok {
		return int32(v)
	}
	return int32(util.DefaultAny[float64](e.Attributes[AttributeLabelStartLine]))
}
func (e *Entity) GetEndLine() int32 {
	if v, ok := e.Attributes[AttributeLabelEndLine].(int); ok {
		return int32(v)
	}
	return int32(util.DefaultAny[float64](e.Attributes[AttributeLabelEndLine]))
}

func (e *Entity) IsTest() bool {
	return util.DefaultAny[bool](e.Attributes[AttributeLabelIsTest])
}

func (e *Entity) IsTopLevel() bool {
	return e.Type == FileTopLevel || e.Type == ClassTopLevel
}

func (e *Entity) GetExternalInfo() []string {
	externalInfo := make([]string, 0)
	if v1, ok1 := e.Attributes[AttributeLabelExternalInfo]; ok1 {
		if v2, ok2 := v1.([]interface{}); ok2 {
			for _, info := range v2 {
				externalInfo = append(externalInfo, util.DefaultAny[string](info))
			}
		}
	}
	return externalInfo
}
