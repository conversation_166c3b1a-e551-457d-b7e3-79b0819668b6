import {afterAll, beforeAll, describe, expect, it, MockInstance, vi} from "vitest";
import {TreeSitterParser} from "../../parse/treesitter/parser";
import {createDocument, wait} from "../../../utils/common";
import {LanguageIdEnum} from "../../parse/share";
import {doFileActiveListener} from "../../common/listener";
import {analyzeIndexAndTask, mergeRanges} from "./incremental";
import * as utilsPath from "../../../utils/path";
import {PSIParser} from "../../parse/psi/parser";
import {FeatureGateManager, FeatureName} from "../../common/config";
import {setEnableLocalLogging} from "../../../utils/logger";
import {processDefinitionAnalysisTaskForTest} from "./share";

describe("test_analyzeIndexAndAndTask", () => {
  let workspacePathMock: MockInstance;
  let psiInitMock: MockInstance;

  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
    FeatureGateManager.INSTANCE.openFeature(FeatureName.ENABLE_AST_CACHE_ANALYSIS);
    FeatureGateManager.INSTANCE.openFeature(FeatureName.ENABLE_DEFINITION_ANALYSIS);
    FeatureGateManager.INSTANCE.openFeature(FeatureName.ENABLE_GO_DEFINITION_ANALYSIS);
    workspacePathMock = vi.spyOn(utilsPath, "getWorkspaceFolderPath").mockImplementation(() => {
      return "file:///";
    });
    const psiIns = PSIParser.INSTANCE
    psiInitMock = vi.spyOn(psiIns, "initialized").mockImplementation(() => {
      return true;
    });
    setEnableLocalLogging(true)
  });

  afterAll(() => {
    workspacePathMock.mockRestore();
    psiInitMock.mockRestore();
  })

  it("case1_multi_change", async () => {
    const docText = `package models

type TreeNode struct {
\tChildren []*TreeNode
}

// 返回以该节点为根的子树的节点数量
func (node *TreeNode) GetLevelNodeNumMap() map[int]int {
\tq := make([]*TreeNode, 0)
\tq = append(q, node)

\tlevelNodesNumMap := make(map[int]int)

\tcounter := 0

\tfor len(q) > 0 {

\t\tlength := len(q)
\t\tlevelNodesNumMap[counter] = length
\t\tcounter += 1

\t\tfor i := 0; i < length; i++ {
\t\t\tcurrent_node := q[0]
\t\t\tq = q[1:]

\t\t\tfor i := 0; i < len(current_node.Children); i++ {
\t\t\t\tchildNode := current_node.Children[i]
\t\t\t\tq = append(q, childNode)
\t\t\t}
\t\t}
\t}

\treturn levelNodesNumMap
}
`;

    const document = createDocument(docText, LanguageIdEnum.Go, "file:///test.go");

    await doFileActiveListener(document);
    await wait(50);

    const docText2 = `package models

type TreeNode struct {
\tChildren []*TreeNode
}

// 返回以该节点为根的子树的节点数量
func (node *TreeNode) GetLevelNodeNumMap() map[int]int {
\tqq := make([]*TreeNode, 0)
\tqq = append(qq, node)

\tlevelNodesNumMap := make(map[int]int)

\tcounter := 0

\tfor len(qq) > 0 {

\t\tlength := len(qq)
\t\tlevelNodesNumMap[counter] = length
\t\tcounter += 1

\t\tfor i := 0; i < length; i++ {
\t\t\tcurrent_node := qq[0]
\t\t\tqq = qq[1:]

\t\t\tfor i := 0; i < len(current_node.Children); i++ {
\t\t\t\tchildNode := current_node.Children[i]
\t\t\t\tqq = append(qq, childNode)
\t\t\t}
\t\t}
\t}

\treturn levelNodesNumMap
}
`;

    const document2 = createDocument(docText2, LanguageIdEnum.Go, "file:///test.go");
    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText2);

    analyzeIndexAndTask({
      document: document2,
      contentChanges: [
        {position: {line: 27, character: 16}, removeLength: 0, text: "q"},
        {position: {line: 27, character: 5}, removeLength: 0, text: "q"},
        {position: {line: 23, character: 8}, removeLength: 0, text: "q"},
        {position: {line: 23, character: 4}, removeLength: 0, text: "q"},
        {position: {line: 22, character: 20}, removeLength: 0, text: "q"},
        {position: {line: 17, character: 17}, removeLength: 0, text: "q"},
        {position: {line: 15, character: 10}, removeLength: 0, text: "q"},
        {position: {line: 9, character: 13}, removeLength: 0, text: "q"},
        {position: {line: 9, character: 2}, removeLength: 0, text: "q"},
        {position: {line: 8, character: 2}, removeLength: 0, text: "q"},
      ],
    }, tree, document);

    const result = await processDefinitionAnalysisTaskForTest();

    expect(result).toEqual(document2.getText().length)
  });
});

describe('test_mergeRanges', () => {
  it('case1_overlapping', () => {
    const ranges = [
      { adjustedNewStartIndex: 1, adjustedNewEndIndex: 3 },
      { adjustedNewStartIndex: 2, adjustedNewEndIndex: 4 },
      { adjustedNewStartIndex: 5, adjustedNewEndIndex: 7 },
    ];

    const mergedRanges = mergeRanges(ranges);

    expect(mergedRanges).toEqual([
      { adjustedNewStartIndex: 1, adjustedNewEndIndex: 4 },
      { adjustedNewStartIndex: 5, adjustedNewEndIndex: 7 },
    ]);
  });

  it('case2_adjacent', () => {
    const ranges = [
      { adjustedNewStartIndex: 1, adjustedNewEndIndex: 3 },
      { adjustedNewStartIndex: 3, adjustedNewEndIndex: 5 },
    ];

    const mergedRanges = mergeRanges(ranges);

    expect(mergedRanges).toEqual([
      { adjustedNewStartIndex: 1, adjustedNewEndIndex: 5 },
    ]);
  });

  it('case3_non-overlapping', () => {
    const ranges = [
      { adjustedNewStartIndex: 1, adjustedNewEndIndex: 3 },
      { adjustedNewStartIndex: 4, adjustedNewEndIndex: 6 },
    ];

    const mergedRanges = mergeRanges(ranges);

    expect(mergedRanges).toEqual([
      { adjustedNewStartIndex: 1, adjustedNewEndIndex: 3 },
      { adjustedNewStartIndex: 4, adjustedNewEndIndex: 6 },
    ]);
  });

  it('case4_empty', () => {
    const ranges: { adjustedNewStartIndex: number, adjustedNewEndIndex: number }[] = [];

    const mergedRanges = mergeRanges(ranges);

    expect(mergedRanges).toEqual([]);
  });
});