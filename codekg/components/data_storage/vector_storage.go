package data_storage

import (
	"context"
	"github.com/philippgille/chromem-go"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"ide/ckg/codekg/components/data_storage/consts"
	"ide/ckg/codekg/components/data_storage/user_storage"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/model"
)

// EmbeddingStorage is the interface for embedding storage.
type EmbeddingStorage interface {
	AddEmbeddingDocument(ctx context.Context, doc *model.EmbeddingDocument, option AddEmbeddingDocumentOptions) error
	AddEmbeddingDocuments(ctx context.Context, docs []*model.EmbeddingDocument, option AddEmbeddingDocumentOptions) error
	GetById(ctx context.Context, id, collectionName string) (*model.EmbeddingDocument, error)
	QueryByEmbedding(ctx context.Context, query *model.Embedding, nResults int, option EmbeddingQueryOptions) ([]*model.EmbeddingQueryResult, error)
	EmbeddingDocumentCount(ctx context.Context) int

	DeleteDocumentsByIds(ctx context.Context, option DeleteDocumentOptions, ids ...string) error
	DeleteDocumentsByMetadata(ctx context.Context, whereMetadata map[string]any, option DeleteDocumentOptions) error
	DeleteDocumentsByEntity(ctx context.Context, entities []StorageEntity, storage Storage, tx *gorm.DB, option DeleteDocumentOptions) error

	Delete() error
	IsDeleted() bool
	IsExistsOnDisk() bool
	GetStoragePath() string
	GetType() string
	GetEmbeddingModel() string
	GetVersion() consts.StorageVersion
}

type EmbeddingStorageOptions interface {
	FillInMetadata(ctx context.Context, metadata map[string]any) error
}

const (
	Collection_Embedding = "embedding"
)

type AddEmbeddingDocumentOptions struct {
}

func (o *AddEmbeddingDocumentOptions) FillInMetadata(ctx context.Context, metadata map[string]any) error {
	if metadata == nil {
		return errors.New("metadata is nil")
	}
	return nil
}

type AddEmbeddingDocumentOptionBuilder struct{ AddEmbeddingDocumentOptions }

func NewAddEmbeddingDocumentOptionBuilder() *AddEmbeddingDocumentOptionBuilder {
	return &AddEmbeddingDocumentOptionBuilder{}
}

func (b *AddEmbeddingDocumentOptionBuilder) Build() AddEmbeddingDocumentOptions {
	return b.AddEmbeddingDocumentOptions
}

// EmbeddingQueryOptions is the options for querying embedding documents.
type EmbeddingQueryOptions struct {
	// ScoreThreshold is the threshold for the score.
	// @default 0
	ScoreThreshold float32
	// WhereMetadata is the metadata to filter the documents.
	WhereMetadata map[string]any
}

func (o *EmbeddingQueryOptions) FillInMetadata(ctx context.Context, metadata map[string]any) error {
	if metadata == nil {
		return errors.New("metadata is nil")
	}
	for k, v := range o.WhereMetadata {
		if _, ok := metadata[k]; ok {
			continue // metadata 中已经设置过该字段了，o.WhereMetadata 的优先级更低。
		}
		if val, ok := v.(string); ok {
			metadata[k] = val
		} else {
			logs.CtxError(ctx, "[initQueryWhereMetadata] metadata value is not string, key: %s, value: %+v", k, v)
		}
	}
	return nil
}

type EmbeddingQueryOptionBuilder struct{ EmbeddingQueryOptions }

// NewEmbeddingQueryOptions creates a new EmbeddingQueryOptions.
func NewEmbeddingQueryOptions() *EmbeddingQueryOptionBuilder        { return &EmbeddingQueryOptionBuilder{} }
func (b *EmbeddingQueryOptionBuilder) Build() EmbeddingQueryOptions { return b.EmbeddingQueryOptions }

func (b *EmbeddingQueryOptionBuilder) SetScoreThreshold(scoreThreshold float32) *EmbeddingQueryOptionBuilder {
	b.ScoreThreshold = scoreThreshold
	return b
}

func (b *EmbeddingQueryOptionBuilder) SetWhereMetadata(whereMetadata map[string]any) *EmbeddingQueryOptionBuilder {
	b.WhereMetadata = whereMetadata
	return b
}

type DeleteDocumentOptions struct {
}

func (o *DeleteDocumentOptions) FillInMetadata(ctx context.Context, metadata map[string]any) error {
	if metadata == nil {
		return errors.New("metadata is nil")
	}
	return nil
}

type DeleteDocumentOptionBuilder struct{ DeleteDocumentOptions }

func NewDeleteDocumentOptionBuilder() *DeleteDocumentOptionBuilder {
	return &DeleteDocumentOptionBuilder{}
}
func (b *DeleteDocumentOptionBuilder) Build() DeleteDocumentOptions { return b.DeleteDocumentOptions }

func CreateEmbeddingStorage(ctx context.Context, es *user_storage.UserStorage, storagePath model.URI, projectID model.URI,
	embeddingModel string, t consts.EmbeddingStorageType, version consts.StorageVersion, forceNew bool) (EmbeddingStorage, error) {
	var storage EmbeddingStorage
	var err error
	switch t {
	case consts.EmbeddingStorage_SQLiteVec:
		if version == consts.StorageVersionV2 {
			storage, err = CreateSQLiteVecStorageV2(ctx, es, storagePath, projectID, embeddingModel, t, forceNew)
		} else {
			storage, err = CreateSQLiteVecStorage(ctx, es, storagePath, projectID, embeddingModel, t, forceNew)
		}
		if err != nil {
			return nil, errors.WithMessagef(err, "[CreateEmbeddingStorage] create sqlite vec storage failed, storagePath: %s, projectID: %s", storagePath, projectID)
		}
	case consts.EmbeddingStorage_ChormemGo:
		storage, err = CreateChromemGoStorage(ctx, es, storagePath, projectID, embeddingModel, t, forceNew)
		if err != nil {
			return nil, errors.WithMessagef(err, "[CreateEmbeddingStorage] create chromem go storage failed, storagePath: %s, projectID: %s", storagePath, projectID)
		}
	default:
		return nil, errors.Errorf("[CreateEmbeddingStorage] unknown storage type: %d", t)
	}
	return storage, nil
}

func ConvertDocumentToChromemDocument(doc *model.EmbeddingDocument) chromem.Document {
	metadata := make(map[string]string)
	for k, v := range doc.Metadata {
		if val, ok := v.(string); ok {
			metadata[k] = val
		}
	}
	return chromem.Document{
		ID:        doc.ID,
		Content:   doc.Content,
		Embedding: doc.Embedding.Embedding,
		Metadata:  metadata,
	}
}

func ConvertChromemDocumentToDocument(doc chromem.Document) *model.EmbeddingDocument {
	return &model.EmbeddingDocument{
		ID:      doc.ID,
		Content: doc.Content,
		Embedding: &model.Embedding{
			Embedding: doc.Embedding,
		},
		Metadata: lo.MapEntries(doc.Metadata, func(k, v string) (string, any) { return k, v }),
	}
}

func ConvertChromemResultToEmbeddingQueryResult(result chromem.Result) *model.EmbeddingQueryResult {
	return &model.EmbeddingQueryResult{
		EmbeddingDocument: model.EmbeddingDocument{
			ID:      result.ID,
			Content: result.Content,
			Embedding: &model.Embedding{
				Embedding: result.Embedding,
			},
			Metadata: lo.MapEntries(result.Metadata, func(k, v string) (string, any) { return k, v }),
		},
		Score: result.Similarity,
	}
}
