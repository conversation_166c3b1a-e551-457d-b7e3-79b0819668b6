{
  "extends": "@artus/tsconfig",
  "compilerOptions": {
    "types": [
      "node"
    ],
    "target": "ES2020",
    "lib": ["dom", "ES2020"],
    "resolveJsonModule": true,
    "strictNullChecks": false,
    "strictFunctionTypes": true,
    "strictPropertyInitialization": false,
    "noImplicitReturns": false,
    "typeRoots": [
      "node_modules/@types",
    ],
    "outDir": "./dist",
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "declaration": true,
    "rootDir": ".",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "module": "commonjs",
    "moduleResolution": "node",
    "paths": {}
  },
  "exclude": [
    "node_modules",
    "**/*.test.ts",
  ]
}