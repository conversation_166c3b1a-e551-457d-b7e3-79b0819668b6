import {TextDocument} from "vscode-languageserver-textdocument";
import {Position, Range} from "vscode-languageserver-types";
import {Logger} from "../../../utils/logger";
import {
  EVENT_CATEGORY_GENERAL,
  EVENT_NAME_FAILED,
  EVENT_NAME_TRIGGER, EVENT_VALUE_ANALYSIS_FAILED,
  EVENT_VALUE_PARSE_TREE_FAILED,
  reportEvent,
} from "../../../utils/event";
import {TreeSitterParser} from "../../parse/treesitter/parser";
import {SyntaxNode} from "web-tree-sitter";
import {TreeSitterDispatcher} from "../../parse/treesitter/dispatcher";
import {TreeSitterFunctionalityEnum} from "../../parse/treesitter/share";

const EVENT_PROCESS_GET_SYMBOL_POSITION = "get_symbol_position";

export function doGetSymbolPositionInRange(document: TextDocument, range: Range, name: string): Position[] {
  Logger.info(`doGetSymbolPositionInRange: '${document.uri}' (${range.start.line}, ${range.start.character}) - (${range.end.line}, ${range.end.character}) for name '${name}'`);
  reportEvent(document.languageId, EVENT_CATEGORY_GENERAL, EVENT_PROCESS_GET_SYMBOL_POSITION, EVENT_NAME_TRIGGER).then();

  const tree = TreeSitterParser.INSTANCE.parse(document.languageId, document.getText());
  if (!tree) {
    Logger.error(`doGetSymbolPositionInRange: failed to parse document '${document.uri}'`);
    reportEvent(document.languageId, EVENT_CATEGORY_GENERAL, EVENT_PROCESS_GET_SYMBOL_POSITION, EVENT_NAME_FAILED, EVENT_VALUE_PARSE_TREE_FAILED).then();
    return [];
  }

  const result: SyntaxNode[] = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetIdentifierInRange, tree.rootNode, range, document.offsetAt(range.start), document.offsetAt(range.end), name);
  if (!result) {
    Logger.error(`doGetSymbolPositionInRange: result is null`);
    reportEvent(document.languageId, EVENT_CATEGORY_GENERAL, EVENT_PROCESS_GET_SYMBOL_POSITION, EVENT_NAME_FAILED, EVENT_VALUE_ANALYSIS_FAILED).then();
    return [];
  }

  return result.map(node => {
    return {
      line: node.startPosition.row,
      character: node.startPosition.column,
    };
  });
}