package kb_splitter

import (
	"context"

	"ide/ckg/codekg/components/env"
	kb "ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/splitter/model"
)

// Split implements the Splitter interface using KB service
func Split(ctx context.Context, opt model.KBSplitOption, caller string, files []*kb.SplitFile) (*kb.SplitFilesResponse, error) {
	return opt.KBClient.SplitFiles(ctx, env.GetToken(opt.UserID), caller, &kb.SplitFilesRequest{
		DatastoreName:  opt.DatastoreName,
		Files:          files,
		ChunkingMethod: opt.ChunkingMethod,
	})
}
