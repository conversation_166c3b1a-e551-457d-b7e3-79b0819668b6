package version_feature

import (
	"github.com/hashicorp/go-version"
	"github.com/samber/lo"
)

// FeatureManager 管理版本和特性的映射关系
type FeatureManager struct {
	// 版本到特性的映射, 如 {"1.0": ["feature1", "feature2"]}
	versionFeatures map[*version.Version][]*CKGFeature
}

func NewFeatureManager() *FeatureManager {
	fm := &FeatureManager{
		versionFeatures: make(map[*version.Version][]*CKGFeature),
	}
	fm.registerFeatures()
	return fm
}

// RegisterFeature 注册一个特性及其默认值
func (fm *FeatureManager) registerFeatures() {
	for _, feat := range versionFeatureMap {
		fm.versionFeatures[feat.FromVersion] = append(fm.versionFeatures[feat.FromVersion], feat)
	}
}

// GetRequiredAction 的 currVersion 为当前 CKG 版本， originVersion 为当前 SQLite 中记录的 CKG 版本，即对应 codekg embedding 构建时使用的 CKG 版本。
// 该函数根据 “版本号”获取一系列 features 中的 action，这些 features 必须满足如下条件。
// 1. 如果 currVersion <= originVersion，返回为空;
// 2. 如果 currVersion > originVersion，需要返回 (originVersion, currVersion] 间所有 feature。其中 feature 可能涉及一些操作，期望用户执行。
func (fm *FeatureManager) GetRequiredAction(currVersion, originVersion *version.Version) []VersionUpdateAction {
	if currVersion.LessThanOrEqual(originVersion) {
		return []VersionUpdateAction{NoAction}
	}
	features := fm.GetFeatures(originVersion, currVersion)
	actions := make(map[VersionUpdateAction]bool)
	for _, feat := range features {
		for _, action := range feat.ExpectedActions {
			actions[action] = true
		}
	}
	res := lo.MapToSlice(actions, func(action VersionUpdateAction, _ bool) VersionUpdateAction { return action })
	if len(res) == 0 || (len(res) == 1 && res[0] == NoAction) {
		return []VersionUpdateAction{NoAction}
	}
	return lo.Filter(res, func(action VersionUpdateAction, _ int) bool { return action != NoAction })
}

// GetFeatures 返回 (fromVersion, toVersion] 间所有 features
func (fm *FeatureManager) GetFeatures(fromVersion, toVersion *version.Version) []*CKGFeature {
	var features []*CKGFeature
	for v, feats := range fm.versionFeatures {
		if fromVersion.LessThan(v) && v.LessThanOrEqual(toVersion) {
			features = append(features, feats...)
		}
	}
	return features
}

type CKGFeature struct {
	Name                string
	Description         string
	FromVersion         *version.Version
	CompatLowestVersion *version.Version
	ExpectedActions     []VersionUpdateAction
	Dependencies        []*CKGFeature

	Deprecated        bool
	DeprecatedVersion *version.Version
	Status            FeatureStatus
}

// VersionUpdateAction 定义了版本更新后希望的用户行为
type VersionUpdateAction int

const (
	NoAction      VersionUpdateAction = 0
	ReIndexAction VersionUpdateAction = 1
)

type FeatureStatus int

const (
	AlphaFeature  FeatureStatus = 0
	BetaFeature   FeatureStatus = 1
	CanaryFeature FeatureStatus = 2
	StableFeature FeatureStatus = 3
)
