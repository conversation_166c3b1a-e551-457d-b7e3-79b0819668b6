from ckg_sdk import ckg_client
from utils import const, util
from ckg_sdk import codekg_pb2
import time
import os


def auto_test_1(download_name, project_ids, binary_version):
    if not util.ckg_start(download_name, 'true'):
        print('ckg start with local failed')
        return False
    client = ckg_client.CKGClient(f'127.0.0.1:{const.port}')
    # 等待ckg启动完成
    time.sleep(3)
    print('auto_test_1 ckg start success')

    if not client.is_version_matched(binary_version).matched:
        print('auto_test_1 version not matched')
        return False
    try:
        token = util.get_token_id()
        resp = client.refresh_token(token, const.user_id)
        if resp.code != 0:
            print(f'auto_test_1 refresh token failed, error code is {resp.code}')
            return False

        resp = client.init_project(project_ids, const.user_id)
        if resp.code != 0:
            print(f'auto_test_1 init project failed, error code is {resp.code}')
            return False
        time.sleep(50)
        return True
    except Exception as e:
        print(f"auto_test_1 error: {str(e)}")
        return False


def auto_test_2(project_id, success_index_files):
    client = ckg_client.CKGClient(f'127.0.0.1:{const.port}')
    resp = client.get_build_status()
    if resp.code != 0:
        print(f'auto_test_2 get build status failed, error code is {resp.code}')
        return False
    for key, value in resp.status.items():
        if key == project_id:
            if value.status != codekg_pb2.BuildStatus.finished:
                print(f'auto_test_2 project {project_id} index not finished')
                return False
            if value.failed_index_files != 0:
                print(f'auto_test_2 failed_index_files not equal 0, failed_index_files is {value.failed_index_files}')
                return False
            if value.empty_project:
                print(f'auto_test_2 project {project_id} is empty project')
                return False
            if value.success_index_files != success_index_files:
                print(f'auto_test_2 {project_id }success_index_files is not {success_index_files}, success_index_files is {value.success_index_files}')
                return False
            if value.total_index_files != success_index_files:
                print(f'auto_test_2 {project_id} total_index_files is not {success_index_files}, total_index_files is {value.total_index_files}')
                return False
            return True
    return False


def auto_test_3(project_id, download_name):
    time.sleep(3)
    if not util.kill_process_by_port(const.port):
        print('kill ckg failed')
        return False

    if not util.ckg_start(download_name, 'true', False):
        print('ckg start with local failed')
        return False
    # 等待ckg启动完成
    time.sleep(3)
    print('auto_test_3 ckg start success')
    client = ckg_client.CKGClient(f'127.0.0.1:{const.port}')

    try:
        token = util.get_token_id()
        resp = client.refresh_token(token, const.user_id)
        if resp.code != 0:
            print(f'auto_test_1 refresh token failed, error code is {resp.code}')
            return False

        resp = client.init_project([project_id], const.user_id)
        if resp.code != 0:
            print(f'auto_test_3 init project failed, error code is {resp.code}')
            return False
        time.sleep(2)

        resp = client.get_build_status()
        if resp.code != 0:
            print(f'auto_test_3 get build status failed, error code is {resp.code}')
            return False
        for key, value in resp.status.items():
            if key == project_id:
                if value.status != codekg_pb2.BuildStatus.finished:
                    print(f'auto_test_3 {project_id} not index finished, please check index log')
                    return False
                if value.failed_index_files != 0:
                    print(f'auto_test_3 {project_id} failed_index_files not equal 0, failed_index_files is {value.failed_index_files}')
                    return False
                if value.empty_project:
                    print(f'auto_test_3 {project_id} is empty project')
                    return False
                if value.total_index_files != const.repo1_success_index_files:
                    print(f'auto_test_3 {project_id} total_index_files is not {const.repo1_success_index_files}, total_index_files is {value.total_index_files}')
                    return False
                return True
    except Exception as e:
        print(f"auto_test_3 error: {str(e)}")
        return False
    return False


def auto_test_4():
    client = ckg_client.CKGClient(f'127.0.0.1:{const.port}')
    file_path = os.path.join(const.repo_path1, const.test_java_name)
    with open(file_path, 'w') as file:
        file.write(const.java_code1)
        file.close()
    os.chmod(file_path, 0o777)
    resp = client.file_create([file_path], const.user_id)
    if resp.code != 0:
        print(f'auto_test_4 file_create failed, error code is {resp.code}')
        return False
    return True


def auto_test_5():
    client = ckg_client.CKGClient(f'127.0.0.1:{const.port}')
    file_path = os.path.join(const.repo_path1, const.test_java_name)
    with open(file_path, 'w') as file:
        file.write(const.java_code2)
        file.close()
    resp = client.file_change([file_path], const.user_id)
    if resp.code != 0:
        print(f'auto_test_5 file_change failed, error code is {resp.code}')
        return False
    return True


def auto_test_6():
    client = ckg_client.CKGClient(f'127.0.0.1:{const.port}')
    file_path = os.path.join(const.repo_path1, const.test_java_name)
    try:
        os.remove(file_path)
    except Exception as e:
        print(f"auto_test_6 remove file path error: {e}")

    resp = client.file_delete([file_path], const.user_id)
    if resp.code != 0:
        print(f'auto_test_6 file_delete failed, error code is {resp.code}')
        return False
    return True


def auto_test_7(project_id):
    client = ckg_client.CKGClient(f'127.0.0.1:{const.port}')
    resp = client.cancel_index(project_id)
    if resp.code != 0:
        print(f'auto_test_7 {project_id} cancel_index failed, error code is {resp.code}')
        return False
    return True


def auto_test_8(project_id):
    client = ckg_client.CKGClient(f'127.0.0.1:{const.port}')
    resp = client.delete_index(project_id)
    if resp.code != 0:
        print(f'auto_test_8 {project_id} delete_index failed')
        return False
    if not util.is_ckg_db_delete():
        print(f'auto_test_8 {project_id} delete_index failed, db is not deleted')
        return False
    return True

def auto_test_9(project_ids):
    client = ckg_client.CKGClient(f'127.0.0.1:{const.port}')
    try:
        token = util.get_token_id()
        resp = client.refresh_token(token, const.user_id)
        if resp.code != 0:
            print(f'auto_test_9 refresh token failed, error code is {resp.code}')
            return False

        resp = client.init_project(project_ids, const.user_id)
        if resp.code != 0:
            print(f'auto_test_9 init {project_ids} failed, error code is {resp.code}')
            return False
        time.sleep(80)

        if not auto_test_2(project_ids[0], const.repo2_success_index_files):
            print(f'auto_test_9 get {project_ids[0]} build status failed')
            return False

        resp = client.retrieve_entity(project_ids,
                                      const.user_id,
                                      const.user_message1,
                                      [const.folder_path1],
                                      const.entity_num, [codekg_pb2.file])
        if resp.code != 0:
            print(f'auto_test_9 retrieve_entity failed, error code is {resp.code}')
            return False
        if len(resp.entities_by_user_message) != const.retrieve_entity_num:
            print(f'auto_test_9 retrieve_entity num is not {const.retrieve_entity_num}, retrieve_entity num is {len(resp.entities_by_user_message)}')
            return False
        if resp.entities_by_user_message[0].entity_id != const.except_entity_id1:
            print(f'auto_test_9 retrieve_entity entity_id is not {const.except_entity_id1}, entity id is {resp.entities_by_user_message[0].entity_id}')
            return False
        return True

    except Exception as e:
        print(f"auto_test_9 error: {str(e)}")
        return False


def auto_test_10(project_ids):
    try:
        token = util.get_token_id()
        client = ckg_client.CKGClient(f'127.0.0.1:{const.port}')
        resp = client.refresh_token(token, const.user_id)
        if resp.code != 0:
            print(f'auto_test_10 refresh token failed, error code is {resp.code}')
            return False

        editor_info = codekg_pb2.CurrentEditorInfo(
            file_path=const.editor_info_file_path,
            project_id=const.repo_path2,
            select_code_range=codekg_pb2.Range(
                start_line=const.editor_info_select_code_start_line,
                end_line=const.editor_info_select_code_end_line
            ),
            select_code_content=const.editor_info_select_code_content
        )
        resp = client.retrieve_entity(project_ids,
                                      const.user_id,
                                      const.user_message1,
                                      None,
                                      None,
                                      None,
                                      editor_info
                                      )
        time.sleep(1)
        if resp.code != 0:
            print(f'auto_test_10 retrieve_entity failed, error code is {resp.code}')
            return False
        if len(resp.entities_by_user_message) <= 0:
            print('auto_test_10 retrieve entity num is empty')
            return False
        for entity in resp.entities_by_user_message:
            if entity.entity_id == const.except_entity_id1:
                return True

        print(f'auto_test_10 retrieve_entity entity_id is not {const.except_entity_id1}')
        return False

    except Exception as e:
        print(f"auto_test_10 error: {str(e)}")
        return False


def auto_test_11(project_ids):
    try:
        token = util.get_token_id()
        client = ckg_client.CKGClient(f'127.0.0.1:{const.port}')
        resp = client.refresh_token(token, const.user_id)
        if resp.code != 0:
            print(f'auto_test_11 refresh token failed, error code is {resp.code}')
            return False

        start_time = time.time()
        retrieve_entity_resp = client.retrieve_entity(project_ids, const.user_id, const.user_message2)
        end_time = time.time()
        execution_time = end_time - start_time

        if execution_time > 1:
            print('auto_test_11 retrieve_entity time cost more than 1 second')
            return False
        if retrieve_entity_resp.code != 0:
            print(f'auto_test_11 retrieve entity failed, error code is {retrieve_entity_resp.code}')
            return False
        if len(retrieve_entity_resp.entities_by_user_message) <= 0:
            print('auto_test_11 retrieve entity num is empty')
            return False
        include_except_entity_id2 = False
        include_except_entity_id3 = False
        for entity in retrieve_entity_resp.entities_by_user_message:
            if entity.entity_id == const.except_entity_id2_windows or entity.entity_id == const.except_entity_id2_linux:
                include_except_entity_id2 = True
            if entity.entity_id == const.except_entity_id3:
                include_except_entity_id3 = True
        if not include_except_entity_id2:
            print(f'auto_test_11 retrieve entity not include except entity id, windows except id {const.except_entity_id2_windows}, linux except id {const.except_entity_id2_linux}')
            return False
        if not include_except_entity_id3:
            print(f'auto_test_11 retrieve entity not include except entity id {const.except_entity_id3}')
            return False

        include_except_entity_id2 = False
        include_except_entity_id3 = False
        start_time = time.time()
        retrieve_relation_resp = client.retrieve_relation(retrieve_entity_resp.entities_by_user_message, const.user_id)
        end_time = time.time()
        execution_time = end_time - start_time

        if execution_time > 1:
            print('auto_test_11 retrieve_relation time cost more than 1 second')
            return False
        if retrieve_relation_resp.code != 0:
            print(f'auto_test_11 retrieve relation failed, error code is {retrieve_relation_resp.code}')
            return False
        if len(retrieve_relation_resp.variables) == 0:
            print('auto_test_11 retrieve_relation entity is empty')
            return False
        for variables in retrieve_relation_resp.variables:
            if variables.method.name == const.except_method_name:
                include_except_entity_id2 = True
            if variables.file.file_path == const.except_file_path:
                include_except_entity_id3 = True
        if include_except_entity_id2 and include_except_entity_id3:
            return True
        print(f'auto_test_11 retrieve_relation result is not except entity method name {const.except_method_name} and file path is not {const.except_file_path}')
        return False
    except Exception as e:
        print(f"auto_test_11 error: {str(e)}")
        return False
