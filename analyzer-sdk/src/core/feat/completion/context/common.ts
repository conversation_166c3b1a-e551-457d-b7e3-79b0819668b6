import {TextDocument} from "vscode-languageserver-textdocument";
import {Position} from "vscode-languageserver-types";
import {LanguageIdEnum} from "../../../parse/share";
import {CompletionContextResult} from "./share";
import {doExtractCompletionContextForGo} from "./go";
import {doExtractCompletionContextForTypescript} from "./typescript";
import {doExtractCompletionContextForJava} from "./java";

export async function doExtractCompletionContext(document: TextDocument, position: Position): Promise<CompletionContextResult[]> {
  switch (document.languageId) {
    case LanguageIdEnum.Go:
      return doExtractCompletionContextForGo(document, position);
    case LanguageIdEnum.Typescript:
    case LanguageIdEnum.Tsx:
    case LanguageIdEnum.TypescriptReact:
      return doExtractCompletionContextForTypescript(document, position);
    case LanguageIdEnum.Java:
      return doExtractCompletionContextForJava(document, position);
  }

  return [];
}