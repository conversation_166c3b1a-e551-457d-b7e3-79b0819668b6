package format_converter

import (
	"context"
	fcmodel "ide/ckg/codekg/components/format_converter/model"
	"ide/ckg/codekg/model"
)

func ConvertWithContent(ctx context.Context, uri model.URI, content string) (string, error) {
	if content == "" {
		return "", nil
	}

	// languageType := model.DetectLanguage(uri, []byte(content))
	// if languageType == model.LanguageHTML {
	// 	converter := html.GetDefaultConverter()
	// 	if converter.SupportedTypes()[fcmodel.FormatTypeHTML] {
	// 		return converter.ConvertWithContent(ctx, content)
	// 	}
	// }

	return "", fcmodel.ErrUnsupportedFormat
}

func ConvertWithURI(ctx context.Context, uri model.URI) (string, error) {
	return "", fcmodel.ErrUnsupportedFormat
}
