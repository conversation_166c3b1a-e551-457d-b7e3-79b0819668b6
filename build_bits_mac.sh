# bits mac 打包脚本
set -e
cd $TARGETCODEPATH
mkdir -p output

if [ x"$SCM_VERSION_ID" != x ]; then
SCM_BUILD_NUMBER=$(echo "$SCM_VERSION_ID" | sed 's/^.*\.\([^.]*\)$/\1/')
CKG_SDK_VERSION=0.$SCM_BUILD_NUMBER.0-test
else
CKG_SDK_VERSION=$(grep -o '"version": *"[^"]*"' ./clients/ts/package.json | sed 's/"version": *"\([^"]*\)"/\1/')
fi

CUSTOM_CKG_ACCESS_KEY=${CKG_ACCESS_KEY}
echo "CKG SDK version: ${CKG_SDK_VERSION}"

COMMIT_ID=$(git rev-parse --short=7 HEAD)
echo "Commit ID: ${COMMIT_ID}"

echo 'Build darwin arm64'
GOOS=darwin GOARCH=arm64 CC=clang CXX=clang++ CGO_ENABLED=1 go build -buildvcs=false -ldflags="-s -w" -o output/ckg_server_darwin_arm64 ./codekg/cmd/server
ldid -S ./output/ckg_server_darwin_arm64
md5 output/ckg_server_darwin_arm64 | awk '{print $4}' > output/ckg_server_darwin_arm64.md5
echo ${COMMIT_ID} > output/ckg_server_darwin_arm64.${COMMIT_ID}
curl -v -X PUT http://marscode-bucket-cn.tos-cn-north.byted.org/ckg_sdk_binary/${CKG_SDK_VERSION}/ckg_server_darwin_arm64 --upload-file ./output/ckg_server_darwin_arm64 -H "X-Tos-Access: ${CUSTOM_CKG_ACCESS_KEY}"
curl -v -X PUT http://marscode-bucket-cn.tos-cn-north.byted.org/ckg_sdk_binary/${CKG_SDK_VERSION}/ckg_server_darwin_arm64.md5 --upload-file ./output/ckg_server_darwin_arm64.md5 -H "X-Tos-Access: ${CUSTOM_CKG_ACCESS_KEY}"
curl -v -X PUT http://marscode-bucket-cn.tos-cn-north.byted.org/ckg_sdk_binary/${CKG_SDK_VERSION}/ckg_server_darwin_arm64.${COMMIT_ID} --upload-file ./output/ckg_server_darwin_arm64.${COMMIT_ID} -H "X-Tos-Access: ${CUSTOM_CKG_ACCESS_KEY}"

echo 'Build darwin x64'
GOOS=darwin GOARCH=amd64 CC=clang CXX=clang++ CGO_ENABLED=1 go build -buildvcs=false -ldflags="-s -w" -o output/ckg_server_darwin_x64 ./codekg/cmd/server
ldid -S ./output/ckg_server_darwin_x64
md5 output/ckg_server_darwin_x64 | awk '{print $4}' > output/ckg_server_darwin_x64.md5
echo ${COMMIT_ID} > output/ckg_server_darwin_x64.${COMMIT_ID}
curl -v -X PUT http://marscode-bucket-cn.tos-cn-north.byted.org/ckg_sdk_binary/${CKG_SDK_VERSION}/ckg_server_darwin_x64 --upload-file ./output/ckg_server_darwin_x64 -H "X-Tos-Access: ${CUSTOM_CKG_ACCESS_KEY}"
curl -v -X PUT http://marscode-bucket-cn.tos-cn-north.byted.org/ckg_sdk_binary/${CKG_SDK_VERSION}/ckg_server_darwin_x64.md5 --upload-file ./output/ckg_server_darwin_x64.md5 -H "X-Tos-Access: ${CUSTOM_CKG_ACCESS_KEY}"
curl -v -X PUT http://marscode-bucket-cn.tos-cn-north.byted.org/ckg_sdk_binary/${CKG_SDK_VERSION}/ckg_server_darwin_x64.${COMMIT_ID} --upload-file ./output/ckg_server_darwin_x64.${COMMIT_ID} -H "X-Tos-Access: ${CUSTOM_CKG_ACCESS_KEY}"

bit env set BINARY_PATH "$TARGETCODEPATH/output"
cd output
ls
pwd