package tasks

import (
	"context"
	"crypto/sha256"
	"fmt"
	"os"
	"path/filepath"
	"runtime/debug"
	"strings"
	"time"

	"github.com/hashicorp/golang-lru/v2/expirable"
	"go.uber.org/atomic"

	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/env"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/components/ignore_service"
	"ide/ckg/codekg/components/ignore_service/ignore_rule_checker"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/components/splitter"
	splittermodel "ide/ckg/codekg/components/splitter/model"
	"ide/ckg/codekg/components/validator"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/util"
)

var lru = expirable.NewLRU[string, *knowledgebase.Embedding](1000, func(_ string, _ *knowledgebase.Embedding) {}, 5*time.Minute)

type IndexTaskCallback func(ctx context.Context, uriData []*model.URIData)

type Task interface {
	do()

	ID() string
	GetFilePath() string
	GetProject() string
	IsRemoved()
	GetChunkingMethod() string
	GetEmbeddingModel() string
}

type IndexFolderTask struct {
	userID    string
	uri       *model.URIStatus
	projectID model.URI

	cli knowledgebase.Client
	fs  file_system.FileSystem

	callback IndexTaskCallback
	attr     model.IndexAttribute

	useV2Ignore bool
	ignore      ignore_service.IgnoreService
	isv2        ignore_rule_checker.IgnoreServiceV2
}

func NewIndexFolderTasks(userID string, uri *model.URIStatus, callback IndexTaskCallback,
	client knowledgebase.Client, projectID model.URI, useV2Ignore bool, ignore ignore_service.IgnoreService, isv2 ignore_rule_checker.IgnoreServiceV2,
	fs file_system.FileSystem, entityStorage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage) *IndexFolderTask {
	return &IndexFolderTask{
		userID:      userID,
		uri:         uri,
		callback:    callback,
		cli:         client,
		projectID:   projectID,
		useV2Ignore: useV2Ignore,
		ignore:      ignore,
		isv2:        isv2,
		fs:          fs,
		attr: model.IndexAttribute{
			ChunkingMethod: entityStorage.GetChunkingMethod(),
			EmbeddingModel: embeddingStorage.GetEmbeddingModel(),
		},
	}
}

func (it *IndexFolderTask) do() {
	ctx := util.NewBackgroundContext("")
	indexEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGIndex, it.userID, "", string(env.GetSourceProduct()))
	ctx = ckg_metrics.SetEvent(ctx, indexEvent)
	defer indexEvent.Report(ctx, false)

	var err error
	result := make([]*model.URIData, 0)

	defer func() {
		if rec := recover(); rec != nil {
			logs.CtxError(ctx, "panic recovered, err: %v, %s", rec, string(debug.Stack()))
		}
		it.callback(ctx, result)
	}()

	result, err = it.createFolderEntityAndRelation(ctx, it.uri)
	if err != nil {
		logs.CtxError(ctx, "error create folder entity and relations, err: %v", err)
		return
	}

	return
}

func (it *IndexFolderTask) ID() string {
	data := []byte(fmt.Sprintf("%s-%s", it.GetFilePath(), it.attr.String()))
	hash := sha256.Sum256(data)
	return fmt.Sprintf("p%x", hash)
}

func (it *IndexFolderTask) GetFilePath() string {
	return it.uri.AbsPath
}

func (it *IndexFolderTask) GetProject() string {
	return it.projectID
}

func (it *IndexFolderTask) IsRemoved() {
	it.callback(context.Background(), make([]*model.URIData, 0))
}

func (it *IndexFolderTask) GetChunkingMethod() string { return "" }
func (it *IndexFolderTask) GetEmbeddingModel() string { return "" }

var repoAliasName = []string{
	"项目",
	"工程",
	"仓库",
	"repo",
	"root folder",
	"root dir",
	"根目录",
}

func (it *IndexFolderTask) createFolderAliasEntity(entityID string, uri string) []*model.AliasEntity {
	var aliasEntity = make([]*model.AliasEntity, 0)
	var aliasNames = make([]string, 0)
	if entityID == "." {
		aliasNames = append(aliasNames, repoAliasName...)
	}

	subDir := util.GetSubdirectory(uri)
	for subDir != "" && (strings.Contains(subDir, "\\") || strings.Contains(subDir, "/")) {
		aliasNames = append(aliasNames, subDir)
		subDir = util.GetSubdirectory(subDir)
	}

	for _, alias := range aliasNames {
		aliasEntity = append(aliasEntity, &model.AliasEntity{
			EntityID: entityID,
			Alias:    alias,
		})
	}
	return aliasEntity
}

var repoLabels = []string{
	"repo",
	"仓库",
	"工程",
	"root",
	"根目录",
	"包",
	"package",
	"文件夹",
	"folder",
}

func (it *IndexFolderTask) createFolderEntityAndRelation(ctx context.Context, uri *model.URIStatus) ([]*model.URIData, error) {
	var results []*model.URIData
	info, err := os.Stat(uri.AbsPath)
	if err != nil {
		return nil, err
	}
	rel, err := filepath.Rel(it.projectID, uri.AbsPath)
	var entities = make([]*model.Entity, 0)
	var relations = make([]*model.Relation, 0)
	if err != nil {
		return nil, err
	}

	attributes := make(map[string]interface{})
	if rel == "." {
		attributes[model.AttributeLabelExternalInfo] = repoLabels
	}

	entity := model.CreateEntity(rel, info.Name(), uri.AbsPath, model.Folder, attributes)
	entities = append(entities, entity)

	files, err := os.ReadDir(uri.AbsPath)
	if err != nil {
		return nil, err
	}

	for _, file := range files {
		fullPath := filepath.Join(uri.AbsPath, file.Name())

		var needCreate bool
		if it.useV2Ignore {
			fileInfo, err := os.Stat(fullPath)
			if err != nil {
				logs.CtxWarn(ctx, "fullPath %s is not found", fullPath)
				continue
			}
			ignored, _, err := it.isv2.IsIgnore(ctx, it.userID, it.projectID, fullPath, fileInfo)
			if err != nil {
				logs.CtxError(ctx, "isValidUri err is %v  fullPath is %v", err, fullPath)
				continue
			}
			needCreate = !ignored
		} else {
			result, err := it.ignore.IsIgnored(ctx, fullPath)
			if err != nil {
				return nil, err
			}
			isValid, err := validator.IsValidForKnowledgeGraph(ctx, it.projectID, fullPath, 0, it.ignore, it.fs)
			if err != nil {
				logs.CtxError(ctx, "isValidUri err is %v  fullPath is %v", err, fullPath)
				continue
			}
			needCreate = !result && isValid
		}

		if needCreate {
			childUri := filepath.Join(uri.AbsPath, file.Name())
			childID, err := filepath.Rel(it.projectID, childUri)
			if err != nil {
				continue
			}

			relation := &model.Relation{
				StartID:   entity.ID,
				StartName: entity.Name,
				EndID:     childID,
				EndName:   file.Name(),
				Type:      model.FileToFile,
			}
			relations = append(relations, relation)
		}
	}

	alias := it.createFolderAliasEntity(entity.ID, uri.AbsPath)
	uriData := &model.URIData{
		Status:      *it.uri,
		Entities:    entities,
		Relation:    relations,
		AliasEntity: alias,
	}
	results = append(results, uriData)
	return results, nil
}

type IndexFileTask struct {
	userID    string
	uri       *model.URIStatus
	projectID model.URI

	cli knowledgebase.Client
	fs  file_system.FileSystem

	caller          string
	knowledgebaseID string

	fileToBeIndexedNum int

	callback IndexTaskCallback
	attr     model.IndexAttribute
}

func NewIndexFileTasks(knowledgebaseID, userID, caller string, uri *model.URIStatus,
	callback IndexTaskCallback, client knowledgebase.Client, projectID model.URI, fileToBeIndexedNum int,
	fs file_system.FileSystem, entityStorage data_storage.Storage) *IndexFileTask {
	return &IndexFileTask{
		knowledgebaseID:    knowledgebaseID,
		userID:             userID,
		uri:                uri,
		callback:           callback,
		cli:                client,
		projectID:          projectID,
		fs:                 fs,
		fileToBeIndexedNum: fileToBeIndexedNum,
		caller:             caller,
		attr: model.IndexAttribute{
			ChunkingMethod: entityStorage.GetChunkingMethod(),
			EmbeddingModel: "",
		},
	}
}

func (it *IndexFileTask) ID() string {
	data := []byte(fmt.Sprintf("%s-%s", it.GetFilePath(), it.attr.String()))
	hash := sha256.Sum256(data)
	return fmt.Sprintf("p%x", hash)
}

func (it *IndexFileTask) GetFilePath() string {
	return it.uri.AbsPath
}

func (it *IndexFileTask) GetProject() string {
	return it.projectID
}

func (it *IndexFileTask) IsRemoved() {
	it.callback(context.Background(), make([]*model.URIData, 0))
}

func (it *IndexFileTask) GetChunkingMethod() string { return it.attr.ChunkingMethod }
func (it *IndexFileTask) GetEmbeddingModel() string { return it.attr.EmbeddingModel }

func (it *IndexFileTask) do() {
	ctx := util.NewBackgroundContext("")
	indexEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGIndex, it.userID, "", string(env.GetSourceProduct()))
	ctx = ckg_metrics.SetEvent(ctx, indexEvent)
	defer indexEvent.Report(ctx, false)

	var err error
	result := make([]*model.URIData, 0)

	defer func() {
		if rec := recover(); rec != nil {
			logs.CtxError(ctx, "panic recovered, err: %v, %s", rec, string(debug.Stack()))
		}
		it.callback(ctx, result)
	}()

	if env.TokenIsOutdated(ctx, env.GetToken(it.userID)) {
		indexEvent.AddTeaParam(ckg_metrics.TeaParamRemoteIndexTokenExpired, true)
		logs.CtxDebug(ctx, "token may expired, skip index file %s", validator.EncryptPath(it.uri.AbsPath))
		return
	}

	rel, err := it.fs.Rel(it.projectID, it.uri.AbsPath)
	if err != nil {
		indexEvent.AddTeaParam(ckg_metrics.TeaParamRemoteIndexFileNotExisted, true)
		return
	}
	value, err := it.fs.ReadFile(it.uri.AbsPath)
	if err != nil {
		indexEvent.AddTeaParam(ckg_metrics.TeaParamRemoteIndexFileNotExisted, true)
		return
	}
	content := string(value)

	files := []*knowledgebase.File{
		{
			FileID:      rel,
			Content:     content,
			ContentHash: it.uri.ContentHash,
		},
	}
	projectFiles := []*knowledgebase.ProjectFile{
		{
			ProjectID: it.projectID,
			Files:     files,
		},
	}
	request := &knowledgebase.IndexFilesRequest{
		ProjectFiles: projectFiles,
	}

	indexEvent.AddTeaParam(ckg_metrics.TeaParamIndexRequestSent, true)

	resp, err := it.cli.IndexFiles(ctx, env.GetToken(it.userID), it.knowledgebaseID, it.caller, request)
	if err != nil {
		indexEvent.AddTeaParam(ckg_metrics.TeaParamIndexRequestFailed, true)
		logs.CtxError(ctx, "IndexFiles request failed: err is %v file is %s", err, validator.EncryptPath(it.uri.AbsPath))
		return
	}
	indexEvent.AddTeaParam(ckg_metrics.TeaParamRemoteIndexCurrFileNum, it.fileToBeIndexedNum)
	if len(resp.ProjectFiles) == 0 {
		indexEvent.AddTeaParam(ckg_metrics.TeaParamRemoteIndexRequestUnexpected, true)
		logs.CtxError(ctx, "IndexFiles failed: resp project files is empty: %s", validator.EncryptPath(it.uri.AbsPath))
	} else if len(resp.ProjectFiles[0].Files) == 0 {
		indexEvent.AddTeaParam(ckg_metrics.TeaParamRemoteIndexRequestUnexpected, true)
		logs.CtxError(ctx, "IndexFiles failed: project files -> files is empty: %s", validator.EncryptPath(it.uri.AbsPath))
	} else if len(resp.ProjectFiles[0].Files[0].Segments) == 0 {
		indexEvent.AddTeaParam(ckg_metrics.TeaParamIndexFailed, true)
		logs.CtxError(ctx, "IndexFiles failed: project files segments is empty: %s", validator.EncryptPath(it.uri.AbsPath))
	}

	result = resp.ToURIData(it.uri)
}

type LocalIndexFileTask struct {
	uri                *model.URIStatus
	callback           LocalIndexTaskCallback
	cli                knowledgebase.Client
	projectID          model.URI
	projectType        model.ProjectType
	userID             string
	config             *ckg_config.Config
	fs                 file_system.FileSystem
	caller             string
	attr               model.IndexAttribute
	indexTaskAttribute *model.IndexTaskAttribute

	useV2Ignore bool
	ignore      ignore_service.IgnoreService
	isv2        ignore_rule_checker.IgnoreServiceV2
}

type LocalIndexTaskCallback func(ctx context.Context, uriData []*model.URIData, embeddingData []*model.EmbeddingData)

func NewLocalIndexFileTasks(storage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage, userID, caller string, uri *model.URIStatus,
	callback LocalIndexTaskCallback, client knowledgebase.Client, projectID model.URI, projectType model.ProjectType, useV2Ignore bool, ignore ignore_service.IgnoreService,
	isv2 ignore_rule_checker.IgnoreServiceV2, config *ckg_config.Config, fs file_system.FileSystem, indexTaskAttribute *model.IndexTaskAttribute) *LocalIndexFileTask {
	return &LocalIndexFileTask{
		userID:      userID,
		uri:         uri,
		callback:    callback,
		cli:         client,
		projectID:   projectID,
		projectType: projectType,
		useV2Ignore: useV2Ignore,
		ignore:      ignore,
		isv2:        isv2,
		config:      config,
		fs:          fs,
		caller:      caller,
		attr: model.IndexAttribute{
			ChunkingMethod: storage.GetChunkingMethod(),
			EmbeddingModel: embeddingStorage.GetEmbeddingModel(),
		},
		indexTaskAttribute: indexTaskAttribute,
	}
}

func (lit *LocalIndexFileTask) ID() string {
	data := []byte(fmt.Sprintf("%s-%s", lit.GetFilePath(), lit.attr.String()))
	hash := sha256.Sum256(data)
	return fmt.Sprintf("p%x", hash)
}

func (lit *LocalIndexFileTask) GetFilePath() model.URI {
	return lit.uri.AbsPath
}

func (lit *LocalIndexFileTask) GetProject() string {
	return lit.projectID
}

func (lit *LocalIndexFileTask) IsRemoved() {
	lit.callback(context.Background(), make([]*model.URIData, 0), make([]*model.EmbeddingData, 0))
}

func (lit *LocalIndexFileTask) GetChunkingMethod() string { return lit.attr.ChunkingMethod }
func (lit *LocalIndexFileTask) GetEmbeddingModel() string { return lit.attr.EmbeddingModel }

func (lit *LocalIndexFileTask) do() {
	var err error
	ctx := util.NewBackgroundContext("")
	localIndexEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGLocalIndex, lit.userID, "", string(env.GetSourceProduct()))
	ctx = ckg_metrics.SetEvent(ctx, localIndexEvent)
	defer localIndexEvent.Report(ctx, false)

	uriData := make([]*model.URIData, 0)
	embeddingData := make([]*model.EmbeddingData, 0)
	defer func() {
		if rec := recover(); rec != nil {
			logs.CtxError(ctx, "panic recovered, err: %v, %s", rec, string(debug.Stack()))
		}
		lit.callback(ctx, uriData, embeddingData)
	}()

	if lit.uri == nil || lit.cli == nil || lit.fs == nil {
		logs.CtxError(ctx, "invalid parameters: uri, cli or fs is nil")
		return
	}

	if env.TokenIsOutdated(ctx, env.GetToken(lit.userID)) {
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexTokenExpired, true)
		logs.CtxDebug(ctx, "token may expired, skip index file %s", lit.uri.AbsPath)
		return
	}

	// 再次判断 Pop 出的任务是否应被忽略
	// 但是 ProjectTypeVirtual 不能判断，因为它不在磁盘上，Stat 会报错从而返回 false
	if lit.projectType != model.ProjectTypeVirtual {
		if lit.useV2Ignore {
			info, err := os.Stat(lit.GetFilePath())
			if err != nil {
				logs.CtxError(ctx, "file %s not existed", lit.GetFilePath())
				return
			}
			ignored, _, err := lit.isv2.IsIgnore(ctx, lit.userID, lit.GetProject(), lit.GetFilePath(), info)
			if err != nil {
				logs.CtxError(ctx, "validation failed for file %s, err: %+v", lit.GetFilePath(), err)
				return
			}
			if ignored {
				localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexIgnore, true)
				logs.CtxInfo(ctx, "file %s ignored by new rules", lit.GetFilePath())
				return
			}
		} else {
			config := lit.config.GetTCCConfig(ctx, lit.cli, lit.userID)
			valid, err := validator.IsValidForKnowledgeGraph(ctx, lit.GetProject(), lit.GetFilePath(), config.FileSizeThreshold, lit.ignore, lit.fs)
			if err != nil {
				logs.CtxError(ctx, "validation failed for file %s, err: %+v", lit.GetFilePath(), err)
				return
			}
			if !valid {
				localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexIgnore, true)
				logs.CtxInfo(ctx, "file %s ignored by new rules", lit.GetFilePath())
				return
			}
		}
	}

	// 兼容 index virtual project 的场景（content 在 URIStatus 中）
	var content string
	var rel string
	if lit.projectType == model.ProjectTypeVirtual {
		content = lit.uri.Content
		if len(content) == 0 {
			data, err := lit.fs.ReadFile(lit.uri.AbsPath)
			if err != nil {
				localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexFileNotExisted, true)
				return
			}
			content = string(data)
		}
		rel = lit.uri.RelPath
	} else {
		// 切分该文件，根据 uri 的 AbsPath，最终得到 SplitFiles 以及对应的 URIData (写入 SQLite)
		var err error
		rel, err = lit.fs.Rel(lit.projectID, lit.uri.AbsPath)
		if err != nil {
			localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexFileNotExisted, true)
			return
		}
		data, err := lit.fs.ReadFile(lit.uri.AbsPath)
		if err != nil {
			localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexFileNotExisted, true)
			return
		}
		content = string(data)
	}

	var splitResp *knowledgebase.SplitFilesResponse
	if lit.projectType == model.ProjectTypeVirtual {
		// localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexSplitSent, true)
		splitFile := &knowledgebase.SplitFile{
			Content:   content,
			Path:      rel,
			ChunkSize: int32(lit.indexTaskAttribute.ChunkingSize),
		}

		opts := &splittermodel.SplitOption{
			// splitter 类型（本地/远端）
			SplitterType: splittermodel.GetSplitterType(lit.indexTaskAttribute.ChunkingMethod),
			// 本地 splitter 选项
			LocalOpt: &splittermodel.LocalSplitOption{
				// 内容类型
				ContentType: splittermodel.GetContentType(lit.indexTaskAttribute.ChunkingMethod),
				// 本地 splitter 子类型
				LocalSubtype: splittermodel.GetLocalSubtype(lit.indexTaskAttribute.ChunkingMethod),
				// Langchain 特定选项
				LangchainOpt: &splittermodel.LangchainSplitOption{
					// Langchain splitter 类型
					SplitterType: splittermodel.GetLangchainType(lit.indexTaskAttribute.ChunkingMethod),
					// 基本选项
					ChunkSize:    lit.indexTaskAttribute.ChunkingSize,
					ChunkOverlap: lit.indexTaskAttribute.ChunkingOverlap,
				},
			},
			// 知识库选项（用于回退）
			KBOpt: &splittermodel.KBSplitOption{
				KBClient:       lit.cli,
				DatastoreName:  lit.attr.EmbeddingModel,
				ChunkingMethod: lit.attr.ChunkingMethod,
				UserID:         lit.userID,
			},
		}

		splitResp, err = splitter.Split(ctx, opts, lit.caller, []*knowledgebase.SplitFile{splitFile})
		if err != nil {
			logs.CtxError(ctx, "[LocalIndexFileTask] split file failed, err is %v", err)
			localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexSplitFailed, true)
			return
		}
		// 如果是空文件，splitResp 或 Results 为空是正常的
		if splitResp == nil || splitResp.Results == nil {
			logs.CtxInfo(ctx, "[LocalIndexFileTask] empty file or no results: %s", lit.uri.AbsPath)
			return
		}
		uriData = splitResp.ToURIData([]*model.URIStatus{lit.uri})
		if lit.indexTaskAttribute.RemoveEntityContent && !env.KeepVirtualProjectEntityContent {
			for _, data := range uriData {
				for _, entity := range data.Entities {
					delete(entity.Attributes, "content")
				}
			}
		}
	} else {
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexSplitSent, true)
		splitFile := knowledgebase.GetSplitFile(rel, content)

		if lit.config.IsIndexFeatureEnabled(ctx, lit.userID, ckg_config.UseV2SplitEmbeddingAPI) {
			localIndexEvent.AddTeaParam(ckg_metrics.TeaParamUseSplitFilesAPIVersion, "v2")
			splitResp, err = lit.cli.SplitFilesV2(ctx, env.GetToken(lit.userID), lit.caller, rel, &knowledgebase.SplitFilesRequestV2{
				EmbeddingModel: lit.attr.EmbeddingModel,
				Files:          []*knowledgebase.SplitFile{splitFile},
				ChunkingMethod: lit.attr.ChunkingMethod,
			})
		} else {
			localIndexEvent.AddTeaParam(ckg_metrics.TeaParamUseSplitFilesAPIVersion, "v1")
			splitResp, err = lit.cli.SplitFiles(ctx, env.GetToken(lit.userID), lit.caller, &knowledgebase.SplitFilesRequest{
				DatastoreName:  lit.attr.EmbeddingModel,
				Files:          []*knowledgebase.SplitFile{splitFile},
				ChunkingMethod: lit.attr.ChunkingMethod,
			})

		}
		if err != nil {
			logs.CtxError(ctx, "[LocalIndexFileTask] split file failed, err is %v", err)
			localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexSplitFailed, true)
			return
		}
		uriData = splitResp.ToURIData([]*model.URIStatus{lit.uri})
	}
	if splitResp == nil {
		logs.CtxError(ctx, "[LocalIndexFileTask] split file failed, uri is %s, filePath is %s, relative path is %s", lit.uri.UriCanonical, lit.uri.AbsPath, lit.uri.RelPath)
		return
	}

	// 为未计算过 embedding 的 content 计算 embedding，最终得到 EmbeddingData (写入向量库)
	embeddingInvokeTime, embeddingInvokeFailedTime := atomic.NewInt32(0), atomic.NewInt32(0)
	embeddingsLenNotEq, stillNotAllEmbedding := atomic.NewInt32(0), atomic.NewInt32(0)
	embeddingContentTotalNum, embeddingMemCacheHitNum, embeddingAbaseHitNum := atomic.NewInt32(0), atomic.NewInt32(0), atomic.NewInt32(0)
	embeddingFileTotalNum, embeddingAllCacheHitFileNum := atomic.NewInt32(0), atomic.NewInt32(0)
	defer func() {
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbedSent, embeddingInvokeTime.Load())
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbedFailed, embeddingInvokeFailedTime.Load())
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingLenNotEq, embeddingsLenNotEq.Load())
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingContentTotal, embeddingContentTotalNum.Load())
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingMemCacheHit, embeddingMemCacheHitNum.Load())
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingAbaseHit, embeddingAbaseHitNum.Load())
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingFileTotal, embeddingFileTotalNum.Load())
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingAllCacheHit, embeddingAllCacheHitFileNum.Load())
		localIndexEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingStillNotAll, stillNotAllEmbedding.Load())
	}()
	for fileRelPath, splitFileRes := range splitResp.Results {
		if splitFileRes == nil {
			continue
		}
		embeddingFileTotalNum.Inc()
		var allContentsNeedEmbedded []string
		for _, seg := range splitFileRes.SegmentsWithEmbedding {
			if seg == nil {
				continue
			}
			embeddingContentTotalNum.Add(int32(len(seg.EmbeddingContents)))
			if seg.IsAllEmbedding {
				embeddingAbaseHitNum.Add(int32(len(seg.EmbeddingContents)))
				continue
			}
			if seg.Embeddings == nil {
				seg.Embeddings = make(map[string][]float64)
			}
			for _, embeddingContent := range seg.EmbeddingContents {
				if _, ok := seg.Embeddings[embeddingContent]; ok {
					// 如果该 content 已经计算过 embedding 并从 abase 返回了，不需要加入
					embeddingAbaseHitNum.Inc()
					continue
				}
				if val, ok := lru.Get(embeddingContent); ok {
					// 如果该 content 在 lru 中，说明该 content 曾计算过 embedding 并返回到本地过
					embeddingMemCacheHitNum.Inc()
					seg.Embeddings[embeddingContent] = val.Embedding
					continue
				}
				allContentsNeedEmbedded = append(allContentsNeedEmbedded, embeddingContent)
			}
		}
		if len(allContentsNeedEmbedded) == 0 {
			// allEmbeddingContent 是待计算 embedding 的 content。
			// 如果 allEmbeddingContent 为空，说明该文件的所有 content 都已经计算过 embedding。
			// 需要在 continue 前 "将获取的 embedding 结果添加到 embeddingData 中"。
			embeddingData = append(embeddingData, splitFileRes.ToEmbeddingData(fileRelPath)...)
			embeddingAllCacheHitFileNum.Inc()
			continue
		}

		embeddingInvokeTime.Inc()
		var embeddingResp *knowledgebase.EmbeddingResponse
		var err error
		if lit.config.IsIndexFeatureEnabled(ctx, lit.userID, ckg_config.UseV2SplitEmbeddingAPI) {
			embeddingResp, err = lit.cli.EmbeddingV2(ctx, env.GetToken(lit.userID), lit.caller, &knowledgebase.EmbeddingRequestV2{
				EmbeddingModel:    lit.attr.EmbeddingModel,
				EmbeddingContents: allContentsNeedEmbedded,
				OfflineCluster:    true,
			})
		} else {
			embeddingResp, err = lit.cli.Embedding(ctx, env.GetToken(lit.userID), lit.caller, &knowledgebase.EmbeddingRequest{
				DatastoreName:     lit.attr.EmbeddingModel,
				EmbeddingContents: allContentsNeedEmbedded,
				OfflineCluster:    true,
			})
		}
		if err != nil || embeddingResp == nil {
			logs.CtxError(ctx, "Embedding error: %+v", err)
			embeddingInvokeFailedTime.Inc()
			continue
		}
		if len(embeddingResp.Embeddings) != len(allContentsNeedEmbedded) {
			logs.CtxWarn(ctx, "length of resp embedding not equal to length of contents that need embedded")
			embeddingsLenNotEq.Inc()
			continue
		}
		// 收集所有的 embedding content -> embedding 结果，组成 map
		embeddingsMap := make(map[string]*knowledgebase.Embedding)
		for idx, embeddingContent := range allContentsNeedEmbedded {
			if idx >= len(embeddingResp.Embeddings) || embeddingResp.Embeddings[idx] == nil {
				continue
			}
			embeddingsMap[embeddingContent] = embeddingResp.Embeddings[idx]
			// set memory cache
			lru.Add(embeddingContent, embeddingResp.Embeddings[idx])
		}
		for _, seg := range splitFileRes.SegmentsWithEmbedding {
			if seg == nil {
				continue
			}
			if seg.IsAllEmbedding || len(seg.Embeddings) == len(seg.EmbeddingContents) {
				seg.IsAllEmbedding = true
				continue
			}
			for _, embeddingContent := range seg.EmbeddingContents {
				if kbEmbedding, ok := embeddingsMap[embeddingContent]; ok {
					seg.Embeddings[embeddingContent] = kbEmbedding.Embedding
				}
			}
			seg.IsAllEmbedding = len(seg.Embeddings) >= len(seg.EmbeddingContents)
			if !seg.IsAllEmbedding {
				// 异常情况：调用 Embedding 后仍然小于 embeddingContents 大小
				stillNotAllEmbedding.Inc()
			}
		}
		embeddingData = append(embeddingData, splitFileRes.ToEmbeddingData(fileRelPath)...)
	}
}

type DeleteTaskCallback func(ctx context.Context, uriMeta *data_storage.StorageURIMeta, uri *model.URIStatus)

type DeleteTask struct {
	uri             *model.URIStatus
	uriMeta         *data_storage.StorageURIMeta
	callback        DeleteTaskCallback
	projectID       model.URI
	cli             knowledgebase.Client
	userID          string
	knowledgebaseID string
	attr            model.IndexAttribute
}

func (dt *DeleteTask) ID() string {
	data := []byte(fmt.Sprintf("%s-%s", dt.GetFilePath(), dt.attr.String()))
	hash := sha256.Sum256(data)
	return fmt.Sprintf("p%x", hash)
}

func (dt *DeleteTask) GetFilePath() string {
	return dt.uri.AbsPath
}

func (dt *DeleteTask) GetProject() string {
	return dt.projectID
}
func (dt *DeleteTask) GetChunkingMethod() string { return dt.attr.ChunkingMethod }
func (dt *DeleteTask) GetEmbeddingModel() string { return dt.attr.EmbeddingModel }

// IsRemoved of DeleteTask doesn't need invoked, since we cannot cancel a delete task
func (dt *DeleteTask) IsRemoved() {}

func NewDeleteTasks(knowledgebaseID, userID string, uriMeta *data_storage.StorageURIMeta, uri *model.URIStatus,
	callback DeleteTaskCallback, projectID model.URI, cli knowledgebase.Client,
	entityStorage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage) *DeleteTask {
	return &DeleteTask{
		knowledgebaseID: knowledgebaseID,
		userID:          userID,
		uriMeta:         uriMeta,
		uri:             uri,
		callback:        callback,
		cli:             cli,
		projectID:       projectID,
		attr: model.IndexAttribute{
			ChunkingMethod: entityStorage.GetChunkingMethod(),
			EmbeddingModel: embeddingStorage.GetEmbeddingModel(),
		},
	}
}

func (dt *DeleteTask) do() {
	ctx := util.NewBackgroundContext("")
	defer func() {
		if rec := recover(); rec != nil {
			logs.CtxError(ctx, "panic recovered, err: %v, %s", rec, string(debug.Stack()))
		}
		dt.callback(ctx, dt.uriMeta, dt.uri)
	}()

	rel, err := filepath.Rel(dt.projectID, dt.uri.AbsPath)
	if err != nil {
		return
	}
	files := []*knowledgebase.DeleteFile{
		{
			FileID: rel,
		},
	}
	projectFiles := []*knowledgebase.DeleteProjectFile{
		{
			ProjectID: dt.projectID,
			Files:     files,
		},
	}
	request := &knowledgebase.DeleteFileRequest{
		DeleteFiles: projectFiles,
	}
	err = dt.cli.DeleteFile(ctx, env.GetToken(dt.userID), dt.knowledgebaseID, request)
	if err != nil {
		logs.CtxError(ctx, "DeleteFile err is %v file is %s", err, dt.uri.AbsPath)
		return
	}
}

type LocalDeleteTask struct {
	uri       *model.URIStatus
	uriMeta   *data_storage.StorageURIMeta
	callback  DeleteTaskCallback
	projectID model.URI
	attr      model.IndexAttribute
}

func (ldt *LocalDeleteTask) GetFilePath() model.URI { return ldt.uri.AbsPath }

func NewLocalDeleteTasks(uriMeta *data_storage.StorageURIMeta, uri *model.URIStatus, projectID model.URI,
	callback DeleteTaskCallback, entityStorage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage) *LocalDeleteTask {
	return &LocalDeleteTask{
		uriMeta:  uriMeta,
		uri:      uri,
		callback: callback,
		attr: model.IndexAttribute{
			ChunkingMethod: entityStorage.GetChunkingMethod(),
			EmbeddingModel: embeddingStorage.GetEmbeddingModel(),
		},
	}
}

func (ldt *LocalDeleteTask) ID() string {
	data := []byte(fmt.Sprintf("%s-%s", ldt.GetFilePath(), ldt.attr.String()))
	hash := sha256.Sum256(data)
	return fmt.Sprintf("p%x", hash)
}

func (ldt *LocalDeleteTask) do() {
	ctx := util.NewBackgroundContext("")
	defer func() {
		if rec := recover(); rec != nil {
			logs.CtxError(ctx, "panic recovered, err: %v, %s", rec, string(debug.Stack()))
		}
		ldt.callback(ctx, ldt.uriMeta, ldt.uri)
	}()
}

func (ldt *LocalDeleteTask) GetProject() string {
	return ldt.projectID
}

// IsRemoved of LocalDeleteTask doesn't need invoked, since we cannot cancel a delete task
func (ldt *LocalDeleteTask) IsRemoved()                {}
func (ldt *LocalDeleteTask) GetChunkingMethod() string { return ldt.attr.ChunkingMethod }
func (ldt *LocalDeleteTask) GetEmbeddingModel() string { return ldt.attr.EmbeddingModel }

type LocalChangedPreTask struct {
	filePath string
	callback LocalChangedPreTaskCallback
}

type LocalChangedPreTaskCallback func(ctx context.Context)

func NewLocalChangedPreTask(filePath string, callback LocalChangedPreTaskCallback) *LocalChangedPreTask {
	return &LocalChangedPreTask{
		filePath: filePath,
		callback: callback,
	}
}

func (lcpt *LocalChangedPreTask) ID() string {
	data := []byte(fmt.Sprintf("change-pre-task-%s", lcpt.GetFilePath()))
	hash := sha256.Sum256(data)
	return fmt.Sprintf("p%x", hash)
}

func (lcpt *LocalChangedPreTask) do() { lcpt.callback(util.NewBackgroundContext("")) }

// GetProject 用于取消索引，FileChange 不取消
func (lcpt *LocalChangedPreTask) GetProject() string  { return "" }
func (lcpt *LocalChangedPreTask) GetFilePath() string { return lcpt.filePath }

// IsRemoved of LocalChangedPreTask doesn't need invoked, since we cannot cancel a file-changed pre-task
func (lcpt *LocalChangedPreTask) IsRemoved()                {}
func (lcpt *LocalChangedPreTask) GetChunkingMethod() string { return "" }
func (lcpt *LocalChangedPreTask) GetEmbeddingModel() string { return "" }
