import {DocumentUri, Position} from "vscode-languageserver-types";

export interface PsiTask {
  command: string;
  languageId: string;
  uri: DocumentUri;
  position: Position;
  args: any[];
}

export interface PsiTaskInQueue extends PsiTask {
  promise: {resolve: any, reject: any},
}

export const psiQueue: PsiTaskInQueue[] = [];

export async function addWorkerPsiTask(task: PsiTask): Promise<any> {
  return new Promise<any>((resolve, reject) => {
    const taskInQueue = {...task, promise: {resolve, reject}};
    psiQueue.push(taskInQueue);
  });
}