package tasks

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime/debug"

	"github.com/samber/lo"
	"go.uber.org/atomic"

	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/env"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/components/ignore_service"
	"ide/ckg/codekg/components/ignore_service/ignore_rule_checker"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/components/splitter"
	splittermodel "ide/ckg/codekg/components/splitter/model"
	"ide/ckg/codekg/components/validator"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/util"
)

type BatchLocalIndexSplitFileTask struct {
	userId             string
	projectId          model.URI
	projectType        model.ProjectType
	config             *ckg_config.Config
	uri                *model.URIStatus
	attr               model.IndexAttribute
	indexTaskAttribute *model.IndexTaskAttribute

	cli knowledgebase.Client
	fs  file_system.FileSystem

	callback BatchLocalIndexSplitTaskCallback

	useV2Ignore bool
	ignore      ignore_service.IgnoreService
	isv2        ignore_rule_checker.IgnoreServiceV2
}

type BatchLocalIndexSplitTaskCallback func(ctx context.Context,
	uriData []*model.URIData, embeddingData []*model.EmbeddingData, embeddingContents []*model.BatchEmbeddingMsg)

func NewBatchLocalIndexSplitFileTask(userId string, projectId model.URI, projectType model.ProjectType,
	config *ckg_config.Config,
	uri *model.URIStatus, attr model.IndexAttribute,
	indexTaskAttribute *model.IndexTaskAttribute,
	cli knowledgebase.Client, useV2Ignore bool, ignore ignore_service.IgnoreService, isv2 ignore_rule_checker.IgnoreServiceV2, fs file_system.FileSystem,
	callback BatchLocalIndexSplitTaskCallback) *BatchLocalIndexSplitFileTask {
	return &BatchLocalIndexSplitFileTask{
		userId:             userId,
		projectId:          projectId,
		projectType:        projectType,
		config:             config,
		uri:                uri,
		attr:               attr,
		indexTaskAttribute: indexTaskAttribute,
		cli:                cli,
		fs:                 fs,
		callback:           callback,

		useV2Ignore: useV2Ignore,
		ignore:      ignore,
		isv2:        isv2,
	}
}

func (bst *BatchLocalIndexSplitFileTask) do() {
	ctx := util.NewBackgroundContext("")
	batchSplitEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGBatchSplit, bst.userId, "", string(env.GetSourceProduct()))
	ctx = ckg_metrics.SetEvent(ctx, batchSplitEvent)
	defer batchSplitEvent.Report(ctx, false)

	uriData, embeddingData := make([]*model.URIData, 0), make([]*model.EmbeddingData, 0)
	embeddingContents := make([]*model.BatchEmbeddingMsg, 0)
	defer func() {
		if rec := recover(); rec != nil {
			logs.CtxError(ctx, "panic recovered, err: %v, %s", rec, string(debug.Stack()))
		}
		bst.callback(ctx, uriData, embeddingData, embeddingContents)
	}()
	if env.TokenIsOutdated(ctx, env.GetToken(bst.userId)) {
		batchSplitEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexTokenExpired, true)
		logs.CtxDebug(ctx, "token may expired, skip index file %s", validator.EncryptPath(bst.uri.AbsPath))
		return
	}

	// 再次判断 Pop 出的任务是否应被忽略，同 LocalIndexFileTask
	// 但是 ProjectTypeVirtual 不能判断，因为它不在磁盘上，Stat 会报错从而返回 false
	if bst.projectType != model.ProjectTypeVirtual {
		if bst.useV2Ignore {
			info, err := os.Stat(bst.GetFilePath())
			if err != nil {
				logs.CtxError(ctx, "get file %s stat error", bst.GetFilePath())
				return
			}
			ignored, _, err := bst.isv2.IsIgnore(ctx, bst.userId, bst.GetProject(), bst.GetFilePath(), info)
			if err != nil {
				logs.CtxError(ctx, "validation failed for file %s, err: %+v", validator.EncryptPath(bst.GetFilePath()), err)
				return
			}
			if ignored {
				batchSplitEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexIgnore, true)
				logs.CtxInfo(ctx, "file %s ignored by new rules", validator.EncryptPath(bst.GetFilePath()))
				return
			}
		} else {
			config := bst.config.GetTCCConfig(ctx, bst.cli, bst.userId)
			valid, err := validator.IsValidForKnowledgeGraph(ctx, bst.GetProject(), bst.GetFilePath(), config.FileSizeThreshold, bst.ignore, bst.fs)
			if err != nil {
				logs.CtxError(ctx, "validation failed for file %s, err: %+v", validator.EncryptPath(bst.GetFilePath()), err)
				return
			}
			if !valid {
				batchSplitEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexIgnore, true)
				logs.CtxInfo(ctx, "file %s ignored by new rules", validator.EncryptPath(bst.GetFilePath()))
				return
			}
		}
	}

	uriData, embeddingData, embeddingContents = bst.innerSplitFile(ctx, batchSplitEvent, bst.projectId, bst.projectType, bst.userId, bst.uri, bst.attr, bst.indexTaskAttribute, bst.cli, bst.fs)
}

func (bst *BatchLocalIndexSplitFileTask) ID() string {
	data := []byte(fmt.Sprintf("%s-%s", bst.GetFilePath(), bst.attr.String()))
	hash := sha256.Sum256(data)
	return fmt.Sprintf("p%x", hash)
}

func (bst *BatchLocalIndexSplitFileTask) GetFilePath() string       { return bst.uri.AbsPath }
func (bst *BatchLocalIndexSplitFileTask) GetProject() string        { return bst.projectId }
func (bst *BatchLocalIndexSplitFileTask) GetChunkingMethod() string { return bst.attr.ChunkingMethod }
func (bst *BatchLocalIndexSplitFileTask) GetEmbeddingModel() string { return bst.attr.EmbeddingModel }

func (bst *BatchLocalIndexSplitFileTask) IsRemoved() {
	bst.callback(context.Background(), []*model.URIData{}, []*model.EmbeddingData{}, []*model.BatchEmbeddingMsg{})
}

// 该方法部分内容直接 copy 自 task.go 中的 LocalIndexFileTask 的 do()
// 但有部分更改，e.g. 删去了其中调用 Embedding API 的部分，并且没有统计 Embedding 相关的部分数据。
func (bst *BatchLocalIndexSplitFileTask) innerSplitFile(ctx context.Context, event ckg_metrics.Event, projectId model.URI, projectType model.ProjectType, userId string,
	uri *model.URIStatus, attr model.IndexAttribute, indexTaskAttribute *model.IndexTaskAttribute,
	cli knowledgebase.Client, fs file_system.FileSystem,
) (uriData []*model.URIData, embeddingData []*model.EmbeddingData, allContentsNeedEmbedded []*model.BatchEmbeddingMsg) {
	if uri == nil || cli == nil || fs == nil {
		logs.CtxError(ctx, "invalid parameters: uri, cli or fs is nil")
		return nil, nil, nil
	}

	// 兼容 index virtual project 的场景（content 在 URIStatus 中）
	var content string
	var rel string
	if projectType == model.ProjectTypeVirtual {
		content = uri.Content
		if len(content) == 0 {
			data, err := fs.ReadFile(uri.AbsPath)
			if err != nil {
				event.AddTeaParam(ckg_metrics.TeaParamLocalIndexFileNotExisted, true)
				return
			}
			content = string(data)
		}
		rel = uri.RelPath
	} else {
		// 切分该文件，根据 uri 的 AbsPath，最终得到 SplitFiles 以及对应的 URIData (写入 SQLite)
		var err error
		rel, err = fs.Rel(projectId, uri.AbsPath)
		if err != nil {
			event.AddTeaParam(ckg_metrics.TeaParamLocalIndexFileNotExisted, true)
			return
		}
		data, err := fs.ReadFile(uri.AbsPath)
		if err != nil {
			event.AddTeaParam(ckg_metrics.TeaParamLocalIndexFileNotExisted, true)
			return
		}
		content = string(data)
	}

	if projectType == model.ProjectTypeVirtual {
		// localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexSplitSent, true)
		return bst.splitVirtualProjectFiles(ctx, event, projectId, userId, uri, attr, indexTaskAttribute, rel, content, cli, fs)
	} else {
		event.AddTeaParam(ckg_metrics.TeaParamLocalIndexSplitSent, true)
		return bst.splitFiles(ctx, event, projectId, userId, uri, attr, rel, content, cli, fs)
	}
}

func (bst *BatchLocalIndexSplitFileTask) splitFiles(ctx context.Context, event ckg_metrics.Event, projectId model.URI, userId string,
	uri *model.URIStatus, attr model.IndexAttribute,
	rel, content string, cli knowledgebase.Client, fs file_system.FileSystem,
) (uriData []*model.URIData, embeddingData []*model.EmbeddingData, allContentsNeedEmbedded []*model.BatchEmbeddingMsg) {
	event.AddTeaParam(ckg_metrics.TeaParamLocalIndexSplitSent, true)
	splitFile := knowledgebase.GetSplitFile(rel, content)

	var splitResp *knowledgebase.SplitFilesResponse
	var err error
	if bst.config.IsIndexFeatureEnabled(ctx, bst.userId, ckg_config.UseV2SplitEmbeddingAPI) {
		event.AddTeaParam(ckg_metrics.TeaParamUseSplitFilesAPIVersion, "v2")
		splitResp, err = cli.SplitFilesV2(ctx, env.GetToken(userId), ckg_metrics.EventNameCKGBatchSplit, rel, &knowledgebase.SplitFilesRequestV2{
			Files:          []*knowledgebase.SplitFile{splitFile},
			EmbeddingModel: attr.EmbeddingModel,
			ChunkingMethod: attr.ChunkingMethod,
		})
	} else {
		event.AddTeaParam(ckg_metrics.TeaParamUseSplitFilesAPIVersion, "v1")
		splitResp, err = cli.SplitFiles(ctx, env.GetToken(userId), ckg_metrics.EventNameCKGBatchSplit, &knowledgebase.SplitFilesRequest{
			Files:          []*knowledgebase.SplitFile{splitFile},
			DatastoreName:  attr.EmbeddingModel,
			ChunkingMethod: attr.ChunkingMethod,
		})
	}
	if err != nil {
		logs.CtxError(ctx, "[BatchLocalIndexSplitFileTask] split file failed, err is %v", err)
		event.AddTeaParam(ckg_metrics.TeaParamLocalIndexSplitFailed, true)
		return
	}
	if splitResp == nil {
		logs.CtxError(ctx, "[BatchLocalIndexSplitFileTask] split file failed, uri is %s, filePath is %s, relative path is %s", validator.EncryptPath(uri.UriCanonical), validator.EncryptPath(uri.AbsPath), validator.EncryptPath(uri.RelPath))
		event.AddTeaParam(ckg_metrics.TeaParamLocalIndexSplitFailed, true)
		return
	}
	uriData = splitResp.ToURIData([]*model.URIStatus{uri})

	// 为未计算过 embedding 的 content 计算 embedding，最终得到 EmbeddingData (写入向量库)
	embeddingContentTotalNum, embeddingAbaseHitNum := atomic.NewInt32(0), atomic.NewInt32(0)
	embeddingFileTotalNum, embeddingAllCacheHitFileNum := atomic.NewInt32(0), atomic.NewInt32(0)
	defer func() {
		event.AddTeaParam(ckg_metrics.TeaParamEmbeddingContentTotal, embeddingContentTotalNum.Load())
		event.AddTeaParam(ckg_metrics.TeaParamEmbeddingAbaseHit, embeddingAbaseHitNum.Load())
		event.AddTeaParam(ckg_metrics.TeaParamEmbeddingFileTotal, embeddingFileTotalNum.Load())
		event.AddTeaParam(ckg_metrics.TeaParamEmbeddingAllCacheHit, embeddingAllCacheHitFileNum.Load())
	}()
	for fileRelPath, splitFileRes := range splitResp.Results {
		if splitFileRes == nil {
			continue
		}
		embeddingFileTotalNum.Inc()
		for _, seg := range splitFileRes.SegmentsWithEmbedding {
			if seg == nil {
				continue
			}
			embeddingContentTotalNum.Add(int32(len(seg.EmbeddingContents)))
			if seg.IsAllEmbedding {
				embeddingAbaseHitNum.Add(int32(len(seg.EmbeddingContents)))
				continue
			}
			if seg.Embeddings == nil {
				seg.Embeddings = make(map[string][]float64)
			}
			for _, embeddingContent := range seg.EmbeddingContents {
				if _, ok := seg.Embeddings[embeddingContent]; ok {
					// 如果该 content 已经计算过 embedding 并从 abase 返回了，不需要加入
					embeddingAbaseHitNum.Inc()
					continue
				}
				allContentsNeedEmbedded = append(allContentsNeedEmbedded, &model.BatchEmbeddingMsg{
					FileRelPath:      fileRelPath,
					EntityId:         seg.EntityID,
					EntityType:       seg.Segment.GetEntityType(),
					EmbeddingContent: embeddingContent,
					RetryNum:         0,
				})
			}
		}
		if len(allContentsNeedEmbedded) == 0 {
			// allEmbeddingContent 是待计算 embedding 的 content。
			// 如果 allEmbeddingContent 为空，说明该文件的所有 content 都已经计算过 embedding。
			// 需要在 continue 前 "将获取的 embedding 结果添加到 embeddingData 中"。
			embeddingData = append(embeddingData, splitFileRes.ToEmbeddingData(fileRelPath)...)
			embeddingAllCacheHitFileNum.Inc()
			continue
		}
	}
	return
}

func (bst *BatchLocalIndexSplitFileTask) splitVirtualProjectFiles(ctx context.Context, event ckg_metrics.Event, projectId model.URI, userId string,
	uri *model.URIStatus, attr model.IndexAttribute,
	indexTaskAttribute *model.IndexTaskAttribute,
	rel, content string, cli knowledgebase.Client, fs file_system.FileSystem,
) (uriData []*model.URIData, embeddingData []*model.EmbeddingData, allContentsNeedEmbedded []*model.BatchEmbeddingMsg) {
	var splitResp *knowledgebase.SplitFilesResponse
	var err error

	splitFile := &knowledgebase.SplitFile{
		Content:   content,
		Path:      rel,
		ChunkSize: int32(indexTaskAttribute.ChunkingSize),
	}

	opts := &splittermodel.SplitOption{
		// 顶级 splitter 类型
		SplitterType: splittermodel.GetSplitterType(indexTaskAttribute.ChunkingMethod),
		// 本地 splitter 选项
		LocalOpt: &splittermodel.LocalSplitOption{
			// 内容类型
			ContentType: splittermodel.GetContentType(indexTaskAttribute.ChunkingMethod),
			// 本地 splitter 子类型
			LocalSubtype: splittermodel.GetLocalSubtype(indexTaskAttribute.ChunkingMethod),
			// Langchain 特定选项
			LangchainOpt: &splittermodel.LangchainSplitOption{
				// Langchain splitter 类型
				SplitterType: splittermodel.GetLangchainType(indexTaskAttribute.ChunkingMethod),
				// 基本选项
				ChunkSize:    indexTaskAttribute.ChunkingSize,
				ChunkOverlap: indexTaskAttribute.ChunkingOverlap,
			},
		},
		// 知识库选项
		KBOpt: &splittermodel.KBSplitOption{
			KBClient:       cli,
			DatastoreName:  attr.EmbeddingModel,
			ChunkingMethod: attr.ChunkingMethod,
			UserID:         userId,
		},
	}

	splitResp, err = splitter.Split(ctx, opts, ckg_metrics.EventNameCKGBatchSplit, []*knowledgebase.SplitFile{splitFile})
	if err != nil {
		logs.CtxError(ctx, "[BatchLocalIndexSplitFileTask] split file failed, err is %v", err)
		event.AddTeaParam(ckg_metrics.TeaParamLocalIndexSplitFailed, true)
		return
	}
	// 如果是空文件，splitResp 或 Results 为空是正常的
	if splitResp == nil || splitResp.Results == nil {
		logs.CtxInfo(ctx, "[BatchLocalIndexSplitFileTask] empty file or no results: %s", uri.AbsPath)
		return
	}
	uriData = splitResp.ToURIData([]*model.URIStatus{uri})
	if indexTaskAttribute.RemoveEntityContent && !env.KeepVirtualProjectEntityContent {
		for _, data := range uriData {
			for _, entity := range data.Entities {
				delete(entity.Attributes, "content")
			}
		}
	}

	// 为未计算过 embedding 的 content 计算 embedding，最终得到 EmbeddingData (写入向量库)
	embeddingContentTotalNum, embeddingAbaseHitNum := atomic.NewInt32(0), atomic.NewInt32(0)
	embeddingFileTotalNum, embeddingAllCacheHitFileNum := atomic.NewInt32(0), atomic.NewInt32(0)
	defer func() {
		event.AddTeaParam(ckg_metrics.TeaParamEmbeddingContentTotal, embeddingContentTotalNum.Load())
		event.AddTeaParam(ckg_metrics.TeaParamEmbeddingAbaseHit, embeddingAbaseHitNum.Load())
		event.AddTeaParam(ckg_metrics.TeaParamEmbeddingFileTotal, embeddingFileTotalNum.Load())
		event.AddTeaParam(ckg_metrics.TeaParamEmbeddingAllCacheHit, embeddingAllCacheHitFileNum.Load())
	}()
	for fileRelPath, splitFileRes := range splitResp.Results {
		if splitFileRes == nil {
			continue
		}
		embeddingFileTotalNum.Inc()
		for _, seg := range splitFileRes.SegmentsWithEmbedding {
			if seg == nil {
				continue
			}
			embeddingContentTotalNum.Add(int32(len(seg.EmbeddingContents)))
			if seg.IsAllEmbedding {
				embeddingAbaseHitNum.Add(int32(len(seg.EmbeddingContents)))
				continue
			}
			if seg.Embeddings == nil {
				seg.Embeddings = make(map[string][]float64)
			}
			for _, embeddingContent := range seg.EmbeddingContents {
				if _, ok := seg.Embeddings[embeddingContent]; ok {
					// 如果该 content 已经计算过 embedding 并从 abase 返回了，不需要加入
					embeddingAbaseHitNum.Inc()
					continue
				}
				allContentsNeedEmbedded = append(allContentsNeedEmbedded, &model.BatchEmbeddingMsg{
					FileRelPath:      fileRelPath,
					EntityId:         seg.EntityID,
					EntityType:       seg.Segment.GetEntityType(),
					EmbeddingContent: embeddingContent,
					RetryNum:         0,
				})
			}
		}
		if len(allContentsNeedEmbedded) == 0 {
			// allEmbeddingContent 是待计算 embedding 的 content。
			// 如果 allEmbeddingContent 为空，说明该文件的所有 content 都已经计算过 embedding。
			// 需要在 continue 前 "将获取的 embedding 结果添加到 embeddingData 中"。
			embeddingData = append(embeddingData, splitFileRes.ToEmbeddingData(fileRelPath)...)
			embeddingAllCacheHitFileNum.Inc()
			continue
		}
	}
	return
}

// BatchLocalIndexEmbeddingTask 接收来自各个 queue 的
type BatchLocalIndexEmbeddingTask struct {
	userId    string
	projectId model.URI
	config    *ckg_config.Config
	messages  []*model.BatchEmbeddingMsg
	attr      model.IndexAttribute
	cli       knowledgebase.Client
	callback  BatchLocalIndexEmbeddingTaskCallback
}

func NewBatchLocalIndexEmbeddingTask(userId string, projectId model.URI, config *ckg_config.Config,
	messages []*model.BatchEmbeddingMsg, attr model.IndexAttribute,
	cli knowledgebase.Client, callback BatchLocalIndexEmbeddingTaskCallback) *BatchLocalIndexEmbeddingTask {
	return &BatchLocalIndexEmbeddingTask{
		userId:    userId,
		projectId: projectId,
		config:    config,
		messages:  messages,
		attr:      attr,
		cli:       cli,
		callback:  callback,
	}
}

type BatchLocalIndexEmbeddingTaskCallback func(ctx context.Context, embeddingData []*model.EmbeddingData)

func (bet *BatchLocalIndexEmbeddingTask) do() {
	ctx := util.NewBackgroundContext("")
	batchEmbeddingEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGBatchEmbedding, bet.userId, "", string(env.GetSourceProduct()))
	ctx = ckg_metrics.SetEvent(ctx, batchEmbeddingEvent)
	defer batchEmbeddingEvent.Report(ctx, false)

	embeddingData := make([]*model.EmbeddingData, 0)
	defer func() {
		if rec := recover(); rec != nil {
			logs.CtxError(ctx, "panic recovered, err: %v, %s", rec, string(debug.Stack()))
		}
		bet.callback(ctx, embeddingData)
	}()
	if env.TokenIsOutdated(ctx, env.GetToken(bet.userId)) {
		batchEmbeddingEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexTokenExpired, true)
		logs.CtxDebug(ctx, "token may expired, skip index entity")
		return
	}
	embeddingData = bet.innerEmbedding(ctx, batchEmbeddingEvent, bet.userId, bet.messages, bet.attr, bet.cli)
}

func (bet *BatchLocalIndexEmbeddingTask) innerEmbedding(ctx context.Context, event ckg_metrics.Event, userId string, messages []*model.BatchEmbeddingMsg,
	attr model.IndexAttribute, cli knowledgebase.Client) []*model.EmbeddingData {
	embeddingInvokeTime, embeddingInvokeFailedTime := atomic.NewInt32(0), atomic.NewInt32(0)
	embeddingsLenNotEq := atomic.NewInt32(0)
	contents := lo.Map(messages, func(msg *model.BatchEmbeddingMsg, _ int) string { return msg.EmbeddingContent })
	defer func() {
		event.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbedSent, embeddingInvokeTime.Load())
		event.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbedFailed, embeddingInvokeFailedTime.Load())
		event.AddTeaParam(ckg_metrics.TeaParamEmbeddingLenNotEq, embeddingsLenNotEq.Load())
		event.AddTeaParam(ckg_metrics.TeaParamBatchEmbeddingBatchSize, len(contents))
	}()

	// 用于 dump 压测数据 & debug
	if env.DumpEmbeddingRequest {
		dumpEmbeddingRequest(messages)
	}
	embeddingInvokeTime.Inc()
	var embeddingResp *knowledgebase.EmbeddingResponse
	var err error
	if bet.config.IsIndexFeatureEnabled(ctx, userId, ckg_config.UseV2SplitEmbeddingAPI) {
		event.AddTeaParam(ckg_metrics.TeaParamUseEmbeddingAPIVersion, "v2")
		embeddingResp, err = cli.EmbeddingV2(ctx, env.GetToken(userId), ckg_metrics.EventNameCKGBatchEmbedding, &knowledgebase.EmbeddingRequestV2{
			EmbeddingModel:    attr.EmbeddingModel,
			EmbeddingContents: contents,
			OfflineCluster:    true,
		})
	} else {
		event.AddTeaParam(ckg_metrics.TeaParamUseEmbeddingAPIVersion, "v1")
		embeddingResp, err = cli.Embedding(ctx, env.GetToken(userId), ckg_metrics.EventNameCKGBatchEmbedding, &knowledgebase.EmbeddingRequest{
			DatastoreName:     attr.EmbeddingModel,
			EmbeddingContents: contents,
			OfflineCluster:    true,
		})
	}
	if err != nil || embeddingResp == nil {
		logs.CtxError(ctx, "Embedding error: %+v", err)
		embeddingInvokeFailedTime.Inc()
		return nil
	}
	if len(embeddingResp.Embeddings) != len(contents) {
		logs.CtxWarn(ctx, "length of resp embedding not equal to length of contents that need embedded")
		embeddingsLenNotEq.Inc()
		return nil
	}
	// 收集所有的 embedding content -> embedding 结果，组成 map
	embeddingsMap := make(map[string]*knowledgebase.Embedding)
	for idx, embeddingContent := range contents {
		if idx >= len(embeddingResp.Embeddings) || embeddingResp.Embeddings[idx] == nil {
			continue
		}
		embeddingsMap[embeddingContent] = embeddingResp.Embeddings[idx]
	}

	embeddingData := make([]*model.EmbeddingData, 0)
	for _, msg := range messages {
		if _, ok := embeddingsMap[msg.EmbeddingContent]; !ok {
			continue
		}
		kbEmbedding := embeddingsMap[msg.EmbeddingContent]
		data := msg.ToEmbeddingData(kbEmbedding.Embedding)
		embeddingData = append(embeddingData, data)
	}
	return embeddingData
}

func dumpEmbeddingRequest(messages []*model.BatchEmbeddingMsg) {
	const EmbeddingReqFullLog = "embedding_request_full.log"
	// const EmbeddingReqCSV = "embedding_request.csv" // TODO
	embeddingLogAbsPath := filepath.Join(env.GetStoragePath(), EmbeddingReqFullLog)
	res, err := json.Marshal(messages)
	if err != nil {
		return
	}
	f, err := file_system.RealFS.OpenFile(embeddingLogAbsPath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	defer func(f *os.File) {
		if err := f.Close(); err != nil {
			return
		}
	}(f)
	if err != nil {
		return
	}
	_, err = f.Write(res)
	_, err = f.Write([]byte("\n"))
}

func (bet *BatchLocalIndexEmbeddingTask) ID() string {
	// EmbeddingTask 没有 FilePath，只能通过
	data := []byte(fmt.Sprintf("%s-%s-%+v", bet.GetFilePath(), bet.attr.String(), bet.messages))
	hash := sha256.Sum256(data)
	return fmt.Sprintf("p%x", hash)
}

func (bet *BatchLocalIndexEmbeddingTask) GetFilePath() string       { return "" }
func (bet *BatchLocalIndexEmbeddingTask) GetProject() string        { return bet.projectId }
func (bet *BatchLocalIndexEmbeddingTask) GetChunkingMethod() string { return bet.attr.ChunkingMethod }
func (bet *BatchLocalIndexEmbeddingTask) GetEmbeddingModel() string { return bet.attr.EmbeddingModel }

func (bet *BatchLocalIndexEmbeddingTask) IsRemoved() {
	bet.callback(context.Background(), make([]*model.EmbeddingData, 0))
}
