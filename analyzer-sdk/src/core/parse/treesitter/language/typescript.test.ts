import {beforeAll, describe, expect, it} from "vitest";
import {TreeSitterParser} from "../parser";
import {DefNodeKindEnum, LanguageIdEnum} from "../../share";
import {TypescriptTreeSitterHandler} from "./typescript";

describe("test_getInvokeInFile", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
function m1(t: T): void {
  f()
  t.m1();
  t.f1.m2();
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Typescript, docText);

    const result = TypescriptTreeSitterHandler.TYPESCRIPT_INSTANCE.getInvokeInFile(tree.rootNode);

    expect(result.map(node => node.text)).toEqual(["f", "m1", "m2"]);
  });
});

describe("test_getDeclVariableInFile", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
class C {
  f = 2;
}

function varExample(p: T) {
  var v1 = 1;
  let v2 = 1;
  const v3 = 1;
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Typescript, docText);

    const result = TypescriptTreeSitterHandler.TYPESCRIPT_INSTANCE.getDeclVariableInFile(tree.rootNode);

    expect(result.map(node => node.text)).toEqual(["f", "p", "v1", "v2", "v3"]);
  });
});

describe("test_getUsageVariableInFile", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
import {T1, T2} from "xx";
import T3 from "xx";

class C {
  f = 2;
  
  m(): void {
    f = 3
  }
}

function varExample(p: T) {
  var v1 = 1;
  let v2 = 1;
  const v3 = 1;
  
  m(v3, p.f)
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Typescript, docText);

    const result = TypescriptTreeSitterHandler.TYPESCRIPT_INSTANCE.getUsageVariableInFile(tree.rootNode);

    expect(result.map(node => node.text)).toEqual(["T1", "T2", "T3", "f", "v3", "p", "f"]);
  });
});

describe("test_genClassSimplifiedContent", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
class ExampleClass {
    // 字段
    private _name: string;
    private _value: number;
    
    // 构造函数
    constructor(name: string, value: number) {
        this._name = name;
        this._value = value;
    }

    // 传统方法
    public greet(): string {
        return "";
    }

    // 箭头函数方法
    public getValue = (): number => {
        return this._value;
    }

    // 静态方法
    public static createInstance(name: string, value: number): ExampleClass {
        return new ExampleClass(name, value);
    }

    // Getter 方法
    public get name(): string {
        return this._name;
    }

    // Setter 方法
    public set name(newName: string) {
        if (newName.length > 0) {
            this._name = newName;
        }
    }

    // 私有方法（只能在类内部调用）
    private calculateSquare(): number {
        return this._value * this._value;
    }

    // 公共方法调用私有方法
    public getSquare(): number {
        return this.calculateSquare();
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Typescript, docText);

    const defNodeInfo = {
      languageId: LanguageIdEnum.Typescript,
      uri: "file:///test.ts",
      kind: DefNodeKindEnum.Class,
      name: "ExampleClass",
      defNode: tree.rootNode.namedChild(0),
      subDefNodeInfo: null,
    };
    const result = TypescriptTreeSitterHandler.TYPESCRIPT_INSTANCE.genClassSimplifiedContent(defNodeInfo);

    expect(result).toEqual(`
class ExampleClass {
    // 字段
    private _name: string;
    private _value: number;
    
    // 构造函数
    constructor(name: string, value: number)  { ... }

    // 传统方法
    public greet(): string  { ... }

    // 箭头函数方法
    public getValue = (): number => {
        return this._value;
    }

    // 静态方法
    public static createInstance(name: string, value: number): ExampleClass  { ... }

    // Getter 方法
    public get name(): string  { ... }

    // Setter 方法
    public set name(newName: string)  { ... }

    // 私有方法（只能在类内部调用）
    private calculateSquare(): number  { ... }

    // 公共方法调用私有方法
    public getSquare(): number  { ... }
}
`.trim());
  });
});