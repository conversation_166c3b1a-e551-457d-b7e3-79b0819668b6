import {TextDocument} from "vscode-languageserver-textdocument";
import {Position} from "vscode-languageserver-types";
import {
  CompletionContextInfo,
  CompletionContextResult,
  ContextItem,
  postProcessingAndSort,
  PriorityEnum,
} from "./share";
import {getActiveFileCachedTreeAndDocument} from "../../../cache/astCache";
import {
  EVENT_CATEGORY_CONTEXT_EXTRACTION,
  EVENT_NAME_FAILED,
  EVENT_VALUE_NO_CACHE,
  reportEvent,
} from "../../../../utils/event";
import {Logger} from "../../../../utils/logger";
import {SyntaxNode} from "web-tree-sitter";
import {TreeSitterDispatcher} from "../../../parse/treesitter/dispatcher";
import {TreeSitterFunctionalityEnum} from "../../../parse/treesitter/share";
import {queryDefinitionForPosition, queryDefinitionForRange} from "../../../async/definition/query";
import {pointToPosition} from "../../../../utils/common";
import {ResolvedTypeInfo} from "../../../share/common";
import {getWorkspaceFolderPath} from "../../../../utils/path";
import {DefNodeKindEnum} from "../../../parse/share";

const EVENT_PROCESS_COMPLETION_TS = "completion_ts";

export async function doExtractCompletionContextForTypescript(document: TextDocument, position: Position): Promise<CompletionContextResult[]> {
  const cachedTree = getActiveFileCachedTreeAndDocument(document)?.tree;
  if (!cachedTree) {
    reportEvent(document.languageId, EVENT_CATEGORY_CONTEXT_EXTRACTION, EVENT_PROCESS_COMPLETION_TS, EVENT_NAME_FAILED, EVENT_VALUE_NO_CACHE).then();
    Logger.error(`doExtractCompletionContextForTypescript: no cached tree found for '${document.uri}'`);
    return [];
  }

  const resultContexts: CompletionContextInfo[] = [];

  const currentNode = cachedTree.rootNode.descendantForPosition({row: position.line, column: position.character});

  resultContexts.push(...getFunctionCallContext(document, position, currentNode));
  resultContexts.push(...getInlineContext(document, position));
  resultContexts.push(...getScopeFunctionContext(document, position, currentNode));
  resultContexts.push(...getAdjacentContext(document, position, currentNode));
  resultContexts.push(...getBranchContext(document, position, currentNode));

  return postProcessingAndSort(resultContexts);
}

function getFunctionCallContext(document: TextDocument, position: Position, curNode: SyntaxNode): CompletionContextInfo[] {
  const callExpressionNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetParentNodeWithTypeListForNode, curNode, ["call_expression"]);
  if (!callExpressionNode) {
    return [];
  }

  let funcIdentifierNode: SyntaxNode = null;
  if (callExpressionNode.childForFieldName("function")?.type === "identifier") {
    funcIdentifierNode = callExpressionNode.childForFieldName("function");
  } else if (callExpressionNode.childForFieldName("function")?.type === "member_expression") {
    funcIdentifierNode = callExpressionNode.childForFieldName("function").childForFieldName("property");
  }

  if (!funcIdentifierNode) {
    return [];
  }

  const definitionQueryResult = queryDefinitionForPosition(document, pointToPosition(funcIdentifierNode.startPosition));
  if (!definitionQueryResult || !definitionQueryResult.typeInfo) {
    return [];
  }

  return [{
    priority: PriorityEnum.P0,
    distanceFromPosition: Math.abs(document.offsetAt(position) - funcIdentifierNode.startIndex),
    symbolName: definitionQueryResult.symbolName,
    definitionKey: definitionQueryResult.definitionKey,
    contextInfo: handleResolvedTypeInfoContent(definitionQueryResult.typeInfo),
  }];
}

function getInlineContext(document: TextDocument, position: Position): CompletionContextInfo[] {
  const result: CompletionContextInfo[] = [];

  queryDefinitionForRange(
    document,
    document.offsetAt({line: position.line, character: 0}),
    document.offsetAt({line: position.line + 1, character: 0}),
  ).forEach((value, key) => {
    result.push({
      priority: PriorityEnum.P1,
      distanceFromPosition: Math.abs(document.offsetAt(position) - key),
      symbolName: value.symbolName,
      definitionKey: value.definitionKey,
      contextInfo: handleResolvedTypeInfoContent(value.typeInfo),
    });
  });

  return result;
}

function getScopeFunctionContext(document: TextDocument, position: Position, curNode: SyntaxNode): CompletionContextInfo[] {
  const funcDeclNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetFuncParentNodeForNode, curNode);
  if (!funcDeclNode) {
    return [];
  }

  const funcSignatureString = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetFuncSignatureString, funcDeclNode);
  if (!funcSignatureString) {
    return [];
  }

  const result: CompletionContextInfo[] = [];

  queryDefinitionForRange(
    document,
    funcDeclNode.startIndex,
    funcDeclNode.startIndex + funcSignatureString.length,
  ).forEach((value, key) => {
    result.push({
      priority: PriorityEnum.P2,
      distanceFromPosition: Math.abs(document.offsetAt(position) - key),
      symbolName: value.symbolName,
      definitionKey: value.definitionKey,
      contextInfo: handleResolvedTypeInfoContent(value.typeInfo),
    });
  });

  return result;
}

function getAdjacentContext(document: TextDocument, position: Position, curNode: SyntaxNode): CompletionContextInfo[] {
  const funcDeclNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetFuncParentNodeForNode, curNode);
  let minLine = 0;
  if (funcDeclNode) {
    minLine = funcDeclNode.startPosition.row;
  }

  const result: CompletionContextInfo[] = [];

  const p1AvailableLines = 5;
  let p1LineCount = 0;
  let p1i = 1;
  while (true) {
    const lineRange = {
      start: {line: position.line - p1i, character: 0},
      end: {line: position.line - p1i + 1, character: 0},
    };
    if (document.getText(lineRange).trim().length > 0) {
      p1LineCount++;
      if (p1LineCount === p1AvailableLines) {
        break;
      }
    }
    if (position.line - p1i < minLine) {
      break;
    }
    p1i++;
  }

  queryDefinitionForRange(
    document,
    document.offsetAt({line: position.line - p1i, character: 0}),
    document.offsetAt({line: position.line, character: 0}),
  ).forEach((value, key) => {
    result.push({
      priority: PriorityEnum.P1,
      distanceFromPosition: Math.abs(document.offsetAt(position) - key),
      symbolName: value.symbolName,
      definitionKey: value.definitionKey,
      contextInfo: handleResolvedTypeInfoContent(value.typeInfo),
    });
  });

  return result;
}

function getBranchContext(document: TextDocument, position: Position, curNode: SyntaxNode): CompletionContextInfo[] {
  const branchIndexRanges = getBranchIndexRanges(curNode);

  const result: CompletionContextInfo[] = [];

  branchIndexRanges.forEach(range => {
    queryDefinitionForRange(document, range.startIndex, range.endIndex).forEach((value, key) => {
      result.push({
        priority: PriorityEnum.P2,
        distanceFromPosition: Math.abs(document.offsetAt(position) - key),
        symbolName: value.symbolName,
        definitionKey: value.definitionKey,
        contextInfo: handleResolvedTypeInfoContent(value.typeInfo),
      });
    });
  });

  return result;
}

function getBranchIndexRanges(curNode: SyntaxNode): { startIndex: number, endIndex: number }[] {
  const result: { startIndex: number, endIndex: number }[] = [];

  let iterNode = curNode;
  while (iterNode) {
    if (iterNode.type === "if_statement" || iterNode.type === "while_statement") {
      const conditionNode = iterNode.childForFieldName("condition");
      if (conditionNode) {
        result.push({
          startIndex: conditionNode.startIndex,
          endIndex: conditionNode.endIndex,
        });
      }
    } else if (iterNode.type === "switch_statement") {
      const valueNode = iterNode.childForFieldName("value");
      if (valueNode) {
        result.push({
          startIndex: valueNode.startIndex,
          endIndex: valueNode.endIndex,
        });
      }
    } else if (iterNode.type === "for_statement" || iterNode.type === "for_in_statement") {
      const firstNamedNode = iterNode.namedChild(0);
      const bodyNode = iterNode.childForFieldName("body");
      if (firstNamedNode && bodyNode) {
        result.push({
          startIndex: firstNamedNode.startIndex,
          endIndex: bodyNode.startIndex,
        });
      }
    }

    iterNode = iterNode.parent;
  }

  return result;
}

function handleResolvedTypeInfoContent(typeInfo: ResolvedTypeInfo): ContextItem {
  if (!typeInfo.path.startsWith(getWorkspaceFolderPath())) {
    return null;
  }

  if (typeInfo.path.includes("node_modules/") || typeInfo.path.includes(".pnpm/")) {
    return null;
  }

  const result = {
    path: typeInfo.path,
    startIndex: typeInfo.startIndex,
    code: "",
    subInfo: [],
  };

  if (typeInfo.category === DefNodeKindEnum.Function) {
    result.code = typeInfo.optionalContent[ResolvedTypeInfo.OPTIONAL_CONTENT_FUNC_SIGNATURE] ?? "";
  } else if (typeInfo.category === DefNodeKindEnum.VarDecl) {
    const funcSignature = typeInfo.optionalContent[ResolvedTypeInfo.OPTIONAL_CONTENT_FUNC_SIGNATURE];
    if (funcSignature) {
      result.code = funcSignature;
    } else {
      result.code = typeInfo.content;
    }
  } else if (typeInfo.category === DefNodeKindEnum.Class) {
    result.code = typeInfo.optionalContent[ResolvedTypeInfo.OPTIONAL_CONTENT_CLASS_SIMPLIFIED_CONTENT];
  } else if (typeInfo.category === DefNodeKindEnum.Complex) {
    result.code = typeInfo.content;
    result.subInfo.push(...typeInfo.subTypeInfo?.map(item => handleResolvedTypeInfoContent(item)).filter(item => item !== null) ?? []);
  } else if ([DefNodeKindEnum.Namespace.toString(), DefNodeKindEnum.Alias.toString(), DefNodeKindEnum.Primitive.toString(), DefNodeKindEnum.Unknown.toString()].includes(typeInfo.category)) {
    result.code = "";
  } else {
    result.code = typeInfo.content;
  }

  return result.code === "" ? null : result;
}