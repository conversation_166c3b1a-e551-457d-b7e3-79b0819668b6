package ignore_rule_checker

import (
	"context"
	"fmt"
	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/env"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/model"
	"os"
	"path/filepath"
	"testing"
)

func TestRuleChecker(t *testing.T) {
	ctx := context.Background()
	envOptions := env.Options{
		Host:          "https://copilot.byted.org/",
		AppID:         "a4c6c500-6846-45b6-94f6-1b231eb53742",
		UserID:        "jiagengli",
		SourceProduct: model.SourceProductNativeIDE,
	}
	_ = env.RefreshToken(ctx, &model.TokenValue{
		Scheme: "Cloud-IDE-JWT",
		Token:  "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
		UserID: "jiagengli",
	}, "jiagengli")
	env.InitEnvValue(ctx, envOptions)
	cli, _ := knowledgebase.GetClient()
	c := ckg_config.MustNewConfig()

	isV2 := NewIgnoreServiceV2(c, cli)
	_ = isV2.Register(ctx, NewExcludeFolderRuleChecker())
	_ = isV2.Register(ctx, NewExcludeFileRuleChecker())
	_ = isV2.Register(ctx, NewExcludeExtFileRuleChecker())
	_ = isV2.Register(ctx, NewFileMetadataRuleChecker())
	_ = isV2.Register(ctx, NewTCCIgnoreRuleChecker())
	_ = isV2.Register(ctx, NewGitIgnoreRuleChecker())
	_ = isV2.Register(ctx, NewTraeIgnoreRuleChecker())
	_ = isV2.Register(ctx, NewFileContentRuleChecker(
		NewIncludeExtFileRuleChecker(),
	))
	base := "/Users/<USER>/Desktop/Projects/test_ckg"
	_ = filepath.Walk(base, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		ignored, skipped, err := isV2.IsIgnore(ctx, "jiagengli", base, path, info)
		if err != nil {
			return err
		}
		if ignored {
			fmt.Printf("Skip ignored: %s\n", path)
			return skipped
		}
		fmt.Printf("%s not ignored\n", path)
		return nil
	})
	return
}
