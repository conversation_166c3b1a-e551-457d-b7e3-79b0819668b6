package code

import (
	"context"

	kb "ide/ckg/codekg/components/knowledgebase"
	lsmodel "ide/ckg/codekg/components/splitter/local_splitter/model"
	"ide/ckg/codekg/model"
)

// codeSplitterImpl implements the FileSplitter interface for code files
type codeSplitterImpl struct {
	// Add any necessary fields for code splitting
	// For example, you might want to have language-specific splitters
	supportedLanguages map[model.LanguageType]bool
}

type CodeSplitter interface {
	lsmodel.LocalSplitter
}

// NewCodeSplitter creates a new codeSplitterImpl
func NewCodeSplitter() CodeSplitter {
	return &codeSplitterImpl{
		supportedLanguages: map[model.LanguageType]bool{
			model.LanguageGo:         false,
			model.LanguageJava:       false,
			model.LanguageTypeScript: false,
			model.LanguageJavaScript: false,
			model.LanguagePython:     false,
			model.LanguageKotlin:     false,
			model.LanguageRust:       false,
			model.LanguageCXX:        false,
			model.LanguageCSharp:     false,
			model.LanguageC:          false,
			model.LanguageLua:        false,
			model.LanguageOCaml:      false,
			model.LanguagePHP:        false,
			model.LanguageRuby:       false,
			model.LanguageScala:      false,
			model.LanguageSwift:      false,
			model.LanguageObjC:       false,
		},
	}
}

// SplitFile implements CodeSplitter.SplitFile
func (s *codeSplitterImpl) SplitFile(ctx context.Context, file *kb.SplitFile) (*kb.SplitFileResult_, error) {
	return nil, nil
}

func (s *codeSplitterImpl) SupportedLanguages() map[model.LanguageType]bool {
	return s.supportedLanguages
}
