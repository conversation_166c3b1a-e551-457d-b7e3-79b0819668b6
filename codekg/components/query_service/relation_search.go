package query_service

import (
	"context"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/components/ignore_service"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/protocol"
	"io/fs"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"unicode/utf8"

	"gorm.io/gorm"

	"ide/ckg/codekg/components/logs"
	"github.com/pkg/errors"

	"github.com/samber/lo"
)

var defaultUsefulFiles = []string{
	"readme.md",
}

const WorkspaceProvider = "workspace"

func getFolderTreeHelper(ctx context.Context, projectId model.URI, entity *model.Entity, is ignore_service.IgnoreService, fs file_system.FileSystem) (string, error) {
	result, err := readAndProcessDirectory(ctx, projectId, entity.URI, is, fs)
	if err != nil {
		logs.CtxError(ctx, "readAndProcessDirectory err is %v", err)
		return "", nil
	}

	output, err := printFileTree(ctx, result, 500)
	if err != nil {
		logs.CtxError(ctx, "PrintFileTree failed err is %s", err)
		return "", nil
	}

	return output, nil
}

func getTestCallerHelper(ctx context.Context, storage data_storage.Storage, conn *gorm.DB, entity *model.Entity) ([]data_storage.CallerEntityResult, error) {
	if storage == nil || entity == nil {
		logs.CtxError(ctx, "getTestCallerSummary paramNil")
		return nil, nil
	}

	callers, err := storage.SearchCallerEntity(ctx, conn, entity)
	if err != nil {
		logs.CtxError(ctx, "SearchCallerEntity err is %s", err)
		return nil, nil
	}
	if len(callers) == 0 {
		return nil, nil
	}

	testCallers := lo.Filter(callers, func(caller data_storage.CallerEntityResult, _ int) bool {
		return caller.Entity.IsTest()
	})

	if len(testCallers) == 0 {
		testCallers = lo.Slice(callers, 0, 2)
	} else {
		testCallers = lo.Slice(testCallers, 0, 5)
	}
	return testCallers, nil
}

func (s *service) getCodeRelation(ctx context.Context, entity *model.Entity, uriMeta *data_storage.StorageURIMeta) (*protocol.Variable, []*protocol.Reference, error) {
	content := getFullCodeContent(entity, uriMeta)
	if isUTF8 := utf8.ValidString(content); !isUTF8 {
		logs.CtxWarn(ctx, "filtered entity with invalid control character: %s", entity.ID)
		return nil, nil, &fs.PathError{
			Op:   "getCodeRelation",
			Path: entity.URI,
			Err:  errors.Errorf("invalid code character"),
		}
	}
	return &protocol.Variable{
			CodeChunk: &protocol.CodeChunkVariable{
				FilePath:  entity.URI,
				StartLine: entity.GetStartLine(),
				EndLine:   entity.GetEndLine(),
				Content:   content,
				Provider:  WorkspaceProvider,
			},
		}, []*protocol.Reference{
			{
				FilePath:  entity.URI,
				StartLine: entity.GetStartLine(),
				EndLine:   entity.GetEndLine(),
				Uri:       uriMeta.UriCanonical,
				Name:      uriMeta.Name,
			},
		}, nil
}

func (s *service) getTextRelation(ctx context.Context, entity *model.Entity, uriMeta *data_storage.StorageURIMeta) (*protocol.Variable, []*protocol.Reference, error) {
	content := getFullCodeContent(entity, uriMeta)
	// Check if there are non-UTF-8 characters in the text
	if isUTF8 := utf8.ValidString(content); !isUTF8 {
		logs.CtxWarn(ctx, "filtered entity with invalid control character: %s", entity.ID)
		return nil, nil, &fs.PathError{
			Op:   "getTextRelation",
			Path: entity.URI,
			Err:  errors.Errorf("invalid text character"),
		}
	}

	return &protocol.Variable{
			Text: &protocol.TextVariable{
				FilePath:  entity.URI,
				StartLine: entity.GetStartLine(),
				EndLine:   entity.GetEndLine(),
				Content:   content,
				Provider:  WorkspaceProvider,
			},
		}, []*protocol.Reference{
			{
				FilePath:  entity.URI,
				StartLine: entity.GetStartLine(),
				EndLine:   entity.GetEndLine(),
				Uri:       uriMeta.UriCanonical,
				Name:      uriMeta.Name,
			},
		}, nil
}

func (s *service) getFolderRelation(ctx context.Context, entity *model.Entity, projectId string) (*protocol.Variable, []*protocol.Reference, error) {
	variable := &protocol.Variable{
		Folder: &protocol.FolderVariable{
			FilePath: entity.URI,
			Provider: WorkspaceProvider,
		},
	}
	references := make([]*protocol.Reference, 0)

	// 获得 folder tree
	folderTree, err := getFolderTreeHelper(ctx, projectId, entity, s.is, s.fs)
	if err != nil {
		logs.CtxWarn(ctx, "[getFolderRelation] get folder tree err: %v, entity id: %s", err, entity.ID)
		return nil, nil, errors.WithMessage(err, "error get folder tree")
	}
	variable.Folder.FolderTree = folderTree

	// 获得可能有用的文件
	usefulInfos, err := getUsefulFileInfosHelper(ctx, entity.URI)
	if err != nil {
		logs.CtxWarn(ctx, "[getFolderRelation] get useful file info err: %v, entity id: %s", err, entity.ID)
		return nil, nil, errors.WithMessagef(err, "error get useful file infos")
	}

	usefulFiles := make([]*protocol.UsefulFileInfo, 0)
	for key, value := range usefulInfos {
		usefulFiles = append(usefulFiles, &protocol.UsefulFileInfo{
			FilePath: key,
			Content:  value,
		})
	}
	variable.Folder.UsefulFiles = usefulFiles

	return variable, references, nil
}

func (s *service) getFileRelation(ctx context.Context, entity *model.Entity, uriMeta *data_storage.StorageURIMeta) (*protocol.Variable, []*protocol.Reference, error) {
	variable := &protocol.Variable{
		File: &protocol.FileVariable{
			FilePath: entity.URI,
			Comment:  entity.GetComment(),
			Provider: WorkspaceProvider,
		},
	}

	content, err := getFileFullContent(ctx, entity, uriMeta)
	if err != nil {
		logs.CtxWarn(ctx, "[getFileRelation] get file full content err: %v. entity id: %s", err, entity.ID)
		return nil, nil, errors.WithMessagef(err, "error get file full content")
	}
	if isUTF8 := utf8.ValidString(content); !isUTF8 {
		logs.CtxWarn(ctx, "filtered entity with invalid control character: %s", entity.ID)
		return nil, nil, &fs.PathError{
			Op:   "getFileRelation",
			Path: entity.URI,
			Err:  errors.Errorf("invalid file character"),
		}
	}
	variable.File.Content = content
	return variable, []*protocol.Reference{
		{
			FilePath:  entity.URI,
			StartLine: 1,
			EndLine:   int32(len(strings.Split(content, "\n"))),
			Uri:       uriMeta.UriCanonical,
			Name:      uriMeta.Name,
		},
	}, nil
}

func (s *service) getFileTopLevelRelation(ctx context.Context, entity *model.Entity, uriMeta *data_storage.StorageURIMeta) (*protocol.Variable, []*protocol.Reference, error) {
	content := getEntityContent(entity)

	if isUTF8 := utf8.ValidString(content); !isUTF8 {
		logs.CtxWarn(ctx, "filtered entity with invalid control character: %s", entity.ID)
		return nil, nil, &fs.PathError{
			Op:   "getFileTopLevelRelation",
			Path: entity.URI,
			Err:  errors.Errorf("invalid file top level character"),
		}
	}
	return &protocol.Variable{
			FileTopLevel: &protocol.FileTopLevelVariable{
				FilePath:  entity.URI,
				StartLine: entity.GetStartLine(),
				EndLine:   entity.GetEndLine(),
				Content:   content,
				Provider:  WorkspaceProvider,
			},
		}, []*protocol.Reference{
			{
				FilePath:  entity.URI,
				StartLine: entity.GetStartLine(),
				EndLine:   entity.GetEndLine(),
				Uri:       uriMeta.UriCanonical,
				Name:      uriMeta.Name,
			},
		}, nil
}

func (s *service) getClassRelation(ctx context.Context, entity *model.Entity, storage data_storage.Storage, uriMeta *data_storage.StorageURIMeta, tx *gorm.DB) (*protocol.Variable, []*protocol.Reference, error) {
	if isUTF8 := utf8.ValidString(getFullCodeContent(entity, uriMeta)); !isUTF8 {
		logs.CtxWarn(ctx, "filtered entity with invalid control character: %s", entity.ID)
		return nil, nil, &fs.PathError{
			Op:   "getClassRelation",
			Path: entity.URI,
			Err:  errors.Errorf("invalid class character"),
		}
	}
	variable := &protocol.Variable{
		Class: &protocol.ClassVariable{
			FilePath:  entity.URI,
			StartLine: entity.GetStartLine(),
			EndLine:   entity.GetEndLine(),
			Name:      getRealName(entity.Name),
			Comment:   entity.GetComment(),
			Content:   getFullCodeContent(entity, uriMeta),
			Provider:  WorkspaceProvider,
		},
	}
	references := make([]*protocol.Reference, 0)
	references = append(references, &protocol.Reference{
		FilePath:  entity.URI,
		StartLine: entity.GetStartLine(),
		EndLine:   entity.GetEndLine(),
		Uri:       uriMeta.UriCanonical,
		Name:      uriMeta.Name,
	})

	// 获取 class 的 member
	memberEntities, err := storage.SearchClassMember(ctx, tx, entity)
	if err != nil {
		logs.CtxWarn(ctx, "[getClassRelation] search class member error: %v, entity id: %s", err, entity.ID)
		return nil, nil, errors.WithMessagef(err, "error search class member")
	}

	members := make([]*protocol.Member, 0)
	sort.Slice(memberEntities, func(i, j int) bool {
		return memberEntities[i].GetStartLine() > memberEntities[j].GetStartLine()
	})
	for _, item := range memberEntities {
		members = append(members, &protocol.Member{
			Content: getEntityContent(item),
		})
	}
	variable.Class.Members = members

	// 获取 class 的 method
	methodEntities, err := storage.SearchClassMethod(ctx, tx, entity)
	if err != nil {
		logs.CtxWarn(ctx, "[getClassRelation] search class method err: %v, entity id: %s", err, entity.ID)
		return nil, nil, errors.WithMessagef(err, "error search class method")
	}
	variable.Class.Methods = lo.Map(methodEntities, func(item *model.Entity, _ int) *protocol.MethodInfo {
		return &protocol.MethodInfo{
			FilePath:  item.URI,
			StartLine: item.GetStartLine(),
			EndLine:   item.GetEndLine(),
			Name:      item.Name,
			Signature: item.GetSignature(),
			Comment:   item.GetComment(),
			Content:   getEntityContent(item),
		}
	})

	// 获得 class 的所有测试函数
	// TODO: 暂时用不到，速度较慢，先关闭
	// classTestFunctionInfos, err := getClassTestFunctionsHelper(ctx, storage, entity)
	// if err != nil {
	// 	return nil, nil, errors.WithMessagef(err, "error get test functions")
	// }
	// variable.Class.TestFunctions = lo.Map(classTestFunctionInfos, func(item *model.Entity, _ int) *protocol.MethodInfo {
	// 	return &protocol.MethodInfo{
	// 		FilePath:  item.URI,
	// 		StartLine: item.GetStartLine(),
	// 		EndLine:   item.GetEndLine(),
	// 		Name:      item.Name,
	// 		Signature: item.GetSignature(),
	// 		Comment:   item.GetComment(),
	// 		Content:   GetFullCodeContent(item),
	// 	}
	// })
	variable.Class.TestFunctions = make([]*protocol.MethodInfo, 0)

	return variable, references, nil
}

func (s *service) getClassTopLevelRelation(ctx context.Context, entity *model.Entity, uriMeta *data_storage.StorageURIMeta) (*protocol.Variable, []*protocol.Reference, error) {
	content := getFullCodeContent(entity, uriMeta)
	if isUTF8 := utf8.ValidString(content); !isUTF8 {
		logs.CtxWarn(ctx, "filtered entity with invalid control character: %s", entity.ID)
		return nil, nil, &fs.PathError{
			Op:   "getClassTopLevelRelation",
			Path: entity.URI,
			Err:  errors.Errorf("invalid class top level character"),
		}
	}
	return &protocol.Variable{
			ClassTopLevel: &protocol.ClassTopLevelVariable{
				FilePath:  entity.URI,
				StartLine: entity.GetStartLine(),
				EndLine:   entity.GetEndLine(),
				Content:   content,
				Provider:  WorkspaceProvider,
			},
		}, []*protocol.Reference{
			{
				FilePath:  entity.URI,
				StartLine: entity.GetStartLine(),
				EndLine:   entity.GetEndLine(),
				Uri:       uriMeta.UriCanonical,
				Name:      uriMeta.Name,
			},
		}, nil
}

func (s *service) getMethodRelation(ctx context.Context, entity *model.Entity, storage data_storage.Storage, uriMeta *data_storage.StorageURIMeta, tx *gorm.DB) (*protocol.Variable, []*protocol.Reference, error) {
	content := getFullCodeContent(entity, uriMeta)
	if isUTF8 := utf8.ValidString(content); !isUTF8 {
		logs.CtxWarn(ctx, "filtered entity with invalid control character: %s", entity.ID)
		return nil, nil, &fs.PathError{
			Op:   "getMethodRelation",
			Path: entity.URI,
			Err:  errors.Errorf("invalid method character"),
		}
	}
	variable := &protocol.Variable{
		Method: &protocol.MethodVariable{
			FilePath:      entity.URI,
			StartLine:     entity.GetStartLine(),
			EndLine:       entity.GetEndLine(),
			Name:          getRealName(entity.Name),
			Signature:     entity.GetSignature(),
			Comment:       entity.GetComment(),
			Content:       content,
			Callers:       nil,
			Callees:       nil,
			TestFunctions: nil,
			Provider:      WorkspaceProvider,
		},
	}
	references := make([]*protocol.Reference, 0)
	references = append(references, &protocol.Reference{
		FilePath:  entity.URI,
		StartLine: entity.GetStartLine(),
		EndLine:   entity.GetEndLine(),
		Uri:       uriMeta.UriCanonical,
		Name:      uriMeta.Name,
	})

	// 获取 caller
	callerEntities, err := storage.SearchCallerEntity(ctx, tx, entity)
	if err != nil {
		logs.CtxWarn(ctx, "[getMethodRelation] search caller entity err: %v, entity id: %s", err, entity.ID)
		return nil, nil, errors.WithMessagef(err, "error search caller entity")
	}

	callers := make([]*protocol.MethodInfo, 0)
	for _, item := range callerEntities {
		caller := item.Entity
		if caller.Type != model.Method {
			continue
		}

		code, _, err := getCallerCodeAndReference(ctx, caller, item.Ranges)
		if err != nil {
			logs.CtxWarn(ctx, "getCallerCodeAndReference err is %v, file is %v", err, item.Entity.URI)
			continue
		}

		if isUTF8 := utf8.ValidString(code); !isUTF8 {
			logs.CtxWarn(ctx, "filtered entity with invalid control character: %s", caller.ID)
			continue
		}
		callers = append(callers, &protocol.MethodInfo{
			FilePath:  caller.URI,
			StartLine: caller.GetStartLine(),
			EndLine:   caller.GetEndLine(),
			Name:      caller.Name,
			Signature: caller.GetSignature(),
			Comment:   caller.GetComment(),
			Content:   code,
		})
	}
	variable.Method.Callers = callers

	// 获取 callee
	calleeEntities, err := storage.SearchCalleeEntity(ctx, tx, entity, nil)
	if err != nil {
		logs.CtxWarn(ctx, "[getMethodRelation] search callee entity err: %v, entity id: %s", err, entity.ID)
		return nil, nil, errors.WithMessagef(err, "error search callee entity")
	}

	validEntities := lo.Filter(calleeEntities, func(item *model.Entity, _ int) bool {
		if isUTF8 := utf8.ValidString(getEntityContent(item)); !isUTF8 {
			logs.CtxWarn(ctx, "filtered entity with invalid control character: %s", item.ID)
			return false
		}
		return true
	})

	variable.Method.Callees = lo.Map(validEntities, func(item *model.Entity, _ int) *protocol.MethodInfo {
		return &protocol.MethodInfo{
			FilePath:  item.URI,
			StartLine: item.GetStartLine(),
			EndLine:   item.GetEndLine(),
			Name:      item.Name,
			Signature: item.GetSignature(),
			Comment:   item.GetComment(),
			Content:   getEntityContent(item),
		}
	})

	// 获取测试函数
	testCallerEntities, err := getTestCallerHelper(ctx, storage, tx, entity)
	if err != nil {
		logs.CtxError(ctx, "getTestCallerHelper err is %v", err)
		return variable, references, err
	}

	testCallers := make([]*protocol.MethodInfo, 0)
	for _, item := range testCallerEntities {
		caller := item.Entity
		if caller.Type != model.Method {
			continue
		}

		code, _, err := getCallerCodeAndReference(ctx, caller, item.Ranges)
		if err != nil {
			logs.CtxWarn(ctx, "getCallerCodeAndReference err is %v, file is %v", err, item.Entity.URI)
			continue
		}

		if isUTF8 := utf8.ValidString(code); !isUTF8 {
			logs.CtxWarn(ctx, "filtered entity with invalid control character: %s", entity.ID)
			continue
		}

		testCallers = append(testCallers, &protocol.MethodInfo{
			FilePath:  caller.URI,
			StartLine: caller.GetStartLine(),
			EndLine:   caller.GetEndLine(),
			Name:      caller.Name,
			Signature: caller.GetSignature(),
			Comment:   caller.GetComment(),
			Content:   code,
		})
	}
	variable.Method.TestFunctions = testCallers

	return variable, references, nil
}

type editorRelation struct {
	editorEntity *protocol.CurrentEditorEntity
	callees      []*protocol.MethodInfo
}

func (s *service) getEditorRelation(ctx context.Context, storage data_storage.Storage, uri string, selectedRange *protocol.Range, uriMeta *data_storage.StorageURIMeta) (*editorRelation, error) {
	logs.CtxDebug(ctx, "query relation for selected code")
	editorEntity := &protocol.CurrentEditorEntity{
		SelectedClasses: nil,
		SelectedMethods: nil,
	}

	allEntities, err := storage.GetAllEntityByURI(ctx, storage.GetConn(), uriMeta.ID)
	if err != nil {
		return nil, errors.WithMessagef(err, "error get all entity by uri")
	}

	// 获取直接选中的函数和类
	selectedClasses := make([]*entitySearchResult, 0)
	selectedMethods := make([]*entitySearchResult, 0)
	selectedClasses = append(selectedClasses, searchSelectedEntities(lo.Filter(allEntities, func(item *model.Entity, _ int) bool {
		return item.Type == model.Clazz
	}), selectedRange.StartLine, selectedRange.EndLine)...)
	selectedMethods = append(selectedMethods, searchSelectedEntities(lo.Filter(allEntities, func(item *model.Entity, _ int) bool {
		return item.Type == model.Method
	}), selectedRange.StartLine, selectedRange.EndLine)...)

	validClasses := lo.Filter(selectedClasses, func(item *entitySearchResult, _ int) bool {
		if isUTF8 := utf8.ValidString(getFullCodeContent(item.Entity, uriMeta)); !isUTF8 {
			logs.CtxWarn(ctx, "filtered entity with invalid control character: %s", item.Entity.ID)
			return false
		}
		return true
	})

	selectedClassInfos := lo.Map(validClasses, func(item *entitySearchResult, _ int) *protocol.SelectedClassInfo {
		content := getEntityContent(item.Entity)
		return &protocol.SelectedClassInfo{
			StartLine:       item.Entity.GetStartLine(),
			EndLine:         item.Entity.GetEndLine(),
			Name:            item.Entity.Name,
			Comment:         item.Entity.GetComment(),
			Content:         content,
			SelectedMethods: make([]*protocol.SelectedMethodInfo, 0),
		}
	})

	globalSelectedMethodInfos := make([]*protocol.SelectedMethodInfo, 0) // 不属于任何类的函数
	for _, m := range selectedMethods {
		content := getEntityContent(m.Entity)
		if isUTF8 := utf8.ValidString(content); !isUTF8 {
			logs.CtxWarn(ctx, "filtered entity with invalid control character: %s", m.Entity.ID)
			continue
		}
		methodInfo := &protocol.SelectedMethodInfo{
			StartLine: m.Entity.GetStartLine(),
			EndLine:   m.Entity.GetEndLine(),
			Name:      m.Entity.Name,
			Comment:   m.Entity.GetComment(),
			Content:   content,
		}

		belongsToClass := false
		for _, c := range selectedClassInfos {
			if c.StartLine <= methodInfo.StartLine && methodInfo.EndLine <= c.EndLine {
				belongsToClass = true
				c.SelectedMethods = append(c.SelectedMethods, methodInfo)
				break
			}
		}

		if !belongsToClass {
			globalSelectedMethodInfos = append(globalSelectedMethodInfos, methodInfo)
		}
	}
	editorEntity.SelectedClasses = selectedClassInfos
	editorEntity.SelectedMethods = globalSelectedMethodInfos

	callees, err := getSelectCodeCallee(ctx, storage, selectedRange, lo.Map(selectedMethods, func(item *entitySearchResult, _ int) *model.Entity {
		return item.Entity
	}))
	if err != nil {
		return nil, errors.WithMessagef(err, "error get select code callee")
	}

	return &editorRelation{
		editorEntity: editorEntity,
		callees:      callees,
	}, nil
}

func getSelectCodeCallee(ctx context.Context, storage data_storage.Storage, selectedRange *protocol.Range, callers []*model.Entity) ([]*protocol.MethodInfo, error) {
	result := make([]*protocol.MethodInfo, 0)

	calleeEntity := make([]*model.Entity, 0)
	for _, caller := range callers {
		calleeEntities, err := storage.SearchCalleeEntity(ctx, storage.GetConn(), caller, selectedRange)
		if err != nil {
			return nil, errors.WithMessagef(err, "error search callee entity")
		}
		calleeEntity = append(calleeEntity, calleeEntities...)
	}

	for _, item := range calleeEntity {
		content := getEntityContent(item)
		if isUTF8 := utf8.ValidString(content); !isUTF8 {
			logs.CtxWarn(ctx, "filtered entity with invalid control character: %s", item.ID)
			continue
		}
		result = append(result, &protocol.MethodInfo{
			FilePath:  item.URI,
			StartLine: item.GetStartLine(),
			EndLine:   item.GetEndLine(),
			Name:      item.Name,
			Signature: item.GetSignature(),
			Comment:   item.GetComment(),
			Content:   content,
		})
	}

	return result, nil
}

func getUsefulFileInfosHelper(ctx context.Context, directoryPath string) (map[string]string, error) {
	result := make(map[string]string, 0)
	files, err := os.ReadDir(directoryPath)
	if err != nil {
		logs.CtxError(ctx, "readDir failed err is %s", err)
		return nil, err
	}

	for _, file := range files {
		if !file.IsDir() && lo.Contains(defaultUsefulFiles, strings.ToLower(file.Name())) {
			filePath := filepath.Join(directoryPath, file.Name())
			content, err := os.ReadFile(filePath)
			if err != nil {
				logs.CtxError(ctx, "ReadFile failed err is %s", err)
				continue
			}
			if len(content) > 0 {
				if isUTF8 := utf8.ValidString(string(content)); !isUTF8 {
					logs.CtxWarn(ctx, "filtered entity with invalid control character: %s", directoryPath)
					continue
				}
				result[filePath] = string(content)
			}
		}
	}
	return result, nil
}

func getFileFullContent(ctx context.Context, entity *model.Entity, uriMeta *data_storage.StorageURIMeta) (string, error) {
	if uriMeta.Content != "" {
		return uriMeta.Content, nil
	}

	fullContent, err := os.ReadFile(entity.URI)
	if err != nil {
		logs.CtxError(ctx, "ReadFile failed err is %s", err)
		return "", err
	}
	return string(fullContent), nil
}

func getCallerCodeAndReference(ctx context.Context, caller *model.Entity, ranges []data_storage.CallerToCalleeAttribute) (string, []*model.CKGReferences, error) {
	fullContent := getEntityContent(caller)
	splitCodeChunks := strings.Split(fullContent, "\n")

	var toReplaceContents []string
	var reference = make([]*model.CKGReferences, 0)

	commentMarker := "//"
	indentation := "  "

	for _, r := range ranges {
		reference = append(reference, &model.CKGReferences{
			Path:      caller.URI,
			StartLine: r.StartLine,
			EndLine:   r.EndLine,
		})
		toReplaceContents = append(toReplaceContents, indentation+commentMarker+" ...")

		startLineInMethod := r.StartLine - caller.GetStartLine()
		length := r.EndLine - r.StartLine + 1

		if startLineInMethod >= 0 && startLineInMethod+length <= int32(len(splitCodeChunks)) {
			content := strings.Join(splitCodeChunks[startLineInMethod:startLineInMethod+length], "\n")
			toReplaceContents = append(toReplaceContents, content)
		} else {
			logs.CtxWarn(ctx, "cannot get caller code, caller entity: %+v, range: %+v", caller.ID, ranges)
		}
	}

	toReplaceContents = append(toReplaceContents, indentation+commentMarker+" ...")

	simplifiedContent := caller.GetSimplifiedContent()
	if simplifiedContent == "" || !strings.Contains(simplifiedContent, model.SimplifiedContentReplaceMarker) {
		return strings.Join(toReplaceContents, "\n"), reference, nil
	}

	calleeContent := strings.Replace(simplifiedContent, model.SimplifiedContentReplaceMarker, strings.Join(toReplaceContents, "\n"), -1)
	return calleeContent, reference, nil
}

func getEntityContent(entity *model.Entity) (result string) {
	contentChunks := entity.GetContent()
	result = strings.Join(contentChunks, "\n")
	return
}

func getFullCodeContent(entity *model.Entity, uriMeta *data_storage.StorageURIMeta) (result string) {
	result = getEntityContent(entity)
	if result == "" {
		uriContent := uriMeta.Content
		if uriContent != "" && entity.GetStartLine() != 0 && entity.GetEndLine() != 0 {
			uriContentLines := strings.Split(uriContent, "\n")
			if int(entity.GetStartLine()) <= len(uriContentLines) && int(entity.GetEndLine()) <= len(uriContentLines) {
				result = strings.Join(uriContentLines[entity.GetStartLine()-1:entity.GetEndLine()], "\n")
			}
		}
	}
	return
}

func getRealName(name string) string {
	re := regexp.MustCompile(`^(.*?)-\d+-sub$`)

	matches := re.FindStringSubmatch(name)

	if len(matches) > 1 {
		return matches[1]
	} else {
		return name
	}
}

type GetFileRelationError struct{ err error }
type GetFolderRelationError struct{ err error }
type GetClassRelationError struct{ err error }
type GetMethodRelationError struct{ err error }

func (e *GetFileRelationError) Error() string   { return e.Error() }
func (e *GetFolderRelationError) Error() string { return e.Error() }
func (e *GetClassRelationError) Error() string  { return e.Error() }
func (e *GetMethodRelationError) Error() string { return e.Error() }
