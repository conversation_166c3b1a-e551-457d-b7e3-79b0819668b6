import type {Position, TextDocument} from 'vscode-languageserver-textdocument';
import {getActiveFileCachedTreeAndDocument} from '../../../cache/astCache';
import {TreeSitterParser} from '../../../parse/treesitter/parser';
import {Logger} from '../../../../utils/logger';
import {
  EVENT_CATEGORY_COMPLETION,
  EVENT_NAME_FAILED,
  EVENT_VALUE_NO_CACHE,
  reportEvent,
} from '../../../../utils/event';
import {
  genDocState, getMatchingSuffixLength,
  needCheckBracketLanguage,
  spliceCompletionText,
} from './share';
import {createDocument, FILE_URI_PREFIX} from "../../../../utils/common";
import {TimeoutError} from "../../../../utils/error";

export const EVENT_PROCESS_COMPLETION_SYNTAX_FIX = "completion_syntax_fix";

const EVENT_VALUE_PARSE_ERROR = "parse_error";

export interface SyntaxFixResult {
  completionText: string
  suffixLengthToRemove: number
  modified: boolean
}

export function doSyntaxFix(document: TextDocument, position: Position, completionText: string, timeoutMs: number): SyntaxFixResult {
  if (!document.uri.startsWith(FILE_URI_PREFIX)) {
    return;
  }

  if (completionText.trim().length === 0) {
    Logger.info(`doSyntaxFix: empty completion text`);
    return null;
  }

  const startTime = Date.now();

  const cachedTree = getActiveFileCachedTreeAndDocument(document)?.tree;
  if (!cachedTree) {
    Logger.error(`doSyntaxFix: no cached tree found for '${document.uri}'`);
    reportEvent(document.languageId, EVENT_CATEGORY_COMPLETION, EVENT_PROCESS_COMPLETION_SYNTAX_FIX, EVENT_NAME_FAILED, EVENT_VALUE_NO_CACHE).then();
    return null;
  }

  const docState = genDocState(document, position);

  let maxMatchingSuffixLength = getMatchingSuffixLength(completionText, docState);

  for (let i = maxMatchingSuffixLength; i >= 0; i--) {
    const {
      textAfterModification,
      edit,
    } = spliceCompletionText(document.getText(), completionText, docState, i);

    const copiedCachedTree = cachedTree.copy();
    copiedCachedTree.edit(edit);

    const currentTree = TreeSitterParser.INSTANCE.parse(document.languageId, textAfterModification, copiedCachedTree, timeoutMs);
    if (!currentTree) {
      Logger.error(`doSyntaxFix: failed to get AST after inserting completion text for '${document.uri}'`);
      reportEvent(document.languageId, EVENT_CATEGORY_COMPLETION, EVENT_PROCESS_COMPLETION_SYNTAX_FIX, EVENT_NAME_FAILED, EVENT_VALUE_PARSE_ERROR).then();
      return null;
    }
    if (Date.now() - startTime > timeoutMs) {
      throw new TimeoutError(`after splicing completion text with removing ${i} suffix characters`);
    }

    if (!currentTree.rootNode.hasError) {
      const duration = Date.now() - startTime;
      Logger.info(`doSyntaxFix: no syntax error found after removing ${i} suffix characters, cost ${duration} ms`);
      return {
        completionText,
        suffixLengthToRemove: i,
        modified: false,
      };
    }
  }

  if (needCheckBracketLanguage(document.languageId)) {
    let {
      textAfterModification,
      edit,
    } = spliceCompletionText(document.getText(), completionText, docState, maxMatchingSuffixLength);

    const checkStartIndex = document.offsetAt({line: edit.startPosition.row, character: 0});
    const checkEndIndex = document.offsetAt({line: edit.newEndPosition.row + 1, character: 0});

    const documentAfterModification = createDocument(textAfterModification, document.languageId, document.uri);
    const missingBracketInfo = analyzeMissingBracket(documentAfterModification, checkStartIndex, checkEndIndex, edit.newEndIndex);
    if (missingBracketInfo.length > 0) {
      Logger.info(`doSyntaxFix: missingBracketInfo: ${missingBracketInfo.map(c => `${c.bracket} ${c.indent} ${c.newLine}`)}`);

      const completionTextWithMissingBracket = generateNewCompletionText(completionText, missingBracketInfo);

      maxMatchingSuffixLength = getMatchingSuffixLength(completionTextWithMissingBracket, docState);

      const spliceCompletionResult = spliceCompletionText(document.getText(), completionTextWithMissingBracket, docState, maxMatchingSuffixLength);

      textAfterModification = spliceCompletionResult.textAfterModification;
      edit = spliceCompletionResult.edit;

      const copiedCachedTreeTryFix = cachedTree.copy();
      copiedCachedTreeTryFix.edit(edit);

      const currentTree = TreeSitterParser.INSTANCE.parse(document.languageId, textAfterModification, copiedCachedTreeTryFix, timeoutMs);
      if (!currentTree) {
        Logger.error(`doSyntaxFix: failed to get AST after adding missing bracket`);
        reportEvent(document.languageId, EVENT_CATEGORY_COMPLETION, EVENT_PROCESS_COMPLETION_SYNTAX_FIX, EVENT_NAME_FAILED, EVENT_VALUE_PARSE_ERROR).then();
        return null;
      }
      if (Date.now() - startTime > timeoutMs) {
        throw new TimeoutError("after adding missing bracket");
      }

      Logger.info(`doSyntaxFix: add ${missingBracketInfo.length} missing bracket, resultCompletionText: '${completionTextWithMissingBracket}'`);

      if (!currentTree.rootNode.hasError) {
        const duration = Date.now() - startTime;
        Logger.info(`doSyntaxFix: no syntax error found after adding missing bracket, cost ${duration} ms`);
        return {
          completionText: completionTextWithMissingBracket,
          suffixLengthToRemove: maxMatchingSuffixLength,
          modified: true,
        };
      }
    }
  }

  for (let i = 1; i < completionText.length; i++) {
    if (!["{", "[", "(", ")", "]", "}"].includes(completionText[completionText.length - i])) {
      break;
    }

    const completionTextRemoveSuffixBracket = completionText.slice(0, completionText.length - i);

    maxMatchingSuffixLength = getMatchingSuffixLength(completionTextRemoveSuffixBracket, docState);

    const {
      textAfterModification,
      edit,
    } = spliceCompletionText(document.getText(), completionTextRemoveSuffixBracket, docState, maxMatchingSuffixLength);

    const copiedCachedTreeTryFix = cachedTree.copy();
    copiedCachedTreeTryFix.edit(edit);

    const currentTree = TreeSitterParser.INSTANCE.parse(document.languageId, textAfterModification, copiedCachedTreeTryFix, timeoutMs);
    if (!currentTree) {
      Logger.error(`doSyntaxFix: failed to get AST after removing ${i} suffix bracket`);
      reportEvent(document.languageId, EVENT_CATEGORY_COMPLETION, EVENT_PROCESS_COMPLETION_SYNTAX_FIX, EVENT_NAME_FAILED, EVENT_VALUE_PARSE_ERROR).then();
      return null;
    }
    if (Date.now() - startTime > timeoutMs) {
      throw new TimeoutError(`after removing ${i} suffix bracket`);
    }

    if (!currentTree.rootNode.hasError) {
      const duration = Date.now() - startTime;
      Logger.info(`doSyntaxFix: no syntax error found after remove ${i} suffix bracket, cost ${duration} ms`);
      return {
        completionText: completionTextRemoveSuffixBracket,
        suffixLengthToRemove: maxMatchingSuffixLength,
        modified: true,
      };
    }
  }

  const duration = Date.now() - startTime;
  Logger.info(`doSyntaxFix: failed to fix completion, cost ${duration} ms`);
  return null;
}

export function analyzeMissingBracket(document: TextDocument, startIndex: number, endIndex: number, targetIndex: number): {
  bracket: string,
  indent: string,
  newLine: boolean
}[] {
  const firstHalfOpeningBracket = getFirstHalfBracketString(document, startIndex, targetIndex);
  const lastHalfClosingBracket = getLastHalfBracketsString(document, targetIndex, endIndex);

  Logger.info(`analyzeMissingBracket: firstHalfText: ${document.getText().slice(startIndex, targetIndex)}, openingBracket: ${firstHalfOpeningBracket}`);
  Logger.info(`analyzeMissingBracket: lastHalfText: ${document.getText().slice(targetIndex, endIndex)}, closingBracket: ${lastHalfClosingBracket}`);

  if (firstHalfOpeningBracket.length <= lastHalfClosingBracket.length) {
    return [];
  }

  if (firstHalfOpeningBracket.map(c => c.bracket).join("").startsWith(lastHalfClosingBracket)) {
    const openingBracketPairs = {
      '(': ')',
      '[': ']',
      '{': '}',
    };

    return firstHalfOpeningBracket.slice(lastHalfClosingBracket.length).reverse().map(c => {
      return {bracket: openingBracketPairs[c.bracket], indent: c.indent, newLine: c.lastChar};
    });
  }

  return [];
}

function getFirstHalfBracketString(document: TextDocument, from: number, to: number): {
  bracket: string,
  indent: string,
  lastChar: boolean
}[] {
  const closingBracketPairs = {
    ')': '(',
    ']': '[',
    '}': '{',
  };

  const openingStack: { bracket: string, indent: string, lastChar: boolean }[] = [];

  const text = document.getText();
  for (let i = from; i < to; i++) {
    const char = text[i];
    if (Object.values(closingBracketPairs).includes(char)) {
      const pos = document.positionAt(i);
      const lineText = document.getText({
        start: {line: pos.line, character: 0},
        end: {line: pos.line + 1, character: 0},
      });
      const spaceLength = lineText.length - lineText.trimStart().length;
      let lastChar = false;
      if (i + 1 === to || text[i + 1] === '\n') {
        lastChar = true;
      }
      openingStack.push({bracket: char, indent: lineText.slice(0, spaceLength), lastChar});
    } else if (Object.keys(closingBracketPairs).includes(char)) {
      if (openingStack.length === 0 || openingStack.at(-1).bracket !== closingBracketPairs[char]) {
        return [];
      }

      openingStack.pop();
    }
  }

  return openingStack;
}

function getLastHalfBracketsString(document: TextDocument, from: number, to: number): string {
  const openingBracketPairs = {
    '(': ')',
    '[': ']',
    '{': '}',
  };
  const closingBracketPairs = {
    ')': '(',
    ']': '[',
    '}': '{',
  };

  const closingStack: string[] = [];

  const text = document.getText();
  for (let i = to - 1; i >= from; i--) {
    const char = text[i];
    if (Object.values(openingBracketPairs).includes(char)) {
      closingStack.push(char);
    } else if (Object.keys(openingBracketPairs).includes(char)) {
      if (closingStack.length === 0 || closingStack.at(-1)[0] !== openingBracketPairs[char]) {
        return "";
      }

      closingStack.pop();
    }
  }

  return closingStack.map(c => closingBracketPairs[c]).join("");
}

export function generateNewCompletionText(completionText: string, missingBracketText: {
  bracket: string,
  indent: string,
  newLine: boolean
}[]): string {
  const resultStrings: string[] = [];
  resultStrings.push(...completionText.split("\n"));
  missingBracketText.forEach((bracketInfo: { bracket: string, indent: string, newLine: boolean }) => {
    if (resultStrings.length > 0) {
      if (bracketInfo.newLine) {
        resultStrings.push(`${bracketInfo.indent}${bracketInfo.bracket}`);
      } else {
        const lastLine = resultStrings.pop();
        resultStrings.push(`${lastLine}${bracketInfo.bracket}`);
      }
    } else {
      resultStrings.push(`${bracketInfo.indent}${bracketInfo.bracket}`);
    }
  });
  return resultStrings.join("\n");
}