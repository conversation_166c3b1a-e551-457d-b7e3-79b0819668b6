{
  "extends": "@artus/tsconfig",
  "compilerOptions": {
    "types": [
      "node"
    ],
    "target": "ES2020",
    "lib": ["dom", "ES2020"],
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "resolveJsonModule": true,
    "strictNullChecks": false,
    "strictFunctionTypes": true,
    "strictPropertyInitialization": false,
    "noImplicitReturns": false,
    "typeRoots": [
      "node_modules/@types",
    ]
  },
  "ts-node": {
    // Do not forget to `npm i -D tsconfig-paths`
    "require": [
      "tsconfig-paths/register"
    ]
  }
}
