import {MessageType, MethodType, WorkerMessage, MessagePort} from "./share";
import {Logger} from "../utils/logger";

export abstract class BaseHandler {
  private messageId = 0;
  private messageMap = new Map<number, { resolve: any, reject: any }>();
  private methodHandler = new Map<MethodType, (args: any) => Promise<any>>();

  init(): void {
    this.port().on('message', (message: WorkerMessage) => {
      this.onMessage(message).then();
    });

    this.registerMethodHandlers();
  }

  protected async sendToPort(method: MethodType, params: any): Promise<any> {
    const id = this.messageId++;

    this.port().postMessage({
      id,
      type: MessageType.Request,
      method,
      payload: params,
    });

    return new Promise<any>((resolve, reject) => {
      this.messageMap.set(id, {resolve, reject});
    });
  }

  private async onMessage(message: WorkerMessage): Promise<void> {
    switch (message.type) {
      case MessageType.Response:
        this.messageMap.get(message.id)?.resolve(message.payload);
        this.messageMap.delete(message.id);
        break;
      case MessageType.Request:
        const method = this.methodHandler.get(message.method);
        if (method) {
          try {
            const result = await method(message.payload);
            this.port().postMessage({
              id: message.id,
              type: MessageType.Response,
              method: message.method,
              payload: result,
            });
          } catch (e) {
            Logger.error(`BaseHandler.onMessage: exception for '${message.method}': ${e}`);
            Logger.error(e.stack);
            this.port().postMessage({
              id: message.id,
              type: MessageType.Response,
              method: message.method,
              payload: null,
            });
          }
        } else {
          this.port().postMessage({
            id: message.id,
            type: MessageType.Response,
            method: message.method,
            payload: null,
          });
        }
        break;
    }
  }

  private registerMethodHandlers(): void {
    for (const [key, value] of this.getMethodHandlers()) {
      this.methodHandler.set(key, value);
    }
  }

  abstract port(): MessagePort;

  abstract getMethodHandlers(): Map<MethodType, (args: any) => Promise<any>>;
}