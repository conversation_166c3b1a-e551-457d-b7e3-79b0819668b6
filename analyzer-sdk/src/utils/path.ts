import path from "path";
import {TextDocument} from "vscode-languageserver-textdocument";
import {Logger} from "./logger";

export function getRelativePathFromDocument(document: TextDocument, projectId: string): string {
  let filePath = document.uri;
  if (!filePath.startsWith("file://")) {
    Logger.warn(`getRelativePathFromDocument: filePath '${filePath}' is not a file path`);
    return null;
  }
  filePath = filePath.slice(7);

  if (projectId && filePath.startsWith(projectId)) {
    return path.relative(projectId, filePath);
  }

  Logger.warn(`getRelativePathFromDocument: projectId '${projectId}' is not a prefix of filePath '${filePath}'`);
  return null;
}

let workspaceFolderPath: string;
export function getWorkspaceFolderPath(): string {
  return workspaceFolderPath;
}
export function setWorkspaceFolderPath(path: string): void {
  workspaceFolderPath = path;
}