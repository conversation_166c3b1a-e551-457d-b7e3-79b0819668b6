package model

import "fmt"

type IndexAttribute struct {
	ChunkingMethod string
	EmbeddingModel string
}

func (a *IndexAttribute) String() string {
	return fmt.Sprintf("chunking-method-%s-embedding-model-%s", a.<PERSON>od, a.EmbeddingModel)
}

// 目前只在 virtual projects 中使用
type IndexTaskAttribute struct {
	ChunkingMethod      string `json:"chunking_method"`
	ChunkingSize        int    `json:"chunking_size"`
	ChunkingOverlap     int    `json:"chunking_overlap"`
	RemoveEntityContent bool   `json:"remove_entity_content"`
}
