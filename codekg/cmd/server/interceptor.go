package main

import (
	"code.byted.org/gopkg/ctxvalues"
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"golang.org/x/sync/singleflight"
	"google.golang.org/grpc"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/util"
	"runtime/debug"
)

var (
	group = &singleflight.Group{}
)

func unaryInjectLogIDInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	ctx = ctxvalues.SetLogID(ctx, util.GenLogID())
	return handler(ctx, req)
}

func unaryPanicInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	defer func() {
		if r := recover(); r != nil {
			logs.CtxInfo(ctx, "unary panic recovered, err: %v, %s", r, string(debug.Stack()))
		}
	}()

	return handler(ctx, req)
}

func unarySingleFlightInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	// 相同的 request 同时只处理一次
	reqData, err := json.Marshal(req)
	if err != nil {
		logs.CtxInfo(ctx, "interceptor marshal request failed, err: %v", err)
		return handler(ctx, req)
	}

	sum := sha256.Sum256(reqData)
	key := fmt.Sprintf("%s_%x", info.FullMethod, sum)
	resp, err, shared := group.Do(key, func() (interface{}, error) {
		return handler(ctx, req)
	})
	if shared {
		logs.CtxInfo(ctx, "single flight key: %s is shared", key)
	}
	return resp, err
}

func streamPanicInterceptor(srv any, stream grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) (err error) {
	defer func() {
		if r := recover(); r != nil {
			logs.Info("stream panic recovered, err: %v, %s", r, string(debug.Stack()))
		}
	}()

	return handler(srv, stream)
}
