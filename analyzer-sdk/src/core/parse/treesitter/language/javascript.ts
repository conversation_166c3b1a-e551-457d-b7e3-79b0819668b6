import {SyntaxNode} from "web-tree-sitter";
import {Range} from "vscode-languageserver-textdocument";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON>} from "./common";
import {TreeSitterParser} from "../parser";
import {DefNodeInfo, DefNodeKindEnum, LanguageIdEnum} from "../../share";
import {DocumentUri} from "vscode-languageserver-types";

export class JavascriptTreeSitterHandler extends TreeSitterHandler {
  static JAVASCRIPT_INSTANCE = new JavascriptTreeSitterHandler(LanguageIdEnum.Javascript);
  static JSX_INSTANCE = new JavascriptTreeSitterHandler(LanguageIdEnum.Jsx);
  static JAVASCRIPT_REACT_INSTANCE = new JavascriptTreeSitterHandler(LanguageIdEnum.JavascriptReact);

  languageId = LanguageIdEnum.Javascript;

  funcTypes = ["function_declaration", "method_definition", "generator_function", "generator_function_declaration", "function", "arrow_function"];
  typeTypes = ["class_declaration", "class"];
  fileTypes = ["program"];
  varTypes = ["variable_declarator", "required_parameter", "public_field_definition"];

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor(languageId: LanguageIdEnum) {
    super();
    this.languageId = languageId;
  }

  getParentNodeForKeywordSearch(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, [
      ...this.funcTypes,
      ...this.typeTypes,
      ...this.fileTypes,
    ]);
  }

  getFuncParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.funcTypes);
  }

  getFuncParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.funcTypes);
  }

  getOutermostFuncParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getOutermostParentNodeWithTypeListForRange(node, range, this.funcTypes);
  }

  getTypeParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.typeTypes);
  }

  getTypeParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.typeTypes);
  }

  getVarParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.varTypes);
  }

  getVarParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.varTypes);
  }

  getMethodInRange(rootNode: SyntaxNode, range: Range, startIndex: number, endIndex: number, completelyIncluded: boolean): SyntaxNode[] {
    return this.getElementInRangeWithTypeList(rootNode, range, startIndex, endIndex, completelyIncluded, this.funcTypes);
  }

  getImportFileOrModule(node: SyntaxNode): string[] {
    const result: string[] = [];

    const importQueryStr = `
(program
  (import_statement) @import
)
`;

    const importMatchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, importQueryStr, node);

    for (const match of importMatchResult) {
      const importInfoNode = match.matches.get("import");

      if (importInfoNode.length === 1) {
        result.push(importInfoNode[0].text);
      }
    }

    const requireVarValueQueryStr = `
(program
  (lexical_declaration
    (variable_declarator
      value:(_) @var_value
    )
  )
)
`;

    const requireVarValueMatchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, requireVarValueQueryStr, node);

    for (const match of requireVarValueMatchResult) {
      const varValueNode = match.matches.get("var_value");

      if (varValueNode.length === 1 && varValueNode[0].text.startsWith("require")) {
        let curNode = varValueNode[0];
        while (curNode.type !== "lexical_declaration") {
          curNode = curNode.parent;
        }
        result.push(curNode.text);
      }
    }

    return result;
  }

  genTypeInfoFromDefNode(nodeUri: DocumentUri, node: SyntaxNode, typeName?: string): DefNodeInfo {
    let name = typeName;
    if (!name) {
      name = node.childForFieldName("name")?.text ?? "";
    }

    return {
      languageId: this.languageId,
      uri: nodeUri,
      kind: DefNodeKindEnum.Class,
      name,
      defNode: node,
      subDefNodeInfo: null,
    };
  }

  getImportInfo(rootNode: SyntaxNode): any {
    // TODO
    return null;
  }
}