import {DocumentUri, Location} from "vscode-languageserver-types";
import type {SyntaxNode} from "web-tree-sitter";
import {findDescendantNodeForRange, SyntaxNodeWithUri, UsageNodeInfo} from "../share";
import {LSPParser} from "./parser";
import {pointToPosition} from "../../../utils/common";
import {getTreeAndDocumentFromUri} from "../../cache/astCache";

export class LspExecutor {
  static async getDefinitionNode(languageId: string, uri: DocumentUri, node: SyntaxNode, resultFilter: (location: Location) => boolean): Promise<SyntaxNodeWithUri[]> {
    return this.lspLocationOperation(languageId, "vscode.executeDefinitionProvider", uri, node, resultFilter);
  }

  static async getUsage(languageId: string, uri: DocumentUri, node: SyntaxNode, resultFilter: (location: Location) => boolean): Promise<UsageNodeInfo[]> {
    const psiResult = await this.lspLocationOperation(languageId, "vscode.executeReferenceProvider", uri, node, resultFilter);
    return psiResult.map(item => {
      return {
        languageId,
        uri: item.uri,
        usageNode: item.node,
      };
    });
  }

  static async getUsageInUri(languageId: string, uri: DocumentUri, node: SyntaxNode, scopeUri: DocumentUri): Promise<UsageNodeInfo[]> {
    const usageInfos = await this.getUsage(languageId, uri, node, null);
    return usageInfos.map(usageInfo => {
      if (!scopeUri || usageInfo.uri === scopeUri) {
        return usageInfo;
      } else {
        return null;
      }
    }).filter(item => item !== null);
  }

  private static async lspLocationOperation(languageId: string, command: string, uri: DocumentUri, node: SyntaxNode, resultFilter: (location: Location) => boolean): Promise<SyntaxNodeWithUri[]> {
    const result: SyntaxNodeWithUri[] = [];

    const definitionResult: Location[] = await LSPParser.INSTANCE.executeInMainThread(command, languageId, uri, pointToPosition(node.startPosition));
    for (const location of definitionResult ?? []) {
      if (location.uri === uri && location.range.start.line === node.startPosition.row && location.range.start.character === node.startPosition.column) {
        continue;
      }

      if (!resultFilter || resultFilter(location)) {
        const {tree, document} = getTreeAndDocumentFromUri(location.uri, languageId);

        if (tree && document) {
          const endOffset = document.offsetAt(location.range.end);
          const endChar = document.getText().at(endOffset > 0 ? endOffset - 1: 0);

          const justifiedRange = {
            start: {
              line: location.range.start.line,
              character: location.range.start.character,
            },
            end: {
              line: location.range.end.line,
              character: endChar === ";" ? location.range.end.character - 1 : location.range.end.character,
            },
          };

          result.push({
            uri: location.uri,
            node: findDescendantNodeForRange(tree.rootNode, document, justifiedRange),
          });
        }
      }
    }

    return result;
  }
}