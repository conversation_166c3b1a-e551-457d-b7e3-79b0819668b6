package util

import (
	"fmt"
	"path/filepath"
	"strconv"
	"strings"
	"sync"

	"ide/ckg/codekg/components/knowledgebase"

	"github.com/go-enry/go-enry/v2"
	"go.uber.org/atomic"
)

const (
	SimplifiedContentReplaceMarker = "{$0x233REPLACE_ME332x0$}"
)

// SegmentIDGenerator is a generator for segment ID.
type SegmentIDGenerator struct {
	counter map[string]atomic.Int64
	fileID  string
	lang    string
	lock    sync.RWMutex
}

func NewSegmentIDGenerator(fileID string) *SegmentIDGenerator {
	lang, _ := enry.GetLanguageByExtension(fileID)
	return &SegmentIDGenerator{
		counter: map[string]atomic.Int64{},
		fileID:  fileID,
		lang:    lang,
	}
}

func (s *SegmentIDGenerator) GenerateFileTopLevelSegmentID(segment *knowledgebase.FileTopLevelSegment) string {
	return s.deduplicateSegmentID(fmt.Sprintf("FT.%s.%s", s.fileID, hashSegment(segment)))
}

func (s *SegmentIDGenerator) GenerateFileSegmentID() string {
	// There's no deduplicate on file segment id. It is always unique.
	return fmt.Sprintf("F.%s", s.fileID)
}

func (s *SegmentIDGenerator) GenerateClassSegmentID(pkgPath string, className string) string {
	// There's no deduplicate on class segment id. It is always unique.
	// class & method names are unique only within single file, so we need to add base file name to its class name when generating id.
	fileName := filepath.Base(s.fileID)
	fileNameWithoutExtension := strings.TrimSuffix(fileName, filepath.Ext(fileName))
	className = fmt.Sprintf("%s.%s", fileNameWithoutExtension, className)
	if pkgPath == "." {
		return fmt.Sprintf("C.%s", className)
	}
	return fmt.Sprintf("C.%s.%s", pkgPath, className)
}

func (s *SegmentIDGenerator) GenerateMethodSegmentID(pkgPath string, className string, methodName string) string {
	// There's no deduplicate on method segment id. It is always unique.
	var strList = []string{"M"}
	if pkgPath != "." {
		strList = append(strList, pkgPath)
	}

	// class & method names are unique only within a file, so we need to add base file name to its class name when generating id.
	fileName := filepath.Base(s.fileID)
	fileNameWithoutExtension := strings.TrimSuffix(fileName, filepath.Ext(fileName))
	strList = append(strList, fileNameWithoutExtension)

	if className != "" {
		strList = append(strList, className)
	}
	strList = append(strList, methodName)
	return strings.Join(strList, ".")
}

func (s *SegmentIDGenerator) GenerateCodeChunkSegmentID(segment *knowledgebase.CodeChunkSegment) string {
	return s.deduplicateSegmentID(fmt.Sprintf("CC.%s.%s", s.fileID, hashSegment(segment)))
}
func (s *SegmentIDGenerator) GenerateTextSegmentID(segment *knowledgebase.TextSegment) string {
	return s.deduplicateSegmentID(fmt.Sprintf("T.%s.%s", s.fileID, hashSegment(segment)))
}

func (s *SegmentIDGenerator) GenerateClassTopLevelSegmentID(className string, segment *knowledgebase.ClassTopLevelSegment) string {
	return s.deduplicateSegmentID(fmt.Sprintf("CT.%s.%s.%s", s.fileID, className, hashSegment(segment)))
}

func (s *SegmentIDGenerator) deduplicateSegmentID(segmentID string) string {
	s.lock.RLock()
	counter, ok := s.counter[segmentID]
	s.lock.RUnlock()
	if ok {
		inc := counter.Inc()
		segmentID = segmentID + "." + strconv.FormatInt(inc, 10)
	} else {
		s.lock.Lock()
		s.counter[segmentID] = atomic.Int64{}
		s.lock.Unlock()
	}
	return segmentID
}
