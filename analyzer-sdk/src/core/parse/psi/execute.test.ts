import {beforeAll, describe, expect, it} from "vitest";
import {setEnableLocalLogging} from "../../../utils/logger";
import {TreeSitterParser} from "../treesitter/parser";
import {LanguageIdEnum} from "../share";
import {PsiExecutor} from "./executor";

describe("test_adjustRangeForJava", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
    setEnableLocalLogging(true)
  });

  it("case1_timeout_interval", async () => {
    const docText = `
/**
 * comment
 */
@annotation
class ExampleClass {
    // 字段
    private _name: string;
    private _value: number;
    
    // 构造函数
    constructor(name: string, value: number) {
        this._name = name;
        this._value = value;
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Java, docText);

    const result = PsiExecutor.adjustRangeForJava(tree.rootNode, {start: {line: 1, character: 0}, end: {line: 15, character: 1}});
    expect(result).toEqual({start: {line: 4, character: 0}, end: {line: 15, character: 1}});
  });
});