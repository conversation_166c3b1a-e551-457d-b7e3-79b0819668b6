import axios from "axios";
import child_process, {
  ChildProcessWithoutNullStreams,
  spawn,
} from "child_process";
import * as crypto from "crypto";
import * as lockfile from "proper-lockfile";
import fs from "fs";
import os from "os";
import path from "path";
import util from "util";
import pkg from "../../package.json";
import {
  DownloadRetryMaxError,
  GlibcVersionError,
  LockReleaseError,
  NetworkError,
  UnknownError,
  UnsupportedOSArchError, UserEnvUnsupportedCKG,
} from "../errors/errors";
import { Logger } from "../metrics/logger";
import { toSnakeCase } from "../utils/util";

const mkdir = util.promisify(fs.mkdir);

export const CKG_PORT = 50051;
export const CKG_HOST = "127.0.0.1";

export let binaryFileName = "ckg_server";
export const binaryLinuxX64 = "ckg_server_linux_x64";
export const binaryLinuxArm64 = "ckg_server_linux_arm64";
export const binaryWindowsX64 = "ckg_server_windows_x64.exe";
export const binaryDarwinX64 = "ckg_server_darwin_x64";
export const binaryDarwinArm64 = "ckg_server_darwin_arm64";

export enum CKGBinaryStatus {
  VALID = "VALID",
  NOT_EXIST = "NOT_EXIST",
  BROKEN = "BROKEN", // md5 check failed
  GLIBC_VERSION_CHECK_FAILED = "GLIBC_VERSION_CHECK_FAILED",
  USER_ENV_UNSURPPORTED_CKG = "USER_ENV_UNSURPPORTED_CKG",
}

export enum CKGProcessStatus {
  NOTEXIST = "NOTEXIST",
  RUNNING = "RUNNING",
  TERMINATED = "TERMINATED",
  KILLED = "KILLED",
}

export enum SourceProductType {
  Codeverse = "codeverse",
  Marscode = "marscode",
  Ide = "ide",
  NativeIDE = "native_ide",
  A0 = "a0",
}

// 独立进程启动参数
export interface CKGCommandArgs {
  ideID?: string;
  host?: string;
  appID?: string; // 插件传入
  storagePath: string;
  port?: number; // 随机选取一个ports
  ideVersion?: string; // 插件传入
  versionCode?: string; // 插件传入
  region?: string; // 插件传入
  extensionVersion?: string; // 插件传入
  limitCpu?: number;
  sourceProduct?: SourceProductType; // 插件传入
  localEmbedding?: string;
  embeddingStorageType?: string;
  [key: string]: string | number | undefined;
}

export interface CKGProcessOptions {
  channelType: string;  // 插件传入，表示运行插件的产品，e.g. VsCode, GoLand, IDEA 之类
  binaryRootDir: string;
  commandArgs: CKGCommandArgs;
}

// 二进制下载和路径相关函数，放在这里

export const getOutdatedBinaries = (binaryDir: string): string[] => {
  if (!fs.existsSync(binaryDir)) {
    return [];
  }
  const versionRegex = /^(\d+)\.(\d+)\.(\d+)(?:-([\da-zA-Z\-]+(?:\.[\da-zA-Z\-]+)*))?(?:\+([\da-zA-Z\-]+(?:\.[\da-zA-Z\-]+)*))?$/;
  const files = fs.readdirSync(binaryDir);
  const binaryCandidates = [];
  for (const file of files) {
    const absPath = path.join(binaryDir, file);
    let fileInfo = null;
    try {
      fileInfo = fs.statSync(absPath);
      if (fileInfo.isDirectory() && versionRegex.exec(file) && file !== pkg.version && file !== "storage") {
        Logger.info(`CodeKG found outdated binary, path ${file}, abs path: ${absPath}`);
        binaryCandidates.push(absPath);
      }
    } catch (e) {
      Logger.warn(`CodeKG error getting file info, path ${absPath}, error ${e}`);
    }
  }
  return binaryCandidates;
};

export const cleanOtherVersionedBinary = (binaryDir: string): void => {
  const outdatedBinaryDirs = getOutdatedBinaries(binaryDir);
  for (const dir of outdatedBinaryDirs) {
    try {
      fs.rmSync(dir, { recursive: true, force: true });
      Logger.info(`CodeKG deleted outdated binary, dir ${dir}`);
    } catch (e) {
      Logger.warn(`CodeKG error deleting outdated binary, error ${e}, dir ${dir}`);
    }
  }
};

export const cleanOtherVersionedBinaryWithLock = async (binaryDir: string): Promise<void> => {
  try {
    let release = await acquireDownloadLock(binaryDir);
    cleanOtherVersionedBinary(binaryDir);
    await releaseDownloadLock(binaryDir, release);
  } catch (e) {
    Logger.error(`CodeKG error releasing lock when cleaning binaries under ${binaryDir}, error ${e}`);
    throw e;
  }
};

export async function setExecutePermission(filePath: string): Promise<void> {
  if (os.platform() === "win32" || os.platform() === "cygwin") {
    // Windows 平台不需要设置文件权限
  } else {
    // Unix-like 平台 (如 Linux, MacOS)
    return fs.chmodSync(filePath, 0o555);
  }
};

export const getBinaryFileName = (): string => {
  const osType = os.type();
  const archType = os.arch();
  if (osType === "Linux" && archType === "arm64") {
    binaryFileName = binaryLinuxArm64;
    return binaryLinuxArm64;
  }
  if (osType === "Linux" && archType === "x64") {
    binaryFileName = binaryLinuxX64;
    return binaryLinuxX64;
  }
  if (osType === "Darwin" && archType === "arm64") {
    binaryFileName = binaryDarwinArm64;
    return binaryDarwinArm64;
  }
  if (osType === "Darwin" && archType === "x64") {
    binaryFileName = binaryDarwinX64;
    return binaryDarwinX64;
  }
  if (osType === "Windows_NT" && archType === "x64") {
    binaryFileName = binaryWindowsX64;
    return binaryWindowsX64;
  }
  throw new UnsupportedOSArchError(`ckg not support ${os.type()} ${os.arch()}`);
};

/**
 * getChannelBinaryPath 根据传入的二进制存储根路径和 channelType，拼接出具体的某个 channel 的二进制的存储路径
 * @param binaryRootDir 二进制存储根路径
 * @param channelType 插件传入的 channelType
 * @returns 具体的某个 channel 的二进制的存储路径
 */
export const getChannelBinaryPath = (binaryRootDir: string, channelType: string): string => {
  const dirPath = path.join(binaryRootDir, channelType);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
  return dirPath;
}

/**
 * getCKGBinaryPath 根据传入的二进制存储根路径，拼接出具体 CKG 二进制的存储路径
 * @param storagePath 二进制存储根路径
 * @returns 具体的某个 CKG 二进制的存储路径
 */
export const getCKGBinaryPath = (storagePath: string): string => {
  const dirPath = path.join(storagePath, pkg.version);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
  return path.join(dirPath, getBinaryFileName());
};

export const acquireDownloadLock = async (binaryRootDir: string): Promise<() => Promise<void>> => {
  const lockOpt = {
    stale: 10000, // 当锁不再被持有时，锁的过期时间
    update: 5000, // 当锁被持有时，更新锁的时间间隔
    onCompromised: e => {
      Logger.warn(`CKG error locking binary, error ${e}`);
      throw e;
    },
  };
  let retryCount = 0;
  // 自旋锁
  while (true) {
    try {
      return await lockfile.lockSync(binaryRootDir, lockOpt);
    } catch (e) {
      retryCount++;
      Logger.warn(`CKG failed to lock ${binaryRootDir}, retry num: ${retryCount}, error ${e}`);
    }
    let timeout = crypto.randomInt(3000, 5000);
    await new Promise(resolve => setTimeout(resolve, timeout));
  }
};

export const releaseDownloadLock = async (binaryRootDir: string, release: () => Promise<void>): Promise<void> => {
  try {
    await release();
  } catch (e) {
    Logger.error(`CKG error releasing, error ${e}`);
    throw new LockReleaseError(`release lock error: ${binaryRootDir}`);
  }
};

export const getCWD = (binaryFilePath: string): string => {
  return path.dirname(binaryFilePath);
};

export const getBinaryMd5HashFromRemote = async (
  downloadHost: string,
  binaryFilePath: string,
): Promise<string> => {
  let md5URL = `${downloadHost}/obj/marscode-bucket-cn/ckg_sdk_binary/${pkg.version}/${getBinaryFileName()}.md5`;
  const md5Response = await axios.get(md5URL);
  const expectedMd5 = md5Response.data.trim().split(/\s+/)[0];
  return expectedMd5;
};

export const downloadBinary = async (downloadHost: string, binaryFilePath: string): Promise<void> => {
  const dir = path.dirname(binaryFilePath);
  if (!fs.existsSync(dir)) {
    await mkdir(dir, { recursive: true });
  }

  let downloadURL = `${downloadHost}/obj/marscode-bucket-cn/ckg_sdk_binary/${pkg.version}/${getBinaryFileName()}`;


  if (fs.existsSync(binaryFilePath)) {
    await fs.rmSync(binaryFilePath, { recursive: true, force: true });
  }

  // download binary file
  const response = await axios.get(downloadURL, {
    responseType: "stream",
  });
  const stream = fs.createWriteStream(binaryFilePath, { flags: "w" });
  return new Promise<void>((resolve, reject) => {
    response.data.pipe(stream);
    let err: any = null;
    stream.on("error", e => {
      err = e;
      stream.close();
      reject(new NetworkError(`download binary failed, error ${err}`));
    });
    stream.on("close", () => {
      stream.close();
      if (!err) {
        resolve();
      }
    });
  });
};

export const getBinaryStatus = async (
  path: string,
): Promise<CKGBinaryStatus> => {
  return new Promise((resolve, _reject) => {
    if (!fs.existsSync(path)) {
      resolve(CKGBinaryStatus.NOT_EXIST);
      return;
    }
    const wrappedShell = `"${path}" --help`;
    child_process.exec(wrappedShell, {}, (err, _stdout, _stderr) => {
      if (err) {
        if (`${err}`.includes("GLIBC")) {
          resolve(CKGBinaryStatus.GLIBC_VERSION_CHECK_FAILED);
        } else {
          resolve(CKGBinaryStatus.USER_ENV_UNSURPPORTED_CKG);
          Logger.error(`ckg binary check failed, error ${err}`);
        }
      } else {
        resolve(CKGBinaryStatus.VALID);
      }
    });
  });
};

export const isBinaryExist = async (path: string): Promise<boolean> => {
  return new Promise((resolve, _reject) => {
    resolve(fs.existsSync(path));
  });
};

export const getBinaryMD5 = async (path: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash("md5");
    const stream = fs.createReadStream(path);

    stream.on("data", data => {
      hash.update(data);
    });

    stream.on("end", () => {
      const md5sum = hash.digest("hex");
      resolve(md5sum);
    });

    stream.on("error", err => {
      reject(err);
    });
  });
};

export const checkBinaryMD5 = async (downloadHost: string, path: string): Promise<boolean> => {
  // 获取本地md5 sum
  let binaryMD5;
  try {
    binaryMD5 = await getBinaryMD5(path);
  } catch (e) {
    throw new NetworkError(`get binary md5 failed, ${e}`);
  }
  // 读取远端下载到本地的md5的内容
  const remoteMd5Hash = await getBinaryMd5HashFromRemote(downloadHost, path);
  Logger.info(`local binaryMD5: ${binaryMD5}, remoteMd5Hash: ${remoteMd5Hash}`);
  // 返回校验结果
  return binaryMD5 === remoteMd5Hash;
};

export class CKGProcess {
  // _ckgProcess可以得到pid
  private _ckgProcess: ChildProcessWithoutNullStreams;
  private _opts: CKGProcessOptions;
  private _status: CKGProcessStatus;

  constructor(opts: CKGProcessOptions) {
    this._opts = opts;
    this._status = CKGProcessStatus.NOTEXIST;
  }

  // 二进制下载，CKGProcess初始化, 下载重试逻辑
  async ckgBinaryDownload(downloadHost: string): Promise<void> {
    // 删除历史在项目存储目录下载的过期二进制，以及当前 channel 下的过期二进制
    const storagePath = this._opts.commandArgs.storagePath;
    const channelBinaryPath = getChannelBinaryPath(this._opts.binaryRootDir, this._opts.channelType);
    Logger.info(`CodeKG storage path ${storagePath}, channelBinaryPath ${channelBinaryPath}`);
    await cleanOtherVersionedBinaryWithLock(storagePath);
    await cleanOtherVersionedBinaryWithLock(channelBinaryPath);

    // 二进制下载
    const versionedBinaryPath = getCKGBinaryPath(channelBinaryPath);
    Logger.info(`CodeKG versioned binary path ${versionedBinaryPath}`);
    let binaryStatus = await this.ckgBinaryCheck(downloadHost, versionedBinaryPath);
    if (binaryStatus === CKGBinaryStatus.VALID) {
      return;
    }
    // 当前二进制不存在或错误，执行下载逻辑
    // 自旋锁 “当前运行插件的产品”，e.g. GoLand 下载与 VsCode 下载不冲突
    let release = await acquireDownloadLock(channelBinaryPath);
    for (let retry = 0; retry < 3; retry++) {
      try {
        await downloadBinary(downloadHost, versionedBinaryPath);
        await setExecutePermission(versionedBinaryPath);
      } catch (e) {
        await new Promise(resolve => setTimeout(resolve, 3 * 1000));
        continue;
      }
      binaryStatus = await this.ckgBinaryCheck(downloadHost, versionedBinaryPath); // binaryStatus = await getBinaryStatus(versionedBinaryPath);
      // 根据binaryStatus执行不同的操作
      if (binaryStatus === CKGBinaryStatus.VALID) {
        Logger.info(`CKG binary is valid, ${versionedBinaryPath}, retry ${retry}}`);
        try {
          // 确保正常退出时释放锁
          await releaseDownloadLock(channelBinaryPath, release);
          return;
        } catch (e) {
          Logger.error(`CKG release lock but failed, err: ${e}`);
          throw new LockReleaseError(`CKG release lock, err: ${e}`);
        }
      } else if (binaryStatus === CKGBinaryStatus.BROKEN) {
        // broken和not exist的操作相同
        // broken多一个删除的逻辑，在download里有了
        Logger.warn(`CKG binary is broken, delete and download, retry ${retry}`);
      } else if (binaryStatus === CKGBinaryStatus.NOT_EXIST) {
        Logger.info(`CKG binary is not exist, download binary, retry ${retry}`);
      }
    }
    try {
      await releaseDownloadLock(channelBinaryPath, release);
    } catch (e) {
      // 锁释放失败，直接抛出异常。
      // 如果再次尝试获取锁。目前获取锁的逻辑是 “随机等待自旋”，可能导致死锁。
      // 防止下载时抛出异常导致锁未释放.
      Logger.error(`CKG release lock but failed, err: ${e}`);
      throw new LockReleaseError(`CKG release lock, err: ${e}`);
    }

    if (binaryStatus === CKGBinaryStatus.NOT_EXIST) {
      throw new NetworkError(
        `CodeKG ckg binary is not exist, retry max, status: ${binaryStatus}`,
      );
    }
    // 重试结束仍然没有return, 抛出错误
    throw new DownloadRetryMaxError(
      `CodeKG ckg binary is not valid, retry max, status: ${binaryStatus}`,
    );
  }

  /**
   * ckgBinaryCheck 仅检查一下部分
   * 1. 是否存在二进制
   * 2. 二进制 md5 是否一致
   * @param versionedBinaryPath
   */
  async ckgBinaryCheck(downloadHost: string, versionedBinaryPath: string): Promise<CKGBinaryStatus> {
    let binaryStatus = CKGBinaryStatus.NOT_EXIST;
    if (!(await isBinaryExist(versionedBinaryPath))) {
      binaryStatus = CKGBinaryStatus.NOT_EXIST;
    } else {
      // md5 check
      let isValid = false;
      try {
        isValid = await checkBinaryMD5(downloadHost, versionedBinaryPath);
      } catch (e) {
        // 下载MD5都出错，直接抛出error
        Logger.error(`ckg error checking binary md5, error ${e}`);
        // NetworkError
        throw new NetworkError(`get binary md5 failed, ${e}`);
      }
      if (isValid) {
        binaryStatus = CKGBinaryStatus.VALID;
        Logger.info(`ckg binary md5 check success`);
      } else {
        // md5 check failed
        binaryStatus = CKGBinaryStatus.BROKEN;
        Logger.info(`ckg binary md5 check faled`);
      }
    }
    return binaryStatus;
  }

  // 执行检测
  async ckgBinaryExecCheck(): Promise<void> {
    const channelBinaryPath = getChannelBinaryPath(this._opts.binaryRootDir, this._opts.channelType);
    const ckgBinaryPath = getCKGBinaryPath(channelBinaryPath);
    const ckgStatus = await getBinaryStatus(ckgBinaryPath);
    if (ckgStatus === CKGBinaryStatus.GLIBC_VERSION_CHECK_FAILED) {
      throw new GlibcVersionError("Glibc version check failed");
    } else if (ckgStatus === CKGBinaryStatus.USER_ENV_UNSURPPORTED_CKG) {
      throw new UserEnvUnsupportedCKG('user env is not supported ckg');
    }
    return;
  }

  // 启动
  async start(isDaemon: boolean, ppe?: string): Promise<boolean> {
    const channelBinaryPath = getChannelBinaryPath(this._opts.binaryRootDir, this._opts.channelType);
    const ckgBinaryPath = getCKGBinaryPath(channelBinaryPath);
    const args: string[] = [
      `--port`,
      `${this._opts.commandArgs.port || CKG_PORT}`,
    ];

    Object.keys(this._opts.commandArgs).forEach(arg =>
      args.push(...[`--${toSnakeCase(arg)}=${this._opts.commandArgs[arg]}`]),
    );
    Logger.info(`CKG process start, args: ${args.join(" ")}`);
    return new Promise((resolve, _reject) => {
      try {
        if (isDaemon) {
          // 守护进程
          // todo
          resolve(false);
        } else {
          // 子进程
          if (this.getStatus() === CKGProcessStatus.RUNNING) {
            resolve(true);
            return;
          }

          this._status = CKGProcessStatus.NOTEXIST;
          this._ckgProcess = spawn(ckgBinaryPath, args, {
            cwd: getCWD(ckgBinaryPath),
            env: ppe ? { ...process.env, CKG_PPE: ppe } : { ...process.env },
          });
          this._ckgProcess.stdout.on("data", (data: Buffer) => {
            this._status = CKGProcessStatus.RUNNING; // CKG 输出日志说明启动成功
            Logger.info(data.toString());
            // 处理golang标准输出流
          });
          this._ckgProcess.stderr.on("data", (data: Buffer) => {
            Logger.info(data.toString());
            // 处理golang错误流
          });
          this._ckgProcess.on(
            "exit",
            (code: number | null, signal: string | null) => {
              Logger.info(
                `CKG process exit, code: ${code}, signal: ${signal} `,
              );
              if (signal == 'SIGKILL'){
                this._status = CKGProcessStatus.KILLED;
              } else {
                this._status = CKGProcessStatus.TERMINATED;
              }
            },
          );
          this._ckgProcess.on("error", err => {
            this._status = CKGProcessStatus.TERMINATED;
            Logger.info(`CKG process error, error: ${err}`);
          });
          resolve(true);
        }
      } catch (err) {
        this._status = CKGProcessStatus.TERMINATED;
        Logger.error(`start ckg process failed: ${err} `);
        resolve(false);
      }
    });
  }

  // 设置状态，只用于 debug
  setStatus(status: CKGProcessStatus): void {
    this._status = status;
  }

  // 返回当前状态
  getStatus(): CKGProcessStatus {
    return this._status;
  }

  async kill(): Promise<void> {
    this._ckgProcess.kill();
  }
}
