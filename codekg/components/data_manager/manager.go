package data_manager

import (
	"context"
	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/components/ignore_service"
	"ide/ckg/codekg/components/ignore_service/ignore_rule_checker"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/relation_manager"
	"ide/ckg/codekg/components/tasks"
	"ide/ckg/codekg/components/version_feature"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/protocol"
	"sync"
)

type DataManager interface {
	// index.go & remote.go
	InitProject(ctx context.Context, cli knowledgebase.Client, option *InitProjectOption) error
	InitVirtualProject(ctx context.Context, cli knowledgebase.Client, projectID string, uri model.URI, userID string, loadFilesFromFs bool, relativeGlobsToLoad []string) error
	DeleteIndex(ctx context.Context, project string) error
	CancelIndex(ctx context.Context, project string) error

	// status.go
	GetProjectsBuildStatus(ctx context.Context, req *protocol.GetBuildStatusRequest) map[model.URI]*protocol.ProjectBuildStatus
	GetDocumentsIndexStatus(ctx context.Context, req *protocol.GetDocumentsIndexStatusRequest) map[model.URI]*protocol.ProjectDocumentsIndexStatus

	// file.go
	FileCreated(ctx context.Context, cli knowledgebase.Client, filePaths []string, userID string, documents []model.Document) error
	FileChanged(ctx context.Context, cli knowledgebase.Client, filePaths []string, userID string) error
	FileDeleted(ctx context.Context, cli knowledgebase.Client, filePaths []string, userID string, documents []model.Document) error

	// relation.go
	CursorMove(ctx context.Context, project, filePath string, userID string, line int32, version string) error
	InitRelationManager(ctx context.Context, cli knowledgebase.Client, project string, userID string) error
	GetRelationManager(ctx context.Context, project string, userID string, version string) relation_manager.RelationManager

	// storage.go
	GetProjectEmbeddingType(ctx context.Context, project model.URI) EmbeddingStorageType
	GetProjectType(ctx context.Context, projectID string) model.ProjectType
	GetEntityStorage(ctx context.Context, project model.URI) (data_storage.Storage, error)
	GetLocalEmbeddingStorage(ctx context.Context, project model.URI) (data_storage.EmbeddingStorage, error)
}

type manager struct {
	indexFileTaskQueue      *tasks.TaskQueue
	indexFolderTaskQueue    *tasks.TaskQueue
	deleteTaskQueue         *tasks.TaskQueue
	changePreTaskQueue      *tasks.TaskQueue
	localIndexFileTaskQueue *tasks.TaskQueue

	// for batch local embedding, 每个 user id
	batchEmbeddingMgr                   *tasks.EmbeddingQueueMgr
	batchLocalIndexSplitTaskQueue       *tasks.TaskQueue
	batchLocalIndexEmbeddingTaskQueue   *tasks.TaskQueue
	batchIndexVirtualSplitTaskQueue     *tasks.TaskQueue
	batchIndexVirtualEmbeddingTaskQueue *tasks.TaskQueue

	ignoreService   ignore_service.IgnoreService
	ignoreServiceV2 ignore_rule_checker.IgnoreServiceV2

	storageMu *sync.Mutex
	storages  map[model.URI]data_storage.Storage

	relationMu *sync.Mutex
	relations  map[model.URI]relation_manager.RelationManager

	// embedding 向量存储
	embeddingStorageMu *sync.Mutex
	embeddingStorages  map[model.URI]data_storage.EmbeddingStorage

	projects              *sync.Map
	projectTypes          *sync.Map
	projectUris           *sync.Map
	projectInitProgress   *sync.Map
	bgProjectInitProgress *sync.Map
	projectCancelFlag     *sync.Map

	config *ckg_config.Config
	fs     file_system.FileSystem
	fm     *version_feature.FeatureManager
}

func NewDataManager(indexFileTaskQueue, indexFolderTaskQueue, deleteTaskQueue, changePreTaskQueue, localIndexFileTaskQueue,
	batchLocalIndexSplitTaskQueue, batchLocalIndexEmbeddingTaskQueue, batchIndexVirtualSplitTaskQueue, batchIndexVirtualEmbeddingTaskQueue *tasks.TaskQueue, batchEmbeddingMgr *tasks.EmbeddingQueueMgr,
	ignoreService ignore_service.IgnoreService, ignoreServiceV2 ignore_rule_checker.IgnoreServiceV2,
	config *ckg_config.Config, fs file_system.FileSystem, fm *version_feature.FeatureManager) DataManager {
	return &manager{
		indexFileTaskQueue:      indexFileTaskQueue,
		indexFolderTaskQueue:    indexFolderTaskQueue,
		deleteTaskQueue:         deleteTaskQueue,
		changePreTaskQueue:      changePreTaskQueue,
		localIndexFileTaskQueue: localIndexFileTaskQueue,

		batchEmbeddingMgr:                   batchEmbeddingMgr,
		batchLocalIndexSplitTaskQueue:       batchLocalIndexSplitTaskQueue,
		batchLocalIndexEmbeddingTaskQueue:   batchLocalIndexEmbeddingTaskQueue,
		batchIndexVirtualSplitTaskQueue:     batchIndexVirtualSplitTaskQueue,
		batchIndexVirtualEmbeddingTaskQueue: batchIndexVirtualEmbeddingTaskQueue,

		ignoreService:   ignoreService,
		ignoreServiceV2: ignoreServiceV2,

		storageMu: new(sync.Mutex),
		storages:  make(map[model.URI]data_storage.Storage),

		relationMu: new(sync.Mutex),
		relations:  make(map[model.URI]relation_manager.RelationManager),

		embeddingStorageMu: new(sync.Mutex),
		embeddingStorages:  make(map[model.URI]data_storage.EmbeddingStorage),

		projects:              new(sync.Map),
		projectTypes:          new(sync.Map),
		projectUris:           new(sync.Map),
		projectInitProgress:   new(sync.Map),
		bgProjectInitProgress: new(sync.Map),
		projectCancelFlag:     new(sync.Map),

		config: config,
		fs:     fs,
		fm:     fm,
	}
}
