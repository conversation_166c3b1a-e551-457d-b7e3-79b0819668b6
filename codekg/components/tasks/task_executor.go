package tasks

import (
	"golang.org/x/time/rate"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/util"
	"time"
)

type TaskExecutor struct {
	queue     *TaskQueue
	numWorker int
	tasks     chan Task
	sleepTime time.Duration
	limiter   *rate.Limiter
}

func NewTaskExecutor(queue *TaskQueue, numWorker int, limiter *rate.Limiter) *TaskExecutor {
	return &TaskExecutor{
		queue:     queue,
		numWorker: numWorker,
		tasks:     make(chan Task),
		sleepTime: time.Millisecond * 100,
		limiter:   limiter,
	}
}

func (e *TaskExecutor) Work() {
	taskExecutorCtx := util.NewBackgroundContext("")
	util.SafeGo(taskExecutorCtx, func() {
		for i := 0; i < e.numWorker; i++ {
			ctx := util.NewBackgroundContext("")
			util.SafeGo(ctx, func() {
				for task := range e.tasks {
					if e.limiter != nil {
						err := e.limiter.Wait(ctx)
						if err != nil {
							logs.Error("error during rate limit, err: %v", err)
						}
					}
					task.do()
				}
			})
		}

		for {
			if e.queue.Len() > 0 {
				e.tasks <- e.queue.Pop()
			} else {
				time.Sleep(e.sleepTime)
			}
		}
	})
}
