import {beforeAll, describe, expect, it, vi} from "vitest";
import * as utilsPath from "../../../utils/path";
import {<PERSON>IParser} from "./parser";
import type {DocumentUri, Location, Position} from "vscode-languageserver-types";
import {doSetConfigById, FeatureGateManager, FeatureName, getConfig} from "../../common/config";
import {wait} from "../../../utils/common";
import {setEnableLocalLogging} from "../../../utils/logger";
import {isInTimeoutInterval} from "../../../utils/control";
import {MainHandler} from "../../../worker/main";

describe("test_execute", () => {
  beforeAll(async () => {
    FeatureGateManager.INSTANCE.openFeature(FeatureName.ENABLE_PSI_LSP_REQUEST)
    FeatureGateManager.INSTANCE.openFeature(FeatureName.ENABLE_PSI_LSP_TIMEOUT_INTERVAL)
    setEnableLocalLogging(true)
  });

  it("case1_timeout_interval", async () => {
    MainHandler.INSTANCE.init_("./dist/resources", "/test", false, false, false);

    const originPsiTimeoutMs = getConfig("parse.psi.timeoutMs");
    const originPsiTimeoutInterval = getConfig("parse.common.timeoutInterval");
    doSetConfigById("parse.psi.timeoutMs", 5);
    doSetConfigById("parse.common.timeoutInterval", 100);

    const workspacePathMock = vi.spyOn(utilsPath, "getWorkspaceFolderPath").mockImplementation(() => {
      return "file:///";
    });

    const psiProxy = {
      execute: async (command: string, _uri: DocumentUri, _position: Position, _args: any[]): Promise<Location[]> => {
        if (command === "cmd1") {
          return [{
            uri: "file:///cmd1.go",
            range: {start: {line: 0, character: 0}, end: {line: 1, character: 1}},
          }];
        }
        if (command === "cmd2") {
          await wait(10);
          return [];
        }

        return [];
      },
    }

    PSIParser.INSTANCE.init(psiProxy);

    await PSIParser.INSTANCE.execute("cmd1", "", "file:///", {line: 0, character: 0}, []);
    expect(isInTimeoutInterval()).toEqual(false);

    await PSIParser.INSTANCE.execute("cmd2", "", "file:///", {line: 0, character: 0}, []);
    expect(isInTimeoutInterval()).toEqual(true);

    await PSIParser.INSTANCE.execute("cmd1", "", "file:///", {line: 0, character: 0}, []);
    expect(isInTimeoutInterval()).toEqual(true);

    await wait(110);

    await PSIParser.INSTANCE.execute("cmd1", "", "file:///", {line: 0, character: 0}, []);
    expect(isInTimeoutInterval()).toEqual(false);

    await PSIParser.INSTANCE.execute("cmd2", "", "file:///", {line: 0, character: 0}, []);
    expect(isInTimeoutInterval()).toEqual(true);

    await PSIParser.INSTANCE.execute("cmd1", "", "file:///", {line: 0, character: 0}, []);
    expect(isInTimeoutInterval()).toEqual(true);

    workspacePathMock.mockRestore();

    doSetConfigById("parse.psi.timeoutMs", originPsiTimeoutMs);
    doSetConfigById("parse.common.timeoutInterval", originPsiTimeoutInterval);
  });
});