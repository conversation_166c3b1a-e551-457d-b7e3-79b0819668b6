import {TextDocument} from 'vscode-languageserver-textdocument';
import {Logger} from "./logger";
import * as fs from "node:fs";
import {TimeoutError} from "./error";
import {DocumentUri, Position, Range} from "vscode-languageserver-types";
import {Point} from "web-tree-sitter";

export const FILE_URI_PREFIX = "file://";

export interface FileEditInfo {
  document: TextDocument
  contentChanges: TextDocumentContentChange[]
}

export interface TextDocumentContentChange {
  position: Position;
  removeLength: number;
  text: string;
}

export interface EditorSelectionInfo {
  document: TextDocument
  selectionRange: Range
}

export function changeInfoToString(changes: TextDocumentContentChange[]): string {
  const resultList: string[] = [];
  for (const change of changes) {
    resultList.push(`(${change.position.line},${change.position.character},${change.removeLength},${change.text.length})`);
  }

  return `${resultList.join(",")}`;
}

export function getEditLines(editInfo: FileEditInfo): number[] {
  const lines: number[] = [];

  if (editInfo.contentChanges.length !== 1) {
    // TODO: support multi-change
    return [];
  }

  const change = editInfo.contentChanges[0];
  const startLine = change.position.line;
  const endLine = editInfo.document.positionAt(editInfo.document.offsetAt(change.position) + change.text.length).line;
  for (let line = startLine; line <= endLine; line++) {
    lines.push(line);
  }

  return Array.from(new Set(lines)).sort((a, b) => a - b);
}

export function createDocument(
  text: string,
  languageId: string,
  uriString: string,
): TextDocument {
  return TextDocument.create(uriString, languageId, 0, text);
}

export function reflectCall(handler: any, methodName: string, ...args: any[]): any {
  const method = Reflect.get(handler, methodName);
  if (typeof method === "function") {
    return Reflect.apply(method, handler, args);
  } else {
    Logger.error(`reflectCall: invalid method ${methodName}`);
    return undefined;
  }
}

export async function asyncReflectCall(handler: any, methodName: string, ...args: any[]): Promise<any> {
  const method = Reflect.get(handler, methodName);
  if (typeof method === "function") {
    return Reflect.apply(method, handler, args);
  } else {
    Logger.error(`asyncReflectCall: invalid method ${methodName}`);
    return undefined;
  }
}

export function readFile(uri: DocumentUri): string {
  if (uri.startsWith(FILE_URI_PREFIX)) {
    return fs.readFileSync(decodeURIComponent(uri.slice(FILE_URI_PREFIX.length)), "utf8");
  } else {
    return "";
  }
}

export async function wait(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export async function withTimeout<T>(promise: Promise<T>, timeout_ms: number, useFallbackValue: boolean, fallbackValue?: T): Promise<T> {
  const timeout = new Promise<T>((resolve, reject) => {
    setTimeout(() => {
      if (useFallbackValue) {
        resolve(fallbackValue);
      } else {
        reject(new TimeoutError(`Operation execution timeout (${timeout_ms}ms)`));
      }
    }, timeout_ms);
  });
  return Promise.race([promise, timeout]);
}

export function convertToPlainObject(obj: any): any {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => convertToPlainObject(item));
  }

  if (obj instanceof Set) {
    return Array.from(obj).map(item => convertToPlainObject(item));
  }


  if (obj instanceof Map) {
    const plainObject: any = {};
    obj.forEach((value, key) => {
      plainObject[convertToPlainObject(key)] = convertToPlainObject(value);
    });
    return plainObject;
  }
  const plainObject: any = {};
  for (const key of Object.keys(obj)) {
    plainObject[key] = convertToPlainObject((obj as any)[key]);
  }

  return plainObject;
}

export function positionToPoint(position: Position): Point {
  if (position) {
    return {
      row: position.line,
      column: position.character,
    };
  }

  return null;
}

export function pointToPosition(point: Point): Position {
  if (point) {
    return {
      line: point.row,
      character: point.column,
    };
  }

  return null;
}