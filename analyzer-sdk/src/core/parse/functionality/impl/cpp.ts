import {CommonFunctionalityExecutor} from "./common";
import {DefNodeInfo, DefNodeKindEnum, LanguageIdEnum, ParserEnum, VarDeclNodeInfo} from "../../share";
import {DocumentUri, Location} from "vscode-languageserver-types";
import {SyntaxNode} from "web-tree-sitter";
import {TreeSitterDispatcher} from "../../treesitter/dispatcher";
import {TreeSitterFunctionalityEnum} from "../../treesitter/share";

export class CppFunctionalityExecutor extends CommonFunctionalityExecutor {
  static INSTANCE = new CppFunctionalityExecutor();

  languageId = LanguageIdEnum.Cpp;

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
    super();
  }

  async getInvokeDefinition(parses: ParserEnum[], uri: DocumentUri, invokeNameNode: SyntaxNode, resultFilter: (location: Location) => boolean): Promise<DefNodeInfo[]> {
    const result: DefNodeInfo[] = [];

    for (const node of await this.getDefinitionNode(parses, uri, invokeNameNode, resultFilter)) {
      const realDefNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(this.languageId, TreeSitterFunctionalityEnum.GetFuncParentNodeForNode, node.node);

      if (realDefNode) {
        result.push({
          languageId: this.languageId,
          uri: node.uri,
          kind: DefNodeKindEnum.Function,
          name: invokeNameNode.text,
          defNode: realDefNode,
          subDefNodeInfo: null,
        });
      }
    }

    return result;
  }

  async getTypeDefinition(parses: ParserEnum[], uri: DocumentUri, typeNameNode: SyntaxNode, preFilter: (node: SyntaxNode) => boolean, resultFilter: (location: Location) => boolean): Promise<DefNodeInfo[]> {
    if (preFilter && !preFilter(typeNameNode)) {
      return [];
    }

    const realDefNodes = (await this.getDefinitionNode(parses, uri, typeNameNode, resultFilter)).map(node => {
      return TreeSitterDispatcher.INSTANCE.dispatch(this.languageId, TreeSitterFunctionalityEnum.GetTypeParentNodeForNode, node.node);
    });
    return realDefNodes.map(node => {
      return TreeSitterDispatcher.INSTANCE.dispatch(this.languageId, TreeSitterFunctionalityEnum.GenTypeInfoFromDefNode, node.uri, node.node, typeNameNode.text);
    });
  }

  async getVarDeclaration(parses: ParserEnum[], uri: DocumentUri, varNameNode: SyntaxNode, resultFilter: (location: Location) => boolean): Promise<VarDeclNodeInfo[]> {
    const nodes = await this.getDefinitionNode(parses, uri, varNameNode, resultFilter);

    return nodes.map(node => {
      const realDeclNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(this.languageId, TreeSitterFunctionalityEnum.GetVarParentNodeForNode, node.node);

      if (realDeclNode) {
        return {
          languageId: this.languageId,
          uri: node.uri,
          kind: DefNodeKindEnum.VarDecl,
          name: varNameNode.text,
          declNode: realDeclNode,
        };
      } else {
        return null;
      }
    }).filter(item => item !== null);
  }
}