{"name": "@byted-ckg/client", "version": "0.0.82", "main": "./lib/index.js", "types": "./lib/index.d.ts", "license": "ISC", "files": ["lib"], "scripts": {"build": "tsc && tsc-alias && cp -rf src/protocol lib/protocol", "build:ts": "tsc && tsc-alias", "build:server": "go build -o lib/binary/ckg_server ../../codekg/cmd/server", "build:codekg_linux_arm64": "GOOS=linux GOARCH=arm64 CGO_ENABLED=1 CC=aarch64-linux-gnu-gcc CXX=aarch64-linux-gnu-g++ go build -o output/ckg_server_linux_arm64 ../../codekg/cmd/server && md5sum output/ckg_server_linux_arm64 > output/ckg_server_linux_arm64.md5", "build:codekg_linux_x64": "go build -o output/ckg_server_linux_x64 ../../codekg/cmd/server && md5sum output/ckg_server_linux_x64 > output/ckg_server_linux_x64.md5", "build:codekg_windows_x64": "GOOS=windows GOARCH=amd64 CGO_ENABLED=1 CC=x86_64-w64-mingw32-gcc CXX=x86_64-w64-mingw32-g++ go build -o output/ckg_server_windows_x64.exe ../../codekg/cmd/server && md5sum output/ckg_server_windows_x64.exe > output/ckg_server_windows_x64.exe.md5", "build:codekg_darwin_arm64": "GOOS=darwin GOARCH=arm64 CC=oa64-clang CXX=oa64-clang++ CGO_ENABLED=1 go build -buildvcs=false -o output/ckg_server_darwin_arm64 ../../codekg/cmd/server && md5sum output/ckg_server_darwin_arm64 > output/ckg_server_darwin_arm64.md5", "build:codekg_darwin_x64": "GOOS=darwin GOARCH=amd64 CC=o64-clang CXX=o64-clang++ CGO_ENABLED=1 go build -buildvcs=false -o output/ckg_server_darwin_x64 ../../codekg/cmd/server && md5sum output/ckg_server_darwin_x64 > output/ckg_server_darwin_x64.md5", "gen": "rm -rf ./src/protocol/* && npm run gen:proto && npm run gen:ts", "gen:proto": "grpc_tools_node_protoc --js_out=import_style=commonjs,binary:./src/protocol --grpc_out=grpc_js:./src/protocol --proto_path=../.. --plugin=grpc_tools_node_protoc_plugin ../../*.proto", "gen:ts": "grpc_tools_node_protoc --ts_out=service=grpc-node,mode=grpc-js:./src/protocol --plugin=protoc-gen-ts=./node_modules/.bin/protoc-gen-ts --proto_path=../.. ../../*.proto", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lint": "eslint . --ext .ts"}, "dependencies": {"@grpc/grpc-js": "1.9.2", "axios": "^1.7.4", "crypto-js": "4.2.0", "execa": "^5.1.1", "get-port": "7.1.0", "google-protobuf": "3.18.0-rc.2", "proper-lockfile": "^4.1.2", "tslib": "^2.6.2", "vscode-languageserver-types": "3.17.5"}, "devDependencies": {"@artus/tsconfig": "~1.0.1", "@babel/preset-typescript": "^7.24.7", "@rollup/plugin-commonjs": "^26.0.1", "@types/google-protobuf": "^3.7.2", "@types/jest": "^29.5.12", "@types/node": "^20.14.15", "chai": "^5.1.1", "grpc-tools": "^1.12.4", "jest": "^29.7.0", "mocha": "^10.7.3", "pidusage": "^3.0.2", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "ts-protoc-gen": "0.14.0", "tsc-alias": "^1.8.8", "typescript": "^5.6.2"}}