package query_service

import (
	"context"
	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/env"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/protocol"
	"ide/ckg/codekg/util"
	"strings"
	"sync"

	"github.com/pkg/errors"
	"ide/ckg/codekg/components/logs"
)

type RelevantSnippets struct {
	WorkspaceSnippets      []*protocol.Snippet
	SelectedFolderSnippets []*protocol.Snippet
	SelectedFileSnippets   []*protocol.Snippet
	SelectedCodeSnippets   []*protocol.Snippet
	CurrentEditorSnippets  []*protocol.Snippet

	UserInteractionSnippets []*protocol.Snippet
}

func (s *service) RetrieveRelevantSnippet(ctx context.Context, cli knowledgebase.Client, request *protocol.RetrieveRelevantSnippetRequest) (*RelevantSnippets, error) {
	recallEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGRelevantRecall, request.UserId, "", string(env.GetSourceProduct()))
	defer recallEvent.Report(ctx, false)
	ctx = ckg_metrics.SetEvent(ctx, recallEvent)
	recallEvent.AddTeaParam(ckg_metrics.TeaParamRelevantRecallEvent, "retrieve_relevant_snippet")

	entityStorage, err := s.dm.GetEntityStorage(ctx, request.ProjectId)
	if err != nil {
		logs.CtxError(ctx, "error get entity storage, err: %v", err)
	}

	processedQuery, err := s.preprocessUserQuery(ctx, cli, request.UserId, request.UserQuery, s.config.GetEmbeddingModelBaseline(ctx, request.UserId))
	if err != nil {
		logs.CtxError(ctx, "error preprocessUserQuery, err: %v", err)
		recallEvent.AddTeaParam(ckg_metrics.TeaParamEmptyRelevantSnippets, true)
		recallEvent.AddTeaParam(ckg_metrics.TeaParamRelevantSnippetsQueryProcessFailed, true)
		return &RelevantSnippets{}, nil
	}

	snippets, err := s.relevantSnippetFromUserSelectedContexts(ctx, cli, processedQuery, entityStorage, request)
	if err != nil || snippets == nil {
		logs.CtxError(ctx, "error recallSnippetFromUserSelectedContexts, err: %v", err)
		recallEvent.AddTeaParam(ckg_metrics.TeaParamEmptyRelevantSnippets, true)
		recallEvent.AddTeaParam(ckg_metrics.TeaParamRelevantSnippetsRetrieveFailed, true)
		return &RelevantSnippets{}, nil
	}

	logs.CtxInfo(ctx, "retrieve user interaction snippets, count: %d", len(snippets.UserInteractionSnippets))

	recallEvent.AddTeaParam(ckg_metrics.TeaParamRelevantSnippetsWorkspaceSnippetsNum, len(snippets.WorkspaceSnippets))
	recallEvent.AddTeaParam(ckg_metrics.TeaParamRelevantSnippetsFolderSnippetsNum, len(snippets.SelectedFolderSnippets))
	recallEvent.AddTeaParam(ckg_metrics.TeaParamRelevantSnippetsFileSnippetsNum, len(snippets.SelectedFileSnippets))
	recallEvent.AddTeaParam(ckg_metrics.TeaParamRelevantSnippetsCodeSnippetsNum, len(snippets.SelectedCodeSnippets))
	recallEvent.AddTeaParam(ckg_metrics.TeaParamRelevantSnippetsCurrentEditorSnippetsNum, len(snippets.CurrentEditorSnippets))
	recallEvent.AddTeaParam(ckg_metrics.TeaParamRelevantSnippetsUserInteractionSnippetsNum, len(snippets.UserInteractionSnippets))

	return snippets, nil
}

func (s *service) preprocessUserQuery(ctx context.Context, cli knowledgebase.Client, userID, query, datastoreName string) (*model.ProcessedQuery, error) {
	var embedding *model.Embedding
	if len(query) != 0 {
		var embeddingResp *knowledgebase.EmbeddingResponse
		var err error
		if s.config.IsIndexFeatureEnabled(ctx, userID, ckg_config.UseV2SplitEmbeddingAPI) {
			embeddingResp, err = cli.EmbeddingV2(ctx, env.GetToken(userID), ckg_metrics.EventNameCKGRelevantRecall, &knowledgebase.EmbeddingRequestV2{
				EmbeddingModel:    datastoreName,
				EmbeddingContents: []string{query},
				OfflineCluster:    false,
			})
		} else {
			embeddingResp, err = cli.Embedding(ctx, env.GetToken(userID), ckg_metrics.EventNameCKGRelevantRecall, &knowledgebase.EmbeddingRequest{
				DatastoreName:     datastoreName,
				EmbeddingContents: []string{query},
				OfflineCluster:    false,
			})
		}
		if err != nil {
			return nil, errors.WithMessage(err, "error preprocessUserQuery")
		}
		embedding = knowledgebase.ConvertKBEmbeddingToEmbedding(embeddingResp.Embeddings[0])
	}

	return &model.ProcessedQuery{
		OriginalQuery: query,
		Embedding:     embedding,
		Keywords:      strings.Split(query, " "),
	}, nil
}

func (s *service) relevantSnippetFromUserSelectedContexts(ctx context.Context, cli knowledgebase.Client,
	processedQuery *model.ProcessedQuery, storage data_storage.Storage, request *protocol.RetrieveRelevantSnippetRequest) (*RelevantSnippets, error) {
	relevantSnippetNum := s.config.GetTCCConfig(ctx, cli, request.UserId).RecentCursorRelationNum

	// 召回用户指定 Context 的关键代码片段
	var (
		workspaceSnippets       = make([]*protocol.Snippet, 0)
		selectedFolderSnippets  = make([]*protocol.Snippet, 0)
		selectedFileSnippets    = make([]*protocol.Snippet, 0)
		selectedCodeSnippets    = make([]*protocol.Snippet, 0)
		currentEditorSnippets   = make([]*protocol.Snippet, 0)
		userInteractionSnippets = make([]*protocol.Snippet, 0)
	)

	// 获取 relationManager
	relationManager := s.dm.GetRelationManager(ctx, request.ProjectId, request.UserId, request.Version)

	tccConfig := s.config.GetTCCConfig(ctx, cli, request.UserId)

	errorCh := make(chan error, 5)
	wg := sync.WaitGroup{}
	wg.Add(6)
	util.SafeGo(ctx, func() {
		defer wg.Done()
		var err error
		if request.Workspace {
			workspaceSnippets, err = s.relevantSnippetsFromSelectedFolders(ctx, []string{request.ProjectId})
			if err != nil {
				errorCh <- err
				logs.CtxError(ctx, "error relevantSnippetFromWorkspace, err: %v", err)
				return
			}
		}
	})
	util.SafeGo(ctx, func() {
		defer wg.Done()
		var err error
		if len(request.SelectedFolders) > 0 {
			selectedFolderSnippets, err = s.relevantSnippetsFromSelectedFolders(ctx, request.SelectedFolders)
			if err != nil {
				errorCh <- err
				logs.CtxError(ctx, "error relevantSnippetsFromSelectedFolders, err: %v", err)
				return
			}
		}
	})
	util.SafeGo(ctx, func() {
		defer wg.Done()
		var err error
		if len(request.SelectedFiles) > 0 {
			selectedFileSnippets, err = s.relevantSnippetsFromSelectedFiles(ctx, request.SelectedFiles)
			if err != nil {
				errorCh <- err
				logs.CtxError(ctx, "error relevantSnippetsFromSelectedFiles, err: %v", err)
				return
			}
		}
	})
	util.SafeGo(ctx, func() {
		defer wg.Done()
		var err error
		if len(request.SelectedCodes) > 0 {
			selectedCodeSnippets, err = s.relevantSnippetsFromSelectedCodes(ctx, request.SelectedCodes)
			if err != nil {
				errorCh <- err
				logs.CtxError(ctx, "error relevantSnippetsFromSelectedCodes, err: %v", err)
				return
			}
		}
	})
	util.SafeGo(ctx, func() {
		defer wg.Done()
		var err error
		if request.CurrentEditor != nil {
			currentEditorSnippets, err = s.relevantSnippetFromCurrentEditor(ctx, cli, request)
			if err != nil {
				errorCh <- err
				logs.CtxError(ctx, "error relevantSnippetFromCurrentEditor, err: %v", err)
				return
			}
		}
	})
	util.SafeGo(ctx, func() {
		defer wg.Done()
		var err error
		if relationManager == nil {
			logs.CtxInfo(ctx, "relevantSnippetFromUserSelectedContexts skip userInteraction, relationManager is nil")
			return
		}
		userInteractionSnippets, err = relationManager.RetrieveRelevantContextFromInteraction(ctx, cli, processedQuery, request, tccConfig, relevantSnippetNum)
		if err != nil {
			errorCh <- err
			logs.CtxError(ctx, "error relevantSnippetFromUserInteraction, err: %v", err)
			return
		}
	})
	wg.Wait()
	close(errorCh)

	if err, ok := <-errorCh; ok {
		return nil, errors.WithMessagef(err, "error recallSnippetFromUserSelectedContexts")
	}

	return &RelevantSnippets{
		WorkspaceSnippets:       workspaceSnippets,
		SelectedFolderSnippets:  selectedFolderSnippets,
		SelectedFileSnippets:    selectedFileSnippets,
		SelectedCodeSnippets:    selectedCodeSnippets,
		CurrentEditorSnippets:   currentEditorSnippets,
		UserInteractionSnippets: userInteractionSnippets,
	}, nil
}

func (s *service) relevantSnippetsFromSelectedFolders(ctx context.Context, folder []string) ([]*protocol.Snippet, error) {
	// 用户选中 #Folder 时，识别对应文件夹中的关键代码片段
	// TODO: to be implemented
	return []*protocol.Snippet{}, nil
}

func (s *service) relevantSnippetsFromSelectedFiles(ctx context.Context, files []string) ([]*protocol.Snippet, error) {
	// 用户选中 #File 时，识别当前文件中关键代码片段
	// TODO: to be implemented
	return []*protocol.Snippet{}, nil
}

func (s *service) relevantSnippetsFromSelectedCodes(ctx context.Context, codes []*protocol.CodeSnippet) ([]*protocol.Snippet, error) {
	// 用户选中 #Code 时，识别当前代码片段中关键代码片段
	// TODO: to be implemented
	return []*protocol.Snippet{}, nil
}

func (s *service) relevantSnippetFromCurrentEditor(ctx context.Context, cli knowledgebase.Client, request *protocol.RetrieveRelevantSnippetRequest) ([]*protocol.Snippet, error) {
	return []*protocol.Snippet{}, nil
}
