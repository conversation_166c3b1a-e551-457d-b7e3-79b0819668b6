package ignore_rule_checker

import (
	"context"
	"github.com/samber/lo"
	"os"
	"path/filepath"
	"strings"
	"time"
)

const (
	// ckg 内置的 ignore 黑名单
	ExcludeFileRule    = "ExcludeFileRuleChecker"
	ExcludeFolderRule  = "ExcludeFolderRuleChecker"
	ExcludeExtFileRule = "ExcludeExtFileRuleChecker"

	// ckg 内置的 ignore 黑名单
	ExcludeFolderRuleRank  = 10
	ExcludeFileRuleRank    = 20
	ExcludeExtFileRuleRank = 30
)

var (
	DefaultExcludeFolderRules = map[IgnoreRule]struct{}{
		"node_modules": {},
		"__pycache__":  {},
		"venv":         {},
		"vendor":       {},
		"tmp":          {},
		"temp":         {},
		"Pods":         {},
		"bundle":       {},
		"obj":          {},
		"target":       {},
		"output":       {},
		"dist":         {},
		"eggs":         {},
		"gradle":       {},
		"thrift_gen":   {},
		"kitex_gen":    {}, // for bytedance
		"hertz_gen":    {}, // for bytedance
	}
	DefaultExcludeFileRules = map[IgnoreRule]struct{}{
		"go.sum":            {},
		"yarn.lock":         {},
		"package-lock.json": {},
		"pnpm-lock.yaml":    {},
		"cargo.lock":        {},
	}
	DefaultExcludeExtFileRules = map[IgnoreRule]struct{}{
		".class": {},
		".jar":   {},
		".dex":   {},
		".o":     {},
		".obj":   {},
		".a":     {},
		".out":   {},
		".pyc":   {},
		".pyo":   {},
		".pyd":   {},
		".egg":   {},
		".whl":   {},
		".log":   {},
		".lock":  {},
		".bak":   {},
		// copied from imageAndMediaExtensions
		".jpg":  {},
		".jpeg": {},
		".jpe":  {},
		".png":  {},
		".gif":  {},
		".bmp":  {},
		".tif":  {},
		".tiff": {},
		".tga":  {},
		".ico":  {},
		".webp": {},
		".svg":  {},
		".eps":  {},
		".heif": {},
		".heic": {},
		".pdf":  {},
		".raw":  {},
		".mp4":  {},
		".m4v":  {},
		".mkv":  {},
		".webm": {},
		".mov":  {},
		".avi":  {},
		".wmv":  {},
		".flv":  {},
		".mp3":  {},
		".wav":  {},
		".m4a":  {},
		".flac": {},
		".ogg":  {},
		".wma":  {},
		".weba": {},
		".aac":  {},
		".7z":   {},
		".bz2":  {},
		".gz":   {},
		".rar":  {},
		".tar":  {},
		".xz":   {},
		".zip":  {},
		".vsix": {},
		".db":   {},
		".bin":  {},
		".dat":  {},
		".hex":  {},
		".map":  {},
		".wasm": {},
		".pdb":  {},
		".sym":  {},
		".git":  {},
		// copied from binaryExtensions
		".exe":   {},
		".dll":   {},
		".so":    {},
		".dylib": {},
	}
)

// ExcludeFolderRuleChecker 负责通过 “准确匹配” 过滤文件夹
type ExcludeFolderRuleChecker struct {
	excludeFolders map[IgnoreRule]struct{}
	is             IgnoreServiceV2
	expire         time.Time
}

func NewExcludeFolderRuleChecker() *ExcludeFolderRuleChecker {
	return &ExcludeFolderRuleChecker{
		excludeFolders: DefaultExcludeFolderRules,
		expire:         time.Now(),
	}
}

func (e *ExcludeFolderRuleChecker) Name() CheckerName                   { return ExcludeFolderRule }
func (e *ExcludeFolderRuleChecker) Rank() int                           { return ExcludeFolderRuleRank }
func (e *ExcludeFolderRuleChecker) Kind() CheckerKind                   { return Folder }
func (e *ExcludeFolderRuleChecker) SetIgnoreService(is IgnoreServiceV2) { e.is = is }
func (e *ExcludeFolderRuleChecker) AddRules(_ context.Context, _, _ string, rules []IgnoreRule) error {
	for _, rule := range rules {
		e.excludeFolders[rule] = struct{}{}
	}
	return nil
}
func (e *ExcludeFolderRuleChecker) UpdateRulesFromTCC(ctx context.Context, userId, base string) error {
	if time.Now().Before(e.expire) {
		return nil
	} else {
		e.expire = time.Now().Add(5 * time.Minute)
	}
	config, cli := e.is.GetConfig(), e.is.GetClient()
	if config == nil || cli == nil {
		return nil
	}
	tcc := config.GetTCCConfig(ctx, cli, userId)
	rules := strings.Split(tcc.ExcludeFolderRule, "\n")
	rules = lo.Filter(rules, func(rule string, _ int) bool { return len(rule) > 0 })
	if len(rules) == 0 {
		return nil // 暂不清空更新规则
	}
	// 不能完全清除掉现有的过滤规则，因为用户可能通过 AddRules 添加规则
	for _, rule := range rules {
		e.excludeFolders[IgnoreRule(rule)] = struct{}{}
	}
	return nil
}

func (e *ExcludeFolderRuleChecker) IsIgnore(_ context.Context, _, _ string, path string, _ os.FileInfo) (ignored bool, skip error, err error) {
	segments := strings.Split(path, string(os.PathSeparator))
	if len(segments) >= 1 {
		segments = lo.Slice(segments, 0, len(segments)-1)
	}
	for _, segment := range segments {
		if strings.HasPrefix(segment, ".") {
			return true, filepath.SkipDir, nil
		}
		if _, ok := e.excludeFolders[IgnoreRule(segment)]; ok {
			return true, filepath.SkipDir, nil
		}
	}
	return false, nil, nil
}

// ExcludeFileRuleChecker 负责通过 “准确匹配” 过滤文件
type ExcludeFileRuleChecker struct {
	excludeFiles map[IgnoreRule]struct{}
	is           IgnoreServiceV2
	expire       time.Time
}

func NewExcludeFileRuleChecker() *ExcludeFileRuleChecker {
	return &ExcludeFileRuleChecker{
		excludeFiles: DefaultExcludeFileRules,
		expire:       time.Now(),
	}
}

func (e *ExcludeFileRuleChecker) Name() CheckerName                   { return ExcludeFileRule }
func (e *ExcludeFileRuleChecker) Rank() int                           { return ExcludeFileRuleRank }
func (e *ExcludeFileRuleChecker) Kind() CheckerKind                   { return File }
func (e *ExcludeFileRuleChecker) SetIgnoreService(is IgnoreServiceV2) { e.is = is }
func (e *ExcludeFileRuleChecker) AddRules(_ context.Context, _, _ string, rules []IgnoreRule) error {
	for _, rule := range rules {
		e.excludeFiles[rule] = struct{}{}
	}
	return nil
}
func (e *ExcludeFileRuleChecker) UpdateRulesFromTCC(ctx context.Context, userId, base string) error {
	if time.Now().Before(e.expire) {
		return nil
	} else {
		e.expire = time.Now().Add(5 * time.Minute)
	}
	config, cli := e.is.GetConfig(), e.is.GetClient()
	if config == nil || cli == nil {
		return nil
	}
	tcc := config.GetTCCConfig(ctx, cli, userId)
	rules := strings.Split(tcc.ExcludeFileRule, "\n")
	rules = lo.Filter(rules, func(rule string, _ int) bool { return len(rule) > 0 })
	if len(rules) == 0 {
		return nil
	}
	// 不能完全清除掉现有的过滤规则，因为用户可能通过 AddRules 添加规则
	for _, rule := range rules {
		e.excludeFiles[IgnoreRule(rule)] = struct{}{}
	}
	return nil
}

func (e *ExcludeFileRuleChecker) IsIgnore(ctx context.Context, _, _ string, _ string, info os.FileInfo) (bool, error, error) {
	if _, ok := e.excludeFiles[IgnoreRule(strings.ToLower(info.Name()))]; ok {
		return true, nil, nil
	}
	return false, nil, nil
}

// ExcludeExtFileRuleChecker 用来检查文件扩展名是否在忽略列表中
type ExcludeExtFileRuleChecker struct {
	extFiles map[IgnoreRule]struct{}
	is       IgnoreServiceV2
	expire   time.Time
}

func NewExcludeExtFileRuleChecker() *ExcludeExtFileRuleChecker {
	return &ExcludeExtFileRuleChecker{
		extFiles: DefaultExcludeExtFileRules,
		expire:   time.Now(),
	}
}

func (e *ExcludeExtFileRuleChecker) Name() CheckerName                   { return ExcludeExtFileRule }
func (e *ExcludeExtFileRuleChecker) Rank() int                           { return ExcludeExtFileRuleRank }
func (e *ExcludeExtFileRuleChecker) Kind() CheckerKind                   { return File }
func (e *ExcludeExtFileRuleChecker) SetIgnoreService(is IgnoreServiceV2) { e.is = is }
func (e *ExcludeExtFileRuleChecker) AddRules(ctx context.Context, _, _ string, rules []IgnoreRule) error {
	for _, rule := range rules {
		e.extFiles[rule] = struct{}{}
	}
	return nil
}
func (e *ExcludeExtFileRuleChecker) UpdateRulesFromTCC(ctx context.Context, userId, base string) error {
	if time.Now().Before(e.expire) {
		return nil
	} else {
		e.expire = time.Now().Add(5 * time.Minute)
	}
	config, cli := e.is.GetConfig(), e.is.GetClient()
	if config == nil || cli == nil {
		return nil
	}
	tcc := config.GetTCCConfig(ctx, cli, userId)
	rules := strings.Split(tcc.ExcludeExtFileRule, "\n")
	rules = lo.Filter(rules, func(rule string, _ int) bool { return len(rule) > 0 })
	if len(rules) == 0 {
		return nil
	}
	// 不能完全清除掉现有的过滤规则，因为用户可能通过 AddRules 添加规则
	for _, rule := range rules {
		e.extFiles[IgnoreRule(rule)] = struct{}{}
	}
	return nil
}

func (e *ExcludeExtFileRuleChecker) IsIgnore(ctx context.Context, _, _ string, path string, info os.FileInfo) (ignored bool, skip error, err error) {
	ext := strings.ToLower(filepath.Ext(path))
	if _, ok := e.extFiles[IgnoreRule(ext)]; ok {
		return true, nil, nil
	}
	return false, nil, nil
}
