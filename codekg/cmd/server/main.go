package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"os"
	"runtime"
	"runtime/debug"
	"time"

	"ide/ckg/clients/ts"
	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/data_manager"
	"ide/ckg/codekg/components/data_storage/consts"
	"ide/ckg/codekg/components/env"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/components/ignore_service"
	"ide/ckg/codekg/components/ignore_service/ignore_rule_checker"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/components/query_service"
	"ide/ckg/codekg/components/tasks"
	"ide/ckg/codekg/components/version_feature"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/protocol"
	"ide/ckg/codekg/util"

	"github.com/samber/lo"

	"code.byted.org/gopkg/lang/maths"
	"golang.org/x/time/rate"
	"google.golang.org/grpc"
)

var (
	ideID       = flag.String("ide_id", "", "IDE ID")
	host        = flag.String("host", "", "Host for server request")
	appID       = flag.String("app_id", "", "App id")
	storagePath = flag.String("storage_path", "", "Path for storing CKG data")
	logPath     = flag.String("log_path", "", "Path for storing CKG log")
	port        = flag.String("port", "50051", "Port on which the server is listening")
	userID      = flag.String("user_id", "", "user id")

	ideVersion       = flag.String("ide_version", "", "ide version")
	versionCode      = flag.Int64("version_code", 0, "ide version code")
	region           = flag.String("region", "", "ide region, one of cn,sg,us")
	extensionVersion = flag.String("extension_version", "", "ide extension version")
	sourceProduct    = flag.String("source_product", "", "source product, one of codeverse, ide, a0")

	limitCPU = flag.Int("limit_cpu", 0, "cpu core used by code kg")

	isMerlin = flag.Bool("is_merlin", false, "is merlin")

	// debug config
	dumpEmbeddingRequest            = flag.Bool("dump_embedding_request", false, "serialize batch embedding request body")
	keepVirtualProjectEntityContent = flag.Bool("keep_vp_entity_content", false, "keep virtual project entity content")

	// features config
	localEmbedding       = flag.Bool("local_embedding", true, "using local embedding or not")
	embeddingStorageType = flag.String("embedding_storage_type", consts.EmbeddingStorage_SQLiteVec, "embedding storage type, one of 0,1")
)

const (
	BootErrStepEmptyStoragePath = "empty_storage_path"
	BootErrStepListenLocalhost  = "listen_localhost"
	BootErrStepInitRPCService   = "init_rpc_service"
)

func main() {
	flag.Parse()
	did := lo.Ternary(*userID == "", model.GetProcessID(), *userID)
	logs.Info("CKG Process id is %s, user id is: %s, did %s", model.GetProcessID(), *userID, did)

	ctx := util.NewBackgroundContext("")
	defer func() {
		if r := recover(); r != nil {
			recoverEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGRecover, did, "", *sourceProduct)
			ctx = ckg_metrics.SetEvent(ctx, recoverEvent)
			recoverEvent.AddTeaParam(ckg_metrics.TeaCKGCrash, "main")
			recoverEvent.Report(ctx, false)

			util.CheckSystemResources(ctx, true)
			logs.CtxInfo(ctx, "panic recovered, err: %v, %s", r, string(debug.Stack()))
		}
	}()

	bootEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGBoot, did, "", *sourceProduct)
	ctx = ckg_metrics.SetEvent(ctx, bootEvent)
	startTime := time.Now()
	if *logPath == "" {
		logs.Init(*storagePath)
	} else {
		logs.Init(*logPath)
	}
	if *limitCPU > 0 {
		maxProcessors := maths.MaxInt(*limitCPU, 1)
		logs.Info("CodeKG process %d", maxProcessors)
		runtime.GOMAXPROCS(maxProcessors)
	}

	product := model.GetSourceProduct(*sourceProduct)
	if len(*storagePath) == 0 {
		bootEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		bootEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, BootErrStepEmptyStoragePath)
		bootEvent.Report(ctx, false)
		panic("storagePath is empty")
	}

	env.KeepVirtualProjectEntityContent = *keepVirtualProjectEntityContent
	env.DumpEmbeddingRequest = *dumpEmbeddingRequest
	env.LocalEmbedding = true
	env.EmbeddingStorageT = consts.EmbeddingStorage_SQLiteVec
	logs.Info("CKG local embedding: %v, embedding storage type is %v", env.LocalEmbedding, env.EmbeddingStorageT)
	envOptions := env.Options{
		Host:             *host,
		AppID:            *appID,
		StoragePath:      *storagePath,
		Port:             *port,
		IdeVersion:       *ideVersion,
		ExtensionVersion: *extensionVersion,
		VersionCode:      *versionCode,
		Region:           *region,
		LimitCPU:         *limitCPU,
		SourceProduct:    product,
		IdeID:            *ideID,
		UserID:           *userID,
		IsMerlin:         *isMerlin,
	}
	env.InitEnvValue(ctx, envOptions)

	envData, err := json.Marshal(envOptions)
	if err == nil {
		bootEvent.AddParam(ckg_metrics.ParamProcessRecoverProjects, string(envData))
	}

	deleteTaskQueue := tasks.NewTaskQueue()
	indexFileTaskQueue := tasks.NewTaskQueue()
	indexFolderTaskQueue := tasks.NewTaskQueue()
	localIndexFileTaskQueue := tasks.NewTaskQueue()
	batchLocalIndexSplitTaskQueue := tasks.NewTaskQueue()
	batchLocalIndexEmbeddingTaskQueue := tasks.NewTaskQueue()
	batchIndexVirtualSplitTaskQueue := tasks.NewTaskQueue()
	batchIndexVirtualEmbeddingTaskQueue := tasks.NewTaskQueue()
	changePreTaskQueue := tasks.NewTaskQueue()

	c := ckg_config.MustNewConfig()
	p := ts.MustNewPackage()
	bootEvent.AddTeaParam(ckg_metrics.TeaParamProcessVersion, p.GetVersionCode())

	tasks.NewTaskExecutor(deleteTaskQueue, 1, rate.NewLimiter(rate.Limit(5), 1)).Work()
	indexRateLimiter := rate.NewLimiter(rate.Limit(6), 1)
	tasks.NewTaskExecutor(indexFileTaskQueue, 10, indexRateLimiter).Work()
	tasks.NewTaskExecutor(indexFolderTaskQueue, 10, nil).Work()
	tasks.NewTaskExecutor(localIndexFileTaskQueue, 10, indexRateLimiter).Work()
	tasks.NewTaskExecutor(batchLocalIndexSplitTaskQueue, 10, indexRateLimiter).Work()
	tasks.NewTaskExecutor(batchIndexVirtualSplitTaskQueue, 10, indexRateLimiter).Work()
	tasks.NewTaskExecutor(changePreTaskQueue, 10, indexRateLimiter).Work()

	embeddingRateLimiter := rate.NewLimiter(rate.Limit(10), 1)
	tasks.NewTaskExecutor(batchLocalIndexEmbeddingTaskQueue, 10, embeddingRateLimiter).Work()
	tasks.NewTaskExecutor(batchIndexVirtualEmbeddingTaskQueue, 10, embeddingRateLimiter).Work()
	is := ignore_service.NewIgnoreService(".gitignore")
	// init ignore service v2 & register checkers
	isv2 := ignore_rule_checker.NewIgnoreServiceV2(c, nil)
	_ = isv2.Register(ctx, ignore_rule_checker.NewExcludeFolderRuleChecker())
	_ = isv2.Register(ctx, ignore_rule_checker.NewExcludeFileRuleChecker())
	_ = isv2.Register(ctx, ignore_rule_checker.NewExcludeExtFileRuleChecker())
	_ = isv2.Register(ctx, ignore_rule_checker.NewFileMetadataRuleChecker())
	_ = isv2.Register(ctx, ignore_rule_checker.NewTCCIgnoreRuleChecker())
	_ = isv2.Register(ctx, ignore_rule_checker.NewGitIgnoreRuleChecker())
	_ = isv2.Register(ctx, ignore_rule_checker.NewTraeIgnoreRuleChecker())
	_ = isv2.Register(ctx, ignore_rule_checker.NewFileContentRuleChecker(
		ignore_rule_checker.NewIncludeExtFileRuleChecker()))

	fm := version_feature.NewFeatureManager()
	qm := tasks.NewEmbeddingQueueMgr()
	dm := data_manager.NewDataManager(indexFileTaskQueue, indexFolderTaskQueue, deleteTaskQueue, changePreTaskQueue, localIndexFileTaskQueue,
		batchLocalIndexSplitTaskQueue, batchLocalIndexEmbeddingTaskQueue, batchIndexVirtualSplitTaskQueue, batchIndexVirtualEmbeddingTaskQueue, qm,
		is, isv2, c, file_system.RealFS, fm)
	qs := query_service.NewQueryService(dm, is, c, file_system.RealFS)

	codeKGServerStartTime := time.Now()
	codeKGServer := newCodeKGServer(dm, qs, p, c, *userID)
	listen, err := net.Listen("tcp", fmt.Sprintf("127.0.0.1:%s", *port))
	if err != nil {
		bootEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		bootEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, BootErrStepListenLocalhost)
		bootEvent.Report(ctx, false)
		logs.Error("error start server, err: %v", err)
		os.Exit(1)
	}
	bootEvent.AddTeaParam(ckg_metrics.TeaParamCodeKGServerCreateTime, time.Since(codeKGServerStartTime))

	s := grpc.NewServer(
		grpc.ChainUnaryInterceptor(unaryInjectLogIDInterceptor, unaryPanicInterceptor, unarySingleFlightInterceptor),
		grpc.ChainStreamInterceptor(streamPanicInterceptor),
	)
	protocol.RegisterCodeKGServer(s, codeKGServer)
	logs.Info("server start at %v", *port)

	bootEvent.AddTeaParam(ckg_metrics.TeaParamProcessRecoverTime, time.Since(startTime).Milliseconds())
	bootEvent.Report(ctx, false)
	util.CheckSystemResources(ctx, false)

	if err := s.Serve(listen); err != nil {
		logs.Error("error start CodeKG server, err: %v", err)
		bootEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		bootEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, BootErrStepInitRPCService)
		bootEvent.Report(ctx, false)
		os.Exit(2)
	}
}
