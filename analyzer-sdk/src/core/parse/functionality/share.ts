import {ParserEnum} from "../share";

export enum FunctionalityEnum {
  GetInvokeDefinition = "getInvokeDefinition",
  GetTypeDefinition = "getTypeDefinition",
  GetVarDeclaration = "getVarDeclaration",
  GetUsageInUri = "getUsageInUri",
  GetUsage = "getUsage",
  GetTypeDefinitionPreFilter = "getTypeDefinitionPreFilter",
}

export const isAsyncExecuteMap = new Map<FunctionalityEnum, boolean>([
  [FunctionalityEnum.GetInvokeDefinition, true],
  [FunctionalityEnum.GetTypeDefinition, true],
  [FunctionalityEnum.GetVarDeclaration, true],
  [FunctionalityEnum.GetUsageInUri, true],
  [FunctionalityEnum.GetUsage, true],
  [FunctionalityEnum.GetTypeDefinitionPreFilter, false],
]);

export const defaultParserMap = new Map<FunctionalityEnum, ParserEnum[]>([
  [FunctionalityEnum.GetInvokeDefinition, [ParserEnum.PSI_LSP]],
  [FunctionalityEnum.GetTypeDefinition, [ParserEnum.PSI_LSP]],
  [FunctionalityEnum.GetVarDeclaration, [ParserEnum.PSI_LSP]],
  [FunctionalityEnum.GetUsageInUri, [ParserEnum.PSI_LSP]],
  [FunctionalityEnum.GetUsage, [ParserEnum.PSI_LSP]],
  [FunctionalityEnum.GetTypeDefinitionPreFilter, []],
]);