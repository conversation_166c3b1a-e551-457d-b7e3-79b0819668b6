package tasks

import (
	"context"
	"fmt"
	"github.com/emirpasic/gods/trees/redblacktree"
	"github.com/samber/lo"
	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/util"
	"sync"
	"time"
)

var defaultQueueBounds = []int{
	128, 128 * 2, 128 * 4, 128 * 6, 128 * 8,
	128 * 10, 128 * 12, 128 * 14, 128 * 16,
}

const defaultQueueSize = 1024

// EmbeddingQueueMgr 管理 CKG 进程多用户多项目的 Embedding Content Queue
type EmbeddingQueueMgr struct {
	managerMu *sync.Mutex
	managers  map[string]map[string]*ProjectEmbeddingQueueMgr
}

func NewEmbeddingQueueMgr() *EmbeddingQueueMgr {
	return &EmbeddingQueueMgr{
		managerMu: new(sync.Mutex),
		managers:  make(map[string]map[string]*ProjectEmbeddingQueueMgr),
	}
}

// GetOrCreateProjectEmbeddingQueueMgr 获取根据 userId 获取对应的 embedding queue mgr。
// 如果没有，会创建一个。如果创建失败，则返回 false。
// 保证 bool 为 false 时，mgr 为 nil；为 true 时 mgr，不为 nil。
func (e *EmbeddingQueueMgr) GetOrCreateProjectEmbeddingQueueMgr(userId string, projectId model.URI, config *ckg_config.Config,
	storage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage,
	cli knowledgebase.Client, attr model.IndexAttribute,
	target *TaskQueue, setting EmbeddingQueueMgrSetting) (*ProjectEmbeddingQueueMgr, bool) {
	e.managerMu.Lock()
	defer e.managerMu.Unlock()

	if e.managers[userId] == nil {
		e.managers[userId] = make(map[string]*ProjectEmbeddingQueueMgr)
	}

	mgr, ok := e.managers[userId][projectId]
	if !ok || mgr.EntityStorage.IsDeleted() || mgr.EmbeddingStorage.IsDeleted() {
		// 如果内存中的 mgr 对应的 db 已被删除，则使用外部传入的新 db 来创建新 mgr。
		mgr = NewProjectEmbeddingQueueMgr(userId, projectId, config, storage, embeddingStorage, cli, attr, target, setting)
		e.managers[userId][projectId] = mgr
	}
	return mgr, true
}

// ProjectEmbeddingQueueMgr 是单个用户的 Embedding Queue 的 Manager，单个用户的所有 Project 构建都受同一个 Mgr 管理
type ProjectEmbeddingQueueMgr struct {
	queues  *redblacktree.Tree
	workers []*EmbeddingWorker

	// default queue 和 worker 是为了
	defaultQueue  *EmbeddingContentQueue
	defaultWorker *EmbeddingWorker

	// 该 task queue 是 “攒好 32 个 Embedding Content” 或 “超时等待” 后写入的异步队列
	target *TaskQueue

	userId           string
	projectId        model.URI
	EntityStorage    data_storage.Storage
	EmbeddingStorage data_storage.EmbeddingStorage
	cli              knowledgebase.Client
	attr             model.IndexAttribute
}

type EmbeddingQueueMgrSetting struct {
	queueBounds []int
}

func NewProjectEmbeddingQueueMgr(userId string, projectId model.URI, config *ckg_config.Config,
	storage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage,
	cli knowledgebase.Client, attr model.IndexAttribute,
	target *TaskQueue, setting EmbeddingQueueMgrSetting) *ProjectEmbeddingQueueMgr {
	m := &ProjectEmbeddingQueueMgr{
		queues:           redblacktree.NewWithIntComparator(),
		workers:          make([]*EmbeddingWorker, 0),
		target:           target,
		userId:           userId,
		projectId:        projectId,
		EntityStorage:    storage,
		EmbeddingStorage: embeddingStorage,
		cli:              cli,
		attr:             attr,
	}
	defaultQueue := NewEmbeddingContentQueue(m, 2048)
	m.defaultQueue = defaultQueue
	m.defaultWorker = NewEmbeddingWorker(defaultQueue, target, config)
	m.defaultWorker.Run()

	// 每个长度都对应特定的 queue 和处理 msg 的 worker
	setting.queueBounds = lo.Ternary(len(setting.queueBounds) == 0, defaultQueueBounds, setting.queueBounds)
	for _, bound := range setting.queueBounds {
		queue := NewEmbeddingContentQueue(m, bound)
		worker := NewEmbeddingWorker(queue, target, config)
		worker.Run()
		m.queues.Put(bound, queue)
		m.workers = append(m.workers, worker)
	}
	return m
}

func (m *ProjectEmbeddingQueueMgr) Push(ctx context.Context, msg *model.BatchEmbeddingMsg) {
	if _, queue, ok := findMinKeyAboveValue(m.queues, len(msg.EmbeddingContent)); !ok {
		// default queue to push
		logs.CtxWarn(ctx, "[Push] push to default queue, since queues not usable: %+v : %+v", m.queues.Keys(), m.queues.Values())
		m.defaultQueue.ch <- msg
	} else {
		queue.ch <- msg
	}
}

func (m *ProjectEmbeddingQueueMgr) Close() {
	for _, worker := range m.workers {
		worker.Stop()
	}
	m.defaultWorker.Stop()
	for _, queue := range m.queues.Values() {
		q, ok := queue.(*EmbeddingContentQueue)
		if !ok {
			continue
		}
		q.Close()
	}
	m.defaultQueue.Close()
}

type EmbeddingContentQueue struct {
	id    int
	mgr   *ProjectEmbeddingQueueMgr
	ch    chan *model.BatchEmbeddingMsg
	bound int
}

func (q *EmbeddingContentQueue) Close() { close(q.ch) }

var nextEmbeddingContentQueueId = 0

func NewEmbeddingContentQueue(mgr *ProjectEmbeddingQueueMgr, bound int) *EmbeddingContentQueue {
	nextEmbeddingContentQueueId++
	return &EmbeddingContentQueue{
		id:    nextEmbeddingContentQueueId,
		mgr:   mgr,
		ch:    make(chan *model.BatchEmbeddingMsg, defaultQueueSize),
		bound: bound,
	}
}

func (q *EmbeddingContentQueue) String() string {
	return fmt.Sprintf("[embedding-content-queue-%d-%d]", q.id, q.bound)
}

type EmbeddingWorker struct {
	id     int
	source *EmbeddingContentQueue
	target *TaskQueue
	config *ckg_config.Config

	// 并发控制
	ctx    context.Context
	cancel func()
}

var nextEmbeddingWorkerId = 0

func NewEmbeddingWorker(source *EmbeddingContentQueue, target *TaskQueue, config *ckg_config.Config) *EmbeddingWorker {
	ctx, cancel := context.WithCancel(context.Background())
	nextEmbeddingWorkerId++
	return &EmbeddingWorker{
		id:     nextEmbeddingWorkerId,
		source: source,
		target: target,
		config: config,
		ctx:    ctx,
		cancel: cancel,
	}
}

func (w *EmbeddingWorker) Run()  { util.SafeGo(w.ctx, func() { w.run(w.ctx) }) }
func (w *EmbeddingWorker) Stop() { w.cancel() }

func (w *EmbeddingWorker) run(ctx context.Context) {
	const batchSize = 32
	const retryMaxNum = 3

	const checkEmbeddingContentQueuePeriod = 15 * time.Second
	tick := time.NewTicker(checkEmbeddingContentQueuePeriod)
	defer tick.Stop()

	var batch = make([]*model.BatchEmbeddingMsg, 0)
	for {
		select {
		case <-ctx.Done():
			// 如果 context done，处理完所有 msg 后 return
			if len(batch) > 0 {
				w.pushEmbeddingTaskToTarget(ctx, batch)
				batch = lo.Slice(batch, 0, 0)
			}
			return
		case msg, ok := <-w.source.ch:
			if ok {
				if msg.RetryNum >= retryMaxNum {
					// 如果某 batch embedding msg retry 次数大于 3，则不处理该消息，等同于直接丢弃
					logs.CtxDebug(ctx, "%s: drop msg, failed %d times, file path: %s entity id: %s",
						w.String(), retryMaxNum, msg.FileRelPath, msg.EntityId)
				} else {
					if msg.RetryNum != 0 {
						logs.CtxDebug(ctx, "%s: msg failed %d times, file path: %s entity id: %s",
							w.String(), msg.RetryNum, msg.FileRelPath, msg.EntityId)
					}
					batch = append(batch, msg)
					tick.Reset(checkEmbeddingContentQueuePeriod)
					if len(batch) >= batchSize {
						toPush := lo.Slice(batch, 0, batchSize)
						w.pushEmbeddingTaskToTarget(ctx, toPush)
						// 弹出前 32 个 msg
						batch = lo.Slice(batch, batchSize, len(batch))
					}
				}
			} else {
				// 如果 channel 已被关闭，处理完所有 msg 后 return
				if len(batch) > 0 {
					w.pushEmbeddingTaskToTarget(ctx, batch)
					batch = lo.Slice(batch, 0, 0)
				}
				return
			}
		case <-tick.C:
			// 如果走该 case，说明最近没有 msg push 到 channel 中，同理 batch 中元素个数 < batchSize
			if len(batch) > 0 {
				w.pushEmbeddingTaskToTarget(ctx, batch)
				batch = lo.Slice(batch, 0, 0)
			}
			tick.Stop()
			tick = time.NewTicker(checkEmbeddingContentQueuePeriod)
		}
	}
}

func (w *EmbeddingWorker) pushEmbeddingTaskToTarget(ctx context.Context, batch []*model.BatchEmbeddingMsg) {
	pushed := w.target.Push(ctx, NewBatchLocalIndexEmbeddingTask(
		w.source.mgr.userId, w.source.mgr.projectId, w.config,
		batch, w.source.mgr.attr, w.source.mgr.cli,
		func(ctx context.Context, embeddingData []*model.EmbeddingData) {
			// 获取 storage 和 embedding storage
			err := data_storage.WriteOnlyEmbeddingData(ctx, w.source.mgr.EntityStorage, w.source.mgr.EmbeddingStorage, embeddingData)
			if err != nil {
				logs.CtxWarn(ctx, "fail to write embedding data (len: %d) : %+v", len(embeddingData), err)
				return
			}
		}), 1)
	if !pushed {
		for _, failedMsg := range batch {
			failedMsg.RetryNum++
			w.source.mgr.Push(ctx, failedMsg)
		}
		logs.CtxWarn(ctx, "%s pushed failed, re-push to queue", w.String())
	}
}

func (w *EmbeddingWorker) String() string {
	return fmt.Sprintf("[batch-embedding-worker-%d] (%s)", w.id, w.source.String())
}
