import {TextDocument} from "vscode-languageserver-textdocument";
import {Logger} from "../../../utils/logger";
import {FileSymbolDef} from "../../share/common";
import {getConfig} from "../../common/config";
import {
  addDefinitionAnalysisTask,
  definition<PERSON><PERSON>ler,
  EVENT_PROCESS_FILE_ACTIVE,
} from "./share";
import {Tree} from "web-tree-sitter";
import {getFileSymbolDef, GetSymbolDefSource, isLanguageSupported} from "../../share/definition";
import {getWorkspaceFolderPath} from "../../../utils/path";
import {
  EVENT_CATEGORY_TYPE_ANALYSIS, EVENT_NAME_FAILED,
  EVENT_VALUE_NO_CACHE,
  reportEvent,
} from "../../../utils/event";
import {getCachedFileSymbolDef} from "../../cache/symbolDefCache";

export async function completeOndemandTypeAnalysis(document: TextDocument, tree: Tree): Promise<void> {
  if (!isLanguageSupported(document.languageId)) {
    return;
  }

  if (!document.uri.startsWith(getWorkspaceFolderPath())) {
    return;
  }

  if (document.getText().length <= getConfig("analysis.definition.completeAnalysisMaxLength")) {
    Logger.info(`completeOndemandTypeAnalysis: document '${document.uri}' only has ${document.getText().length} characters (<= ${getConfig("analysis.definition.completeAnalysisMaxLength")}), analyzing definition info for entire document`);

    if (!tree) {
      Logger.error(`completeOndemandTypeAnalysis: no tree found for '${document.uri}'`);
      reportEvent(document.languageId, EVENT_CATEGORY_TYPE_ANALYSIS, EVENT_PROCESS_FILE_ACTIVE, EVENT_NAME_FAILED, EVENT_VALUE_NO_CACHE).then();
      return;
    }

    const taskFunc = async (): Promise<FileSymbolDef> => {
      const result = new FileSymbolDef(document);

      const cachedSymbolDef = getCachedFileSymbolDef(document, true);
      if (cachedSymbolDef) {
        result.indexDefInfo = [
          ...cachedSymbolDef.indexDefInfo,
        ];
      } else {
        result.indexDefInfo = new Array(document.getText().length).fill(null);
      }

      await getFileSymbolDef(GetSymbolDefSource.Complete, result, document, tree, [{
        start: {line: 0, character: 0},
        end: {line: document.lineCount, character: 0},
      }], definitionHandler);

      return result;
    };

    addDefinitionAnalysisTask({
      id: Date.now(),
      source: GetSymbolDefSource.Complete,
      document,
      taskFunc,
    });
  }
}

