// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v5.28.3
// source: codekg.proto

package protocol

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CodeKGClient is the client API for CodeKG service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CodeKGClient interface {
	Ping(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	SetUp(ctx context.Context, in *SetUpRequest, opts ...grpc.CallOption) (*SetUpResponse, error)
	Init(ctx context.Context, in *InitRequest, opts ...grpc.CallOption) (*InitResponse, error)
	InitVirtualProjects(ctx context.Context, in *InitVirtualProjectsRequest, opts ...grpc.CallOption) (*InitVirtualProjectsResponse, error)
	DocumentCreate(ctx context.Context, in *DocumentRequest, opts ...grpc.CallOption) (*DocumentResponse, error)
	DocumentChange(ctx context.Context, in *DocumentRequest, opts ...grpc.CallOption) (*DocumentResponse, error)
	DocumentDelete(ctx context.Context, in *DocumentRequest, opts ...grpc.CallOption) (*DocumentResponse, error)
	DocumentSelect(ctx context.Context, in *DocumentRequest, opts ...grpc.CallOption) (*DocumentResponse, error)
	CursorMove(ctx context.Context, in *CursorMoveRequest, opts ...grpc.CallOption) (*Empty, error)
	GetBuildStatus(ctx context.Context, in *GetBuildStatusRequest, opts ...grpc.CallOption) (*GetBuildStatusResponse, error)
	GetDocumentsIndexStatus(ctx context.Context, in *GetDocumentsIndexStatusRequest, opts ...grpc.CallOption) (*GetDocumentsIndexStatusResponse, error)
	CancelIndex(ctx context.Context, in *CancelIndexRequest, opts ...grpc.CallOption) (*CancelIndexResponse, error)
	DeleteIndex(ctx context.Context, in *DeleteIndexRequest, opts ...grpc.CallOption) (*DeleteIndexResponse, error)
	RetrieveRelation(ctx context.Context, in *RetrieveRelationRequest, opts ...grpc.CallOption) (*RetrieveRelationResponse, error)
	RetrieveEntity(ctx context.Context, in *RetrieveEntityRequest, opts ...grpc.CallOption) (*RetrieveEntityResponse, error)
	RetrieveRelevantSnippet(ctx context.Context, in *RetrieveRelevantSnippetRequest, opts ...grpc.CallOption) (*RetrieveRelevantSnippetResponse, error)
	RerankSnippet(ctx context.Context, in *RerankSnippetRequest, opts ...grpc.CallOption) (*RerankSnippetResponse, error)
	RefreshToken(ctx context.Context, in *RefreshTokenRequest, opts ...grpc.CallOption) (*RefreshTokenResponse, error)
	IsVersionMatched(ctx context.Context, in *IsVersionMatchedRequest, opts ...grpc.CallOption) (*IsVersionMatchedResponse, error)
	ImportAnalysis(ctx context.Context, in *ImportAnalysisRequest, opts ...grpc.CallOption) (*ImportAnalysisResponse, error)
	FilesImportAnalysis(ctx context.Context, in *FilesImportAnalysisRequest, opts ...grpc.CallOption) (*ImportAnalysisResponse, error)
	SearchCKGDB(ctx context.Context, in *SearchCKGDBRequest, opts ...grpc.CallOption) (*SearchCKGDBResponse, error)
	IsCKGEnabledForNonWorkspaceScenario(ctx context.Context, in *IsCKGEnabledForNonWorkspaceScenarioRequest, opts ...grpc.CallOption) (*IsCKGEnabledForNonWorkspaceScenarioResponse, error)
}

type codeKGClient struct {
	cc grpc.ClientConnInterface
}

func NewCodeKGClient(cc grpc.ClientConnInterface) CodeKGClient {
	return &codeKGClient{cc}
}

func (c *codeKGClient) Ping(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/Ping", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) SetUp(ctx context.Context, in *SetUpRequest, opts ...grpc.CallOption) (*SetUpResponse, error) {
	out := new(SetUpResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/SetUp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) Init(ctx context.Context, in *InitRequest, opts ...grpc.CallOption) (*InitResponse, error) {
	out := new(InitResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/Init", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) InitVirtualProjects(ctx context.Context, in *InitVirtualProjectsRequest, opts ...grpc.CallOption) (*InitVirtualProjectsResponse, error) {
	out := new(InitVirtualProjectsResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/InitVirtualProjects", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) DocumentCreate(ctx context.Context, in *DocumentRequest, opts ...grpc.CallOption) (*DocumentResponse, error) {
	out := new(DocumentResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/DocumentCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) DocumentChange(ctx context.Context, in *DocumentRequest, opts ...grpc.CallOption) (*DocumentResponse, error) {
	out := new(DocumentResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/DocumentChange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) DocumentDelete(ctx context.Context, in *DocumentRequest, opts ...grpc.CallOption) (*DocumentResponse, error) {
	out := new(DocumentResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/DocumentDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) DocumentSelect(ctx context.Context, in *DocumentRequest, opts ...grpc.CallOption) (*DocumentResponse, error) {
	out := new(DocumentResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/DocumentSelect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) CursorMove(ctx context.Context, in *CursorMoveRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/CursorMove", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) GetBuildStatus(ctx context.Context, in *GetBuildStatusRequest, opts ...grpc.CallOption) (*GetBuildStatusResponse, error) {
	out := new(GetBuildStatusResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/GetBuildStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) GetDocumentsIndexStatus(ctx context.Context, in *GetDocumentsIndexStatusRequest, opts ...grpc.CallOption) (*GetDocumentsIndexStatusResponse, error) {
	out := new(GetDocumentsIndexStatusResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/GetDocumentsIndexStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) CancelIndex(ctx context.Context, in *CancelIndexRequest, opts ...grpc.CallOption) (*CancelIndexResponse, error) {
	out := new(CancelIndexResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/CancelIndex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) DeleteIndex(ctx context.Context, in *DeleteIndexRequest, opts ...grpc.CallOption) (*DeleteIndexResponse, error) {
	out := new(DeleteIndexResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/DeleteIndex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) RetrieveRelation(ctx context.Context, in *RetrieveRelationRequest, opts ...grpc.CallOption) (*RetrieveRelationResponse, error) {
	out := new(RetrieveRelationResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/RetrieveRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) RetrieveEntity(ctx context.Context, in *RetrieveEntityRequest, opts ...grpc.CallOption) (*RetrieveEntityResponse, error) {
	out := new(RetrieveEntityResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/RetrieveEntity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) RetrieveRelevantSnippet(ctx context.Context, in *RetrieveRelevantSnippetRequest, opts ...grpc.CallOption) (*RetrieveRelevantSnippetResponse, error) {
	out := new(RetrieveRelevantSnippetResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/RetrieveRelevantSnippet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) RerankSnippet(ctx context.Context, in *RerankSnippetRequest, opts ...grpc.CallOption) (*RerankSnippetResponse, error) {
	out := new(RerankSnippetResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/RerankSnippet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) RefreshToken(ctx context.Context, in *RefreshTokenRequest, opts ...grpc.CallOption) (*RefreshTokenResponse, error) {
	out := new(RefreshTokenResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/RefreshToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) IsVersionMatched(ctx context.Context, in *IsVersionMatchedRequest, opts ...grpc.CallOption) (*IsVersionMatchedResponse, error) {
	out := new(IsVersionMatchedResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/IsVersionMatched", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) ImportAnalysis(ctx context.Context, in *ImportAnalysisRequest, opts ...grpc.CallOption) (*ImportAnalysisResponse, error) {
	out := new(ImportAnalysisResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/ImportAnalysis", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) FilesImportAnalysis(ctx context.Context, in *FilesImportAnalysisRequest, opts ...grpc.CallOption) (*ImportAnalysisResponse, error) {
	out := new(ImportAnalysisResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/FilesImportAnalysis", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) SearchCKGDB(ctx context.Context, in *SearchCKGDBRequest, opts ...grpc.CallOption) (*SearchCKGDBResponse, error) {
	out := new(SearchCKGDBResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/SearchCKGDB", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *codeKGClient) IsCKGEnabledForNonWorkspaceScenario(ctx context.Context, in *IsCKGEnabledForNonWorkspaceScenarioRequest, opts ...grpc.CallOption) (*IsCKGEnabledForNonWorkspaceScenarioResponse, error) {
	out := new(IsCKGEnabledForNonWorkspaceScenarioResponse)
	err := c.cc.Invoke(ctx, "/protocol.CodeKG/IsCKGEnabledForNonWorkspaceScenario", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CodeKGServer is the server API for CodeKG service.
// All implementations must embed UnimplementedCodeKGServer
// for forward compatibility
type CodeKGServer interface {
	Ping(context.Context, *Empty) (*Empty, error)
	SetUp(context.Context, *SetUpRequest) (*SetUpResponse, error)
	Init(context.Context, *InitRequest) (*InitResponse, error)
	InitVirtualProjects(context.Context, *InitVirtualProjectsRequest) (*InitVirtualProjectsResponse, error)
	DocumentCreate(context.Context, *DocumentRequest) (*DocumentResponse, error)
	DocumentChange(context.Context, *DocumentRequest) (*DocumentResponse, error)
	DocumentDelete(context.Context, *DocumentRequest) (*DocumentResponse, error)
	DocumentSelect(context.Context, *DocumentRequest) (*DocumentResponse, error)
	CursorMove(context.Context, *CursorMoveRequest) (*Empty, error)
	GetBuildStatus(context.Context, *GetBuildStatusRequest) (*GetBuildStatusResponse, error)
	GetDocumentsIndexStatus(context.Context, *GetDocumentsIndexStatusRequest) (*GetDocumentsIndexStatusResponse, error)
	CancelIndex(context.Context, *CancelIndexRequest) (*CancelIndexResponse, error)
	DeleteIndex(context.Context, *DeleteIndexRequest) (*DeleteIndexResponse, error)
	RetrieveRelation(context.Context, *RetrieveRelationRequest) (*RetrieveRelationResponse, error)
	RetrieveEntity(context.Context, *RetrieveEntityRequest) (*RetrieveEntityResponse, error)
	RetrieveRelevantSnippet(context.Context, *RetrieveRelevantSnippetRequest) (*RetrieveRelevantSnippetResponse, error)
	RerankSnippet(context.Context, *RerankSnippetRequest) (*RerankSnippetResponse, error)
	RefreshToken(context.Context, *RefreshTokenRequest) (*RefreshTokenResponse, error)
	IsVersionMatched(context.Context, *IsVersionMatchedRequest) (*IsVersionMatchedResponse, error)
	ImportAnalysis(context.Context, *ImportAnalysisRequest) (*ImportAnalysisResponse, error)
	FilesImportAnalysis(context.Context, *FilesImportAnalysisRequest) (*ImportAnalysisResponse, error)
	SearchCKGDB(context.Context, *SearchCKGDBRequest) (*SearchCKGDBResponse, error)
	IsCKGEnabledForNonWorkspaceScenario(context.Context, *IsCKGEnabledForNonWorkspaceScenarioRequest) (*IsCKGEnabledForNonWorkspaceScenarioResponse, error)
	mustEmbedUnimplementedCodeKGServer()
}

// UnimplementedCodeKGServer must be embedded to have forward compatible implementations.
type UnimplementedCodeKGServer struct {
}

func (UnimplementedCodeKGServer) Ping(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (UnimplementedCodeKGServer) SetUp(context.Context, *SetUpRequest) (*SetUpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUp not implemented")
}
func (UnimplementedCodeKGServer) Init(context.Context, *InitRequest) (*InitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Init not implemented")
}
func (UnimplementedCodeKGServer) InitVirtualProjects(context.Context, *InitVirtualProjectsRequest) (*InitVirtualProjectsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitVirtualProjects not implemented")
}
func (UnimplementedCodeKGServer) DocumentCreate(context.Context, *DocumentRequest) (*DocumentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DocumentCreate not implemented")
}
func (UnimplementedCodeKGServer) DocumentChange(context.Context, *DocumentRequest) (*DocumentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DocumentChange not implemented")
}
func (UnimplementedCodeKGServer) DocumentDelete(context.Context, *DocumentRequest) (*DocumentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DocumentDelete not implemented")
}
func (UnimplementedCodeKGServer) DocumentSelect(context.Context, *DocumentRequest) (*DocumentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DocumentSelect not implemented")
}
func (UnimplementedCodeKGServer) CursorMove(context.Context, *CursorMoveRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CursorMove not implemented")
}
func (UnimplementedCodeKGServer) GetBuildStatus(context.Context, *GetBuildStatusRequest) (*GetBuildStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBuildStatus not implemented")
}
func (UnimplementedCodeKGServer) GetDocumentsIndexStatus(context.Context, *GetDocumentsIndexStatusRequest) (*GetDocumentsIndexStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDocumentsIndexStatus not implemented")
}
func (UnimplementedCodeKGServer) CancelIndex(context.Context, *CancelIndexRequest) (*CancelIndexResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelIndex not implemented")
}
func (UnimplementedCodeKGServer) DeleteIndex(context.Context, *DeleteIndexRequest) (*DeleteIndexResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteIndex not implemented")
}
func (UnimplementedCodeKGServer) RetrieveRelation(context.Context, *RetrieveRelationRequest) (*RetrieveRelationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetrieveRelation not implemented")
}
func (UnimplementedCodeKGServer) RetrieveEntity(context.Context, *RetrieveEntityRequest) (*RetrieveEntityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetrieveEntity not implemented")
}
func (UnimplementedCodeKGServer) RetrieveRelevantSnippet(context.Context, *RetrieveRelevantSnippetRequest) (*RetrieveRelevantSnippetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetrieveRelevantSnippet not implemented")
}
func (UnimplementedCodeKGServer) RerankSnippet(context.Context, *RerankSnippetRequest) (*RerankSnippetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RerankSnippet not implemented")
}
func (UnimplementedCodeKGServer) RefreshToken(context.Context, *RefreshTokenRequest) (*RefreshTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshToken not implemented")
}
func (UnimplementedCodeKGServer) IsVersionMatched(context.Context, *IsVersionMatchedRequest) (*IsVersionMatchedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsVersionMatched not implemented")
}
func (UnimplementedCodeKGServer) ImportAnalysis(context.Context, *ImportAnalysisRequest) (*ImportAnalysisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportAnalysis not implemented")
}
func (UnimplementedCodeKGServer) FilesImportAnalysis(context.Context, *FilesImportAnalysisRequest) (*ImportAnalysisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FilesImportAnalysis not implemented")
}
func (UnimplementedCodeKGServer) SearchCKGDB(context.Context, *SearchCKGDBRequest) (*SearchCKGDBResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchCKGDB not implemented")
}
func (UnimplementedCodeKGServer) IsCKGEnabledForNonWorkspaceScenario(context.Context, *IsCKGEnabledForNonWorkspaceScenarioRequest) (*IsCKGEnabledForNonWorkspaceScenarioResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsCKGEnabledForNonWorkspaceScenario not implemented")
}
func (UnimplementedCodeKGServer) mustEmbedUnimplementedCodeKGServer() {}

// UnsafeCodeKGServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CodeKGServer will
// result in compilation errors.
type UnsafeCodeKGServer interface {
	mustEmbedUnimplementedCodeKGServer()
}

func RegisterCodeKGServer(s grpc.ServiceRegistrar, srv CodeKGServer) {
	s.RegisterService(&CodeKG_ServiceDesc, srv)
}

func _CodeKG_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/Ping",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).Ping(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_SetUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).SetUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/SetUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).SetUp(ctx, req.(*SetUpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_Init_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).Init(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/Init",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).Init(ctx, req.(*InitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_InitVirtualProjects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitVirtualProjectsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).InitVirtualProjects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/InitVirtualProjects",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).InitVirtualProjects(ctx, req.(*InitVirtualProjectsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_DocumentCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DocumentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).DocumentCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/DocumentCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).DocumentCreate(ctx, req.(*DocumentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_DocumentChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DocumentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).DocumentChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/DocumentChange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).DocumentChange(ctx, req.(*DocumentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_DocumentDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DocumentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).DocumentDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/DocumentDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).DocumentDelete(ctx, req.(*DocumentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_DocumentSelect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DocumentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).DocumentSelect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/DocumentSelect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).DocumentSelect(ctx, req.(*DocumentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_CursorMove_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CursorMoveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).CursorMove(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/CursorMove",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).CursorMove(ctx, req.(*CursorMoveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_GetBuildStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBuildStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).GetBuildStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/GetBuildStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).GetBuildStatus(ctx, req.(*GetBuildStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_GetDocumentsIndexStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDocumentsIndexStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).GetDocumentsIndexStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/GetDocumentsIndexStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).GetDocumentsIndexStatus(ctx, req.(*GetDocumentsIndexStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_CancelIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelIndexRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).CancelIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/CancelIndex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).CancelIndex(ctx, req.(*CancelIndexRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_DeleteIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteIndexRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).DeleteIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/DeleteIndex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).DeleteIndex(ctx, req.(*DeleteIndexRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_RetrieveRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetrieveRelationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).RetrieveRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/RetrieveRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).RetrieveRelation(ctx, req.(*RetrieveRelationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_RetrieveEntity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetrieveEntityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).RetrieveEntity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/RetrieveEntity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).RetrieveEntity(ctx, req.(*RetrieveEntityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_RetrieveRelevantSnippet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetrieveRelevantSnippetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).RetrieveRelevantSnippet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/RetrieveRelevantSnippet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).RetrieveRelevantSnippet(ctx, req.(*RetrieveRelevantSnippetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_RerankSnippet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RerankSnippetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).RerankSnippet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/RerankSnippet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).RerankSnippet(ctx, req.(*RerankSnippetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_RefreshToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).RefreshToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/RefreshToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).RefreshToken(ctx, req.(*RefreshTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_IsVersionMatched_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsVersionMatchedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).IsVersionMatched(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/IsVersionMatched",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).IsVersionMatched(ctx, req.(*IsVersionMatchedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_ImportAnalysis_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportAnalysisRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).ImportAnalysis(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/ImportAnalysis",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).ImportAnalysis(ctx, req.(*ImportAnalysisRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_FilesImportAnalysis_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilesImportAnalysisRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).FilesImportAnalysis(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/FilesImportAnalysis",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).FilesImportAnalysis(ctx, req.(*FilesImportAnalysisRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_SearchCKGDB_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCKGDBRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).SearchCKGDB(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/SearchCKGDB",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).SearchCKGDB(ctx, req.(*SearchCKGDBRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CodeKG_IsCKGEnabledForNonWorkspaceScenario_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsCKGEnabledForNonWorkspaceScenarioRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CodeKGServer).IsCKGEnabledForNonWorkspaceScenario(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/protocol.CodeKG/IsCKGEnabledForNonWorkspaceScenario",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CodeKGServer).IsCKGEnabledForNonWorkspaceScenario(ctx, req.(*IsCKGEnabledForNonWorkspaceScenarioRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CodeKG_ServiceDesc is the grpc.ServiceDesc for CodeKG service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CodeKG_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "protocol.CodeKG",
	HandlerType: (*CodeKGServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Ping",
			Handler:    _CodeKG_Ping_Handler,
		},
		{
			MethodName: "SetUp",
			Handler:    _CodeKG_SetUp_Handler,
		},
		{
			MethodName: "Init",
			Handler:    _CodeKG_Init_Handler,
		},
		{
			MethodName: "InitVirtualProjects",
			Handler:    _CodeKG_InitVirtualProjects_Handler,
		},
		{
			MethodName: "DocumentCreate",
			Handler:    _CodeKG_DocumentCreate_Handler,
		},
		{
			MethodName: "DocumentChange",
			Handler:    _CodeKG_DocumentChange_Handler,
		},
		{
			MethodName: "DocumentDelete",
			Handler:    _CodeKG_DocumentDelete_Handler,
		},
		{
			MethodName: "DocumentSelect",
			Handler:    _CodeKG_DocumentSelect_Handler,
		},
		{
			MethodName: "CursorMove",
			Handler:    _CodeKG_CursorMove_Handler,
		},
		{
			MethodName: "GetBuildStatus",
			Handler:    _CodeKG_GetBuildStatus_Handler,
		},
		{
			MethodName: "GetDocumentsIndexStatus",
			Handler:    _CodeKG_GetDocumentsIndexStatus_Handler,
		},
		{
			MethodName: "CancelIndex",
			Handler:    _CodeKG_CancelIndex_Handler,
		},
		{
			MethodName: "DeleteIndex",
			Handler:    _CodeKG_DeleteIndex_Handler,
		},
		{
			MethodName: "RetrieveRelation",
			Handler:    _CodeKG_RetrieveRelation_Handler,
		},
		{
			MethodName: "RetrieveEntity",
			Handler:    _CodeKG_RetrieveEntity_Handler,
		},
		{
			MethodName: "RetrieveRelevantSnippet",
			Handler:    _CodeKG_RetrieveRelevantSnippet_Handler,
		},
		{
			MethodName: "RerankSnippet",
			Handler:    _CodeKG_RerankSnippet_Handler,
		},
		{
			MethodName: "RefreshToken",
			Handler:    _CodeKG_RefreshToken_Handler,
		},
		{
			MethodName: "IsVersionMatched",
			Handler:    _CodeKG_IsVersionMatched_Handler,
		},
		{
			MethodName: "ImportAnalysis",
			Handler:    _CodeKG_ImportAnalysis_Handler,
		},
		{
			MethodName: "FilesImportAnalysis",
			Handler:    _CodeKG_FilesImportAnalysis_Handler,
		},
		{
			MethodName: "SearchCKGDB",
			Handler:    _CodeKG_SearchCKGDB_Handler,
		},
		{
			MethodName: "IsCKGEnabledForNonWorkspaceScenario",
			Handler:    _CodeKG_IsCKGEnabledForNonWorkspaceScenario_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "codekg.proto",
}
