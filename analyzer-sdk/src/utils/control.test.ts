import {beforeAll, describe, expect, it} from "vitest";
import {FeatureGateManager, FeatureName} from "../core/common/config";
import {setEnableLocalLogging} from "./logger";
import {updateLastTimeoutTimestamp} from "./control";
import {MainHandler} from "../worker/main";

describe("test_isInTimeoutInterval", () => {
  beforeAll(async () => {
    FeatureGateManager.INSTANCE.openFeature(FeatureName.ENABLE_PSI_LSP_TIMEOUT_INTERVAL)
    setEnableLocalLogging(true)
  });

  it("case1_normal", async () => {
    MainHandler.INSTANCE.init_("./dist/resources", "/test", false, false, false);

    await updateLastTimeoutTimestamp(1);
    expect(await MainHandler.INSTANCE.getLastTimeoutTimestamp({})).toEqual(1);
  });
});