# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import codekg_pb2 as codekg__pb2


class CodeKGStub(object):
    """CodeKG service definition.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Ping = channel.unary_unary(
                '/protocol.CodeKG/Ping',
                request_serializer=codekg__pb2.Empty.SerializeToString,
                response_deserializer=codekg__pb2.Empty.FromString,
                )
        self.SetUp = channel.unary_unary(
                '/protocol.CodeKG/SetUp',
                request_serializer=codekg__pb2.SetUpRequest.SerializeToString,
                response_deserializer=codekg__pb2.SetUpResponse.FromString,
                )
        self.Init = channel.unary_unary(
                '/protocol.CodeKG/Init',
                request_serializer=codekg__pb2.InitRequest.SerializeToString,
                response_deserializer=codekg__pb2.InitResponse.FromString,
                )
        self.InitVirtualProjects = channel.unary_unary(
                '/protocol.CodeKG/InitVirtualProjects',
                request_serializer=codekg__pb2.InitVirtualProjectsRequest.SerializeToString,
                response_deserializer=codekg__pb2.InitVirtualProjectsResponse.FromString,
                )
        self.DocumentCreate = channel.unary_unary(
                '/protocol.CodeKG/DocumentCreate',
                request_serializer=codekg__pb2.DocumentRequest.SerializeToString,
                response_deserializer=codekg__pb2.DocumentResponse.FromString,
                )
        self.DocumentChange = channel.unary_unary(
                '/protocol.CodeKG/DocumentChange',
                request_serializer=codekg__pb2.DocumentRequest.SerializeToString,
                response_deserializer=codekg__pb2.DocumentResponse.FromString,
                )
        self.DocumentDelete = channel.unary_unary(
                '/protocol.CodeKG/DocumentDelete',
                request_serializer=codekg__pb2.DocumentRequest.SerializeToString,
                response_deserializer=codekg__pb2.DocumentResponse.FromString,
                )
        self.DocumentSelect = channel.unary_unary(
                '/protocol.CodeKG/DocumentSelect',
                request_serializer=codekg__pb2.DocumentRequest.SerializeToString,
                response_deserializer=codekg__pb2.DocumentResponse.FromString,
                )
        self.CursorMove = channel.unary_unary(
                '/protocol.CodeKG/CursorMove',
                request_serializer=codekg__pb2.CursorMoveRequest.SerializeToString,
                response_deserializer=codekg__pb2.Empty.FromString,
                )
        self.GetBuildStatus = channel.unary_unary(
                '/protocol.CodeKG/GetBuildStatus',
                request_serializer=codekg__pb2.GetBuildStatusRequest.SerializeToString,
                response_deserializer=codekg__pb2.GetBuildStatusResponse.FromString,
                )
        self.GetDocumentsIndexStatus = channel.unary_unary(
                '/protocol.CodeKG/GetDocumentsIndexStatus',
                request_serializer=codekg__pb2.GetDocumentsIndexStatusRequest.SerializeToString,
                response_deserializer=codekg__pb2.GetDocumentsIndexStatusResponse.FromString,
                )
        self.CancelIndex = channel.unary_unary(
                '/protocol.CodeKG/CancelIndex',
                request_serializer=codekg__pb2.CancelIndexRequest.SerializeToString,
                response_deserializer=codekg__pb2.CancelIndexResponse.FromString,
                )
        self.DeleteIndex = channel.unary_unary(
                '/protocol.CodeKG/DeleteIndex',
                request_serializer=codekg__pb2.DeleteIndexRequest.SerializeToString,
                response_deserializer=codekg__pb2.DeleteIndexResponse.FromString,
                )
        self.RetrieveRelation = channel.unary_unary(
                '/protocol.CodeKG/RetrieveRelation',
                request_serializer=codekg__pb2.RetrieveRelationRequest.SerializeToString,
                response_deserializer=codekg__pb2.RetrieveRelationResponse.FromString,
                )
        self.RetrieveEntity = channel.unary_unary(
                '/protocol.CodeKG/RetrieveEntity',
                request_serializer=codekg__pb2.RetrieveEntityRequest.SerializeToString,
                response_deserializer=codekg__pb2.RetrieveEntityResponse.FromString,
                )
        self.RetrieveRelevantSnippet = channel.unary_unary(
                '/protocol.CodeKG/RetrieveRelevantSnippet',
                request_serializer=codekg__pb2.RetrieveRelevantSnippetRequest.SerializeToString,
                response_deserializer=codekg__pb2.RetrieveRelevantSnippetResponse.FromString,
                )
        self.RerankSnippet = channel.unary_unary(
                '/protocol.CodeKG/RerankSnippet',
                request_serializer=codekg__pb2.RerankSnippetRequest.SerializeToString,
                response_deserializer=codekg__pb2.RerankSnippetResponse.FromString,
                )
        self.RefreshToken = channel.unary_unary(
                '/protocol.CodeKG/RefreshToken',
                request_serializer=codekg__pb2.RefreshTokenRequest.SerializeToString,
                response_deserializer=codekg__pb2.RefreshTokenResponse.FromString,
                )
        self.IsVersionMatched = channel.unary_unary(
                '/protocol.CodeKG/IsVersionMatched',
                request_serializer=codekg__pb2.IsVersionMatchedRequest.SerializeToString,
                response_deserializer=codekg__pb2.IsVersionMatchedResponse.FromString,
                )
        self.ImportAnalysis = channel.unary_unary(
                '/protocol.CodeKG/ImportAnalysis',
                request_serializer=codekg__pb2.ImportAnalysisRequest.SerializeToString,
                response_deserializer=codekg__pb2.ImportAnalysisResponse.FromString,
                )
        self.FilesImportAnalysis = channel.unary_unary(
                '/protocol.CodeKG/FilesImportAnalysis',
                request_serializer=codekg__pb2.FilesImportAnalysisRequest.SerializeToString,
                response_deserializer=codekg__pb2.ImportAnalysisResponse.FromString,
                )
        self.SearchCKGDB = channel.unary_unary(
                '/protocol.CodeKG/SearchCKGDB',
                request_serializer=codekg__pb2.SearchCKGDBRequest.SerializeToString,
                response_deserializer=codekg__pb2.SearchCKGDBResponse.FromString,
                )
        self.IsCKGEnabledForNonWorkspaceScenario = channel.unary_unary(
                '/protocol.CodeKG/IsCKGEnabledForNonWorkspaceScenario',
                request_serializer=codekg__pb2.IsCKGEnabledForNonWorkspaceScenarioRequest.SerializeToString,
                response_deserializer=codekg__pb2.IsCKGEnabledForNonWorkspaceScenarioResponse.FromString,
                )


class CodeKGServicer(object):
    """CodeKG service definition.
    """

    def Ping(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetUp(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Init(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def InitVirtualProjects(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DocumentCreate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DocumentChange(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DocumentDelete(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DocumentSelect(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CursorMove(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetBuildStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDocumentsIndexStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelIndex(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteIndex(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RetrieveRelation(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RetrieveEntity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RetrieveRelevantSnippet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RerankSnippet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RefreshToken(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def IsVersionMatched(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ImportAnalysis(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FilesImportAnalysis(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchCKGDB(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def IsCKGEnabledForNonWorkspaceScenario(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CodeKGServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Ping': grpc.unary_unary_rpc_method_handler(
                    servicer.Ping,
                    request_deserializer=codekg__pb2.Empty.FromString,
                    response_serializer=codekg__pb2.Empty.SerializeToString,
            ),
            'SetUp': grpc.unary_unary_rpc_method_handler(
                    servicer.SetUp,
                    request_deserializer=codekg__pb2.SetUpRequest.FromString,
                    response_serializer=codekg__pb2.SetUpResponse.SerializeToString,
            ),
            'Init': grpc.unary_unary_rpc_method_handler(
                    servicer.Init,
                    request_deserializer=codekg__pb2.InitRequest.FromString,
                    response_serializer=codekg__pb2.InitResponse.SerializeToString,
            ),
            'InitVirtualProjects': grpc.unary_unary_rpc_method_handler(
                    servicer.InitVirtualProjects,
                    request_deserializer=codekg__pb2.InitVirtualProjectsRequest.FromString,
                    response_serializer=codekg__pb2.InitVirtualProjectsResponse.SerializeToString,
            ),
            'DocumentCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.DocumentCreate,
                    request_deserializer=codekg__pb2.DocumentRequest.FromString,
                    response_serializer=codekg__pb2.DocumentResponse.SerializeToString,
            ),
            'DocumentChange': grpc.unary_unary_rpc_method_handler(
                    servicer.DocumentChange,
                    request_deserializer=codekg__pb2.DocumentRequest.FromString,
                    response_serializer=codekg__pb2.DocumentResponse.SerializeToString,
            ),
            'DocumentDelete': grpc.unary_unary_rpc_method_handler(
                    servicer.DocumentDelete,
                    request_deserializer=codekg__pb2.DocumentRequest.FromString,
                    response_serializer=codekg__pb2.DocumentResponse.SerializeToString,
            ),
            'DocumentSelect': grpc.unary_unary_rpc_method_handler(
                    servicer.DocumentSelect,
                    request_deserializer=codekg__pb2.DocumentRequest.FromString,
                    response_serializer=codekg__pb2.DocumentResponse.SerializeToString,
            ),
            'CursorMove': grpc.unary_unary_rpc_method_handler(
                    servicer.CursorMove,
                    request_deserializer=codekg__pb2.CursorMoveRequest.FromString,
                    response_serializer=codekg__pb2.Empty.SerializeToString,
            ),
            'GetBuildStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBuildStatus,
                    request_deserializer=codekg__pb2.GetBuildStatusRequest.FromString,
                    response_serializer=codekg__pb2.GetBuildStatusResponse.SerializeToString,
            ),
            'GetDocumentsIndexStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDocumentsIndexStatus,
                    request_deserializer=codekg__pb2.GetDocumentsIndexStatusRequest.FromString,
                    response_serializer=codekg__pb2.GetDocumentsIndexStatusResponse.SerializeToString,
            ),
            'CancelIndex': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelIndex,
                    request_deserializer=codekg__pb2.CancelIndexRequest.FromString,
                    response_serializer=codekg__pb2.CancelIndexResponse.SerializeToString,
            ),
            'DeleteIndex': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteIndex,
                    request_deserializer=codekg__pb2.DeleteIndexRequest.FromString,
                    response_serializer=codekg__pb2.DeleteIndexResponse.SerializeToString,
            ),
            'RetrieveRelation': grpc.unary_unary_rpc_method_handler(
                    servicer.RetrieveRelation,
                    request_deserializer=codekg__pb2.RetrieveRelationRequest.FromString,
                    response_serializer=codekg__pb2.RetrieveRelationResponse.SerializeToString,
            ),
            'RetrieveEntity': grpc.unary_unary_rpc_method_handler(
                    servicer.RetrieveEntity,
                    request_deserializer=codekg__pb2.RetrieveEntityRequest.FromString,
                    response_serializer=codekg__pb2.RetrieveEntityResponse.SerializeToString,
            ),
            'RetrieveRelevantSnippet': grpc.unary_unary_rpc_method_handler(
                    servicer.RetrieveRelevantSnippet,
                    request_deserializer=codekg__pb2.RetrieveRelevantSnippetRequest.FromString,
                    response_serializer=codekg__pb2.RetrieveRelevantSnippetResponse.SerializeToString,
            ),
            'RerankSnippet': grpc.unary_unary_rpc_method_handler(
                    servicer.RerankSnippet,
                    request_deserializer=codekg__pb2.RerankSnippetRequest.FromString,
                    response_serializer=codekg__pb2.RerankSnippetResponse.SerializeToString,
            ),
            'RefreshToken': grpc.unary_unary_rpc_method_handler(
                    servicer.RefreshToken,
                    request_deserializer=codekg__pb2.RefreshTokenRequest.FromString,
                    response_serializer=codekg__pb2.RefreshTokenResponse.SerializeToString,
            ),
            'IsVersionMatched': grpc.unary_unary_rpc_method_handler(
                    servicer.IsVersionMatched,
                    request_deserializer=codekg__pb2.IsVersionMatchedRequest.FromString,
                    response_serializer=codekg__pb2.IsVersionMatchedResponse.SerializeToString,
            ),
            'ImportAnalysis': grpc.unary_unary_rpc_method_handler(
                    servicer.ImportAnalysis,
                    request_deserializer=codekg__pb2.ImportAnalysisRequest.FromString,
                    response_serializer=codekg__pb2.ImportAnalysisResponse.SerializeToString,
            ),
            'FilesImportAnalysis': grpc.unary_unary_rpc_method_handler(
                    servicer.FilesImportAnalysis,
                    request_deserializer=codekg__pb2.FilesImportAnalysisRequest.FromString,
                    response_serializer=codekg__pb2.ImportAnalysisResponse.SerializeToString,
            ),
            'SearchCKGDB': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchCKGDB,
                    request_deserializer=codekg__pb2.SearchCKGDBRequest.FromString,
                    response_serializer=codekg__pb2.SearchCKGDBResponse.SerializeToString,
            ),
            'IsCKGEnabledForNonWorkspaceScenario': grpc.unary_unary_rpc_method_handler(
                    servicer.IsCKGEnabledForNonWorkspaceScenario,
                    request_deserializer=codekg__pb2.IsCKGEnabledForNonWorkspaceScenarioRequest.FromString,
                    response_serializer=codekg__pb2.IsCKGEnabledForNonWorkspaceScenarioResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'protocol.CodeKG', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class CodeKG(object):
    """CodeKG service definition.
    """

    @staticmethod
    def Ping(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/Ping',
            codekg__pb2.Empty.SerializeToString,
            codekg__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetUp(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/SetUp',
            codekg__pb2.SetUpRequest.SerializeToString,
            codekg__pb2.SetUpResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Init(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/Init',
            codekg__pb2.InitRequest.SerializeToString,
            codekg__pb2.InitResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def InitVirtualProjects(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/InitVirtualProjects',
            codekg__pb2.InitVirtualProjectsRequest.SerializeToString,
            codekg__pb2.InitVirtualProjectsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DocumentCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/DocumentCreate',
            codekg__pb2.DocumentRequest.SerializeToString,
            codekg__pb2.DocumentResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DocumentChange(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/DocumentChange',
            codekg__pb2.DocumentRequest.SerializeToString,
            codekg__pb2.DocumentResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DocumentDelete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/DocumentDelete',
            codekg__pb2.DocumentRequest.SerializeToString,
            codekg__pb2.DocumentResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DocumentSelect(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/DocumentSelect',
            codekg__pb2.DocumentRequest.SerializeToString,
            codekg__pb2.DocumentResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CursorMove(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/CursorMove',
            codekg__pb2.CursorMoveRequest.SerializeToString,
            codekg__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetBuildStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/GetBuildStatus',
            codekg__pb2.GetBuildStatusRequest.SerializeToString,
            codekg__pb2.GetBuildStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDocumentsIndexStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/GetDocumentsIndexStatus',
            codekg__pb2.GetDocumentsIndexStatusRequest.SerializeToString,
            codekg__pb2.GetDocumentsIndexStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CancelIndex(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/CancelIndex',
            codekg__pb2.CancelIndexRequest.SerializeToString,
            codekg__pb2.CancelIndexResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteIndex(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/DeleteIndex',
            codekg__pb2.DeleteIndexRequest.SerializeToString,
            codekg__pb2.DeleteIndexResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RetrieveRelation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/RetrieveRelation',
            codekg__pb2.RetrieveRelationRequest.SerializeToString,
            codekg__pb2.RetrieveRelationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RetrieveEntity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/RetrieveEntity',
            codekg__pb2.RetrieveEntityRequest.SerializeToString,
            codekg__pb2.RetrieveEntityResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RetrieveRelevantSnippet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/RetrieveRelevantSnippet',
            codekg__pb2.RetrieveRelevantSnippetRequest.SerializeToString,
            codekg__pb2.RetrieveRelevantSnippetResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RerankSnippet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/RerankSnippet',
            codekg__pb2.RerankSnippetRequest.SerializeToString,
            codekg__pb2.RerankSnippetResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RefreshToken(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/RefreshToken',
            codekg__pb2.RefreshTokenRequest.SerializeToString,
            codekg__pb2.RefreshTokenResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def IsVersionMatched(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/IsVersionMatched',
            codekg__pb2.IsVersionMatchedRequest.SerializeToString,
            codekg__pb2.IsVersionMatchedResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ImportAnalysis(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/ImportAnalysis',
            codekg__pb2.ImportAnalysisRequest.SerializeToString,
            codekg__pb2.ImportAnalysisResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FilesImportAnalysis(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/FilesImportAnalysis',
            codekg__pb2.FilesImportAnalysisRequest.SerializeToString,
            codekg__pb2.ImportAnalysisResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SearchCKGDB(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/SearchCKGDB',
            codekg__pb2.SearchCKGDBRequest.SerializeToString,
            codekg__pb2.SearchCKGDBResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def IsCKGEnabledForNonWorkspaceScenario(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/protocol.CodeKG/IsCKGEnabledForNonWorkspaceScenario',
            codekg__pb2.IsCKGEnabledForNonWorkspaceScenarioRequest.SerializeToString,
            codekg__pb2.IsCKGEnabledForNonWorkspaceScenarioResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
