package util

import (
	"fmt"
	"net"
	"os/exec"
	"strings"
)

func IsPortInUse(port int) bool {
	ln, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		return true
	}
	defer ln.Close()

	return false
}

func KillProcessByPort(port int) error {
	pid, err := getPIDByPort(port)
	if err != nil {
		return fmt.Errorf("failed to get pid by port: %s", err.Error())
	}
	if err = killProcess(pid); err != nil {
		return fmt.Errorf("failed to kill process: %s", err.<PERSON><PERSON><PERSON>())
	}
	return nil
}

func getPIDByPort(port int) (string, error) {
	cmd := exec.Command("lsof", "-i", fmt.Sprintf(":%d", port))
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}

	lines := strings.Split(string(output), "\n")
	if len(lines) > 1 {
		fields := strings.Fields(lines[1])
		if len(fields) > 1 {
			return fields[1], nil
		}
	}
	return "", fmt.Errorf("no process found using port %d", port)
}

func killProcess(pid string) error {
	cmd := exec.Command("kill", "-9", pid)
	return cmd.Run()
}
