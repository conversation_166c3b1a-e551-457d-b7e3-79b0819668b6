import type {DocumentUri, Location, Position} from "vscode-languageserver-types";
import {FILE_URI_PREFIX, wait, withTimeout} from "../../../utils/common";
import {FeatureGateManager, FeatureName, getConfig} from "../../common/config";
import {TimeoutError} from "../../../utils/error";
import {Logger} from "../../../utils/logger";
import {isMainThread} from "worker_threads";
import {addWorkerPsiTask} from "../../../worker/queue/psi";
import {
  EVENT_CATEGORY_PROXY,
  EVENT_NAME_FAILED,
  EVENT_VALUE_EXCEPTION,
  EVENT_VALUE_TIMEOUT,
  reportEvent,
} from "../../../utils/event";
import {updateLastTimeoutTimestamp} from "../../../utils/control";

const EVENT_PROCESS_PSI = "psi";

export interface PSIProxy {
  execute(command: string, uri: DocumentUri, position: Position, args: any[]): Promise<Location[]>;
}

export class PSIParser {
  static INSTANCE = new PSIParser();

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
  }

  private proxy: PSIProxy = null;
  private mainThreadInitialized = false;

  private requestCount = 0;

  init(proxy: PSIProxy): void {
    this.proxy = proxy;
  }

  setMainThreadInitialized(value: boolean): void {
    this.mainThreadInitialized = value;
  }

  initialized(): boolean {
    if (isMainThread) {
      return this.proxy !== null;
    } else {
      return this.mainThreadInitialized;
    }
  }

  async executeInMainThread(command: string, languageId: string, uri: DocumentUri, position: Position, args: any[]): Promise<any> {
    if (isMainThread) {
      return this.execute(command, languageId, uri, position, args);
    } else {
      return addWorkerPsiTask({command, languageId, uri, position, args});
    }
  }

  async execute(command: string, languageId: string, uri: DocumentUri, position: Position, args: any[]): Promise<any> {
    if (this.proxy) {
      if (uri.startsWith(FILE_URI_PREFIX)) {
        while (this.requestCount >= getConfig("parse.psi.maxRequestCount")) {
          await wait(getConfig("parse.psi.waitInterval"));
        }

        try {
          this.requestCount++;
          if (FeatureGateManager.INSTANCE.isFeatureOpen(FeatureName.ENABLE_PSI_LSP_REQUEST)) {
            return await withTimeout(this.proxy.execute(command, uri, position, args), getConfig("parse.psi.timeoutMs"), false);
          } else {
            return [];
          }
        } catch (e) {
          if (e instanceof TimeoutError) {
            reportEvent(languageId, EVENT_CATEGORY_PROXY, EVENT_PROCESS_PSI, EVENT_NAME_FAILED, EVENT_VALUE_TIMEOUT).then();
            Logger.warn(`PSIParser.execute: execution timeout for '${uri} (${position.line}, ${position.character})'`);
            await updateLastTimeoutTimestamp(Date.now());
          } else {
            reportEvent(languageId, EVENT_CATEGORY_PROXY, EVENT_PROCESS_PSI, EVENT_NAME_FAILED, EVENT_VALUE_EXCEPTION).then();
            Logger.error(`PSIParser.execute: exception for '${uri}': ${e}`);
            Logger.error(e.stack);
          }
          return [];
        } finally {
          this.requestCount--;
        }
      } else {
        return [];
      }
    } else {
      return [];
    }
  }
}