package handler

import (
	"byted-ckg/analyzer-offline/manager"
	"code.byted.org/gopkg/logs/v2"
	"context"
	"fmt"
	"io"
	"net/http"
)

type GetDefinitionForRangeRequestData struct {
	File     string   `json:"file"`
	Language string   `json:"language"`
	Start    Position `json:"start"`
	End      Position `json:"end"`
}

func GetDefinitionForRangeHandler(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	if r.Method != http.MethodPost {
		msg := fmt.Sprintf("unsupported request method, required POST, got %s", r.Method)
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		msg := fmt.Sprintf("failed to read request body: %s", err.Error())
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	result, err := manager.GetAnalyzerManager().GetDefinitionForRange(body)
	if err != nil {
		msg := fmt.Sprintf("failed to get definition for range: %s", err.Error())
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusInternalServerError)
		return
	}

	logs.CtxInfo(ctx, "Definition result: %s", result)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	if _, err = w.Write([]byte(result)); err != nil {
		msg := fmt.Sprintf("failed to write response: %s", err.Error())
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusInternalServerError)
		return
	}
}
