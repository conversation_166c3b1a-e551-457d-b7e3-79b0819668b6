package model

import (
	"context"
	"ide/ckg/codekg/components/knowledgebase"
	splittermodel "ide/ckg/codekg/components/splitter/model"
	"ide/ckg/codekg/model"
	"sync"
)

// LocalSplitter defines the interface for splitting markdown content
type LocalSplitter interface {
	// SplitFile is a convenience method that handles file metadata and returns a response compatible with the splitter system
	SplitFile(ctx context.Context, file *knowledgebase.SplitFile) (*knowledgebase.SplitFileResult_, error)

	SupportedLanguages() map[model.LanguageType]bool
}

// LocalSplitterWorkerPool 管理一组 worker 线程来并行处理文件分割任务
type LocalSplitterWorkerPool struct {
	Workers     int
	TaskQueue   chan *LocalSplitTask
	ResultQueue chan *LocalSplitResult
	Wg          sync.WaitGroup
	Ctx         context.Context
	Cancel      context.CancelFunc
}

// LocalSplitTask 表示一个文件分割任务
type LocalSplitTask struct {
	File           *knowledgebase.SplitFile
	Language       model.LanguageType
	AllSplitAsText bool
	ContentType    splittermodel.ContentType
	ChunkSize      int
	ChunkOverlap   int
	Ctx            context.Context
}

// LocalSplitResult 表示一个文件分割结果
type LocalSplitResult struct {
	Path   string
	Result *knowledgebase.SplitFileResult_
	Err    error
}
