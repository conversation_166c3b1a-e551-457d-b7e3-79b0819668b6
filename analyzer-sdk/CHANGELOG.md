# CHANGELOG

## 0.5.1

- Opt: 优化 Go 类型分析 context 提取逻辑

## 0.5.0

- Feat: 补全 Symbol Context 支持 Java
- Refactor: 重构部分代码

## 0.4.9

- Feat: 增加 getDefinitionNodeRange 接口
- Opt: 优化 TreeSitterHandler 逻辑

## 0.4.8

- Fix: 修复 PSI/LSP timeout interval 逻辑不合理的问题

## 0.4.7

- Config: 增加 enable_psi_lsp_timeout_interval 的 FeatureGate 开关

## 0.4.6

- Fix: 调整 PSI/LSP 请求超时时间和间隔

## 0.4.5

- Test: 增加 TreeSitter C/C#/Rust/Java 单测

## 0.4.4

- Fix: 将配置项 analysis.definition.languageEnabled 改为 FeatureGate 管理

## 0.4.3

- Feat: 增加 Config 配置项 analysis.definition.languageEnabled

## 0.4.2

- Fix: 修复 TreeSitter Kotlin 节点描述错误的问题

## 0.4.1

- Feat: 增加 PSI/LSP 请求超时后一段时间内不再请求的逻辑

## 0.4.0

- Feat: 补全 Symbol Context 支持 TS/TSX
- Refactor: 重构部分代码

## 0.3.25

- Fix: 修复 getFileSymbolDef 没有限制导致分析过多的问题

## 0.3.24

- Fix: 修复 Worker 的 FeatureGate 开关接口bug

## 0.3.23

- Feat: 新增 getMethodInRange 功能

## 0.3.22

- Fix: 修复 doGetDefinitionsForRange 的逻辑问题

## 0.3.21

- Feat: 添加 FeatureGate 开关接口

## 0.3.20

- Fix: 修复类型增量分析缓存index不匹配的问题

## 0.3.19

- Feat: 支持 Python 3rd-party module 上下文抽取
- Fix: LSPParser executeDefinition() 参数问题

## 0.3.18

- Fix: 修复获取缓存NPE的问题

## 0.3.17

- Opt: 优化日志打印逻辑

## 0.3.16

- Opt: 优化事件上报，增加UT上下文提取失败事件上报

## 0.3.15

- Feat: 增加JavaScript/Cpp单测上下文提取超时机制

## 0.3.14

- Feat: Add timeout for UT context extraction, default to 1000Ms

## 0.3.13

- Fix: 修复Python单测上下文找不到FuncDecl的问题

## 0.3.12

- Feat: 单测上下文提取支持JetBrains

## 0.3.11

- Feat: 增加 FeatureGate 开关

## 0.3.10

- Feat: 增加分析监听开关

## 0.3.9

- Fix: 修复单测上下文提取失败时的返回值

## 0.3.8

- Fix: 修复Go类型分析在快速编辑时缓存读取不正确的问题

## 0.3.7

- Fix: 修复Javascript单测上下文提取目标函数识别不对的问题

## 0.3.6

- Fix: 修复Go类型分析空对象异常的问题

## 0.3.5

- Feat: 增加增量分析Debounce时间分语言的配置能力

## 0.3.4

- Feat: 增加配置设置接口

## 0.3.3

- Feat: 支持VSCode多点同时编辑时符号缓存的增量更新
- UT: 增加部分单测

## 0.3.2

- Fix: 修复Worker未处理异常导致主线程卡死的问题
- Fix: 修复增量更新后变量定义不更新的问题
- UT: 增加Go异步定义信息查询单测

## 0.3.1

- Fix: 修改 Cpp UT Context Extraction 输出格式

## 0.3.0

- Refactor: 进行Worker改造，所有分析在单独的线程执行
- Fix: 更新代码补全（Go）上下文提取逻辑

## 0.2.9

- Fix: 更新JS单测上下文提取逻辑
- Refactor: 重构类型分析逻辑
- Feat: 新增代码补全（Go）上下文提取逻辑

## 0.2.8

- Fix: 修复日志打印不合理的问题

## 0.2.7

- Fix: 修复补全语法修复逻辑补全括号范围选择不对的问题

## 0.2.6

- Feat: 增加PSI接口

## 0.2.5

- Feat: 重新实现补全中语法检查和语法修复接口

## 0.2.4

- Fix: 修复go symbol analysis (import)中获取当前package符号匹配失败的问题

## 0.2.3

- Fix: 修复getParentNodeForKeywordSearch中不支持语言报错的问题

## 0.2.2

- Fix: 修复补全语法修复括号位置错误的问题

## 0.2.1

- Fix: 修复流式补全语法分析超时机制不起作用的问题

## 0.2.0

- Feat: Cpp UT Context Extraction
- Feat: Definition Analysis for Go
- Refactor: entire repo
