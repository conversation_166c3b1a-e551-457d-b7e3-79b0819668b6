import { Logger } from "metrics/logger";

export enum FileEventType {
	Create = 'create',
	Delete = 'delete',
    Change = 'change',
}

export type FileEvent = {
	path: string;
	event: FileEventType;
};

type fileHandler = (events: FileEvent[]) => void;

export class FileEventCollector {
    private bufferMap: Map<string, FileEventType> = new Map();

    constructor(
		private fileHandler: fileHandler,
		private delay = 1500,
		private maxBufferSize = 1000,
	) {
        setInterval(() => this.flush(), this.delay);
    }

    handleFileChange(filePath: string, eventType: FileEventType) {
		// 如果 buffer 已满并且上传还没发生，则拒绝新事件
		if (this.bufferMap.size >= this.maxBufferSize) {
			return;
		}
		this.bufferMap.set(filePath, eventType);
	}

    private flush() {
		if (this.bufferMap.size === 0) return;
		const events: FileEvent[] = Array.from(this.bufferMap.entries()).map(
			([path, event]) => ({ path, event })
		);
		this.bufferMap.clear();
        try {
            this.fileHandler(events);
        } catch (error) {
        	Logger.error(`error flush file event ${error}`)
        }
	}
}
