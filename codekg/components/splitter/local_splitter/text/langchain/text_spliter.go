package langchain

import (
	lsmodel "ide/ckg/codekg/components/splitter/local_splitter/model"
	"ide/ckg/codekg/model"
)

// TestDocument 是测试用的文档结构体，用于替代外部依赖
type TestDocument struct {
	PageContent string
	Metadata    map[string]interface{}
}

// TextChunk 表示一个带有行号信息的文本块
type TextChunk struct {
	// Content 是文本块的内容
	Content string

	// StartLine 是文本块在原文中的起始行号（1-indexed）
	StartLine int

	// EndLine 是文本块在原文中的结束行号（1-indexed）
	EndLine int
}

// TextSplitter is the standard interface for splitting texts.
type TextSplitter interface {
	// SplitText splits a text into multiple chunks with line information
	SplitText(text string) ([]TextChunk, error)
}

// LocalSplitterAdapter adapts a TextSplitter to the LocalSplitter interface
type LocalSplitterAdapter interface {
	lsmodel.LocalSplitter

	// GetSplitter returns the underlying TextSplitter
	GetSplitter() TextSplitter

	// GetSupportedLanguages returns the supported languages
	GetSupportedLanguages() map[model.LanguageType]bool
}

// Document represents a document with content and metadata
type Document struct {
	// Content is the text content of the document
	Content string

	// Metadata contains additional information about the document
	Metadata map[string]interface{}
}
