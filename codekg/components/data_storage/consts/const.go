package consts

type EmbeddingStorageType string
type StorageVersion int

const (
	EmbeddingStorage_ChormemGo = "chromem_go"
	EmbeddingStorage_SQLiteVec = "sqlite_vec"

	PragmaKey = "ckgno1"

	NoChunkingMethodSinceNoEntityStorage    string = ""
	NoEmbeddingModelSinceNoEmbeddingStorage string = ""

	NoStorageVersion StorageVersion = -1
	StorageVersionV0 StorageVersion = 0 // only for chromem_go
	StorageVersionV1 StorageVersion = 1
	StorageVersionV2 StorageVersion = 2
)

func (t EmbeddingStorageType) Int() int {
	switch t {
	case EmbeddingStorage_ChormemGo:
		return 0
	case EmbeddingStorage_SQLiteVec:
		return 1
	default:
		return 0
	}
}
