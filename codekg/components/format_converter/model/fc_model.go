package model

import (
	"context"
	"errors"
	"ide/ckg/codekg/model"
)

var (
	ErrUnsupportedFormat = errors.New("unsupported format")
)

type FormatType string

const (
	FormatTypeHTML FormatType = "html"
	FormatTypePDF  FormatType = "pdf"
	FormatTypeWord FormatType = "word"
)

type FormatConverter interface {
	ConvertWithContent(ctx context.Context, content string) (string, error)
	ConvertWithURI(ctx context.Context, uri model.URI) (string, error)
	SupportedTypes() map[FormatType]bool
}
