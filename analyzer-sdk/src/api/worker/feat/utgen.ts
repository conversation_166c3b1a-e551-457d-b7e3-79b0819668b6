import {TextDocument} from "vscode-languageserver-textdocument";
import {Logger} from "../../../utils/logger";
import {<PERSON>Handler} from "../../../worker/main";
import {EVENT_CATEGORY_UT_CONTEXT, EVENT_NAME_FAILED, EVENT_NAME_TRIGGER, reportEvent} from "../../../utils/event";

const EVENT_PROCESS_EXTRACT = "extract";

export async function extractUnitTestContext(document: TextDocument, startIndex: number, endIndex: number): Promise<string> {
  reportEvent(document.languageId, EVENT_CATEGORY_UT_CONTEXT, EVENT_PROCESS_EXTRACT, EVENT_NAME_TRIGGER).then();

  let result = "{}";
  try {
    result = await MainHandler.INSTANCE.extractUnitTestContext({document, startIndex, endIndex});
  } catch (e) {
    Logger.error(`extractUnitTestContext: exception for '${document.uri}': ${e}`);
    Logger.error(e.stack);
  }

  if (result === "{}") {
    reportEvent(document.languageId, EVENT_CATEGORY_UT_CONTEXT, EVENT_PROCESS_EXTRACT, EVENT_NAME_FAILED).then();
  }

  return result;
}