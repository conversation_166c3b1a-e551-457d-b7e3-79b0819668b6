import {TextDocument} from "vscode-languageserver-textdocument";
import {Position, Range} from "vscode-languageserver-types";
import {doGetCodeStructNodeForKeywords} from "../../core/feat/general/parentNodeSearch";
import {Logger} from "../../utils/logger";
import {doGetMethodInRange} from "../../core/feat/general/methodInRange";
import {doGetSymbolPositionInRange} from "../../core/feat/general/symbolPosition";
import {doGetDefinitionNodeRange} from "../../core/feat/general/definitionNodeRange";

export function getCodeStructNodeForKeywords(document: TextDocument, keywords: {text: string, range: Range}[]): {
  keyword: { text: string; range: Range };
  codeStruct: { text: string; range: Range }
}[] {
  try {
    return doGetCodeStructNodeForKeywords(document, keywords);
  } catch (e) {
    Logger.error(`getCodeStructNodeForKeywords: exception for '${document.uri}': ${e}`);
    Logger.error(e.stack);
    return null;
  }
}

export function getMethodInRange(document: TextDocument, range: Range, completelyIncluded: boolean): {text: string, range: Range}[] {
  try {
    return doGetMethodInRange(document, range, completelyIncluded);
  } catch (e) {
    Logger.error(`getAllMethodsInRange: exception for '${document.uri}': ${e}`);
    Logger.error(e.stack);
    return [];
  }
}

export function getSymbolPositionInRange(document: TextDocument, range: Range, name: string): Position[] {
  try {
    return doGetSymbolPositionInRange(document, range, name);
  } catch (e) {
    Logger.error(`doGetSymbolPosition: exception for '${document.uri}': ${e}`);
    Logger.error(e.stack);
    return [];
  }
}

export function getDefinitionNodeRange(document: TextDocument, range: Range): Range {
  try {
    return doGetDefinitionNodeRange(document, range);
  } catch (e) {
    Logger.error(`doGetDefinitionNodeRange: exception for '${document.uri}': ${e}`);
    Logger.error(e.stack);
    return null;
  }
}