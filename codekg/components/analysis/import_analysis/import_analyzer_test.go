package import_analysis

import (
	"ide/ckg/codekg/components/logs"
	"encoding/json"
	"google.golang.org/grpc"
	pb "ide/ckg/codekg/protocol"
	u "ide/ckg/codekg/util"
	"testing"
	"time"
)

func TestInitCKGForKiwis(t *testing.T) {
	conn, err := grpc.Dial("localhost:50053", grpc.WithInsecure())
	if err != nil {
		logs.Error("Failed to Dial: %s", err.Error())
		return
	}
	defer conn.Close()

	ctx := u.NewBackgroundContext("")
	c := pb.NewCodeKGClient(conn)

	resp1, err := c.RefreshToken(ctx, &pb.RefreshTokenRequest{
		Token:  u.GetJwtToken("df93f539abfd1e96d56da7d5825598c2"),
		UserID: "liujierui.0723",
	})
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Failed to RefreshToken: %s", err.Error())
		return
	}
	if resp1.Code != 0 {
		t.Errorf("Failed to RefreshToken: %s", resp1.Error.Message)
		return
	}

	resp2, err := c.InitEnvValue(ctx, &pb.InitEnvValueRequest{
		KnowledgebaseID: "liujierui.0723.0822",
	})
	if err != nil {
		t.Errorf("Failed to InitEnvValue: %s", err.Error())
		return
	}
	if resp2.Code != 0 {
		t.Errorf("Failed to InitEnvValue: %s", resp2.Error.Message)
		return
	}

	resp3, err := c.Init(ctx, &pb.InitRequest{
		Projects: []string{"/Users/<USER>/Projects/kiwis"},
		UserID:   "liujierui.0723",
	})
	if err != nil {
		t.Errorf("Failed to Init: %s", err.Error())
		return
	}
	if resp3.Code != 0 {
		t.Errorf("Failed to Init: %s", resp3.Error.Message)
		return
	}

	for {
		resp4, err := c.GetBuildStatus(ctx, &pb.Empty{})
		if err != nil {
			t.Errorf("Failed to GetBuildStatus: %s", err.Error())
			return
		}
		if resp4.Code != 0 {
			t.Errorf("Failed to GetBuildStatus: %s", resp4.Error.Message)
			return
		}

		progress := resp4.Status["/Users/<USER>/Projects/kiwis"].Progress

		logs.CtxInfo(ctx, "%f", progress)

		if progress >= 1.0 {
			break
		}

		time.Sleep(time.Second * 3)
	}
}

func TestImportAnalysisForGo(t *testing.T) {
	ctx := u.NewBackgroundContext("")

	conn, err := grpc.Dial("localhost:50053", grpc.WithInsecure())
	if err != nil {
		logs.CtxError(ctx, "Failed to Dial: %s", err.Error())
		return
	}
	defer conn.Close()

	c := pb.NewCodeKGClient(conn)

	resp, err := c.ImportAnalysis(ctx, &pb.ImportAnalysisRequest{
		ProjectId:       "/Users/<USER>/Projects/kiwis",
		File:            "knowledgebase/splitter/ckgsplitter/code_entity_build.go",
		ImportStatement: "\"code.byted.org/devgpt/kiwis/lib/parser\"",
	})

	if err != nil {
		t.Errorf("Failed to ImportAnalysis: %s", err.Error())
		return
	}
	if resp.Code != 0 {
		t.Errorf("Failed to ImportAnalysis: %s", resp.Error.Message)
		return
	}

	resultJson, _ := json.MarshalIndent(resp.Result, "", "  ")
	logs.CtxInfo(ctx, "\n%s\n", string(resultJson))
}
