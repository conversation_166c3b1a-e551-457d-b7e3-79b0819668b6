// Code generated by MockGen. DO NOT EDIT.
// Source: ide/ckg/codekg/components/knowledgebase (interfaces: Client)
//
// Generated by this command:
//
//	mockgen -destination mock_client/client_mock.go -package client_mock . Client
//

// Package client_mock is a generated GoMock package.
package client_mock

import (
	context "context"
	knowledgebase "ide/ckg/codekg/components/knowledgebase"
	model "ide/ckg/codekg/model"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// DeleteFile mocks base method.
func (m *MockClient) DeleteFile(arg0 context.Context, arg1 *model.TokenValue, arg2 string, arg3 *knowledgebase.DeleteFileRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteFile", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteFile indicates an expected call of DeleteFile.
func (mr *MockClientMockRecorder) DeleteFile(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteFile", reflect.TypeOf((*MockClient)(nil).DeleteFile), arg0, arg1, arg2, arg3)
}

// Embedding mocks base method.
func (m *MockClient) Embedding(arg0 context.Context, arg1 *model.TokenValue, arg2 string, arg3 *knowledgebase.EmbeddingRequest) (*knowledgebase.EmbeddingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Embedding", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*knowledgebase.EmbeddingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Embedding indicates an expected call of Embedding.
func (mr *MockClientMockRecorder) Embedding(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Embedding", reflect.TypeOf((*MockClient)(nil).Embedding), arg0, arg1, arg2, arg3)
}

// EntityDetect mocks base method.
func (m *MockClient) EntityDetect(arg0 context.Context, arg1 *model.TokenValue, arg2 *knowledgebase.EntityDetectRequest) (*knowledgebase.EntityDetectResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EntityDetect", arg0, arg1, arg2)
	ret0, _ := ret[0].(*knowledgebase.EntityDetectResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EntityDetect indicates an expected call of EntityDetect.
func (mr *MockClientMockRecorder) EntityDetect(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EntityDetect", reflect.TypeOf((*MockClient)(nil).EntityDetect), arg0, arg1, arg2)
}

// EntityRerank mocks base method.
func (m *MockClient) EntityRerank(arg0 context.Context, arg1 *model.TokenValue, arg2 *knowledgebase.EntityRerankRequest) (*knowledgebase.EntityRerankResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EntityRerank", arg0, arg1, arg2)
	ret0, _ := ret[0].(*knowledgebase.EntityRerankResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EntityRerank indicates an expected call of EntityRerank.
func (mr *MockClientMockRecorder) EntityRerank(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EntityRerank", reflect.TypeOf((*MockClient)(nil).EntityRerank), arg0, arg1, arg2)
}

// GetCachedFilesAndWrite mocks base method.
func (m *MockClient) GetCachedFilesAndWrite(arg0 context.Context, arg1 *model.TokenValue, arg2, arg3 string, arg4 []*knowledgebase.CachedFile) (chan *knowledgebase.File, chan error, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCachedFilesAndWrite", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(chan *knowledgebase.File)
	ret1, _ := ret[1].(chan error)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetCachedFilesAndWrite indicates an expected call of GetCachedFilesAndWrite.
func (mr *MockClientMockRecorder) GetCachedFilesAndWrite(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCachedFilesAndWrite", reflect.TypeOf((*MockClient)(nil).GetCachedFilesAndWrite), arg0, arg1, arg2, arg3, arg4)
}

// GetCodeKGFeaturesConfig mocks base method.
func (m *MockClient) GetCodeKGFeaturesConfig(arg0 context.Context, arg1 *model.TokenValue) (*knowledgebase.GetFeaturesConfigResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCodeKGFeaturesConfig", arg0, arg1)
	ret0, _ := ret[0].(*knowledgebase.GetFeaturesConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCodeKGFeaturesConfig indicates an expected call of GetCodeKGFeaturesConfig.
func (mr *MockClientMockRecorder) GetCodeKGFeaturesConfig(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCodeKGFeaturesConfig", reflect.TypeOf((*MockClient)(nil).GetCodeKGFeaturesConfig), arg0, arg1)
}

// GetOrCreateKnowledgebase mocks base method.
func (m *MockClient) GetOrCreateKnowledgebase(arg0 context.Context, arg1 *model.TokenValue, arg2 string, arg3 *knowledgebase.GetOrCreateKnowledgebaseRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrCreateKnowledgebase", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetOrCreateKnowledgebase indicates an expected call of GetOrCreateKnowledgebase.
func (mr *MockClientMockRecorder) GetOrCreateKnowledgebase(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrCreateKnowledgebase", reflect.TypeOf((*MockClient)(nil).GetOrCreateKnowledgebase), arg0, arg1, arg2, arg3)
}

// GetUnIndexFileList mocks base method.
func (m *MockClient) GetUnIndexFileList(arg0 context.Context, arg1 *model.TokenValue, arg2 string, arg3 *knowledgebase.GetUnIndexedFilesRequest) (*knowledgebase.GetUnIndexedFilesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUnIndexFileList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*knowledgebase.GetUnIndexedFilesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUnIndexFileList indicates an expected call of GetUnIndexFileList.
func (mr *MockClientMockRecorder) GetUnIndexFileList(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnIndexFileList", reflect.TypeOf((*MockClient)(nil).GetUnIndexFileList), arg0, arg1, arg2, arg3)
}

// IndexFiles mocks base method.
func (m *MockClient) IndexFiles(arg0 context.Context, arg1 *model.TokenValue, arg2, arg3 string, arg4 *knowledgebase.IndexFilesRequest) (*knowledgebase.IndexFilesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IndexFiles", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*knowledgebase.IndexFilesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IndexFiles indicates an expected call of IndexFiles.
func (mr *MockClientMockRecorder) IndexFiles(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IndexFiles", reflect.TypeOf((*MockClient)(nil).IndexFiles), arg0, arg1, arg2, arg3, arg4)
}

// Retrieve mocks base method.
func (m *MockClient) Retrieve(arg0 context.Context, arg1 *model.TokenValue, arg2 string, arg3 *knowledgebase.RetrieveRequest) (*knowledgebase.RetrieveResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Retrieve", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*knowledgebase.RetrieveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Retrieve indicates an expected call of Retrieve.
func (mr *MockClientMockRecorder) Retrieve(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Retrieve", reflect.TypeOf((*MockClient)(nil).Retrieve), arg0, arg1, arg2, arg3)
}

// SplitFiles mocks base method.
func (m *MockClient) SplitFiles(arg0 context.Context, arg1 *model.TokenValue, arg2 string, arg3 *knowledgebase.SplitFilesRequest) (*knowledgebase.SplitFilesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SplitFiles", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*knowledgebase.SplitFilesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SplitFiles indicates an expected call of SplitFiles.
func (mr *MockClientMockRecorder) SplitFiles(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SplitFiles", reflect.TypeOf((*MockClient)(nil).SplitFiles), arg0, arg1, arg2, arg3)
}
