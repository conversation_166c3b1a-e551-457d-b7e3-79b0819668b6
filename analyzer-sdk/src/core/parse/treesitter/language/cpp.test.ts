import {beforeAll, describe, expect, it} from "vitest";
import {SyntaxNode} from "web-tree-sitter";
import {TreeSitterParser} from "../parser";
import {CppTreeSitterHandler} from "./cpp";
import {LanguageIdEnum} from "../../share";

describe("test_getImportFileOrModule", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
#include <iostream>
#include <A_header>
#include "B_header"
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = CppTreeSitterHandler.INSTANCE.getImportFileOrModule(tree.rootNode);

    expect(result.sort()).toEqual(["\"B_header\"", "<A_header>", "<iostream>"]);
  });
});

describe("test_getAliasInfoInFile", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
using ::bar::A;
using std::string; 
using B;

namespace foo {
    using ::foo::bar::A;
    using foo::std::string; 
    using foo::B;
}

namespace bar {
    using ::bar::bar::A;
    using bar::std::string; 
    using bar::B;
    
    namespace sdf {
        using ::sdf::bar::bar::A;
        using sdf::bar::std::string; 
        using sdf::bar::B;
    }
    
    class AC {
        using ::AC::bar::bar::A;
        using AC::bar::std::string; 
        using AC::bar::B;
    }
    
    class BC {
        using ::BC::bar::bar::A;
        using BC::bar::std::string; 
        using BC::bar::B;
        
        void f() {
        }
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse("cpp", docText);

    let funcDefNode: SyntaxNode = null;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Cpp, "(function_definition) @func_def", tree.rootNode);
    for (const match of matchResult) {
      const defNode = match.matches.get("func_def");

      if (defNode.length === 1) {
        funcDefNode = defNode[0];
      }
    }

    const result = CppTreeSitterHandler.INSTANCE.getValidAliasInfoInFile(funcDefNode);

    const expectedResult = [
      "::BC::bar::bar::A",
      "::bar::A",
      "::bar::bar::A",
      "B",
      "BC::bar::B",
      "BC::bar::std::string",
      "bar::B",
      "bar::std::string",
      "std::string",
    ]

    expect(result.sort()).toEqual(expectedResult);
  });
});

describe("test_getFuncSignatureString", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
int add(int a, int b) {
    return a + b;
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);

    const result = CppTreeSitterHandler.INSTANCE.getFuncSignatureString(tree.rootNode.namedChild(0));

    expect(result).toEqual("int add(int a, int b) ");
  });
});

describe("test_getFuncParentNode", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
int main() {
    int num1 = 3;
    int num2 = 5;
    
    // 调用函数并输出结果
    int sum = add(num1, num2);
    std::cout << "The sum of " << num1 << " and " << num2 << " is " << sum << std::endl;

    return 0;
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);
    const result = CppTreeSitterHandler.INSTANCE.getFuncParentNodeForRange(tree.rootNode, {
      start: {
        line: 3,
        character: 0
      }, end: {line: 5, character: 0}
    });
    expect(result.text).toEqual(docText.trim())
  });

  it("case2_null", async () => {
    const docText = `
int add(int a, int b) {
    return a + b;
}
int main() {
    int num1 = 3;
    int num2 = 5;
    
    // 调用函数并输出结果
    int sum = add(num1, num2);
    std::cout << "The sum of " << num1 << " and " << num2 << " is " << sum << std::endl;

    return 0;
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);
    const result =  CppTreeSitterHandler.INSTANCE.getFuncParentNodeForRange(tree.rootNode, {
      start: {
        line: 2,
        character: 0
      }, end: {line: 5, character: 0}
    }) as SyntaxNode;
    expect(result).toEqual(null)
  });
});

describe("test_getTypeParentNode", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
namespace MathUtils {
    // 在命名空间内定义一个类 Calculator
    class Calculator {
    public:
        // 类方法，用于计算两个整数的和
        int add(int a, int b) {
            return a + b;
        }
    };
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Cpp, docText);
    const result =  CppTreeSitterHandler.INSTANCE.getTypeParentNodeForRange(tree.rootNode, { start: { line: 6, character: 0 }, end: { line: 7, character: 0 } }) as SyntaxNode;

    const expectedResult = `
    class Calculator {
    public:
        // 类方法，用于计算两个整数的和
        int add(int a, int b) {
            return a + b;
        }
    }
`
    expect(result.text).toEqual(expectedResult.trim())
  });
});