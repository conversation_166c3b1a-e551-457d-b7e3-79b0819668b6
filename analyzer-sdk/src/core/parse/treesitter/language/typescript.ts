import {Point, SyntaxNode} from "web-tree-sitter";
import {Range} from "vscode-languageserver-textdocument";
import {<PERSON><PERSON>itter<PERSON>and<PERSON>} from "./common";
import {DefNodeInfo, DefNodeKindEnum, LanguageIdEnum} from "../../share";
import {TreeSitterParser} from "../parser";
import {ResolvedTypeInfo} from "../../../share/common";

export class TypescriptTreeSitter<PERSON><PERSON>ler extends TreeSitterHandler {
  static TYPESCRIPT_INSTANCE = new TypescriptTreeSitterHandler(LanguageIdEnum.Typescript);
  static TSX_INSTANCE = new TypescriptTreeSitterHandler(LanguageIdEnum.Tsx);
  static TYPESCRIPT_REACT_INSTANCE = new TypescriptTreeSitterHandler(LanguageIdEnum.TypescriptReact);

  languageId: LanguageIdEnum;

  funcTypes = ["function_declaration", "method_definition", "generator_function", "generator_function_declaration", "function", "arrow_function"];
  typeTypes = ["class_declaration", "interface_declaration", "enum_declaration", "type_alias_declaration", "internal_module"];
  fileTypes = ["program"];
  varTypes = ["variable_declarator", "required_parameter", "public_field_definition"];

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor(languageId: LanguageIdEnum) {
    super();
    this.languageId = languageId;
  }

  getParentNodeForKeywordSearch(node: SyntaxNode, range: Range): SyntaxNode {
    const typeList = [];
    typeList.push(...this.funcTypes);
    typeList.push(...this.typeTypes);
    typeList.push(...this.fileTypes);
    if (this.languageId === LanguageIdEnum.TypescriptReact || this.languageId === LanguageIdEnum.Tsx) {
      typeList.push(["jsx_element", "jsx_fragment"]);
    }
    return this.getParentNodeWithTypeListForRange(node, range, typeList);
  }

  getFuncParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.funcTypes);
  }

  getFuncParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.funcTypes);
  }

  getTypeParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.typeTypes);
  }

  getTypeParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.typeTypes);
  }

  getVarParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.varTypes);
  }

  getVarParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.varTypes);
  }

  getMethodInRange(rootNode: SyntaxNode, range: Range, startIndex: number, endIndex: number, completelyIncluded: boolean): SyntaxNode[] {
    return this.getElementInRangeWithTypeList(rootNode, range, startIndex, endIndex, completelyIncluded, this.funcTypes);
  }

  getInvokeInFile(node: SyntaxNode, startPosition?: Point, endPosition?: Point): SyntaxNode[] {
    const result: SyntaxNode[] = [];

    const queryStr = `
(call_expression
  [
    (identifier) @symbol_name
    (member_expression
      (property_identifier) @symbol_name
    )
  ]
)
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, node, startPosition, endPosition);

    for (const match of matchResult) {
      const nameNode = match.matches.get("symbol_name");

      if (nameNode.length === 1 && nameNode[0].startPosition) {
        result.push(nameNode[0]);
      }
    }

    return result;
  }

  getTypeInFile(node: SyntaxNode, startPosition?: Point, endPosition?: Point): SyntaxNode[] {
    const result: SyntaxNode[] = [];

    const queryStr = `
(type_identifier) @symbol_name
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, node, startPosition, endPosition);

    for (const match of matchResult) {
      const nameNode = match.matches.get("symbol_name");

      if (nameNode.length === 1) {
        result.push(nameNode[0]);
      }
    }

    return result;
  }

  getDeclVariableInFile(node: SyntaxNode, startPosition?: Point, endPosition?: Point): SyntaxNode[] {
    const result: SyntaxNode[] = [];

    const queryStr = `
[
  (variable_declarator
    name: (identifier) @symbol_name
  )
  (required_parameter
    pattern: (identifier) @symbol_name
  )
  (public_field_definition
    name: (property_identifier) @symbol_name
  )
]
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, node, startPosition, endPosition);

    for (const match of matchResult) {
      const nameNode = match.matches.get("symbol_name");

      if (nameNode.length === 1) {
        result.push(nameNode[0]);
      }
    }

    return result;
  }

  getUsageVariableInFile(node: SyntaxNode, startPosition?: Point, endPosition?: Point): SyntaxNode[] {
    const result: SyntaxNode[] = [];

    const invokeNodeIdInFile = this.getInvokeInFile(node, startPosition, endPosition).map(node => node.id);
    const declVarNodeIdInFile = this.getDeclVariableInFile(node, startPosition, endPosition).map(node => node.id);

    const queryStr = `
[
  (identifier) @symbol_name
  (property_identifier) @symbol_name
]
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, node, startPosition, endPosition);

    for (const match of matchResult) {
      const nameNode = match.matches.get("symbol_name");

      if (nameNode.length === 1
        && !invokeNodeIdInFile.includes(nameNode[0].id)
        && (nameNode[0].parent?.type !== "function_declaration" && nameNode[0].parent?.type !== "method_definition")
        && !declVarNodeIdInFile.includes(nameNode[0].id)
      ) {
        result.push(nameNode[0]);
      }
    }

    return result;
  }

  getVarTypeNameNode(node: SyntaxNode, _varName: string): { node: SyntaxNode, typeString: string, index: number } {
    switch (node.type) {
      case "variable_declarator":
      case "required_parameter":
      case "public_field_definition":
        const nodeFromType = this.getTypeNameNodeHelper(node.childForFieldName("type")?.namedChild(0));
        if (nodeFromType) {
          return {node: nodeFromType, typeString: null, index: -1};
        }
        return this.getTypeFromValueHelper(node.childForFieldName("value"));
    }

    return {node: null, typeString: null, index: -1};
  }

  override genResolvedTypeInfoFromDefNode(defNodeInfo: DefNodeInfo): ResolvedTypeInfo {
    const result = super.genResolvedTypeInfoFromDefNode(defNodeInfo);

    if (defNodeInfo.kind === DefNodeKindEnum.VarDecl) {
      const functionSignature = this.checkAndGetFunctionSignatureForVarDecl(defNodeInfo);
      if (functionSignature) {
        result.addOptionalContent(ResolvedTypeInfo.OPTIONAL_CONTENT_FUNC_SIGNATURE, functionSignature);
      }
    } else if (defNodeInfo.kind === DefNodeKindEnum.Class) {
      const classSimplifiedContent = this.genClassSimplifiedContent(defNodeInfo);
      if (classSimplifiedContent !== "") {
        result.addOptionalContent(ResolvedTypeInfo.OPTIONAL_CONTENT_CLASS_SIMPLIFIED_CONTENT, classSimplifiedContent);
      }
    }

    return result;
  }

  getTypeFullString(node: SyntaxNode): string {
    if (node.parent && node.parent.type === "nested_type_identifier") {
      return node.parent.text;
    } else {
      return node.text;
    }
  }

  getVarNameNodeFromVarDeclNode(node: SyntaxNode): SyntaxNode {
    switch (node.type) {
      case "variable_declarator":
      case "public_field_definition":
        return node.childForFieldName("name");
      case "required_parameter":
        return node.childForFieldName("pattern");
      default:
        return null;
    }
  }

  getReturnTypeNodes(node: SyntaxNode): SyntaxNode[] {
    const result: SyntaxNode[] = [];

    if (this.funcTypes.includes(node.type)) {
      const returnTypeNode = node.childForFieldName("return_type")?.namedChild(0);
      if (returnTypeNode) {
        result.push(this.getTypeNameNodeHelper(returnTypeNode));
      }
    }

    return result;
  }

  getSubTypeNodes(node: SyntaxNode): SyntaxNode[] {
    if (node) {
      switch (node.type) {
        case "array_type":
          return [this.getTypeNameNodeHelper(node.namedChild(0))];
      }
    }

    return [];
  }

  getImportInfo(rootNode: SyntaxNode): any {
    // TODO
    return null;
  }

  private getTypeNameNodeHelper(node: SyntaxNode): SyntaxNode {
    if (node) {
      switch (node.type) {
        case "identifier":
        case "type_identifier":
        case "array_type":
          return node;
        case "nested_type_identifier":
        case "generic_type":
          return this.getTypeNameNodeHelper(node.childForFieldName("name"));
        case "member_expression":
          return this.getTypeNameNodeHelper(node.childForFieldName("property"));
      }
    }

    return null;
  }

  private getTypeFromValueHelper(node: SyntaxNode): { node: SyntaxNode, typeString: string, index: number } {
    if (node) {
      switch (node.type) {
        case "string":
          return {node: null, typeString: "string", index: -1};
        case "number":
          return {node: null, typeString: "number", index: -1};
        case "unary_expression":
          return this.getTypeFromValueHelper(node.childForFieldName("argument"));
        case "call_expression":
          return {node: this.getTypeNameNodeHelper(node.childForFieldName("function")), typeString: null, index: 0};
        case "new_expression":
          return {node: this.getTypeNameNodeHelper(node.childForFieldName("constructor")), typeString: null, index: -1};
        default:
          return {node: this.getTypeNameNodeHelper(node), typeString: null, index: -1};
      }
    }

    return {node: null, typeString: null, index: -1};
  }

  private checkAndGetFunctionSignatureForVarDecl(defNodeInfo: DefNodeInfo): string {
    const defNode = defNodeInfo.defNode;
    if (defNode) {
      if (defNode.type === "variable_declarator") {
        let cutIndex = null;
        const toCheckNode = defNode.childForFieldName("value");
        if (toCheckNode) {
          if (toCheckNode.type === "function_expression") {
            cutIndex = toCheckNode.childForFieldName("body").startIndex;
          } else if (toCheckNode.type === "arrow_function") {
            for (const child of toCheckNode.children) {
              if (child.text === "=>") {
                cutIndex = child.startIndex;
              }
            }
          }
        }
        if (cutIndex) {
          return defNode.text.slice(0, cutIndex - defNode.startIndex);
        }
      } else if (defNode.type === "public_field_definition" || defNode.type === "required_parameter") {
        const toCheckNode = defNode.childForFieldName("type")?.namedChild(0);
        if (toCheckNode && toCheckNode.type === "function_type") {
          return defNode.text;
        }
      }
    }

    return null;
  }

  genClassSimplifiedContent(defNodeInfo: DefNodeInfo): string {
    const cutOffRanges: {start: number, end: number}[] = [];

    const queryStr = `
(class_declaration
  body: (class_body
    (method_definition) @method_def
  )
)
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, defNodeInfo.defNode);
    for (const match of matchResult) {
      const defNode = match.matches.get("method_def");

      if (defNode.length === 1) {
        const bodyNode = defNode[0].childForFieldName("body");
        if (bodyNode) {
          cutOffRanges.push({
            start: bodyNode.startIndex,
            end: bodyNode.endIndex,
          });
        }
      }
    }

    const fullContent = defNodeInfo.defNode.text;
    if (cutOffRanges.length > 0) {
      const sortedRanges = cutOffRanges.sort((a, b) => a.start - b.start);

      let result = '';
      let lastIndex = 0;

      for (const range of sortedRanges) {
        result += fullContent.slice(lastIndex, range.start - defNodeInfo.defNode.startIndex);
        result += " { ... }";
        lastIndex = range.end - defNodeInfo.defNode.startIndex;
      }

      result += fullContent.slice(lastIndex);

      return result;
    } else {
      return fullContent;
    }
  }
}