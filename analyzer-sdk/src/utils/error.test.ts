import {describe, expect, it} from "vitest";
import {withTimeout} from "./common";
import {TimeoutError} from "./error";

describe("test_error", () => {
  it("case1_timeout_error", async () => {
    const asyncOperation = async (p: number): Promise<number> => {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(p);
        }, 15);
      });
    };

    try {
      await withTimeout(asyncOperation(1), 10, false)
    } catch (e) {
      if (e instanceof TimeoutError) {
        expect(true).toBe(true);
      } else {
        expect(false).toBe(true);
      }
    }
  });
});