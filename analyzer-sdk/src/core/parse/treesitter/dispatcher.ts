import {GoTreeSitter<PERSON>and<PERSON>} from "./language/go";
import {LanguageIdEnum} from "../share";
import {reflectCall} from "../../../utils/common";
import {TreeSitterParser} from "./parser";
import {PythonTreeSitterHandler} from "./language/python";
import {JavascriptTreeSitterHandler} from "./language/javascript";
import {JavaTreeSitterHandler} from "./language/java";
import {TypescriptTreeSitterHandler} from "./language/typescript";
import {CppTreeSitterHandler} from "./language/cpp";
import {CSharpTreeSitterHandler} from "./language/csharp";
import {CTreeSitterHandler} from "./language/c";
import {RustTreeSitterHandler} from "./language/rust";
import {KotlinTreeSitterHandler} from "./language/kotlin";

export class TreeSitterDispatcher {
  static INSTANCE = new TreeSitterDispatcher();

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
  }

  dispatch(languageId: string, functionality: string, ...args: any): any {
    if (!TreeSitterParser.INSTANCE.initialized) {
      return undefined;
    }

    switch (languageId) {
      case LanguageIdEnum.C:
        return reflectCall(CTreeSitterHandler.INSTANCE, functionality, ...args);
      case LanguageIdEnum.Cpp:
        return reflectCall(CppTreeSitterHandler.INSTANCE, functionality, ...args);
      case LanguageIdEnum.CSharp:
        return reflectCall(CSharpTreeSitterHandler.INSTANCE, functionality, ...args);
      case LanguageIdEnum.Go:
        return reflectCall(GoTreeSitterHandler.INSTANCE, functionality, ...args);
      case LanguageIdEnum.Python:
        return reflectCall(PythonTreeSitterHandler.INSTANCE, functionality, ...args);
      case LanguageIdEnum.Java:
        return reflectCall(JavaTreeSitterHandler.INSTANCE, functionality, ...args);
      case LanguageIdEnum.Kotlin:
        return reflectCall(KotlinTreeSitterHandler.INSTANCE, functionality, ...args);
      case LanguageIdEnum.Javascript:
        return reflectCall(JavascriptTreeSitterHandler.JAVASCRIPT_INSTANCE, functionality, ...args);
      case LanguageIdEnum.Jsx:
        return reflectCall(JavascriptTreeSitterHandler.JSX_INSTANCE, functionality, ...args);
      case LanguageIdEnum.JavascriptReact:
        return reflectCall(JavascriptTreeSitterHandler.JAVASCRIPT_REACT_INSTANCE, functionality, ...args);
      case LanguageIdEnum.Typescript:
        return reflectCall(TypescriptTreeSitterHandler.TYPESCRIPT_INSTANCE, functionality, ...args);
      case LanguageIdEnum.Tsx:
        return reflectCall(TypescriptTreeSitterHandler.TSX_INSTANCE, functionality,...args);
      case LanguageIdEnum.TypescriptReact:
        return reflectCall(TypescriptTreeSitterHandler.TYPESCRIPT_REACT_INSTANCE, functionality, ...args);
      case LanguageIdEnum.Rust:
        return reflectCall(RustTreeSitterHandler.INSTANCE, functionality, ...args);
      default:
        return undefined;
    }
  }
}