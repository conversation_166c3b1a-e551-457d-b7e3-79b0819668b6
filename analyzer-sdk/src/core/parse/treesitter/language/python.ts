import {SyntaxNode} from "web-tree-sitter";
import {Range} from "vscode-languageserver-textdocument";
import {<PERSON><PERSON>itte<PERSON><PERSON>and<PERSON>} from "./common";
import {LanguageIdEnum} from "../../share";
import {TreeSitterParser} from "../parser";

export class <PERSON><PERSON>reeSitter<PERSON><PERSON>ler extends TreeSitterHandler {
  static INSTANCE = new PythonTreeSitterHandler();

  languageId = LanguageIdEnum.Python;

  funcTypes = ["function_definition"];
  typeTypes = ["class_definition"];
  fileTypes = ["module"];
  varTypes = ["assignment", "expression_statement", "class_definition", "function_definition"];

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
    super();
  }

  getParentNodeForKeywordSearch(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, [
      ...this.funcTypes,
      ...this.typeTypes,
      ...this.fileTypes,
    ]);
  }

  getFuncParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.funcTypes);
  }
  getFuncParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.funcTypes);
  }
  getTypeParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.typeTypes);
  }
  getTypeParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.typeTypes);
  }
  getVarParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.varTypes);
  }
  getVarParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.varTypes);
  }
  getMethodInRange(rootNode: SyntaxNode, range: Range, startIndex: number, endIndex: number, completelyIncluded: boolean): SyntaxNode[] {
    return this.getElementInRangeWithTypeList(rootNode, range, startIndex, endIndex, completelyIncluded, this.funcTypes);
  }

  getSimplifiedContentForFunctionDeclNode(node: SyntaxNode): string {
    return node.text;
  }

  getFuncSignatureString(node: SyntaxNode): string {
    if (this.funcTypes.includes(node.type)) {
      const bodyNode = node.childForFieldName("body");
      if (!bodyNode) {
        return null;
      }
      return node.text.slice(0, bodyNode.startIndex - node.startIndex).trim().slice(0, -1);
    }
    return null;
  }

  getImportFileOrModule(node: SyntaxNode): string []{
    const result: string[] = [];
    const importQueryStr = `
    [
(module (import_statement) @import)
(module (import_from_statement) @import)
]
`;
    const importMatchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, importQueryStr, node);
    for (const match of importMatchResult) {
      const importInfoNode = match.matches.get("import");

      if (importInfoNode.length === 1) {
        result.push(importInfoNode[0].text.trim());
      }
    }

    return result;
  }

  getImportInfo(rootNode: SyntaxNode): any {
    // TODO
    return null;
  }
}