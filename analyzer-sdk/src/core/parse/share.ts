import {DocumentUri, Range, Location} from "vscode-languageserver-types";
import {SyntaxNode} from "web-tree-sitter";
import {TextDocument} from "vscode-languageserver-textdocument";
import {positionToPoint} from "../../utils/common";
import {getWorkspaceFolderPath} from "../../utils/path";
import {Logger} from "../../utils/logger";

export enum LanguageIdEnum {
  Python = 'python',
  Javascript = 'javascript',
  JavascriptReact = 'javascriptreact',
  Jsx = 'jsx',
  Typescript = 'typescript',
  TypescriptReact = 'typescriptreact',
  Tsx = 'tsx',
  Go = 'go',
  Ruby = 'ruby',
  Java = 'java',
  C = 'c',
  Cpp = 'cpp',
  CSharp = 'csharp',
  Dart = 'dart',
  Php = 'php',
  Kotlin = 'kotlin',
  Rust = 'rust',
}

export enum DefNodeKindEnum {
  Class = "class",
  Struct = "struct",
  Interface = "interface",
  Enum = "enum",
  Function = "function",
  Namespace = "namespace",
  Union = "union",
  Alias = "alias",
  VarDecl = "var_decl",
  Template = "template",
  Primitive = "primitive",
  Complex = "complex",
  Unknown = "unknown",
}

export interface DefNodeInfo {
  languageId: string;
  uri: DocumentUri;
  kind: DefNodeKindEnum;
  name: string;
  defNode: SyntaxNode;
  subDefNodeInfo: DefNodeInfo[];
}

export interface VarDeclNodeInfo {
  languageId: string;
  uri: DocumentUri;
  kind: DefNodeKindEnum;
  name: string;
  declNode: SyntaxNode;
}

export interface UsageNodeInfo {
  languageId: string;
  uri: DocumentUri;
  usageNode: SyntaxNode;
}

export enum ParserEnum {
  PSI,
  LSP,
  TreeSitter,
  PSI_LSP,
}

export interface SyntaxNodeWithUri {
  uri: DocumentUri;
  node: SyntaxNode;
}

export function findDescendantNodeForRange(rootNode: SyntaxNode, document: TextDocument, range: Range): SyntaxNode {
  let curNode = rootNode.namedDescendantForPosition(positionToPoint(range.start));
  while (curNode.parent && (curNode.startIndex > document.offsetAt(range.start) || curNode.endIndex < document.offsetAt(range.end))) {
    curNode = curNode.parent;
  }
  return curNode;
}

export const inWorkspaceLocationFilter = (location: Location): boolean => {
  return location.uri.startsWith(getWorkspaceFolderPath());
};