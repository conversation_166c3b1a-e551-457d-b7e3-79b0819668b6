{"scripts": {"build:client": "pnpm --filter '@byted-ckg/client' build", "publish:client": "pnpm publish --filter '@byted-ckg/client' --no-git-check -r", "publish:client:alpha": "pnpm publish --filter '@byted-ckg/client' --no-git-check -r --tag alpha", "build:analyzer-sdk": "pnpm --filter '@byted-ckg/analyzer-sdk' build", "build:analyzer-sdk:debug": "pnpm --filter '@byted-ckg/analyzer-sdk' build:debug", "test:analyzer-sdk": "pnpm --filter '@byted-ckg/analyzer-sdk' test", "publish:analyzer-sdk": "pnpm publish --filter './analyzer-sdk' --no-git-check -r", "publish:analyzer-sdk:alpha": "pnpm publish --filter './analyzer-sdk' --no-git-check -r --tag alpha", "lint:fix": "eslint clients/ts --ext .ts --fix", "lint": "eslint clients/ts --ext .ts", "check-circular-dependencies:analyzer-sdk": "madge --circular --extensions ts analyzer-sdk/src"}, "devDependencies": {"@artus/eslint-config-artus": "^0.0.1", "@artus/tsconfig": "~1.0.1", "@types/google-protobuf": "^3.7.2", "@types/node": "^20.11.5", "@types/vscode": "^1.93.0", "esbuild": "^0.19.5", "eslint": "8", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.30.0", "grpc-tools": "^1.12.4", "ts-protoc-gen": "0.14.0", "tsc-alias": "^1.8.8", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3", "vitest": "^2.0.5"}}