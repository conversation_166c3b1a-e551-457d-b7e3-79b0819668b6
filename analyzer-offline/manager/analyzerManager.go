package manager

import (
	"byted-ckg/analyzer-offline/util"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"sync"
	"time"

	"code.byted.org/gopkg/logs/v2"
)

const analyzerPort = 50555

type AnalyzerManager struct {
	ctx     context.Context
	rwLocks map[string]*sync.RWMutex
}

var analyzerServerManagerInstance *AnalyzerManager

func init() {
	analyzerServerManagerInstance = &AnalyzerManager{
		ctx:     context.Background(),
		rwLocks: make(map[string]*sync.RWMutex),
	}
}

func GetAnalyzerManager() *AnalyzerManager {
	return analyzerServerManagerInstance
}

func (p *AnalyzerManager) IsStarted() bool {
	return util.IsPortInUse(analyzerPort)
}

func (p *AnalyzerManager) Shutdown() error {
	return util.KillProcessByPort(analyzerPort)
}

func (p *AnalyzerManager) Start() error {
	cmd := exec.Command(fmt.Sprintf("./bin/analyzer-server-%s-%s", util.GetOS(), util.GetArch()), "-port", fmt.Sprintf("%d", analyzerPort))

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start analzyer: %s", err.Error())
	}

	for i := 0; i < 10; i++ {
		if p.IsStarted() {
			return nil
		}
		time.Sleep(time.Second)
	}

	return fmt.Errorf("failed to start analzyer")
}

func (p *AnalyzerManager) InitializeAnalyzer(repo string, language string) error {
	if _, err := p.sendRequest(http.MethodPost, "/init_analyzer", map[string]interface{}{
		"workspaceFolder": repo,
		"lspPort":         util.GetServerPort(),
		"language":        language,
	}); err != nil {
		return err
	}

	return nil
}

func (p *AnalyzerManager) InitializeTreeSitterParser() error {
	if _, err := p.sendRequest(http.MethodPost, "/init_tree_sitter_parser", map[string]interface{}{}); err != nil {
		return err
	}

	return nil
}

func (p *AnalyzerManager) GetDefinitionForRange(requestData []byte) (string, error) {
	if result, err := p.sendRequest(http.MethodPost, "/get_definition_for_range", requestData); err != nil {
		return "", err
	} else {
		return result, nil
	}
}

func (p *AnalyzerManager) GetDefinitionNodeRange(requestData []byte) (string, error) {
	if result, err := p.sendRequest(http.MethodPost, "/get_definition_node_range", requestData); err != nil {
		return "", err
	} else {
		return result, nil
	}
}

func (p *AnalyzerManager) sendRequest(method string, path string, body interface{}) (string, error) {
	var bodyData []byte
	var err error
	if _, ok := body.([]byte); ok {
		bodyData = body.([]byte)
	} else {
		bodyData, err = json.Marshal(body)
		if err != nil {
			logs.CtxError(p.ctx, "failed to marshal request body: %s", err.Error())
			return "", err
		}
	}

	req, err := http.NewRequest(method, fmt.Sprintf("http://localhost:%d%s", analyzerPort, path), bytes.NewBuffer(bodyData))
	if err != nil {
		logs.CtxError(p.ctx, "failed to create request: %s", err.Error())
		return "", err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logs.CtxError(p.ctx, "failed to send request: %s", err.Error())
		return "", err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.CtxError(p.ctx, "failed to read response body: %s", err.Error())
		return "", err
	}

	return string(respBody), nil
}
