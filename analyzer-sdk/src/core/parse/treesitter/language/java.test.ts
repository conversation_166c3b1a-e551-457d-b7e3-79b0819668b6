import {beforeAll, describe, expect, it} from "vitest";
import {TreeSitterParser} from "../parser";
import {DefNodeKindEnum, LanguageIdEnum} from "../../share";
import {JavaTreeSitterHandler} from "./java";
import {SyntaxNode} from "web-tree-sitter";

describe("test_getFuncParentNodeForRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
public class Calculator {
    public int add(int a, int b) {
        int result = a + b;
        return result;
    }
    
    public int subtract(int a, int b) {
        return a - b;
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Java, docText);
    const result = JavaTreeSitterHandler.INSTANCE.getFuncParentNodeForRange(tree.rootNode, {
      start: {
        line: 3,
        character: 8
      }, 
      end: {
        line: 3, 
        character: 14
      }
    });
    
    expect(result.text).toEqual(`public int add(int a, int b) {
        int result = a + b;
        return result;
    }`);
  });

  it("case2_null", async () => {
    const docText = `
public class Calculator {
    public int add(int a, int b) {
        int result = a + b;
        return result;
    }
    
    public int subtract(int a, int b) {
        return a - b;
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Java, docText);
    const result = JavaTreeSitterHandler.INSTANCE.getFuncParentNodeForRange(tree.rootNode, {
      start: {
        line: 1,
        character: 0
      }, 
      end: {
        line: 1, 
        character: 5
      }
    });
    
    expect(result).toEqual(null);
  });
});

describe("test_getTypeParentNodeForRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
public class Calculator {
    public int add(int a, int b) {
        int result = a + b;
        return result;
    }
    
    public int subtract(int a, int b) {
        return a - b;
    }
}

interface MathOperation {
    int operate(int a, int b);
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Java, docText);
    const result = JavaTreeSitterHandler.INSTANCE.getTypeParentNodeForRange(tree.rootNode, {
      start: {
        line: 3,
        character: 8
      }, 
      end: {
        line: 3, 
        character: 14
      }
    });
    
    expect(result.text).toEqual(`public class Calculator {
    public int add(int a, int b) {
        int result = a + b;
        return result;
    }
    
    public int subtract(int a, int b) {
        return a - b;
    }
}`);
  });
});

describe("test_getMethodInRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
public class Calculator {
    public int add(int a, int b) {
        int result = a + b;
        return result;
    }
    
    public int subtract(int a, int b) {
        return a - b;
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Java, docText);
    const range = {
      start: {
        line: 2,
        character: 4
      },
      end: {
        line: 5,
        character: 5
      }
    };
    
    const result = JavaTreeSitterHandler.INSTANCE.getMethodInRange(tree.rootNode, range, 15, 70, false);
    
    expect(result.map(node => node.text)).toEqual([
      `public int add(int a, int b) {
        int result = a + b;
        return result;
    }`
    ]);
  });
});

describe("test_genClassSimplifiedContent", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
public class VariableUsageExample {

    // 类变量（静态变量）
    private static int staticVariable = 42;

    // 实例变量
    private int instanceVariable;

    // 常量
    private static final String CONSTANT_STRING = "This is a constant string.";
    private static final int CONSTANT_INT = 100;

    // 构造函数
    public VariableUsageExample(int instanceVariable) {
        this.instanceVariable = instanceVariable;
    }

    // 静态方法，用于修改静态变量
    public static void modifyStaticVariable(int newValue) {
        staticVariable = newValue;
    }

    // 实例方法，用于修改实例变量
    public void modifyInstanceVariable(int newValue) {
        this.instanceVariable = newValue;
    }

    // 方法，接收和返回局部变量
    public int manipulateLocalVariable(int input) {
        int localVariable = input * 2;
        return localVariable;
    }

    public static void main(String[] args) {
        // 创建实例
        VariableUsageExample example = new VariableUsageExample(25);

        // 演示变量
        example.demonstrateVariables();

        // 修改静态变量
        VariableUsageExample.modifyStaticVariable(84);
        System.out.println("Modified Static Variable: " + staticVariable);

        // 修改实例变量
        example.modifyInstanceVariable(50);
        System.out.println("Modified Instance Variable: " + example.instanceVariable);

        // 使用和操作局部变量
        int localResult = example.manipulateLocalVariable(5);
        System.out.println("Manipulated Local Variable: " + localResult);
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Java, docText);

    const defNodeInfo = {
      languageId: LanguageIdEnum.Java,
      uri: "file:///test.java",
      kind: DefNodeKindEnum.Class,
      name: "VariableUsageExample",
      defNode: tree.rootNode.namedChild(0),
      subDefNodeInfo: null,
    };
    const result = JavaTreeSitterHandler.INSTANCE.genClassSimplifiedContent(defNodeInfo);

    expect(result).toEqual(`
public class VariableUsageExample {

    // 类变量（静态变量）
    private static int staticVariable = 42;

    // 实例变量
    private int instanceVariable;

    // 常量
    private static final String CONSTANT_STRING = "This is a constant string.";
    private static final int CONSTANT_INT = 100;

    // 构造函数
    public VariableUsageExample(int instanceVariable)  { ... }

    // 静态方法，用于修改静态变量
    public static void modifyStaticVariable(int newValue)  { ... }

    // 实例方法，用于修改实例变量
    public void modifyInstanceVariable(int newValue)  { ... }

    // 方法，接收和返回局部变量
    public int manipulateLocalVariable(int input)  { ... }

    public static void main(String[] args)  { ... }
}
`.trim());
  });
});

describe("test_getNotProcessTypeNode", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
import java.util.SomeJavaType;
import org.bytedance.SomeType;
import java.date.*;

public class ClassExample {
    public static void main(String[] args) {
        SomeJavaType someJavaType;
        SomeType someType;
        Date date; 
    }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Java, docText);
    const allNodes = JavaTreeSitterHandler.INSTANCE.getTypeInFile(tree.rootNode);
    const result1 = JavaTreeSitterHandler.INSTANCE.getNotProcessTypeNode(tree.rootNode, allNodes);

    expect(result1.map(item => item.text)).toEqual([
      "SomeJavaType",
    ]);

    const result2: SyntaxNode[] = [];
    for (const node of allNodes) {
      if (result1.includes(node)) {
        result2.push(node);
      }
    }

    expect(result2.map(item => item.text)).toEqual([
      "SomeJavaType",
    ]);
  });
});