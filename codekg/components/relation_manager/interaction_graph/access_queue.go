package interaction_graph

import (
	"encoding/json"
	"sync"
)

type item[T any] struct {
	value    T
	refCount int // 访问计数
	next     *item[T]
	prev     *item[T]
}

type AccessQueue[T any] struct {
	head     *item[T]
	tail     *item[T]
	capacity int
	size     int
	m        map[string]*item[T] // key 到节点的映射
	mu       *sync.Mutex
	getKey   func(T) string // 用于获取值的唯一标识符
}

func (q *AccessQueue[T]) MarshalJSON() ([]byte, error) {
	q.mu.Lock()
	defer q.mu.Unlock()

	type node struct {
		Value    T   `json:"value"`
		RefCount int `json:"ref_count"`
	}

	values := make([]*node, 0)
	for n := q.head.next; n != q.tail; n = n.next {
		values = append(values, &node{
			Value:    n.value,
			RefCount: n.refCount,
		})
	}

	return json.Marshal(values)
}

// NewAccessQueue 创建一个新的访问队列
// capacity: 队列容量
// getKey: 用于从值中获取唯一标识符的函数
func NewAccessQueue[T any](capacity int, getKey func(T) string) *AccessQueue[T] {
	head := &item[T]{}
	tail := &item[T]{}
	head.next = tail
	tail.prev = head
	return &AccessQueue[T]{
		head:     head,
		tail:     tail,
		capacity: capacity,
		size:     0,
		m:        make(map[string]*item[T]),
		mu:       &sync.Mutex{},
		getKey:   getKey,
	}
}

func (q *AccessQueue[T]) Add(value T) {
	q.mu.Lock()
	defer q.mu.Unlock()

	key := q.getKey(value)

	if n, ok := q.m[key]; ok {
		q.access(n)
	} else {
		newNode := &item[T]{
			value:    value,
			refCount: 0,
		}
		q.access(newNode)
		q.m[key] = newNode
		q.size++
	}
}

func (q *AccessQueue[T]) Get() []T {
	q.mu.Lock()
	defer q.mu.Unlock()

	var values []T
	for n := q.head.next; n != q.tail; n = n.next {
		values = append(values, n.value)
	}
	return values
}

func (q *AccessQueue[T]) Size() int {
	q.mu.Lock()
	defer q.mu.Unlock()
	return q.size
}

func (q *AccessQueue[T]) Clear() {
	q.mu.Lock()
	defer q.mu.Unlock()

	head := &item[T]{}
	tail := &item[T]{}
	head.next = tail
	tail.prev = head

	q.head = head
	q.tail = tail
	q.size = 0
	q.m = make(map[string]*item[T])
}

func (q *AccessQueue[T]) remove(n *item[T]) {
	n.next.prev = n.prev
	n.prev.next = n.next

	delete(q.m, q.getKey(n.value))
	q.size--
}

func (q *AccessQueue[T]) access(n *item[T]) {
	n.refCount++

	if n.prev != nil {
		n.prev.next = n.next
	}
	if n.next != nil {
		n.next.prev = n.prev
	}

	n.next = q.head.next
	n.prev = q.head
	q.head.next.prev = n
	q.head.next = n
}

func (q *AccessQueue[T]) PeekRecent(topK int) []T {
	q.mu.Lock()
	defer q.mu.Unlock()

	if topK == -1 || topK >= q.size {
		topK = q.size
	}
	values := make([]T, 0, topK)
	n := q.head.next
	i := 0

	for n != q.tail && i < topK {
		values = append(values, n.value)
		n = n.next
		i++
	}

	return values
}
