package knowledgebase

import (
	"bufio"
	"bytes"
	"code.byted.org/gopkg/ctxvalues"
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/env"
	bizErr "ide/ckg/codekg/components/error"
	"ide/ckg/codekg/components/version_feature"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/util"
	"net/http"
	"os"
	"strconv"
	"sync"
	"time"

	"ide/ckg/codekg/components/logs"

	"github.com/avast/retry-go"
	"github.com/pkg/errors"
)

const (
	requestApiErr = "request_server_api"
)

//go:generate go run go.uber.org/mock/mockgen -destination mock_client/client_mock.go -package client_mock . Client
type Client interface {
	GetOrCreateKnowledgebase(ctx context.Context, token *model.TokenValue, knowledgebaseID string, request *GetOrCreateKnowledgebaseRequest) error
	GetUnIndexFileList(ctx context.Context, token *model.TokenValue, knowledgebaseID string, request *GetUnIndexedFilesRequest) (*GetUnIndexedFilesResponse, error)
	IndexFiles(ctx context.Context, token *model.TokenValue, knowledgebaseID, caller string, request *IndexFilesRequest) (*IndexFilesResponse, error)
	Retrieve(ctx context.Context, token *model.TokenValue, knowledgebaseID string, request *RetrieveRequest) (*RetrieveResponse, error)
	EntityDetect(ctx context.Context, token *model.TokenValue, request *EntityDetectRequest) (*EntityDetectResponse, error)
	GetCodeKGFeaturesConfig(ctx context.Context, token *model.TokenValue) (*GetFeaturesConfigResponse, error)
	GetCodeKGABConfig(ctx context.Context, token *model.TokenValue) (*GetABConfigResponse, error)
	DeleteFile(ctx context.Context, token *model.TokenValue, knowledgebaseID string, request *DeleteFileRequest) error
	EntityRerank(ctx context.Context, token *model.TokenValue, request *EntityRerankRequest) (*EntityRerankResponse, error)
	GetCachedFilesAndWrite(ctx context.Context, token *model.TokenValue, knowledgebaseID, projectID string, files []*CachedFile) (chan *File, chan error, error)
	SplitFiles(ctx context.Context, token *model.TokenValue, caller string, req *SplitFilesRequest) (*SplitFilesResponse, error)
	Embedding(ctx context.Context, token *model.TokenValue, caller string, req *EmbeddingRequest) (*EmbeddingResponse, error)
	SplitFilesV2(ctx context.Context, token *model.TokenValue, caller string, rel string, req *SplitFilesRequestV2) (*SplitFilesResponse, error)
	EmbeddingV2(ctx context.Context, token *model.TokenValue, caller string, req *EmbeddingRequestV2) (*EmbeddingResponse, error)
}

type client struct {
	cli *resty.Client
}

var clients = sync.Map{}

func GetClient() (Client, error) {
	host := env.GetHost()
	if host == "" {
		return nil, errors.New("empty host")
	}
	appID := env.GetAppID()
	if appID == "" {
		return nil, errors.New("empty app id")
	}
	c, ok := clients.Load(fmt.Sprintf("%s_%s", host, appID))
	if ok {
		return c.(*client), nil
	}
	newClient := &client{
		cli: resty.New().SetHostURL(host).SetHeader("X-APP-ID", appID),
	}
	clients.Store(fmt.Sprintf("%s_%s", host, appID), newClient)
	return newClient, nil
}

func (c *client) EntityRerank(ctx context.Context, token *model.TokenValue, request *EntityRerankRequest) (*EntityRerankResponse, error) {
	if env.TokenIsOutdated(ctx, token) {
		return nil, bizErr.ErrTokenOutOfDate
	}

	requestEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCkgRequest, token.UserID, "", string(env.GetSourceProduct()))
	defer requestEvent.Report(ctx, false)
	resp, err := c.newRequest(ctx, token).
		SetResult(&EntityRerankResponse{}).
		SetHeader("X-Rerank-Version", "1.14.0").
		SetBody(request).
		Post("/api/ide/v1/entity_rerank")

	if err != nil {
		recordApiError(err, requestEvent, "/api/ide/v1/entity_rerank")
		return nil, &util.HTTPError{
			Code:       resp.StatusCode(),
			Message:    err.Error(),
			Extra:      "failed post EntityRerank",
			StatusCode: resp.StatusCode(),
		}
	}

	logs.CtxInfo(ctx, "rerank log id: %v", resp.Header().Get("X-Tt-Logid"))
	recordApiInformation(ctx, resp, requestEvent, "/api/ide/v1/entity_rerank")
	if resp.StatusCode() != http.StatusOK {
		return nil, &util.HTTPError{
			Code:       resp.StatusCode(),
			Message:    "",
			Extra:      "failed post EntityRerank",
			StatusCode: resp.StatusCode(),
		}
	}
	return resp.Result().(*EntityRerankResponse), nil
}

func (c *client) DeleteFile(ctx context.Context, token *model.TokenValue, knowledgebaseID string, request *DeleteFileRequest) error {
	if env.TokenIsOutdated(ctx, token) {
		return bizErr.ErrTokenOutOfDate
	}

	requestEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCkgRequest, token.UserID, "", string(env.GetSourceProduct()))
	defer requestEvent.Report(ctx, false)
	resp, err := c.newRequest(ctx, token).
		SetResult(&EntityDetectResponse{}).
		SetBody(request).
		Post(fmt.Sprintf("/api/ide/v1/knowledgebase/%s/files/delete", knowledgebaseID))

	if err != nil {
		recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/:id/files/delete")
		return &util.HTTPError{
			Code:       resp.StatusCode(),
			Message:    err.Error(),
			Extra:      "failed post DeleteFile",
			StatusCode: resp.StatusCode(),
		}
	}

	recordApiInformation(ctx, resp, requestEvent, "/api/ide/v1/knowledgebase/:id/files/delete")
	if resp.StatusCode() != http.StatusOK {
		return &util.HTTPError{
			Code:       resp.StatusCode(),
			Message:    "",
			Extra:      "failed post DeleteFile",
			StatusCode: resp.StatusCode(),
		}
	}

	return nil
}

func (c *client) EntityDetect(ctx context.Context, token *model.TokenValue, request *EntityDetectRequest) (*EntityDetectResponse, error) {
	if env.TokenIsOutdated(ctx, token) {
		return nil, bizErr.ErrTokenOutOfDate
	}

	requestEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCkgRequest, token.UserID, "", string(env.GetSourceProduct()))
	defer requestEvent.Report(ctx, false)
	resp, err := c.newRequest(ctx, token).
		SetResult(&EntityDetectResponse{}).
		SetBody(request).
		Post(fmt.Sprintf("/api/ide/v1/entity_detect"))

	if err != nil {
		recordApiError(err, requestEvent, "/api/ide/v1/entity_detect")
		return nil, &util.HTTPError{
			Code:       resp.StatusCode(),
			Message:    err.Error(),
			Extra:      "failed post EntityDetect",
			StatusCode: resp.StatusCode(),
		}
	}

	recordApiInformation(ctx, resp, requestEvent, "/api/ide/v1/entity_detect")
	if resp.StatusCode() != http.StatusOK {
		return nil, &util.HTTPError{
			Code:       resp.StatusCode(),
			Message:    "",
			Extra:      "failed post EntityDetect",
			StatusCode: resp.StatusCode(),
		}
	}

	result := resp.Result().(*EntityDetectResponse)
	logs.CtxDebug(ctx, "EntityDetect NER response: %+v", result)
	return result, nil
}

func (c *client) GetOrCreateKnowledgebase(ctx context.Context, token *model.TokenValue, knowledgebaseID string, request *GetOrCreateKnowledgebaseRequest) error {
	if env.TokenIsOutdated(ctx, token) {
		return bizErr.ErrTokenOutOfDate
	}

	err := retry.Do(func() error {
		requestEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCkgRequest, token.UserID, "", string(env.GetSourceProduct()))
		defer requestEvent.Report(ctx, false)
		resp, err := c.newRequest(ctx, token).
			SetBody(request).
			Post(fmt.Sprintf("/api/ide/v1/knowledgebase/%s", knowledgebaseID))
		if err != nil {
			recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/:id")
			return &util.HTTPError{
				Code:       resp.StatusCode(),
				Message:    err.Error(),
				Extra:      "failed post GetOrCreateKnowledgebase",
				StatusCode: resp.StatusCode(),
			}
		}
		recordApiInformation(ctx, resp, requestEvent, "/api/ide/v1/knowledgebase/:id")
		if resp.StatusCode() != http.StatusOK {
			logs.CtxError(ctx, "error post GetOrCreateKnowledgebase, log id: %s", resp.Header().Get("X-Tt-Logid"))
			return &util.HTTPError{
				Code:       resp.StatusCode(),
				Message:    string(resp.Body()),
				Extra:      "failed post GetOrCreateKnowledgebase",
				StatusCode: resp.StatusCode(),
			}
		}
		return nil
	},
		retry.Attempts(3),
		retry.DelayType(retry.BackOffDelay),
		retry.Delay(time.Second),
		retry.MaxDelay(time.Second*20),
		retry.RetryIf(util.JudgeNewWorkError))
	return err
}

func (c *client) GetUnIndexFileList(ctx context.Context, token *model.TokenValue, knowledgebaseID string, request *GetUnIndexedFilesRequest) (*GetUnIndexedFilesResponse, error) {
	if env.TokenIsOutdated(ctx, token) {
		return nil, bizErr.ErrTokenOutOfDate
	}

	var resp *GetUnIndexedFilesResponse
	err := retry.Do(func() error {
		requestEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCkgRequest, token.UserID, "", string(env.GetSourceProduct()))
		defer requestEvent.Report(ctx, false)
		r, err := c.newRequest(ctx, token).
			SetResult(&GetUnIndexedFilesResponse{}).
			SetBody(request).
			Post(fmt.Sprintf("/api/ide/v1/knowledgebase/%s/files/unindexed", knowledgebaseID))
		if err != nil {
			recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/:id/files/unindexed")
			return &util.HTTPError{
				Code:       r.StatusCode(),
				Message:    err.Error(),
				Extra:      "failed post GetUnIndexFileList",
				StatusCode: r.StatusCode(),
			}
		}
		recordApiInformation(ctx, r, requestEvent, "/api/ide/v1/knowledgebase/:id/files/unindexed")
		if r.StatusCode() != http.StatusOK {
			return &util.HTTPError{
				Code:       r.StatusCode(),
				Message:    r.String(),
				Extra:      "failed post GetUnIndexFileList",
				StatusCode: r.StatusCode(),
			}
		}
		resp = r.Result().(*GetUnIndexedFilesResponse)
		return nil
	},
		retry.Attempts(3),
		retry.DelayType(retry.BackOffDelay),
		retry.Delay(time.Second),
		retry.MaxDelay(time.Second*20),
		retry.RetryIf(util.JudgeNewWorkError))
	return resp, err
}

func (c *client) IndexFiles(ctx context.Context, token *model.TokenValue, knowledgebaseID, caller string, request *IndexFilesRequest) (*IndexFilesResponse, error) {
	if env.TokenIsOutdated(ctx, token) {
		return nil, bizErr.ErrTokenOutOfDate
	}
	var result *IndexFilesResponse
	err := retry.Do(func() error {
		start := time.Now()
		requestEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCkgRequest, token.UserID, "", string(env.GetSourceProduct()))
		defer requestEvent.Report(ctx, false)
		resp, err := c.newRequest(ctx, token).
			SetResult(&IndexFilesResponse{}).
			SetBody(request).
			Post(fmt.Sprintf("/api/ide/v1/knowledgebase/%s/files", knowledgebaseID))

		if err != nil {
			recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/:id/files")
			return &util.HTTPError{
				Code:       resp.StatusCode(),
				Message:    err.Error(),
				Extra:      "failed post IndexFiles",
				StatusCode: resp.StatusCode(),
			}
		}

		if time.Since(start).Milliseconds() > 1000 {
			logs.CtxTrace(ctx, "slow IndexFiles request, log id: %s", resp.Header().Get("X-Tt-Logid"))
		}
		recordApiInformation(ctx, resp, requestEvent, "/api/ide/v1/knowledgebase/:id/files")
		requestEvent.AddTeaParam(ckg_metrics.TeaParamIndexCaller, caller)
		if resp.StatusCode() != http.StatusOK {
			return &util.HTTPError{
				Code:       resp.StatusCode(),
				Message:    resp.String(),
				Extra:      "failed post IndexFiles",
				StatusCode: resp.StatusCode(),
			}
		}

		result = resp.Result().(*IndexFilesResponse)

		if len(result.ProjectFiles) == 0 {
			requestEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		}

		return nil
	},
		retry.Attempts(3),
		retry.DelayType(retry.BackOffDelay),
		retry.Delay(time.Second),
		retry.MaxDelay(20*time.Second),
		retry.RetryIf(func(err error) bool {
			var httpError *util.HTTPError
			return errors.As(err, &httpError) && httpError.StatusCode == http.StatusTooManyRequests
		}))

	return result, err
}

func (c *client) Retrieve(ctx context.Context, token *model.TokenValue, knowledgebaseID string, request *RetrieveRequest) (*RetrieveResponse, error) {
	if env.TokenIsOutdated(ctx, token) {
		return nil, bizErr.ErrTokenOutOfDate
	}

	var resp *RetrieveResponse
	err := retry.Do(func() error {
		requestEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCkgRequest, token.UserID, "", string(env.GetSourceProduct()))
		defer requestEvent.Report(ctx, false)
		r, err := c.newRequest(ctx, token).
			SetResult(&RetrieveResponse{}).
			SetBody(request).
			Post(fmt.Sprintf("/api/ide/v1/knowledgebase/%s/retrieval", knowledgebaseID))
		if err != nil {
			recordApiError(err, requestEvent, "/api/ide/v1/knowlegebase/:id/retrieval")
			return &util.HTTPError{
				Code:       r.StatusCode(),
				Message:    err.Error(),
				Extra:      "failed post Retrieve",
				StatusCode: r.StatusCode(),
			}
		}
		recordApiInformation(ctx, r, requestEvent, "/api/ide/v1/knowlegebase/:id/retrieval")
		if r.StatusCode() != http.StatusOK {
			return &util.HTTPError{
				Code:       r.StatusCode(),
				Message:    r.String(),
				Extra:      "failed post Retrieve",
				StatusCode: r.StatusCode(),
			}
		}
		resp = r.Result().(*RetrieveResponse)
		if len(resp.Segments) == 0 {
			logs.CtxWarn(ctx, "empty embedding result, log id: %s", r.Header().Get("X-Tt-Logid"))
		}
		return nil
	},
		retry.Attempts(3),
		retry.DelayType(retry.BackOffDelay),
		retry.Delay(time.Second),
		retry.MaxDelay(time.Second*20),
		retry.RetryIf(util.JudgeNewWorkError))
	return resp, err
}

func recordApiInformation(ctx context.Context, resp *resty.Response, requestEvent ckg_metrics.Event, api string) {
	requestEvent.AddTeaParam(ckg_metrics.TeaParamApi, api)
	requestEvent.AddTeaParam(ckg_metrics.TeaParamHttpCode, resp.StatusCode())
	requestEvent.AddTeaParam(ckg_metrics.TeaParamLogID, resp.Header().Get("X-Tt-Logid"))

	if resp.StatusCode() == http.StatusOK || resp.StatusCode() == http.StatusTooManyRequests {
		serverRecv, err := strconv.ParseInt(resp.Header().Get("x-recv-ts"), 10, 64)
		if err != nil {
			logs.CtxWarn(ctx, "ParseInt err is %v", err)
		}
		serverSend, err := strconv.ParseInt(resp.Header().Get("x-send-ts"), 10, 64)
		if err != nil {
			logs.CtxWarn(ctx, "ParseInt err is %v", err)
		}
		requestEvent.AddTeaParam(ckg_metrics.TeaParamLossSend, serverRecv-requestEvent.GetStartTime().UnixMilli())
		requestEvent.AddTeaParam(ckg_metrics.TeaParamLossRecv, time.Now().UnixMilli()-serverSend)
		requestEvent.AddTeaParam(ckg_metrics.TeaParamServerTime, serverSend-serverRecv)
	}
}

func recordApiError(err error, requestEvent ckg_metrics.Event, api string) {
	requestEvent.AddTeaParam(ckg_metrics.TeaParamApi, api)
	requestEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
	requestEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, requestApiErr)
	requestEvent.AddTeaParam(ckg_metrics.TeaParamRequestServerApiErr, err)
}

func (c *client) GetCodeKGFeaturesConfig(ctx context.Context, token *model.TokenValue) (*GetFeaturesConfigResponse, error) {
	if env.TokenIsOutdated(ctx, token) {
		return nil, bizErr.ErrTokenOutOfDate
	}

	var resp *GetFeaturesConfigResponse
	err := retry.Do(func() error {
		requestEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCkgRequest, token.UserID, "", string(env.GetSourceProduct()))
		defer requestEvent.Report(ctx, false)
		r, err := c.newRequest(ctx, token).SetResult(GetFeaturesConfigResponse{}).
			Get("/api/ide/v1/features")
		if err != nil {
			recordApiError(err, requestEvent, "/api/ide/v1/features")
			return errors.WithMessagef(err, "failed get codekg features config")
		}
		recordApiInformation(ctx, r, requestEvent, "/api/ide/v1/features")
		if r.StatusCode() != http.StatusOK {
			return &util.HTTPError{
				Code:       r.StatusCode(),
				Message:    r.String(),
				Extra:      "failed post GetCodeKGFeaturesConfig",
				StatusCode: r.StatusCode(),
			}
		}
		resp = r.Result().(*GetFeaturesConfigResponse)
		return nil
	},
		retry.Attempts(3),
		retry.DelayType(retry.BackOffDelay),
		retry.Delay(time.Second),
		retry.MaxDelay(time.Second*20),
		retry.RetryIf(util.JudgeNewWorkError))
	return resp, err
}

func (c *client) GetCodeKGABConfig(ctx context.Context, token *model.TokenValue) (*GetABConfigResponse, error) {
	if env.TokenIsOutdated(ctx, token) {
		return nil, bizErr.ErrTokenOutOfDate
	}

	requestEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCkgRequest, token.UserID, "", string(env.GetSourceProduct()))
	defer requestEvent.Report(ctx, false)
	resp, err := c.newRequest(ctx, token).
		SetBody(map[string]string{
			"ckg_version": version_feature.CKG_VERSION.String(),
		}).
		SetResult(GetABConfigResponse{}).
		Post("/api/ide/v1/ckg_ab_params")
	if err != nil {
		recordApiError(err, requestEvent, "/api/ide/v1/ckg_ab_params")
		return nil, errors.WithMessagef(err, "failed get codekg ab config")
	}

	recordApiInformation(ctx, resp, requestEvent, "/api/ide/v1/ckg_ab_params")
	if resp.StatusCode() != http.StatusOK {
		return nil, &util.HTTPError{
			Code:       resp.StatusCode(),
			Message:    resp.String(),
			Extra:      "failed post GetCodeKGABConfig",
			StatusCode: resp.StatusCode(),
		}
	}

	return resp.Result().(*GetABConfigResponse), nil
}

var (
	sseDataPrefix  = []byte("data:")
	sseEventPrefix = []byte("event:")
)

const (
	eventTypeFile     = "file"
	eventTypeSegments = "segments"
)

func (c *client) GetCachedFilesAndWrite(ctx context.Context, token *model.TokenValue, knowledgebaseID, projectID string, files []*CachedFile) (chan *File, chan error, error) {
	if env.TokenIsOutdated(ctx, token) {
		return nil, nil, bizErr.ErrTokenOutOfDate
	}

	requestEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCkgRequest, token.UserID, "", string(env.GetSourceProduct()))
	resp, err := c.newRequest(ctx, token).SetBody(map[string]interface{}{
		"files":      files,
		"project_id": projectID,
	}).SetDoNotParseResponse(true).Post(fmt.Sprintf("/api/ide/v1/knowledgebase/%s/files/cached", knowledgebaseID))
	if err != nil {
		recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/files/cached")
		requestEvent.Report(ctx, false)
		return nil, nil, errors.WithMessagef(err, "failed get files")
	}

	requestEvent.AddTeaParam(ckg_metrics.TeaParamApi, "/api/ide/v1/knowledgebase/files/cached")
	requestEvent.AddTeaParam(ckg_metrics.TeaParamHttpCode, resp.StatusCode())
	requestEvent.AddTeaParam(ckg_metrics.TeaParamLogID, resp.Header().Get("X-Tt-Logid"))
	logs.CtxInfo(ctx, "GetCachedFiles, log id: %v", resp.Header().Get("X-Tt-Logid"))

	if resp.StatusCode() != http.StatusOK {
		requestEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		requestEvent.Report(ctx, false)
		return nil, nil, &util.HTTPError{
			Code:       resp.StatusCode(),
			Message:    resp.String(),
			Extra:      "failed get GetCachedFiles",
			StatusCode: resp.StatusCode(),
		}
	}

	resultCh := make(chan *File)
	errorCh := make(chan error)
	util.SafeGo(ctx, func() {
		defer func() {
			close(resultCh)
			_ = resp.RawBody().Close()
			requestEvent.Report(ctx, false)
		}()

		scanner := bufio.NewScanner(resp.RawBody())
		buf := make([]byte, 0, 65*1024)
		scanner.Buffer(buf, 16*1024*1024)

		var eventType string
		for scanner.Scan() {
			data := scanner.Bytes()

			if len(data) == 0 {
				continue
			}

			switch {
			case bytes.HasPrefix(data, sseEventPrefix):
				eventType = string(trimPrefixAndSpace(data, sseEventPrefix))
			case bytes.HasPrefix(data, sseDataPrefix):
				switch eventType {
				case eventTypeFile:
					file := new(File)
					err := json.Unmarshal(trimPrefixAndSpace(data, sseDataPrefix), file)
					if err != nil {
						recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/files/cached")
						errorCh <- err
						return
					}

					resultCh <- file
				default:
					logs.CtxWarn(ctx, "unexpected event type for GetFiles sse, type: %s", eventTypeFile)
				}
			default:
				// id & retry is ignored
			}
		}

		if scannerErr := scanner.Err(); scannerErr != nil {
			logs.CtxError(ctx, "failed to read SSE stream: %v", scannerErr)
			recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/files/cached")
			errorCh <- scannerErr
		}
	})

	return resultCh, errorCh, nil
}

func (c *client) SplitFiles(ctx context.Context, token *model.TokenValue, caller string, req *SplitFilesRequest) (*SplitFilesResponse, error) {
	req.DatastoreName = "default-latest"
	var resp *SplitFilesResponse
	err := retry.Do(func() error {
		start := time.Now()
		requestEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCkgRequest, token.UserID, "", string(env.GetSourceProduct()))
		defer requestEvent.Report(ctx, false)
		requestEvent.AddTeaParam(ckg_metrics.TeaParamIndexCaller, caller)
		r, err := c.newRequest(ctx, token).
			SetResult(&SplitFilesResponse{}).
			SetBody(req).
			Post(fmt.Sprintf("/api/ide/v1/knowledgebase/files/split"))
		if err != nil {
			recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/files/split")
			return &util.HTTPError{
				Code:       r.StatusCode(),
				Message:    err.Error(),
				Extra:      "failed post SplitFiles",
				StatusCode: r.StatusCode(),
			}
		}
		if time.Since(start).Milliseconds() > 1000 {
			logs.CtxWarn(ctx, "slow SplitFiles request, log id: %s", r.Header().Get("X-Tt-Logid"))
		}
		recordApiInformation(ctx, r, requestEvent, "/api/ide/v1/knowledgebase/files/split")
		if r.StatusCode() != http.StatusOK {
			return &util.HTTPError{
				Code:       r.StatusCode(),
				Message:    r.String(),
				Extra:      "failed post SplitFiles",
				StatusCode: r.StatusCode(),
			}
		}
		resp = r.Result().(*SplitFilesResponse)
		if len(resp.FailedFiles) != 0 {
			err = errors.Errorf("resp contains %d failed files, %v", len(resp.FailedFiles), resp.FailedFiles)
			recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/files/split")
		}
		return nil
	},
		retry.Attempts(3),
		retry.DelayType(retry.BackOffDelay),
		retry.Delay(time.Second),
		retry.MaxDelay(time.Second*20),
		retry.RetryIf(util.JudgeNewWorkError))
	return resp, err
}

func (c *client) Embedding(ctx context.Context, token *model.TokenValue, caller string, req *EmbeddingRequest) (*EmbeddingResponse, error) {
	req.DatastoreName = "default-latest"
	var resp *EmbeddingResponse
	err := retry.Do(func() error {
		start := time.Now()
		requestEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCkgRequest, token.UserID, "", string(env.GetSourceProduct()))
		defer requestEvent.Report(ctx, false)
		requestEvent.AddTeaParam(ckg_metrics.TeaParamIndexCaller, caller)
		r, err := c.newRequest(ctx, token).
			SetResult(&EmbeddingResponse{}).
			SetBody(req).
			Post(fmt.Sprintf("/api/ide/v1/knowledgebase/embedding"))
		if err != nil {
			recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/embedding")
			return &util.HTTPError{
				Code:       r.StatusCode(),
				Message:    err.Error(),
				Extra:      "failed post Embedding",
				StatusCode: r.StatusCode(),
			}
		}
		if time.Since(start).Milliseconds() > 1000 {
			logs.CtxWarn(ctx, "slow Embedding request, log id: %s", r.Header().Get("X-Tt-Logid"))
		}
		recordApiInformation(ctx, r, requestEvent, "/api/ide/v1/knowledgebase/embedding")
		if r.StatusCode() != http.StatusOK {
			err = &util.HTTPError{
				Code:       r.StatusCode(),
				Message:    r.String(),
				Extra:      "failed post Embedding",
				StatusCode: r.StatusCode(),
			}
			recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/embedding")
			return err
		}
		resp = r.Result().(*EmbeddingResponse)
		if len(resp.Embeddings) != len(req.EmbeddingContents) {
			err = &util.HTTPError{
				Code:       http.StatusExpectationFailed,
				Message:    "length of embeddings in response not equal to length of embedding contents in request",
				Extra:      "failed post Embedding",
				StatusCode: http.StatusExpectationFailed,
			}
			recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/embedding")
			return err
		}
		return nil
	},
		retry.Attempts(3),
		retry.DelayType(retry.BackOffDelay),
		retry.Delay(time.Second),
		retry.MaxDelay(time.Second*20),
		retry.RetryIf(util.JudgeNewWorkError))
	return resp, err
}

func (c *client) SplitFilesV2(ctx context.Context, token *model.TokenValue, caller string, rel string, req *SplitFilesRequestV2) (*SplitFilesResponse, error) {
	if env.TokenIsOutdated(ctx, token) {
		return nil, bizErr.ErrTokenOutOfDate
	}

	requestEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCkgRequest, token.UserID, "", string(env.GetSourceProduct()))
	resp, err := c.newRequest(ctx, token).SetBody(map[string]interface{}{
		"embedding_model": req.EmbeddingModel,
		"chunking_method": req.ChunkingMethod,
		"files":           req.Files,
	}).
		SetDoNotParseResponse(true).
		Post(fmt.Sprintf("/api/ide/v1/knowledgebase/files/split_files"))
	if err != nil {
		recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/files/split_files")
		requestEvent.Report(ctx, false)
		return nil, errors.WithMessagef(err, "failed split files")
	}
	requestEvent.AddTeaParam(ckg_metrics.TeaParamApi, "/api/ide/v1/knowledgebase/files/split_files")
	requestEvent.AddTeaParam(ckg_metrics.TeaParamHttpCode, resp.StatusCode())
	requestEvent.AddTeaParam(ckg_metrics.TeaParamLogID, resp.Header().Get("X-Tt-Logid"))

	if resp.StatusCode() != http.StatusOK {
		requestEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		requestEvent.Report(ctx, false)
		return nil, &util.HTTPError{
			Code:       resp.StatusCode(),
			Message:    resp.String(),
			Extra:      "failed split files",
			StatusCode: resp.StatusCode(),
		}
	}

	resultCh := make(chan []*SegmentWithEmbeddingAndFile)
	errorCh := make(chan error)
	util.SafeGo(ctx, func() {
		defer func() {
			close(resultCh)
			_ = resp.RawBody().Close()
			requestEvent.Report(ctx, false)
		}()
		scanner := bufio.NewScanner(resp.RawBody())
		buf := make([]byte, 0, 65*1024)
		scanner.Buffer(buf, 16*1024*1024)
		var eventType string
		for scanner.Scan() {
			data := scanner.Bytes()
			if len(data) == 0 {
				continue
			}
			switch {
			case bytes.HasPrefix(data, sseEventPrefix):
				eventType = string(trimPrefixAndSpace(data, sseEventPrefix))
			case bytes.HasPrefix(data, sseDataPrefix):
				switch eventType {
				case eventTypeSegments:
					segments := new([]*SegmentWithEmbeddingAndFile)
					if err := json.Unmarshal(trimPrefixAndSpace(data, sseDataPrefix), segments); err != nil {
						recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/files/split_files")
						errorCh <- err
						return
					}
					resultCh <- *segments
				default:
					logs.CtxWarn(ctx, "unexpected event type for SplitFilesV2 sse, type: %s", eventType)
				}
			default:
				// id & retry is ignored
			}
		}

		if scannerErr := scanner.Err(); scannerErr != nil {
			logs.CtxError(ctx, "failed to read SSE stream: %v", scannerErr)
			recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/files/split_files")
			errorCh <- scannerErr
		}
	})

	splitFilesResp := &SplitFilesResponse{Results: make(map[string]*SplitFileResult_), FailedFiles: make(map[string]string)}
	splitFileRes := &SplitFileResult_{SegmentsWithEmbedding: map[string]*SegmentWithEmbedding{}}
	splitFilesResp.Results[rel] = splitFileRes
	tick := time.NewTicker(3 * time.Second)
	defer tick.Stop()
loop:
	for {
		select {
		case segment, ok := <-resultCh:
			if !ok {
				break loop
			}
			for _, segWithFile := range segment {
				if segWithFile == nil || segWithFile.SegmentWithEmbedding == nil {
					continue
				}
				if segWithFile.File != rel {
					continue
				}
				splitFileRes.SegmentsWithEmbedding[segWithFile.SegmentWithEmbedding.SegmentUniqueID] = segWithFile.SegmentWithEmbedding
			}
			tick.Reset(3 * time.Second)
		case err, _ = <-errorCh:
			logs.CtxError(ctx, "[splitFilesV2] err: %+v", err)
			break loop
		case <-tick.C:
			logs.CtxError(ctx, "[splitFilesV2] read buffer timeout")
			break loop
		}
	}
	return splitFilesResp, nil
}

func (c *client) EmbeddingV2(ctx context.Context, token *model.TokenValue, caller string, req *EmbeddingRequestV2) (*EmbeddingResponse, error) {
	var resp *EmbeddingResponse
	err := retry.Do(func() error {
		start := time.Now()
		requestEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCkgRequest, token.UserID, "", string(env.GetSourceProduct()))
		defer requestEvent.Report(ctx, false)
		requestEvent.AddTeaParam(ckg_metrics.TeaParamIndexCaller, caller)
		r, err := c.newRequest(ctx, token).
			SetResult(&EmbeddingResponse{}).
			SetBody(req).
			Post(fmt.Sprintf("/api/ide/v1/knowledgebase/embedding_v2"))
		if err != nil {
			recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/embedding_v2")
			return &util.HTTPError{
				Code:       r.StatusCode(),
				Message:    err.Error(),
				Extra:      "failed post Embedding",
				StatusCode: r.StatusCode(),
			}
		}
		if time.Since(start).Milliseconds() > 1000 {
			logs.CtxWarn(ctx, "slow Embedding request, log id: %s", r.Header().Get("X-Tt-Logid"))
		}
		recordApiInformation(ctx, r, requestEvent, "/api/ide/v1/knowledgebase/embedding_v2")
		if r.StatusCode() != http.StatusOK {
			err = &util.HTTPError{
				Code:       r.StatusCode(),
				Message:    r.String(),
				Extra:      "failed post Embedding",
				StatusCode: r.StatusCode(),
			}
			recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/embedding_v2")
			return err
		}
		resp = r.Result().(*EmbeddingResponse)
		if len(resp.Embeddings) != len(req.EmbeddingContents) {
			err = &util.HTTPError{
				Code:       http.StatusExpectationFailed,
				Message:    "length of embeddings in response not equal to length of embedding contents in request",
				Extra:      "failed post Embedding",
				StatusCode: http.StatusExpectationFailed,
			}
			recordApiError(err, requestEvent, "/api/ide/v1/knowledgebase/embedding_v2")
			return err
		}
		return nil
	},
		retry.Attempts(3),
		retry.DelayType(retry.BackOffDelay),
		retry.Delay(time.Second),
		retry.MaxDelay(time.Second*20),
		retry.RetryIf(util.JudgeNewWorkError))
	return resp, err
}

func (c *client) newRequest(ctx context.Context, token *model.TokenValue) *resty.Request {
	req := c.cli.R().SetContext(ctx).SetHeader("Authorization", token.Scheme+" "+token.Token)
	req = req.SetHeader("X-Tt-Logid", ctxvalues.LogIDDefault(ctx))

	ppe := os.Getenv("CKG_PPE")
	if ppe != "" {
		req = req.SetHeader("x-tt-env", ppe).SetHeader("x-use-ppe", "1")
		logs.CtxDebug(ctx, "request using ppe: %s", ppe)
	}

	// 风控相关 header
	if env.GetDeviceCPU() != "" {
		req = req.SetHeader("x-device-cpu", env.GetDeviceCPU())
	}
	if env.GetDeviceID() != "" {
		req = req.SetHeader("x-device-id", env.GetDeviceID())
	}
	if env.GetDeviceBrand() != "" {
		req = req.SetHeader("x-device-brand", env.GetDeviceBrand())
	}
	if env.GetDeviceType() != "" {
		req = req.SetHeader("x-device-type", env.GetDeviceType())
	}
	if env.GetOSVersion() != "" {
		req = req.SetHeader("x-os-version", env.GetOSVersion())
	}
	if env.GetMachineID() != "" {
		req = req.SetHeader("x-machine-id", env.GetMachineID())
	}

	return req
}
