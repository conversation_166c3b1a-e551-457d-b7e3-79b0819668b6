import {SyntaxNode} from "web-tree-sitter";
import {LanguageIdEnum} from "../../share";
import {Range} from "vscode-languageserver-textdocument";
import {TreeSitter<PERSON>andler} from "./common";

export class <PERSON>reeSitterHandler extends TreeSitterHandler {
  static INSTANCE = new CTreeSitterHandler();

  languageId = LanguageIdEnum.C;

  funcTypes = ["function_definition"];
  typeTypes = ["struct_specifier", "union_specifier", "enum_specifier"];
  fileTypes = ["translation_unit"];
  varTypes = ["declaration", "field_declaration", "enumerator"];

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
    super();
  }

  getParentNodeForKeywordSearch(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, [
      ...this.funcTypes,
      ...this.typeTypes,
      ...this.fileTypes,
    ]);
  }

  getFuncParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.funcTypes);
  }

  getTypeParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.typeTypes);
  }

  getVarParentNode(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.varTypes);
  }

  getMethodInRange(rootNode: SyntaxNode, range: Range, startIndex: number, endIndex: number, completelyIncluded: boolean): SyntaxNode[] {
    return this.getElementInRangeWithTypeList(rootNode, range, startIndex, endIndex, completelyIncluded, this.funcTypes);
  }

  getImportInfo(rootNode: SyntaxNode): any {
    // TODO
    return null;
  }
}