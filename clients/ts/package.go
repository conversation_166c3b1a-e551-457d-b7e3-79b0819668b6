package ts

import (
	"ide/ckg/codekg/components/logs"
	_ "embed"
	"encoding/json"
)

const (
	VersionKey = "version"
)

var Version = ""

//go:embed package.json
var PackageJson string

type Package struct {
	Version string `json:"version"`
}

func MustNewPackage() *Package {
	var data Package
	err := json.Unmarshal([]byte(PackageJson), &data)
	if err != nil {
		logs.Error("NewPackage Unmarshal err is %v", err)
		panic("MustNewPackage Unmarshal err")
	}
	logs.Info("ckg version is %v", data.Version)
	Version = data.Version
	return &data
}

func (v *Package) GetVersionCode() string {
	return v.Version
}
