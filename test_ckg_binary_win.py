# 运行windows自动化测试用例脚本
import subprocess
import os

# Configure Git to use SSH for private repositories
subprocess.run(['git', 'config', '--global', 'url.ssh://******************/.insteadOf', 'https://code.byted.org/'], check=True)

# Configure Git user and email
subprocess.run(['git', 'config', '--global', 'user.name', 'zhuhang.leon'], check=True)
subprocess.run(['git', 'config', '--global', 'user.email', '<EMAIL>'], check=True)

# Download and write SSH key
try:
  subprocess.run([
    'curl', '-s', 'https://tosv.byted.org/obj/codegraph/ckg_key',
    '-o', os.path.expanduser('~/.ssh/id_rsa')
  ], check=True)
  # Set correct permissions for SSH key
  os.chmod(os.path.expanduser('~/.ssh/id_rsa'), 0o600)
except subprocess.CalledProcessError as e:
  print(f"Failed to download SSH key: {e}")
  raise

# check git user
subprocess.run(['ssh', '-T', 'code.byted.org'], check=True)

commands = [
    ["python", "--version"],
    ["python", "./ckg_auto_test/main.py"]
]

for command in commands:
    result = subprocess.run(command, check=True)
    if result.returncode != 0:
        print(f"Command {command} failed")