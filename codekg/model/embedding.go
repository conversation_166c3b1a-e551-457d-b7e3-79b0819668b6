package model

import (
	"crypto/sha256"
	"encoding/binary"
	"fmt"
	"github.com/samber/lo"
	"path/filepath"
)

// EmbeddingData works the same as URIData,
// as a pack of inputs for embeddingStorage APIs.
type EmbeddingData struct {
	Embeddings []*EmbeddingDocument
	Relations  []*VectorToEntity
}

func ToPorcelainMetadata(entityId, fileRelPath string, entityType EntityType) map[string]any {
	metadata := make(map[string]any)
	metadata[string(EMK_EntityID)] = entityId // 之后 SQLiteVecStorageV2 会忽略这个字段，不写入到 db 中
	metadata[string(EMK_RelPath)] = filepath.Dir(fileRelPath)
	metadata[string(EMK_Ext)] = filepath.Ext(fileRelPath)
	metadata[string(EMK_EntityType)] = string(entityType)
	return metadata
}

type VectorToEntity struct {
	VectorID string
	EntityID string
}

type BatchEmbeddingMsg struct {
	FileRelPath      string
	EntityId         string
	EntityType       EntityType
	EmbeddingContent string
	RetryNum         int
}

func (b *BatchEmbeddingMsg) ToEmbeddingData(v []float64) *EmbeddingData {
	porcelainMetadata := ToPorcelainMetadata(b.EntityId, b.FileRelPath, b.EntityType)
	v32 := lo.Map(v, func(num float64, _ int) float32 { return float32(num) })
	doc := NewEmbeddingDocument(b.EmbeddingContent, v32, porcelainMetadata)
	relation := &VectorToEntity{EntityID: b.EntityId, VectorID: doc.ID}
	return &EmbeddingData{
		Embeddings: []*EmbeddingDocument{doc},
		Relations:  []*VectorToEntity{relation},
	}
}

type EmbeddingMetadataKey string

const (
	EMK_EmbeddingID EmbeddingMetadataKey = "embedding_id"
	EMK_EntityID    EmbeddingMetadataKey = "entity_id"
	EMK_EntityType  EmbeddingMetadataKey = "entity_type"
	EMK_RelPath     EmbeddingMetadataKey = "rel_path"
	EMK_Ext         EmbeddingMetadataKey = "ext"
	EMK_Hash_Folder EmbeddingMetadataKey = "#Folder"
	EMK_Hash_Ext    EmbeddingMetadataKey = "#Ext"
)

// EmbeddingDocument is used as input for interfaces related to embedding.
type EmbeddingDocument struct {
	ID        string
	Content   string
	Embedding *Embedding
	Metadata  map[string]any
}

func NewEmbeddingDocument(content string, embedding []float32, metadata map[string]any) *EmbeddingDocument {
	return &EmbeddingDocument{
		ID:        getEmbeddingDocumentID(content, metadata),
		Content:   content,
		Embedding: &Embedding{Embedding: embedding},
		Metadata:  metadata,
	}
}

func getEmbeddingDocumentID(content string, metadata map[string]any) string {
	data := content
	for k, v := range metadata {
		if vStr, ok := v.(string); ok {
			data += fmt.Sprintf(";%s:%s", k, vStr)
		}
	}
	hash := sha256.Sum256([]byte(data))
	return fmt.Sprintf("%d", binary.BigEndian.Uint32(hash[:]))
}

// EmbeddingQueryResult is used as output for interfaces related to embedding store.
type EmbeddingQueryResult struct {
	EmbeddingDocument
	Score float32
}

type Embedding struct {
	Embedding []float32
}
