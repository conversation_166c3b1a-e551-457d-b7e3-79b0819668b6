// Code generated by MockGen. DO NOT EDIT.
// Source: ide/ckg/codekg/components/ignore_service (interfaces: IgnoreService)
//
// Generated by this command:
//
//	mockgen -destination mock_ignore_service/mock_ignore_service.go -package mock_ignore_service . IgnoreService
//

// Package mock_ignore_service is a generated GoMock package.
package mock_ignore_service

import (
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockIgnoreService is a mock of IgnoreService interface.
type MockIgnoreService struct {
	ctrl     *gomock.Controller
	recorder *MockIgnoreServiceMockRecorder
}

// MockIgnoreServiceMockRecorder is the mock recorder for MockIgnoreService.
type MockIgnoreServiceMockRecorder struct {
	mock *MockIgnoreService
}

// NewMockIgnoreService creates a new mock instance.
func NewMockIgnoreService(ctrl *gomock.Controller) *MockIgnoreService {
	mock := &MockIgnoreService{ctrl: ctrl}
	mock.recorder = &MockIgnoreServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIgnoreService) EXPECT() *MockIgnoreServiceMockRecorder {
	return m.recorder
}

// AddProject mocks base method.
func (m *MockIgnoreService) AddProject(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddProject", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddProject indicates an expected call of AddProject.
func (mr *MockIgnoreServiceMockRecorder) AddProject(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddProject", reflect.TypeOf((*MockIgnoreService)(nil).AddProject), arg0)
}

// CreateOrChangeURI mocks base method.
func (m *MockIgnoreService) CreateOrChangeURI(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrChangeURI", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOrChangeURI indicates an expected call of CreateOrChangeURI.
func (mr *MockIgnoreServiceMockRecorder) CreateOrChangeURI(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrChangeURI", reflect.TypeOf((*MockIgnoreService)(nil).CreateOrChangeURI), arg0)
}

// DeleteURI mocks base method.
func (m *MockIgnoreService) DeleteURI(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteURI", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteURI indicates an expected call of DeleteURI.
func (mr *MockIgnoreServiceMockRecorder) DeleteURI(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteURI", reflect.TypeOf((*MockIgnoreService)(nil).DeleteURI), arg0)
}

// IsIgnored mocks base method.
func (m *MockIgnoreService) IsIgnored(arg0 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsIgnored", arg0)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsIgnored indicates an expected call of IsIgnored.
func (mr *MockIgnoreServiceMockRecorder) IsIgnored(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsIgnored", reflect.TypeOf((*MockIgnoreService)(nil).IsIgnored), arg0)
}
