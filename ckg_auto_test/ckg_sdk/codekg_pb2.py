# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: codekg.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x63odekg.proto\x12\x08protocol\"\x07\n\x05\x45mpty\"\'\n\x05\x45rror\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\r\n\x05stack\x18\x02 \x01(\t\"5\n\x13RefreshTokenRequest\x12\r\n\x05<PERSON>en\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\n \x01(\t\"T\n\x14RefreshTokenResponse\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\"H\n\x07Project\x12\x12\n\nproject_id\x18\x01 \x01(\t\x12\x14\n\x0cstorage_path\x18\x02 \x01(\t\x12\x13\n\x0bignore_file\x18\x03 \x01(\t\"s\n\x0bInitRequest\x12\x13\n\x0bproject_ids\x18\x01 \x03(\t\x12\x19\n\x11ignore_file_limit\x18\x02 \x01(\x08\x12#\n\x08projects\x18\x03 \x03(\x0b\x32\x11.protocol.Project\x12\x0f\n\x07user_id\x18\n \x01(\t\"L\n\x0cInitResponse\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\"Q\n\x0eVirtualProject\x12\x12\n\nproject_id\x18\x01 \x01(\t\x12\x0b\n\x03uri\x18\x02 \x01(\t\x12\x1e\n\x16relative_globs_to_load\x18\x03 \x03(\t\"u\n\x1aInitVirtualProjectsRequest\x12*\n\x08projects\x18\x01 \x03(\x0b\x32\x18.protocol.VirtualProject\x12\x1a\n\x12load_files_from_fs\x18\x02 \x01(\x08\x12\x0f\n\x07user_id\x18\n \x01(\t\"[\n\x1bInitVirtualProjectsResponse\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\"J\n\x08\x44ocument\x12\x0b\n\x03uri\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x03 \x01(\t\x12\x12\n\nproject_id\x18\x04 \x01(\t\"\\\n\x0f\x44ocumentRequest\x12\x12\n\nfile_paths\x18\x01 \x03(\t\x12%\n\tdocuments\x18\x02 \x03(\x0b\x32\x12.protocol.Document\x12\x0e\n\x06userID\x18\n \x01(\t\"\xbb\x01\n\x10\x44ocumentResponse\x12\x39\n\x08\x65rr_msgs\x18\x01 \x03(\x0b\x32\'.protocol.DocumentResponse.ErrMsgsEntry\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\x1a.\n\x0c\x45rrMsgsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"(\n\x12\x44\x65leteIndexRequest\x12\x12\n\nproject_id\x18\x01 \x01(\t\"d\n\x13\x44\x65leteIndexResponse\x12\x0f\n\x07succeed\x18\x01 \x01(\x08\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\"\xfe\x01\n\x11\x43urrentEditorInfo\x12\x11\n\tfile_path\x18\x01 \x01(\t\x12\x12\n\nproject_id\x18\x02 \x01(\t\x12\x13\n\x0b\x63ursor_line\x18\n \x01(\x05\x12*\n\x11select_code_range\x18\x14 \x01(\x0b\x32\x0f.protocol.Range\x12\x1b\n\x13select_code_content\x18\x15 \x01(\t\x12+\n\x12visible_code_range\x18\x1e \x01(\x0b\x32\x0f.protocol.Range\x12\x1c\n\x14visible_code_content\x18\x1f \x01(\t\x12\x19\n\x11user_file_content\x18( \x01(\t\"\x86\x02\n\x15RetrieveEntityRequest\x12\x13\n\x0bproject_ids\x18\x01 \x03(\t\x12\x14\n\x0cuser_message\x18\x02 \x01(\t\x12\x30\n\x0b\x65\x64itor_info\x18\x03 \x01(\x0b\x32\x1b.protocol.CurrentEditorInfo\x12\x31\n\x13\x65xpect_entity_types\x18\x04 \x03(\x0e\x32\x14.protocol.EntityType\x12\x12\n\nentity_num\x18\x05 \x01(\x05\x12\x0e\n\x06intent\x18\x06 \x01(\t\x12\x14\n\x0c\x66older_paths\x18\x07 \x03(\t\x12\x0f\n\x07user_id\x18\n \x01(\t\x12\x12\n\nsession_id\x18\x14 \x01(\t\"/\n\x06\x45ntity\x12\x12\n\nproject_id\x18\x01 \x01(\t\x12\x11\n\tentity_id\x18\x02 \x01(\t\"\x89\x02\n\x16RetrieveEntityResponse\x12\x32\n\x18\x65ntities_by_user_message\x18\x01 \x03(\x0b\x32\x10.protocol.Entity\x12\x1e\n\x16query_embedding_models\x18\x02 \x03(\t\x12\x1f\n\x17vector_embedding_models\x18\x03 \x03(\t\x12\x1f\n\x17\x65ntity_chunking_methods\x18\x04 \x03(\t\x12\x1b\n\x13\x65mpty_recall_reason\x18\n \x01(\t\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\"\x90\x01\n\x17RetrieveRelationRequest\x12\x32\n\x18\x65ntities_by_user_message\x18\x01 \x03(\x0b\x32\x10.protocol.Entity\x12\x30\n\x0b\x65\x64itor_info\x18\x02 \x01(\x0b\x32\x1b.protocol.CurrentEditorInfo\x12\x0f\n\x07user_id\x18\n \x01(\t\"\xa3\x01\n\x11\x43odeChunkVariable\x12\x1c\n\tfile_path\x18\x01 \x01(\tR\tfile_path\x12\x1e\n\nstart_line\x18\x02 \x01(\x05R\nstart_line\x12\x1a\n\x08\x65nd_line\x18\x03 \x01(\x05R\x08\x65nd_line\x12\x18\n\x07\x63ontent\x18\x04 \x01(\tR\x07\x63ontent\x12\x1a\n\x08provider\x18\x1e \x01(\tR\x08provider\"\x9e\x01\n\x0cTextVariable\x12\x1c\n\tfile_path\x18\x01 \x01(\tR\tfile_path\x12\x1e\n\nstart_line\x18\x02 \x01(\x05R\nstart_line\x12\x1a\n\x08\x65nd_line\x18\x03 \x01(\x05R\x08\x65nd_line\x12\x18\n\x07\x63ontent\x18\x04 \x01(\tR\x07\x63ontent\x12\x1a\n\x08provider\x18\x1e \x01(\tR\x08provider\"H\n\x0eUsefulFileInfo\x12\x1c\n\tfile_path\x18\x01 \x01(\tR\tfile_path\x12\x18\n\x07\x63ontent\x18\x02 \x01(\tR\x07\x63ontent\"\xaa\x01\n\x0e\x46olderVariable\x12\x1c\n\tfile_path\x18\x01 \x01(\tR\tfile_path\x12 \n\x0b\x66older_tree\x18\x02 \x01(\tR\x0b\x66older_tree\x12<\n\x0cuseful_files\x18\x03 \x03(\x0b\x32\x18.protocol.UsefulFileInfoR\x0cuseful_files\x12\x1a\n\x08provider\x18\x1e \x01(\tR\x08provider\"|\n\x0c\x46ileVariable\x12\x1c\n\tfile_path\x18\x01 \x01(\tR\tfile_path\x12\x18\n\x07\x63ontent\x18\x02 \x01(\tR\x07\x63ontent\x12\x18\n\x07\x63omment\x18\x03 \x01(\tR\x07\x63omment\x12\x1a\n\x08provider\x18\x1e \x01(\tR\x08provider\"\xa6\x01\n\x14\x46ileTopLevelVariable\x12\x1c\n\tfile_path\x18\x01 \x01(\tR\tfile_path\x12\x1e\n\nstart_line\x18\x02 \x01(\x05R\nstart_line\x12\x1a\n\x08\x65nd_line\x18\x03 \x01(\x05R\x08\x65nd_line\x12\x18\n\x07\x63ontent\x18\x04 \x01(\tR\x07\x63ontent\x12\x1a\n\x08provider\x18\x1e \x01(\tR\x08provider\"\"\n\x06Member\x12\x18\n\x07\x63ontent\x18\x01 \x01(\tR\x07\x63ontent\"\xe6\x02\n\rClassVariable\x12\x1c\n\tfile_path\x18\x01 \x01(\tR\tfile_path\x12\x1e\n\nstart_line\x18\x02 \x01(\x05R\nstart_line\x12\x1a\n\x08\x65nd_line\x18\x03 \x01(\x05R\x08\x65nd_line\x12\x12\n\x04name\x18\x04 \x01(\tR\x04name\x12\x18\n\x07\x63omment\x18\x05 \x01(\tR\x07\x63omment\x12\x18\n\x07\x63ontent\x18\x06 \x01(\tR\x07\x63ontent\x12)\n\x07members\x18\x07 \x03(\x0b\x32\x10.protocol.MemberR\x06member\x12.\n\x07methods\x18\x08 \x03(\x0b\x32\x14.protocol.MethodInfoR\x07methods\x12<\n\x0etest_functions\x18\t \x03(\x0b\x32\x14.protocol.MethodInfoR\x0etest_functions\x12\x1a\n\x08provider\x18\x1e \x01(\tR\x08provider\"\xa7\x01\n\x15\x43lassTopLevelVariable\x12\x1c\n\tfile_path\x18\x01 \x01(\tR\tfile_path\x12\x1e\n\nstart_line\x18\x02 \x01(\x05R\nstart_line\x12\x1a\n\x08\x65nd_line\x18\x03 \x01(\x05R\x08\x65nd_line\x12\x18\n\x07\x63ontent\x18\x04 \x01(\tR\x07\x63ontent\x12\x1a\n\x08provider\x18\x1e \x01(\tR\x08provider\"\xcc\x01\n\nMethodInfo\x12\x1c\n\tfile_path\x18\x01 \x01(\tR\tfile_path\x12\x1e\n\nstart_line\x18\x02 \x01(\x05R\nstart_line\x12\x1a\n\x08\x65nd_line\x18\x03 \x01(\x05R\x08\x65nd_line\x12\x12\n\x04name\x18\x04 \x01(\tR\x04name\x12\x1c\n\tsignature\x18\x05 \x01(\tR\tsignature\x12\x18\n\x07\x63omment\x18\x06 \x01(\tR\x07\x63omment\x12\x18\n\x07\x63ontent\x18\x07 \x01(\tR\x07\x63ontent\"\x8a\x03\n\x0eMethodVariable\x12\x1c\n\tfile_path\x18\x01 \x01(\tR\tfile_path\x12\x1e\n\nstart_line\x18\x02 \x01(\x05R\nstart_line\x12\x1a\n\x08\x65nd_line\x18\x03 \x01(\x05R\x08\x65nd_line\x12\x12\n\x04name\x18\x04 \x01(\tR\x04name\x12\x1c\n\tsignature\x18\x05 \x01(\tR\tsignature\x12\x18\n\x07\x63omment\x18\x06 \x01(\tR\x07\x63omment\x12\x18\n\x07\x63ontent\x18\x07 \x01(\tR\x07\x63ontent\x12.\n\x07\x63\x61llers\x18\x08 \x03(\x0b\x32\x14.protocol.MethodInfoR\x07\x63\x61llers\x12.\n\x07\x63\x61llees\x18\t \x03(\x0b\x32\x14.protocol.MethodInfoR\x07\x63\x61llees\x12<\n\x0etest_functions\x18\n \x03(\x0b\x32\x14.protocol.MethodInfoR\x0etest_functions\x12\x1a\n\x08provider\x18\x1e \x01(\tR\x08provider\"\xc5\x03\n\x08Variable\x12;\n\ncode_chunk\x18\x01 \x01(\x0b\x32\x1b.protocol.CodeChunkVariableR\ncode_chunk\x12*\n\x04text\x18\x02 \x01(\x0b\x32\x16.protocol.TextVariableR\x04text\x12\x30\n\x06\x66older\x18\x03 \x01(\x0b\x32\x18.protocol.FolderVariableR\x06\x66older\x12*\n\x04\x66ile\x18\x04 \x01(\x0b\x32\x16.protocol.FileVariableR\x04\x66ile\x12\x46\n\x0e\x66ile_top_level\x18\x05 \x01(\x0b\x32\x1e.protocol.FileTopLevelVariableR\x0e\x66ile_top_level\x12-\n\x05\x63lass\x18\x06 \x01(\x0b\x32\x17.protocol.ClassVariableR\x05\x63lass\x12I\n\x0f\x63lass_top_level\x18\x07 \x01(\x0b\x32\x1f.protocol.ClassTopLevelVariableR\x0f\x63lass_top_level\x12\x30\n\x06method\x18\x08 \x01(\x0b\x32\x18.protocol.MethodVariableR\x06method\"\x98\x01\n\x12SelectedMethodInfo\x12\x1e\n\nstart_line\x18\x01 \x01(\x05R\nstart_line\x12\x1a\n\x08\x65nd_line\x18\x02 \x01(\x05R\x08\x65nd_line\x12\x12\n\x04name\x18\x03 \x01(\tR\x04name\x12\x18\n\x07\x63omment\x18\x04 \x01(\tR\x07\x63omment\x12\x18\n\x07\x63ontent\x18\x05 \x01(\tR\x07\x63ontent\"\xe1\x01\n\x11SelectedClassInfo\x12\x1e\n\nstart_line\x18\x01 \x01(\x05R\nstart_line\x12\x1a\n\x08\x65nd_line\x18\x02 \x01(\x05R\x08\x65nd_line\x12\x12\n\x04name\x18\x03 \x01(\tR\x04name\x12\x18\n\x07\x63omment\x18\x04 \x01(\tR\x07\x63omment\x12\x18\n\x07\x63ontent\x18\x05 \x01(\tR\x07\x63ontent\x12H\n\x10selected_methods\x18\x06 \x03(\x0b\x32\x1c.protocol.SelectedMethodInfoR\x10selected_methods\"\xa8\x01\n\x13\x43urrentEditorEntity\x12G\n\x10selected_classes\x18\x01 \x03(\x0b\x32\x1b.protocol.SelectedClassInfoR\x10selected_classes\x12H\n\x10selected_methods\x18\x02 \x03(\x0b\x32\x1c.protocol.SelectedMethodInfoR\x10selected_methods\"C\n\x05Range\x12\x1e\n\nstart_line\x18\x01 \x01(\x05R\nstart_line\x12\x1a\n\x08\x65nd_line\x18\x02 \x01(\x05R\x08\x65nd_line\"\x96\x01\n\x0cRefClassInfo\x12\x1c\n\tfile_path\x18\x01 \x01(\tR\tfile_path\x12\x1e\n\nstart_line\x18\x02 \x01(\tR\nstart_line\x12\x1a\n\x08\x65nd_line\x18\x03 \x01(\tR\x08\x65nd_line\x12\x12\n\x04name\x18\x04 \x01(\tR\x04name\x12\x18\n\x07\x63ontent\x18\x05 \x01(\tR\x07\x63ontent\"\x95\x01\n\x0bRefTypeInfo\x12\x1c\n\tfile_path\x18\x01 \x01(\tR\tfile_path\x12\x1e\n\nstart_line\x18\x02 \x01(\tR\nstart_line\x12\x1a\n\x08\x65nd_line\x18\x03 \x01(\tR\x08\x65nd_line\x12\x12\n\x04name\x18\x04 \x01(\tR\x04name\x12\x18\n\x07\x63ontent\x18\x05 \x01(\tR\x07\x63ontent\"\xb3\x03\n\x15\x43urrentEditorVariable\x12\x1c\n\tfile_path\x18\x01 \x01(\tR\tfile_path\x12\x18\n\x07\x63ontent\x18\x02 \x01(\tR\x07\x63ontent\x12 \n\x0b\x63ursor_line\x18\x03 \x01(\x05R\x0b\x63ursor_line\x12\x33\n\x0cselect_range\x18\x04 \x01(\x0b\x32\x0f.protocol.RangeR\x0cselect_range\x12\x35\n\rvisible_range\x18\x05 \x01(\x0b\x32\x0f.protocol.RangeR\rvisible_range\x12\x35\n\x06\x65ntity\x18\x06 \x01(\x0b\x32\x1d.protocol.CurrentEditorEntityR\x06\x65ntity\x12.\n\x07\x63\x61llees\x18\x07 \x03(\x0b\x32\x14.protocol.MethodInfoR\x07\x63\x61llees\x12\x38\n\x0bref_classes\x18\x08 \x03(\x0b\x32\x16.protocol.RefClassInfoR\x0bref_classes\x12\x33\n\tref_types\x18\t \x03(\x0b\x32\x15.protocol.RefTypeInfoR\tref_types\"_\n\tReference\x12\x11\n\tfile_path\x18\x01 \x01(\t\x12\x12\n\nstart_line\x18\x02 \x01(\x05\x12\x10\n\x08\x65nd_line\x18\x03 \x01(\x05\x12\x0b\n\x03uri\x18\x04 \x01(\t\x12\x0c\n\x04name\x18\x05 \x01(\t\"\xb1\x02\n\x18RetrieveRelationResponse\x12G\n\x0e\x63urrent_editor\x18\x01 \x01(\x0b\x32\x1f.protocol.CurrentEditorVariableR\x0e\x63urrent_editor\x12<\n\tvariables\x18\x02 \x03(\x0b\x32\x12.protocol.VariableR\x15user_message_entities\x12\'\n\nreferences\x18\n \x03(\x0b\x32\x13.protocol.Reference\x12\x13\n\x0bjson_result\x18\x32 \x01(\t\x12\x12\n\nerr_metric\x18\x33 \x01(\t\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\",\n\x15GetBuildStatusRequest\x12\x13\n\x0bproject_ids\x18\x01 \x03(\t\"W\n\x13\x44ocumentBuildStatus\x12%\n\x06status\x18\x01 \x01(\x0e\x32\x15.protocol.BuildStatus\x12\x0b\n\x03uri\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\"\xf1\x01\n\x12ProjectBuildStatus\x12%\n\x06status\x18\x01 \x01(\x0e\x32\x15.protocol.BuildStatus\x12\x10\n\x08progress\x18\x02 \x01(\x02\x12\x1b\n\x13success_index_files\x18\x03 \x01(\x03\x12\x1a\n\x12\x66\x61iled_index_files\x18\x04 \x01(\x03\x12\x19\n\x11total_index_files\x18\x05 \x01(\x03\x12\x15\n\rempty_project\x18\x06 \x01(\x08\x12\x37\n\x10\x64ocuments_status\x18\x07 \x03(\x0b\x32\x1d.protocol.DocumentBuildStatus\"\xe1\x01\n\x16GetBuildStatusResponse\x12<\n\x06status\x18\x01 \x03(\x0b\x32,.protocol.GetBuildStatusResponse.StatusEntry\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\x1aK\n\x0bStatusEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12+\n\x05value\x18\x02 \x01(\x0b\x32\x1c.protocol.ProjectBuildStatus:\x02\x38\x01\"5\n\x1eGetDocumentsIndexStatusRequest\x12\x13\n\x0bproject_ids\x18\x01 \x03(\t\"}\n\x1bProjectDocumentsIndexStatus\x12%\n\x06status\x18\x01 \x01(\x0e\x32\x15.protocol.BuildStatus\x12\x37\n\x10\x64ocuments_status\x18\x07 \x03(\x0b\x32\x1d.protocol.DocumentIndexStatus\"W\n\x13\x44ocumentIndexStatus\x12%\n\x06status\x18\x01 \x01(\x0e\x32\x15.protocol.BuildStatus\x12\x0b\n\x03uri\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\"\x95\x02\n\x1fGetDocumentsIndexStatusResponse\x12V\n\x0fprojects_status\x18\x01 \x03(\x0b\x32=.protocol.GetDocumentsIndexStatusResponse.ProjectsStatusEntry\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\x1a\\\n\x13ProjectsStatusEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x34\n\x05value\x18\x02 \x01(\x0b\x32%.protocol.ProjectDocumentsIndexStatus:\x02\x38\x01\"*\n\x17IsVersionMatchedRequest\x12\x0f\n\x07version\x18\x01 \x01(\t\"i\n\x18IsVersionMatchedResponse\x12\x0f\n\x07matched\x18\x01 \x01(\x08\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\"(\n\x12\x43\x61ncelIndexRequest\x12\x12\n\nproject_id\x18\x01 \x01(\t\"d\n\x13\x43\x61ncelIndexResponse\x12\x0f\n\x07succeed\x18\x01 \x01(\x08\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\"S\n\x15ImportAnalysisRequest\x12\x12\n\nproject_id\x18\x01 \x01(\t\x12\x0c\n\x04\x66ile\x18\x05 \x01(\t\x12\x18\n\x10import_statement\x18\x06 \x01(\t\"D\n\x1a\x46ilesImportAnalysisRequest\x12\x12\n\nproject_id\x18\x01 \x01(\t\x12\x12\n\nrela_files\x18\x02 \x03(\t\"\x86\x01\n\x16ImportAnalysisResponse\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\x12.\n\x06result\x18\x03 \x01(\x0b\x32\x1e.protocol.ImportAnalysisResult\"5\n\x14ImportAnalysisResult\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\t\"O\n\x06Symbol\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tfile_path\x18\x02 \x01(\t\x12\x12\n\nstart_line\x18\x03 \x01(\x05\x12\x10\n\x08\x65nd_line\x18\x04 \x01(\x05\"\x92\x01\n\x12SearchCKGDBRequest\x12\x12\n\nproject_id\x18\x01 \x01(\t\x12\x19\n\x11symbol_identifier\x18\x02 \x01(\t\x12\x14\n\x0csymbol_regex\x18\x03 \x01(\t\x12#\n\x05types\x18\x04 \x03(\x0e\x32\x14.protocol.EntityType\x12\x12\n\nentity_num\x18\x05 \x01(\x05\"\xb8\x01\n\x13SearchCKGDBResponse\x12%\n\tvariables\x18\x02 \x03(\x0b\x32\x12.protocol.Variable\x12\'\n\nreferences\x18\n \x03(\x0b\x32\x13.protocol.Reference\x12\x13\n\x0bjson_result\x18\x32 \x01(\t\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\"W\n\x0b\x43odeSnippet\x12\x11\n\tfile_path\x18\x01 \x01(\t\x12\x12\n\nstart_line\x18\x02 \x01(\x05\x12\x10\n\x08\x65nd_line\x18\x03 \x01(\x05\x12\x0f\n\x07\x63ontent\x18\x04 \x01(\t\"\xa7\x02\n\x1eRetrieveRelevantSnippetRequest\x12\x12\n\nproject_id\x18\x01 \x01(\t\x12\x12\n\nuser_query\x18\x02 \x01(\t\x12\x11\n\tworkspace\x18\n \x01(\x08\x12\x18\n\x10selected_folders\x18\x0b \x03(\t\x12\x16\n\x0eselected_files\x18\x0c \x03(\t\x12-\n\x0eselected_codes\x18\r \x03(\x0b\x32\x15.protocol.CodeSnippet\x12\x33\n\x0e\x63urrent_editor\x18\x0e \x01(\x0b\x32\x1b.protocol.CurrentEditorInfo\x12\x0f\n\x07version\x18\x1e \x01(\t\x12\x0f\n\x07user_id\x18\x64 \x01(\t\x12\x12\n\nsession_id\x18\x65 \x01(\t\"\xce\x01\n\x07Snippet\x12\x12\n\nproject_id\x18\x01 \x01(\t\x12#\n\x04type\x18\x02 \x01(\x0e\x32\x15.protocol.SnippetType\x12\x11\n\tfile_path\x18\x03 \x01(\t\x12\x12\n\nstart_line\x18\x04 \x01(\x05\x12\x10\n\x08\x65nd_line\x18\x05 \x01(\x05\x12\x0f\n\x07\x63ontent\x18\x06 \x01(\t\x12)\n\x0brecall_type\x18\x65 \x01(\x0e\x32\x14.protocol.RecallType\x12\x15\n\rckg_entity_id\x18\x66 \x01(\t\"\x83\x03\n\x1fRetrieveRelevantSnippetResponse\x12\x30\n\x15snippets_by_workspace\x18\x01 \x03(\x0b\x32\x11.protocol.Snippet\x12-\n\x12snippets_by_folder\x18\x02 \x03(\x0b\x32\x11.protocol.Snippet\x12+\n\x10snippets_by_file\x18\x03 \x03(\x0b\x32\x11.protocol.Snippet\x12+\n\x10snippets_by_code\x18\x04 \x03(\x0b\x32\x11.protocol.Snippet\x12\x33\n\x18snippets_by_current_file\x18\x05 \x03(\x0b\x32\x11.protocol.Snippet\x12\x32\n\x17snippets_by_interaction\x18\x06 \x03(\x0b\x32\x11.protocol.Snippet\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\"\xa9\x02\n\x14RerankSnippetRequest\x12\x30\n\x15snippets_by_workspace\x18\x01 \x03(\x0b\x32\x11.protocol.Snippet\x12-\n\x12snippets_by_folder\x18\x02 \x03(\x0b\x32\x11.protocol.Snippet\x12+\n\x10snippets_by_file\x18\x03 \x03(\x0b\x32\x11.protocol.Snippet\x12+\n\x10snippets_by_code\x18\x04 \x03(\x0b\x32\x11.protocol.Snippet\x12\x33\n\x18snippets_by_current_file\x18\x05 \x03(\x0b\x32\x11.protocol.Snippet\x12\r\n\x05token\x18\x64 \x01(\t\x12\x12\n\nsession_id\x18\x65 \x01(\t\"E\n\x10SnippetWithScore\x12\"\n\x07snippet\x18\x01 \x01(\x0b\x32\x11.protocol.Snippet\x12\r\n\x05score\x18\x02 \x01(\x02\"\x83\x01\n\x15RerankSnippetResponse\x12,\n\x08snippets\x18\x01 \x03(\x0b\x32\x1a.protocol.SnippetWithScore\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\"e\n\x11\x43ursorMoveRequest\x12\x12\n\nproject_id\x18\x01 \x01(\t\x12\x0c\n\x04\x66ile\x18\x02 \x01(\t\x12\x0c\n\x04line\x18\x03 \x01(\x05\x12\x0f\n\x07user_id\x18\t \x01(\t\x12\x0f\n\x07version\x18\n \x01(\t\"=\n*IsCKGEnabledForNonWorkspaceScenarioRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\"\x90\x01\n+IsCKGEnabledForNonWorkspaceScenarioResponse\x12\x12\n\nis_enabled\x18\x01 \x01(\x08\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error\"\xbe\x01\n\x0cSetUpRequest\x12\x0c\n\x04host\x18\x01 \x01(\t\x12\x0e\n\x06region\x18\x02 \x01(\t\x12\x16\n\x0esource_product\x18\x03 \x01(\t\x12\x12\n\ndevice_cpu\x18\x04 \x01(\t\x12\x11\n\tdevice_id\x18\x05 \x01(\t\x12\x12\n\nmachine_id\x18\x06 \x01(\t\x12\x14\n\x0c\x64\x65vice_brand\x18\x07 \x01(\t\x12\x13\n\x0b\x64\x65vice_type\x18\x08 \x01(\t\x12\x12\n\nos_version\x18\t \x01(\t\"M\n\rSetUpResponse\x12\x1c\n\x04\x63ode\x18\x64 \x01(\x0e\x32\x0e.protocol.Code\x12\x1e\n\x05\x65rror\x18\x65 \x01(\x0b\x32\x0f.protocol.Error*\x83\x01\n\x04\x43ode\x12\x0b\n\x07succeed\x10\x00\x12\x11\n\runknown_error\x10\x01\x12\t\n\x05panic\x10\x64\x12\x16\n\x12user_id_is_missing\x10\x65\x12\x15\n\x11\x66ile_limit_exceed\x10\x66\x12\x11\n\rinvalid_token\x10g\x12\x0e\n\nnil_client\x10h*C\n\nEntityType\x12\n\n\x06\x66older\x10\x00\x12\x08\n\x04\x66ile\x10\x01\x12\t\n\x05\x63lass\x10\x02\x12\n\n\x06method\x10\x03\x12\x08\n\x04text\x10\x04*5\n\x0b\x42uildStatus\x12\x0c\n\x08\x62uilding\x10\x00\x12\x0c\n\x08\x66inished\x10\x01\x12\n\n\x06\x66\x61iled\x10\x02*\xb6\x01\n\nRecallType\x12\x1e\n\x1arecall_type_user_specified\x10\x00\x12\x19\n\x15recall_type_embedding\x10\x01\x12\x13\n\x0frecall_type_ner\x10\x02\x12-\n)recall_type_relation_by_user_action_trace\x10\x14\x12)\n%recall_type_relation_by_git_relevance\x10\x1e*Y\n\x0bSnippetType\x12\x15\n\x11snippet_type_code\x10\x00\x12\x1c\n\x18snippet_type_folder_tree\x10\x01\x12\x15\n\x11snippet_type_file\x10\x02\x32\xa3\x0f\n\x06\x43odeKG\x12*\n\x04Ping\x12\x0f.protocol.Empty\x1a\x0f.protocol.Empty\"\x00\x12:\n\x05SetUp\x12\x16.protocol.SetUpRequest\x1a\x17.protocol.SetUpResponse\"\x00\x12\x37\n\x04Init\x12\x15.protocol.InitRequest\x1a\x16.protocol.InitResponse\"\x00\x12\x64\n\x13InitVirtualProjects\x12$.protocol.InitVirtualProjectsRequest\x1a%.protocol.InitVirtualProjectsResponse\"\x00\x12I\n\x0e\x44ocumentCreate\x12\x19.protocol.DocumentRequest\x1a\x1a.protocol.DocumentResponse\"\x00\x12I\n\x0e\x44ocumentChange\x12\x19.protocol.DocumentRequest\x1a\x1a.protocol.DocumentResponse\"\x00\x12I\n\x0e\x44ocumentDelete\x12\x19.protocol.DocumentRequest\x1a\x1a.protocol.DocumentResponse\"\x00\x12I\n\x0e\x44ocumentSelect\x12\x19.protocol.DocumentRequest\x1a\x1a.protocol.DocumentResponse\"\x00\x12<\n\nCursorMove\x12\x1b.protocol.CursorMoveRequest\x1a\x0f.protocol.Empty\"\x00\x12U\n\x0eGetBuildStatus\x12\x1f.protocol.GetBuildStatusRequest\x1a .protocol.GetBuildStatusResponse\"\x00\x12p\n\x17GetDocumentsIndexStatus\x12(.protocol.GetDocumentsIndexStatusRequest\x1a).protocol.GetDocumentsIndexStatusResponse\"\x00\x12L\n\x0b\x43\x61ncelIndex\x12\x1c.protocol.CancelIndexRequest\x1a\x1d.protocol.CancelIndexResponse\"\x00\x12L\n\x0b\x44\x65leteIndex\x12\x1c.protocol.DeleteIndexRequest\x1a\x1d.protocol.DeleteIndexResponse\"\x00\x12[\n\x10RetrieveRelation\x12!.protocol.RetrieveRelationRequest\x1a\".protocol.RetrieveRelationResponse\"\x00\x12U\n\x0eRetrieveEntity\x12\x1f.protocol.RetrieveEntityRequest\x1a .protocol.RetrieveEntityResponse\"\x00\x12p\n\x17RetrieveRelevantSnippet\x12(.protocol.RetrieveRelevantSnippetRequest\x1a).protocol.RetrieveRelevantSnippetResponse\"\x00\x12R\n\rRerankSnippet\x12\x1e.protocol.RerankSnippetRequest\x1a\x1f.protocol.RerankSnippetResponse\"\x00\x12O\n\x0cRefreshToken\x12\x1d.protocol.RefreshTokenRequest\x1a\x1e.protocol.RefreshTokenResponse\"\x00\x12[\n\x10IsVersionMatched\x12!.protocol.IsVersionMatchedRequest\x1a\".protocol.IsVersionMatchedResponse\"\x00\x12U\n\x0eImportAnalysis\x12\x1f.protocol.ImportAnalysisRequest\x1a .protocol.ImportAnalysisResponse\"\x00\x12_\n\x13\x46ilesImportAnalysis\x12$.protocol.FilesImportAnalysisRequest\x1a .protocol.ImportAnalysisResponse\"\x00\x12L\n\x0bSearchCKGDB\x12\x1c.protocol.SearchCKGDBRequest\x1a\x1d.protocol.SearchCKGDBResponse\"\x00\x12\x94\x01\n#IsCKGEnabledForNonWorkspaceScenario\x12\x34.protocol.IsCKGEnabledForNonWorkspaceScenarioRequest\x1a\x35.protocol.IsCKGEnabledForNonWorkspaceScenarioResponse\"\x00\x42\x0cZ\n./protocolb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'codekg_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z\n./protocol'
  _globals['_DOCUMENTRESPONSE_ERRMSGSENTRY']._options = None
  _globals['_DOCUMENTRESPONSE_ERRMSGSENTRY']._serialized_options = b'8\001'
  _globals['_GETBUILDSTATUSRESPONSE_STATUSENTRY']._options = None
  _globals['_GETBUILDSTATUSRESPONSE_STATUSENTRY']._serialized_options = b'8\001'
  _globals['_GETDOCUMENTSINDEXSTATUSRESPONSE_PROJECTSSTATUSENTRY']._options = None
  _globals['_GETDOCUMENTSINDEXSTATUSRESPONSE_PROJECTSSTATUSENTRY']._serialized_options = b'8\001'
  _globals['_CODE']._serialized_start=10832
  _globals['_CODE']._serialized_end=10963
  _globals['_ENTITYTYPE']._serialized_start=10965
  _globals['_ENTITYTYPE']._serialized_end=11032
  _globals['_BUILDSTATUS']._serialized_start=11034
  _globals['_BUILDSTATUS']._serialized_end=11087
  _globals['_RECALLTYPE']._serialized_start=11090
  _globals['_RECALLTYPE']._serialized_end=11272
  _globals['_SNIPPETTYPE']._serialized_start=11274
  _globals['_SNIPPETTYPE']._serialized_end=11363
  _globals['_EMPTY']._serialized_start=26
  _globals['_EMPTY']._serialized_end=33
  _globals['_ERROR']._serialized_start=35
  _globals['_ERROR']._serialized_end=74
  _globals['_REFRESHTOKENREQUEST']._serialized_start=76
  _globals['_REFRESHTOKENREQUEST']._serialized_end=129
  _globals['_REFRESHTOKENRESPONSE']._serialized_start=131
  _globals['_REFRESHTOKENRESPONSE']._serialized_end=215
  _globals['_PROJECT']._serialized_start=217
  _globals['_PROJECT']._serialized_end=289
  _globals['_INITREQUEST']._serialized_start=291
  _globals['_INITREQUEST']._serialized_end=406
  _globals['_INITRESPONSE']._serialized_start=408
  _globals['_INITRESPONSE']._serialized_end=484
  _globals['_VIRTUALPROJECT']._serialized_start=486
  _globals['_VIRTUALPROJECT']._serialized_end=567
  _globals['_INITVIRTUALPROJECTSREQUEST']._serialized_start=569
  _globals['_INITVIRTUALPROJECTSREQUEST']._serialized_end=686
  _globals['_INITVIRTUALPROJECTSRESPONSE']._serialized_start=688
  _globals['_INITVIRTUALPROJECTSRESPONSE']._serialized_end=779
  _globals['_DOCUMENT']._serialized_start=781
  _globals['_DOCUMENT']._serialized_end=855
  _globals['_DOCUMENTREQUEST']._serialized_start=857
  _globals['_DOCUMENTREQUEST']._serialized_end=949
  _globals['_DOCUMENTRESPONSE']._serialized_start=952
  _globals['_DOCUMENTRESPONSE']._serialized_end=1139
  _globals['_DOCUMENTRESPONSE_ERRMSGSENTRY']._serialized_start=1093
  _globals['_DOCUMENTRESPONSE_ERRMSGSENTRY']._serialized_end=1139
  _globals['_DELETEINDEXREQUEST']._serialized_start=1141
  _globals['_DELETEINDEXREQUEST']._serialized_end=1181
  _globals['_DELETEINDEXRESPONSE']._serialized_start=1183
  _globals['_DELETEINDEXRESPONSE']._serialized_end=1283
  _globals['_CURRENTEDITORINFO']._serialized_start=1286
  _globals['_CURRENTEDITORINFO']._serialized_end=1540
  _globals['_RETRIEVEENTITYREQUEST']._serialized_start=1543
  _globals['_RETRIEVEENTITYREQUEST']._serialized_end=1805
  _globals['_ENTITY']._serialized_start=1807
  _globals['_ENTITY']._serialized_end=1854
  _globals['_RETRIEVEENTITYRESPONSE']._serialized_start=1857
  _globals['_RETRIEVEENTITYRESPONSE']._serialized_end=2122
  _globals['_RETRIEVERELATIONREQUEST']._serialized_start=2125
  _globals['_RETRIEVERELATIONREQUEST']._serialized_end=2269
  _globals['_CODECHUNKVARIABLE']._serialized_start=2272
  _globals['_CODECHUNKVARIABLE']._serialized_end=2435
  _globals['_TEXTVARIABLE']._serialized_start=2438
  _globals['_TEXTVARIABLE']._serialized_end=2596
  _globals['_USEFULFILEINFO']._serialized_start=2598
  _globals['_USEFULFILEINFO']._serialized_end=2670
  _globals['_FOLDERVARIABLE']._serialized_start=2673
  _globals['_FOLDERVARIABLE']._serialized_end=2843
  _globals['_FILEVARIABLE']._serialized_start=2845
  _globals['_FILEVARIABLE']._serialized_end=2969
  _globals['_FILETOPLEVELVARIABLE']._serialized_start=2972
  _globals['_FILETOPLEVELVARIABLE']._serialized_end=3138
  _globals['_MEMBER']._serialized_start=3140
  _globals['_MEMBER']._serialized_end=3174
  _globals['_CLASSVARIABLE']._serialized_start=3177
  _globals['_CLASSVARIABLE']._serialized_end=3535
  _globals['_CLASSTOPLEVELVARIABLE']._serialized_start=3538
  _globals['_CLASSTOPLEVELVARIABLE']._serialized_end=3705
  _globals['_METHODINFO']._serialized_start=3708
  _globals['_METHODINFO']._serialized_end=3912
  _globals['_METHODVARIABLE']._serialized_start=3915
  _globals['_METHODVARIABLE']._serialized_end=4309
  _globals['_VARIABLE']._serialized_start=4312
  _globals['_VARIABLE']._serialized_end=4765
  _globals['_SELECTEDMETHODINFO']._serialized_start=4768
  _globals['_SELECTEDMETHODINFO']._serialized_end=4920
  _globals['_SELECTEDCLASSINFO']._serialized_start=4923
  _globals['_SELECTEDCLASSINFO']._serialized_end=5148
  _globals['_CURRENTEDITORENTITY']._serialized_start=5151
  _globals['_CURRENTEDITORENTITY']._serialized_end=5319
  _globals['_RANGE']._serialized_start=5321
  _globals['_RANGE']._serialized_end=5388
  _globals['_REFCLASSINFO']._serialized_start=5391
  _globals['_REFCLASSINFO']._serialized_end=5541
  _globals['_REFTYPEINFO']._serialized_start=5544
  _globals['_REFTYPEINFO']._serialized_end=5693
  _globals['_CURRENTEDITORVARIABLE']._serialized_start=5696
  _globals['_CURRENTEDITORVARIABLE']._serialized_end=6131
  _globals['_REFERENCE']._serialized_start=6133
  _globals['_REFERENCE']._serialized_end=6228
  _globals['_RETRIEVERELATIONRESPONSE']._serialized_start=6231
  _globals['_RETRIEVERELATIONRESPONSE']._serialized_end=6536
  _globals['_GETBUILDSTATUSREQUEST']._serialized_start=6538
  _globals['_GETBUILDSTATUSREQUEST']._serialized_end=6582
  _globals['_DOCUMENTBUILDSTATUS']._serialized_start=6584
  _globals['_DOCUMENTBUILDSTATUS']._serialized_end=6671
  _globals['_PROJECTBUILDSTATUS']._serialized_start=6674
  _globals['_PROJECTBUILDSTATUS']._serialized_end=6915
  _globals['_GETBUILDSTATUSRESPONSE']._serialized_start=6918
  _globals['_GETBUILDSTATUSRESPONSE']._serialized_end=7143
  _globals['_GETBUILDSTATUSRESPONSE_STATUSENTRY']._serialized_start=7068
  _globals['_GETBUILDSTATUSRESPONSE_STATUSENTRY']._serialized_end=7143
  _globals['_GETDOCUMENTSINDEXSTATUSREQUEST']._serialized_start=7145
  _globals['_GETDOCUMENTSINDEXSTATUSREQUEST']._serialized_end=7198
  _globals['_PROJECTDOCUMENTSINDEXSTATUS']._serialized_start=7200
  _globals['_PROJECTDOCUMENTSINDEXSTATUS']._serialized_end=7325
  _globals['_DOCUMENTINDEXSTATUS']._serialized_start=7327
  _globals['_DOCUMENTINDEXSTATUS']._serialized_end=7414
  _globals['_GETDOCUMENTSINDEXSTATUSRESPONSE']._serialized_start=7417
  _globals['_GETDOCUMENTSINDEXSTATUSRESPONSE']._serialized_end=7694
  _globals['_GETDOCUMENTSINDEXSTATUSRESPONSE_PROJECTSSTATUSENTRY']._serialized_start=7602
  _globals['_GETDOCUMENTSINDEXSTATUSRESPONSE_PROJECTSSTATUSENTRY']._serialized_end=7694
  _globals['_ISVERSIONMATCHEDREQUEST']._serialized_start=7696
  _globals['_ISVERSIONMATCHEDREQUEST']._serialized_end=7738
  _globals['_ISVERSIONMATCHEDRESPONSE']._serialized_start=7740
  _globals['_ISVERSIONMATCHEDRESPONSE']._serialized_end=7845
  _globals['_CANCELINDEXREQUEST']._serialized_start=7847
  _globals['_CANCELINDEXREQUEST']._serialized_end=7887
  _globals['_CANCELINDEXRESPONSE']._serialized_start=7889
  _globals['_CANCELINDEXRESPONSE']._serialized_end=7989
  _globals['_IMPORTANALYSISREQUEST']._serialized_start=7991
  _globals['_IMPORTANALYSISREQUEST']._serialized_end=8074
  _globals['_FILESIMPORTANALYSISREQUEST']._serialized_start=8076
  _globals['_FILESIMPORTANALYSISREQUEST']._serialized_end=8144
  _globals['_IMPORTANALYSISRESPONSE']._serialized_start=8147
  _globals['_IMPORTANALYSISRESPONSE']._serialized_end=8281
  _globals['_IMPORTANALYSISRESULT']._serialized_start=8283
  _globals['_IMPORTANALYSISRESULT']._serialized_end=8336
  _globals['_SYMBOL']._serialized_start=8338
  _globals['_SYMBOL']._serialized_end=8417
  _globals['_SEARCHCKGDBREQUEST']._serialized_start=8420
  _globals['_SEARCHCKGDBREQUEST']._serialized_end=8566
  _globals['_SEARCHCKGDBRESPONSE']._serialized_start=8569
  _globals['_SEARCHCKGDBRESPONSE']._serialized_end=8753
  _globals['_CODESNIPPET']._serialized_start=8755
  _globals['_CODESNIPPET']._serialized_end=8842
  _globals['_RETRIEVERELEVANTSNIPPETREQUEST']._serialized_start=8845
  _globals['_RETRIEVERELEVANTSNIPPETREQUEST']._serialized_end=9140
  _globals['_SNIPPET']._serialized_start=9143
  _globals['_SNIPPET']._serialized_end=9349
  _globals['_RETRIEVERELEVANTSNIPPETRESPONSE']._serialized_start=9352
  _globals['_RETRIEVERELEVANTSNIPPETRESPONSE']._serialized_end=9739
  _globals['_RERANKSNIPPETREQUEST']._serialized_start=9742
  _globals['_RERANKSNIPPETREQUEST']._serialized_end=10039
  _globals['_SNIPPETWITHSCORE']._serialized_start=10041
  _globals['_SNIPPETWITHSCORE']._serialized_end=10110
  _globals['_RERANKSNIPPETRESPONSE']._serialized_start=10113
  _globals['_RERANKSNIPPETRESPONSE']._serialized_end=10244
  _globals['_CURSORMOVEREQUEST']._serialized_start=10246
  _globals['_CURSORMOVEREQUEST']._serialized_end=10347
  _globals['_ISCKGENABLEDFORNONWORKSPACESCENARIOREQUEST']._serialized_start=10349
  _globals['_ISCKGENABLEDFORNONWORKSPACESCENARIOREQUEST']._serialized_end=10410
  _globals['_ISCKGENABLEDFORNONWORKSPACESCENARIORESPONSE']._serialized_start=10413
  _globals['_ISCKGENABLEDFORNONWORKSPACESCENARIORESPONSE']._serialized_end=10557
  _globals['_SETUPREQUEST']._serialized_start=10560
  _globals['_SETUPREQUEST']._serialized_end=10750
  _globals['_SETUPRESPONSE']._serialized_start=10752
  _globals['_SETUPRESPONSE']._serialized_end=10829
  _globals['_CODEKG']._serialized_start=11366
  _globals['_CODEKG']._serialized_end=13321
# @@protoc_insertion_point(module_scope)
