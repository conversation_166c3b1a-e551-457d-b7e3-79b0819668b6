import {Point, SyntaxNode} from "web-tree-sitter";
import {Range} from "vscode-languageserver-textdocument";
import {<PERSON><PERSON>itter<PERSON>andler} from "./common";
import {DefNodeInfo, DefNodeKindEnum, LanguageIdEnum} from "../../share";
import {TreeSitterParser} from "../parser";
import {ResolvedTypeInfo} from "../../../share/common";

export class JavaTreeSitterHandler extends TreeSitterHandler {
  static INSTANCE = new JavaTreeSitterHandler();

  languageId = LanguageIdEnum.Java;

  funcTypes = ["method_declaration", "constructor_declaration", "lambda_expression"];
  typeTypes = ["class_declaration", "interface_declaration", "enum_declaration", "record_declaration"];
  fileTypes = ["program"];
  varTypes = ["field_declaration", "local_variable_declaration"];

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
    super();
  }

  getParentNodeForKeywordSearch(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, [
      ...this.funcTypes,
      ...this.typeTypes,
      ...this.fileTypes,
    ]);
  }

  getFuncParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.funcTypes);
  }

  getFuncParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.funcTypes);
  }

  getTypeParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.typeTypes);
  }

  getTypeParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.typeTypes);
  }

  getVarParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.varTypes);
  }

  getVarParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.varTypes);
  }

  getMethodInRange(rootNode: SyntaxNode, range: Range, startIndex: number, endIndex: number, completelyIncluded: boolean): SyntaxNode[] {
    return this.getElementInRangeWithTypeList(rootNode, range, startIndex, endIndex, completelyIncluded, this.funcTypes);
  }

  getInvokeInFile(node: SyntaxNode, startPosition?: Point, endPosition?: Point): SyntaxNode[] {
    const result: SyntaxNode[] = [];

    const queryStr = `
(method_invocation
  name: (identifier) @symbol_name
)
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, node, startPosition, endPosition);

    for (const match of matchResult) {
      const nameNode = match.matches.get("symbol_name");

      if (nameNode.length === 1 && nameNode[0].startPosition) {
        result.push(nameNode[0]);
      }
    }

    return result;
  }

  getTypeInFile(node: SyntaxNode, startPosition?: Point, endPosition?: Point): SyntaxNode[] {
    const result: SyntaxNode[] = [];

    const queryStr = `
(type_identifier) @symbol_name
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, node, startPosition, endPosition);

    for (const match of matchResult) {
      const nameNode = match.matches.get("symbol_name");

      if (nameNode.length === 1) {
        result.push(nameNode[0]);
      }
    }

    return result;
  }

  override getNotProcessTypeNode(rootNode: SyntaxNode, allTypeNodes: SyntaxNode[]): SyntaxNode[] {
    const blackListPrefix = [
      "java",
      "javax",
      "android",
      "androidx",
      "lombok",
      "org.apache",
      "org.springframework",
      "com.google",
      "org.slf4j",
      "org.mybatis",
      "com.alibaba",
    ];

    const queryStr = `
(import_declaration
  (scoped_identifier) @import_string
)
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, rootNode);

    const notProcessTypeString: string[] = [];
    for (const match of matchResult) {
      const importStringNode = match.matches.get("import_string");

      if (importStringNode.length === 1) {
        const importString = importStringNode[0].text;
        if (blackListPrefix.find(prefix => importString.startsWith(prefix)) !== undefined) {
          const importStringSplit = importString.split(".");

          if (importStringSplit.length > 0) {
            const typeString = importStringSplit[importStringSplit.length - 1];
            if (typeString !== "*") {
              notProcessTypeString.push(typeString);
            }
          }
        }
      }
    }

    return allTypeNodes.filter(node => {
      return notProcessTypeString.includes(node.text);
    });
  }

  getDeclVariableInFile(node: SyntaxNode, startPosition?: Point, endPosition?: Point): SyntaxNode[] {
    const result: SyntaxNode[] = [];

    const queryStr = `
[
  (field_declaration
    declarator: (variable_declarator
      name: (identifier) @symbol_name
    )
  )
  (local_variable_declaration
    declarator: (variable_declarator
      name: (identifier) @symbol_name
    )
  )
]
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, node, startPosition, endPosition);

    for (const match of matchResult) {
      const nameNode = match.matches.get("symbol_name");

      if (nameNode.length === 1) {
        result.push(nameNode[0]);
      }
    }

    return result;
  }

  getUsageVariableInFile(node: SyntaxNode, startPosition?: Point, endPosition?: Point): SyntaxNode[] {
    const result: SyntaxNode[] = [];

    const invokeNodeIdInFile = this.getInvokeInFile(node, startPosition, endPosition).map(node => node.id);
    const declVarNodeIdInFile = this.getDeclVariableInFile(node, startPosition, endPosition).map(node => node.id);

    const queryStr = `
[
  (identifier) @symbol_name
]
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, node, startPosition, endPosition);

    for (const match of matchResult) {
      const nameNode = match.matches.get("symbol_name");

      if (nameNode.length === 1
        && !invokeNodeIdInFile.includes(nameNode[0].id)
        && (nameNode[0].parent?.type !== "method_declaration" && nameNode[0].parent?.type !== "constructor_declaration")
        && !declVarNodeIdInFile.includes(nameNode[0].id)
      ) {
        result.push(nameNode[0]);
      }
    }

    return result;
  }

  override genResolvedTypeInfoFromDefNode(defNodeInfo: DefNodeInfo): ResolvedTypeInfo {
    const result = super.genResolvedTypeInfoFromDefNode(defNodeInfo);

    if (defNodeInfo.kind === DefNodeKindEnum.Class) {
      const classSimplifiedContent = this.genClassSimplifiedContent(defNodeInfo);
      if (classSimplifiedContent !== "") {
        result.addOptionalContent(ResolvedTypeInfo.OPTIONAL_CONTENT_CLASS_SIMPLIFIED_CONTENT, classSimplifiedContent);
      }
    }

    return result;
  }

  getReturnTypeNodes(node: SyntaxNode): SyntaxNode[] {
    const result: SyntaxNode[] = [];

    if (this.funcTypes.includes(node.type)) {
      const returnTypeNode = node.childForFieldName("type");
      if (returnTypeNode) {
        result.push(this.getTypeNameNodeHelper(returnTypeNode));
      }
    }

    return result;
  }

  getSubTypeNodes(node: SyntaxNode): SyntaxNode[] {
    if (node) {
      switch (node.type) {
        case "array_type":
          return [this.getTypeNameNodeHelper(node.childForFieldName("element"))];
      }
    }

    return [];
  }

  getTypeFullString(node: SyntaxNode): string {
    if (node.parent && node.parent.type === "scoped_type_identifier") {
      return node.parent.text;
    } else {
      return node.text;
    }
  }

  getVarNameNodeFromVarDeclNode(node: SyntaxNode): SyntaxNode {
    switch (node.type) {
      case "field_declaration":
      case "local_variable_declaration":
        return node.childForFieldName("declarator")?.childForFieldName("name");
      default:
        return null;
    }
  }

  getVarTypeNameNode(node: SyntaxNode, _varName: string): { node: SyntaxNode, typeString: string, index: number } {
    switch (node.type) {
      case "field_declaration":
      case "local_variable_declaration":
        const nodeFromType = this.getTypeNameNodeHelper(node.childForFieldName("type"));
        if (nodeFromType) {
          return {node: nodeFromType, typeString: null, index: -1};
        }
    }

    return {node: null, typeString: null, index: -1};
  }

  getImportInfo(rootNode: SyntaxNode): any {
    // TODO
    return null;
  }

  // private
  genClassSimplifiedContent(defNodeInfo: DefNodeInfo): string {
    const cutOffRanges: {start: number, end: number}[] = [];

    const queryStr = `
(class_declaration
  body: (class_body
    [
      (constructor_declaration) @method_def
      (method_declaration) @method_def
    ]
  )
)
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, defNodeInfo.defNode);
    for (const match of matchResult) {
      const defNode = match.matches.get("method_def");

      if (defNode.length === 1) {
        const bodyNode = defNode[0].childForFieldName("body");
        if (bodyNode) {
          cutOffRanges.push({
            start: bodyNode.startIndex,
            end: bodyNode.endIndex,
          });
        }
      }
    }

    const fullContent = defNodeInfo.defNode.text;
    if (cutOffRanges.length > 0) {
      const sortedRanges = cutOffRanges.sort((a, b) => a.start - b.start);

      let result = '';
      let lastIndex = 0;

      for (const range of sortedRanges) {
        result += fullContent.slice(lastIndex, range.start - defNodeInfo.defNode.startIndex);
        result += " { ... }";
        lastIndex = range.end - defNodeInfo.defNode.startIndex;
      }

      result += fullContent.slice(lastIndex);

      return result;
    } else {
      return fullContent;
    }
  }

  private getTypeNameNodeHelper(node: SyntaxNode): SyntaxNode {
    if (node) {
      switch (node.type) {
        case "type_identifier":
        case "array_type":
          return node;
        case "generic_type":
          return this.getTypeNameNodeHelper(node.namedChild(0));
        case "scoped_type_identifier":
          return this.getTypeNameNodeHelper(node.namedChild(node.namedChildren.length - 1));
      }
    }

    return null;
  }
}