import {beforeAll, describe, expect, it, vi} from "vitest";
import {TreeSitterParser} from "../parse/treesitter/parser";
import {LanguageIdEnum} from "../parse/share";
import {createDocument, wait} from "../../utils/common";
import {
  getErrorNodesRange,
  getFileSymbolDef,
  GetSymbolDefSource,
  isLanguageSupported,
  mergeIntervals,
} from "./definition";
import {setEnableLocalLogging} from "../../utils/logger";
import {FileSymbolDef, ResolvedTypeInfo} from "./common";
import {doSetConfigById, FeatureGateManager, FeatureName, getConfig} from "../common/config";
import {MainHandler} from "../../worker/main";
import {handleFeatureChangeListener} from "../common/init";
import * as utilsPath from "../../utils/path";
import type {DocumentUri, Location, Position} from "vscode-languageserver-types";
import {PSIParser} from "../parse/psi/parser";

describe("test_getErrorNodesRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
func (p *ImportAnalyzer) getGoModuleInfo() [2]*string {
  var moduleInfo [2]*string

  goModFile, err := p.getGoModFile()
  if err != nil {
    logs.V1.CtxError(p.Ctx, "failed to getGoModFile: %s", err.Error())
    return moduleInfo
  }
  
  if p.getModuleNameFromGoModFile(goModFile)

  return moduleInfo
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);
    const document = createDocument(docText, LanguageIdEnum.Go, "file:///test.go");

    const result = getErrorNodesRange(document, tree.rootNode, {line: 0, character: 0}, {line: 14, character: 0});

    expect(result).toEqual([{start: 243, end: 306}]);
  });
});

describe('test_mergeIntervals', () => {
  it('case1_overlap', () => {
    const intervals: { start: number, end: number }[] = [
      { start: 1, end: 3 },
      { start: 2, end: 4 },
      { start: 5, end: 7 },
    ];
    const expectedResult: { start: number, end: number }[] = [
      { start: 1, end: 4 },
      { start: 5, end: 7 },
    ];
    const result = mergeIntervals(intervals);
    expect(result).toEqual(expectedResult);
  });

  it('case2_no_overlap', () => {
    const intervals: { start: number, end: number }[] = [
      { start: 1, end: 2 },
      { start: 3, end: 4 },
      { start: 5, end: 6 },
    ];
    const expectedResult: { start: number, end: number }[] = [
      { start: 1, end: 2 },
      { start: 3, end: 4 },
      { start: 5, end: 6 },
    ];
    const result = mergeIntervals(intervals);
    expect(result).toEqual(expectedResult);
  });

  it('case3_touch_endpoints', () => {
    const intervals: { start: number, end: number }[] = [
      { start: 1, end: 2 },
      { start: 2, end: 3 },
    ];
    const expectedResult: { start: number, end: number }[] = [
      { start: 1, end: 3 },
    ];
    const result = mergeIntervals(intervals);
    expect(result).toEqual(expectedResult);
  });

  it('case4_completely_contained', () => {
    const intervals: { start: number, end: number }[] = [
      { start: 1, end: 3 },
      { start: 2, end: 2 },
    ];
    const expectedResult: { start: number, end: number }[] = [
      { start: 1, end: 3 },
    ];
    const result = mergeIntervals(intervals);
    expect(result).toEqual(expectedResult);
  });

  it('case5_empty', () => {
    const intervals: { start: number, end: number }[] = [];
    const expectedResult: { start: number, end: number }[] = [];
    const result = mergeIntervals(intervals);
    expect(result).toEqual(expectedResult);
  });
});

describe("test_getFileSymbolDef", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
    FeatureGateManager.INSTANCE.openFeature(FeatureName.ENABLE_GO_DEFINITION_ANALYSIS);
    FeatureGateManager.INSTANCE.openFeature(FeatureName.ENABLE_PSI_LSP_REQUEST);
    FeatureGateManager.INSTANCE.openFeature(FeatureName.ENABLE_PSI_LSP_TIMEOUT_INTERVAL);
    setEnableLocalLogging(true);
  });

  it("case1_toAnalyzerMaxSymbolCount", async () => {
    const docText = `
func (p *ImportAnalyzer) getGoModuleInfo() [2]*string {
  var moduleInfo [2]*string

  goModFile, err := p.getGoModFile()
  if err != nil {
    logs.V1.CtxError(p.Ctx, "failed to getGoModFile: %s", err.Error())
    return moduleInfo
  }
  
  if p.getModuleNameFromGoModFile(goModFile)

  return moduleInfo
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);
    const document = createDocument(docText, LanguageIdEnum.Go, "file:///test.go");

    const definitionMap = new Map<string, ResolvedTypeInfo>();

    const definitionHandler = (defInfo: ResolvedTypeInfo): string => {
      definitionMap.set(defInfo.uniqueKeyString(), defInfo);
      return defInfo.uniqueKeyString();
    };

    const originToAnalyzerMaxSymbolCount = getConfig("analysis.definition.toAnalyzerMaxSymbolCount");
    doSetConfigById("analysis.definition.toAnalyzerMaxSymbolCount", 30);

    const fileSymbolDef1 = new FileSymbolDef(document);
    fileSymbolDef1.indexDefInfo = new Array(document.getText().length).fill(null);

    const result1 = await getFileSymbolDef(GetSymbolDefSource.Direct, fileSymbolDef1, document, tree, [{
      start: {line: 0, character: 0},
      end: {line: 14, character: 0},
    }], definitionHandler);

    expect(result1).toEqual(21);

    doSetConfigById("analysis.definition.toAnalyzerMaxSymbolCount", 10);

    const fileSymbolDef2 = new FileSymbolDef(document);
    fileSymbolDef2.indexDefInfo = new Array(document.getText().length).fill(null);

    const result2 = await getFileSymbolDef(GetSymbolDefSource.Direct, fileSymbolDef2, document, tree, [{
      start: {line: 0, character: 0},
      end: {line: 14, character: 0},
    }], definitionHandler);

    expect(result2).toEqual(0);

    doSetConfigById("analysis.definition.toAnalyzerMaxSymbolCount", originToAnalyzerMaxSymbolCount);
  });

  it("case2_timeout_interval", async () => {
    MainHandler.INSTANCE.init_("./dist/resources", "/test", false, false, false);

    const originPsiTimeoutMs = getConfig("parse.psi.timeoutMs");
    const originPsiTimeoutInterval = getConfig("parse.common.timeoutInterval");
    doSetConfigById("parse.psi.timeoutMs", 5);
    doSetConfigById("parse.common.timeoutInterval", 100);

    const workspacePathMock = vi.spyOn(utilsPath, "getWorkspaceFolderPath").mockImplementation(() => {
      return "file:///";
    });

    const psiProxy = {
      execute: async (command: string, _uri: DocumentUri, _position: Position, _args: any[]): Promise<Location[]> => {
        if (command === "cmd1") {
          return [{
            uri: "file:///cmd1.go",
            range: {start: {line: 0, character: 0}, end: {line: 1, character: 1}},
          }];
        }
        if (command === "cmd2") {
          await wait(10);
          return [];
        }

        return [];
      },
    }

    PSIParser.INSTANCE.init(psiProxy);

    const docText = "";

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);
    const document = createDocument(docText, LanguageIdEnum.Go, "file:///test.go");

    const definitionMap = new Map<string, ResolvedTypeInfo>();

    const definitionHandler = (defInfo: ResolvedTypeInfo): string => {
      definitionMap.set(defInfo.uniqueKeyString(), defInfo);
      return defInfo.uniqueKeyString();
    };

    const fileSymbolDef = new FileSymbolDef(document);
    fileSymbolDef.indexDefInfo = new Array(document.getText().length).fill(null);

    const result1 = await getFileSymbolDef(GetSymbolDefSource.Direct, fileSymbolDef, document, tree, [{
      start: {line: 0, character: 0},
      end: {line: 0, character: 0},
    }], definitionHandler);

    expect(result1).toEqual(0);

    await PSIParser.INSTANCE.execute("cmd2", "", "file:///", {line: 0, character: 0}, []);

    const result2 = await getFileSymbolDef(GetSymbolDefSource.Direct, fileSymbolDef, document, tree, [{
      start: {line: 0, character: 0},
      end: {line: 0, character: 0},
    }], definitionHandler);

    expect(result2).toEqual(-1);

    await wait(110);

    const result3 = await getFileSymbolDef(GetSymbolDefSource.Direct, fileSymbolDef, document, tree, [{
      start: {line: 0, character: 0},
      end: {line: 0, character: 0},
    }], definitionHandler);

    expect(result3).toEqual(0);

    workspacePathMock.mockRestore();

    doSetConfigById("parse.psi.timeoutMs", originPsiTimeoutMs);
    doSetConfigById("parse.common.timeoutInterval", originPsiTimeoutInterval);
  });
});

describe("test_isLanguageSupported", () => {
  beforeAll(async () => {
    setEnableLocalLogging(true);
  });

  it("case1_normal", async () => {
    MainHandler.INSTANCE.init_("./dist/resources", "/test", false, false, false);

    FeatureGateManager.INSTANCE.cacheFeature("test_isLanguageSupported_case1_normal");
    await MainHandler.INSTANCE.cacheFeature("test_isLanguageSupported_case1_normal");

    const featureMap: Map<string, FeatureName> = new Map();
    featureMap.set("f1", FeatureName.ENABLE_GO_DEFINITION_ANALYSIS);
    featureMap.set("f2", FeatureName.ENABLE_TS_DEFINITION_ANALYSIS);

    const featureState1 = {
      "f1": true,
      "f2": false,
    }

    const listener1 = function(feature: string, onFetchSuccess: (enable: boolean) => void): void {
      onFetchSuccess(featureState1[feature]);
    }

    handleFeatureChangeListener(featureMap, listener1)
    await wait(30);

    const result1 = isLanguageSupported(LanguageIdEnum.Go);
    expect(result1).toEqual(true);
    const result2 = isLanguageSupported(LanguageIdEnum.Java);
    expect(result2).toEqual(false);
    const result3 = isLanguageSupported(LanguageIdEnum.Typescript);
    expect(result3).toEqual(false);
    const result4 = isLanguageSupported(LanguageIdEnum.Python);
    expect(result4).toEqual(false);

    const featureState2 = {
      "f1": false,
      "f2": true,
    }

    const listener2 = function(feature: string, onFetchSuccess: (enable: boolean) => void): void {
      onFetchSuccess(featureState2[feature]);
    }

    handleFeatureChangeListener(featureMap, listener2)
    await wait(30);

    const result5 = isLanguageSupported(LanguageIdEnum.Go);
    expect(result5).toEqual(false);
    const result6 = isLanguageSupported(LanguageIdEnum.Java);
    expect(result6).toEqual(false);
    const result7 = isLanguageSupported(LanguageIdEnum.Typescript);
    expect(result7).toEqual(true);
    const result8 = isLanguageSupported(LanguageIdEnum.Python);
    expect(result8).toEqual(false);

    FeatureGateManager.INSTANCE.restoreFeature("test_isLanguageSupported_case1_normal");
    await MainHandler.INSTANCE.restoreFeature("test_isLanguageSupported_case1_normal");
  });
});