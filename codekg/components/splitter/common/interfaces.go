// Package common 定义了分割器的通用接口和工具函数
package common

import (
	"context"

	kb "ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/model"
)

// Splitter 是所有分割器需要实现的基本接口
type Splitter interface {
	// SplitFile 分割单个文件
	SplitFile(ctx context.Context, file *kb.SplitFile) (*kb.SplitFileResult_, error)

	// SupportedLanguages 返回支持的语言类型
	SupportedLanguages() map[model.LanguageType]bool
}

// SplitterWithOptions 是支持选项配置的分割器接口
type SplitterWithOptions interface {
	Splitter

	// WithOptions 配置分割器选项
	WithOptions(options map[string]interface{}) error
}

// SplitterRegistry 管理多个分割器实例的注册表
type SplitterRegistry interface {
	// RegisterSplitter 注册一个分割器
	RegisterSplitter(name string, splitter Splitter)

	// GetSplitter 获取指定名称的分割器
	GetSplitter(name string) (Splitter, bool)

	// GetSplitterForLanguage 获取支持指定语言的分割器
	GetSplitterForLanguage(language model.LanguageType) (Splitter, bool)

	// RegisteredSplitters 返回所有注册的分割器
	RegisteredSplitters() map[string]Splitter
}

// SplitterFactory 创建分割器的工厂接口
type SplitterFactory interface {
	// CreateSplitter 根据指定的类型和选项创建分割器
	CreateSplitter(splitterType string, options map[string]interface{}) (Splitter, error)
}

// SplitFilesFunc 是分割多个文件的函数类型
type SplitFilesFunc func(ctx context.Context, files []*kb.SplitFile) (*kb.SplitFilesResponse, error)

// SplitterOption 定义了创建分割器的选项
type SplitterOption struct {
	// 基本选项
	ChunkSize    int  // 分块大小
	ChunkOverlap int  // 分块重叠大小
	AsPlainText  bool // 是否作为纯文本处理

	// 分割器类型选择
	SplitterType  string // 顶级分割器类型 (local or kb)
	ContentType   string // 内容类型 (text or code)
	LocalSubtype  string // 本地分割器子类型 (basic_text, langchain_text)
	LangchainType string // Langchain分割器类型 (recursive, markdown, token)

	// 高级选项，由具体分割器解析
	Advanced map[string]interface{}
}

// SplitterKey 表示分割器的多级标识
type SplitterKey struct {
	SplitterType  string // 顶级分割器类型
	ContentType   string // 内容类型
	LocalSubtype  string // 本地分割器子类型
	LangchainType string // Langchain分割器类型
}

// String 返回SplitterKey的字符串表示
func (k SplitterKey) String() string {
	key := k.SplitterType

	if k.ContentType != "" {
		key += "." + k.ContentType
	}

	if k.LocalSubtype != "" {
		key += "." + k.LocalSubtype
	}

	if k.LangchainType != "" {
		key += "." + k.LangchainType
	}

	return key
}

// NewSplitterKey 从选项创建SplitterKey
func NewSplitterKey(opt *SplitterOption) SplitterKey {
	return SplitterKey{
		SplitterType:  opt.SplitterType,
		ContentType:   opt.ContentType,
		LocalSubtype:  opt.LocalSubtype,
		LangchainType: opt.LangchainType,
	}
}

// BatchSplitter 是处理多个文件的分割器接口
type BatchSplitter interface {
	// SplitFiles 分割多个文件
	SplitFiles(ctx context.Context, files []*kb.SplitFile) (*kb.SplitFilesResponse, error)
}

// SplitterAdapter 将单文件分割器适配为批处理分割器
type SplitterAdapter struct {
	Splitter Splitter
}

// SplitFiles 实现BatchSplitter接口
func (a *SplitterAdapter) SplitFiles(ctx context.Context, files []*kb.SplitFile) (*kb.SplitFilesResponse, error) {
	results := make(map[string]*kb.SplitFileResult_)
	failedResults := make(map[string]string)

	for _, file := range files {
		if file == nil {
			continue
		}

		result, err := a.Splitter.SplitFile(ctx, file)
		if err != nil {
			failedResults[file.Path] = err.Error()
			continue
		}

		results[file.Path] = result
	}

	return &kb.SplitFilesResponse{
		Results:     results,
		FailedFiles: failedResults,
	}, nil
}
