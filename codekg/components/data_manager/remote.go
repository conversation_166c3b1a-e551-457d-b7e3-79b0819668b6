package data_manager

import (
	"context"
	"os"
	"sync"
	"sync/atomic"
	"time"

	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/env"
	bizErr "ide/ckg/codekg/components/error"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/tasks"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/util"

	"ide/ckg/codekg/components/logs"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/lang/maths"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (m *manager) tryDownloadFiles(ctx context.Context, cli knowledgebase.Client, storage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage, knowledgebaseID string, project model.URI,
	uris []*model.URIStatus, userID string, downloadCallback func(status *model.URIStatus)) ([]*model.URIStatus, error) {
	fileIDMap := make(map[string]*model.URIStatus)
	downloadFileIDs := make([]string, 0)
	for _, uri := range uris {
		downloadFileIDs = append(downloadFileIDs, uri.RelPath)
		fileIDMap[uri.RelPath] = uri
	}

	fileCh, errorCh, err := cli.GetCachedFilesAndWrite(ctx, env.GetToken(userID),
		knowledgebaseID, project, lo.Map(uris, func(item *model.URIStatus, _ int) *knowledgebase.CachedFile {
			return &knowledgebase.CachedFile{
				FileID:   item.RelPath,
				UniqueID: item.UniqueID,
			}
		}))
	if err != nil {
		return nil, errors.WithMessagef(err, "error download files, err: %v", err)
	}

	downloadFiles := make([]*model.URIStatus, 0)
	for {
		select {
		case file, ok := <-fileCh:
			if !ok {
				return downloadFiles, nil
			}

			uriStatus, found := fileIDMap[file.FileID]
			if !found {
				logs.CtxWarn(ctx, "cannot find download file id, id: %s", file.FileID)
			} else {
				uriData := file.ToURIData(uriStatus)
				err := data_storage.InsertStorage(ctx, storage, uriData)
				if err != nil {
					logs.CtxWarn(ctx, "insertStorage err is %v path is %v", err, uriStatus.AbsPath)
				} else {
					downloadFiles = append(downloadFiles, uriStatus)
					downloadCallback(uriStatus)

					if len(downloadFiles)%10 == 0 {
						logs.CtxInfo(ctx, "downloaded %d files from cached", len(downloadFiles))
					}
				}
			}
		case err := <-errorCh:
			return nil, err
		}
	}
}

func (m *manager) getUnIndexFile(ctx context.Context, cli knowledgebase.Client, knowledgebaseID string, project model.URI, uris []*model.URIStatus, userID string) ([]*model.URIStatus, error) {
	uriMap := make(map[string]*model.URIStatus)
	files := make([]*knowledgebase.File, 0)
	for _, uri := range uris {
		file := &knowledgebase.File{
			FileID:      uri.RelPath,
			UniqueID:    uri.UniqueID,
			ContentHash: uri.ContentHash,
		}
		uriMap[uri.RelPath] = uri
		files = append(files, file)
	}

	request := &knowledgebase.GetUnIndexedFilesRequest{
		ProjectFiles: []*knowledgebase.ProjectFile{
			{
				ProjectID: project,
				Files:     files,
			},
		},
	}
	response, err := cli.GetUnIndexFileList(ctx, env.GetToken(userID), knowledgebaseID, request)
	if err != nil {
		logs.CtxError(ctx, "error GetUnIndexFileList, err: %v", err)
		return nil, errors.WithMessagef(err, "error get un index file, err: %v", err)
	}

	result := make([]*model.URIStatus, 0)
	for _, projectFiles := range response.ProjectFiles {
		for _, file := range projectFiles.Files {
			result = append(result, uriMap[file.FileID])
		}
	}

	return result, nil
}

func (m *manager) remoteIndex(ctx context.Context, cli knowledgebase.Client, storage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage,
	project, userID string, files []model.URI, skipIndex bool, bgIndex bool, indexFinish chan<- bool) error {
	var err error
	startTime := time.Now()
	initEvent := ckg_metrics.GetEvent(ctx)
	initEvent.AddTeaParam(ckg_metrics.TeaParamIndexType, EmbeddingStorageTypeRemote)

	config := m.config.GetTCCConfig(ctx, cli, userID)

	logs.CtxInfo(ctx, "create remote data storage")

	err = m.clearProjectOutdatedData(ctx, storage, embeddingStorage, files)
	if err != nil {
		initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepClearProjectNotExistData)
		initEvent.Report(ctx, false)
		logs.CtxError(ctx, "[remoteIndex] clearProjectOutdatedData err project is %v, err: %+v", project, err)
		return errors.WithMessagef(err, "clearProjectOutdatedData err project is %v", project)
	}

	logs.CtxInfo(ctx, "get knowledgebase id")
	knowledgebaseID, err := env.GetKnowledgebaseID(ctx, userID)
	if err != nil {
		initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepInitKnowledgebaseId)
		logs.CtxError(ctx, "[remoteIndex] get knowledgebase error: %+v", err)
		return errors.WithMessagef(err, "error get knowledgebase id")
	}
	initEvent.AddTeaParam(ckg_metrics.TeaParamKnowledgebaseID, knowledgebaseID)

	logs.CtxInfo(ctx, "init remote knowledgebase")
	// 初始化远端 knowledgebase
	err = cli.GetOrCreateKnowledgebase(ctx, env.GetToken(userID), knowledgebaseID,
		&knowledgebase.GetOrCreateKnowledgebaseRequest{
			IDEID: env.GetIDEID(),
			Projects: []*knowledgebase.Project{
				{
					ProjectID: project,
				},
			},
		})
	if err != nil {
		initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepInitRemoteKnowledgebase)
		initEvent.Report(ctx, false)
		logs.CtxError(ctx, "GetOrCreateKnowledgebase err %v", err)
		return errors.WithMessage(err, "GetOrCreateKnowledgebase err")
	}
	m.projects.Store(project, EmbeddingStorageTypeRemote)

	// 如果需要跳过索引，跳过
	if skipIndex {
		initEvent.AddTeaParam(ckg_metrics.TeaParamSkipped, true)
		initEvent.Report(ctx, false)
		logs.CtxWarn(ctx, "too many files, skip init full project")
		return errors.WithMessagef(bizErr.ErrFileCountExceed, "too many files, skip init full project")
	}

	logs.CtxInfo(ctx, "calculate all files that need indexing")
	// 计算所有需要 index 的文件
	allFileURIs := make([]*model.URIStatus, 0)
	fileToBeIndexed := make(map[string]*model.URIStatus)
	uriToBeIndexed := make(map[string]*model.URIStatus)
	fileStats := make(map[string]*fileInformation)
	computeHashStartTime := time.Now()

	statErrFiles, relErrFiles := make([]string, 0), make([]string, 0)
	createInfoErrFiles, createInfoErrFolders := make([]string, 0), make([]string, 0)
	for _, file := range files {
		info, err := m.fs.Stat(file)
		if err != nil {
			statErrFiles = append(statErrFiles, file)
			if os.IsNotExist(err) {
				logs.CtxInfo(ctx, "[remoteIndex] file is not exist path is %v", file)
				continue
			}
			logs.CtxError(ctx, "[remoteIndex] Stat err, file is %v, err is %v", file, err)
			continue
		}
		relPath, err := m.fs.Rel(project, file)
		if err != nil {
			relErrFiles = append(relErrFiles, file)
			logs.CtxError(ctx, "[remoteIndex] failed to relative path %s, project: %s", file, project)
			continue
		}
		var uriStatus *model.URIStatus
		if !info.IsDir() {
			fileInfo, err := m.createFileInfo(ctx, project, file)
			if err != nil {
				createInfoErrFiles = append(createInfoErrFiles, file)
				if os.IsNotExist(err) {
					logs.CtxInfo(ctx, "[remoteIndex] file %v not exist", file)
					continue
				}
				logs.CtxError(ctx, "[remoteIndex] createFileInfo err file is %v, err is %v", file, err)
				continue
			}
			fileStats[file] = fileInfo
			uriStatus = &model.URIStatus{
				AbsPath:     file,
				RelPath:     relPath,
				ContentHash: fileInfo.hash,
				UniqueID:    fileInfo.uniqueID,
			}
			allFileURIs = append(allFileURIs, uriStatus)
		} else {
			folderInfo, err := m.createFolderInfo(ctx, file)
			if err != nil {
				createInfoErrFolders = append(createInfoErrFolders, file)
				if os.IsNotExist(err) {
					logs.CtxInfo(ctx, "[remoteIndex] folder %v not exist", file)
					continue
				}
				logs.CtxError(ctx, "[remoteIndex] createFolderInfo err folder is %v, err is %v", file, err)
				continue
			}
			uriStatus = &model.URIStatus{
				AbsPath:     file,
				RelPath:     relPath,
				ContentHash: folderInfo.hash,
			}
		}
		uriMeta, err := storage.GetURIMetaFromURIWithoutContent(ctx, storage.GetConn(), uriStatus)
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			uriToBeIndexed[file] = uriStatus
			if !info.IsDir() {
				fileToBeIndexed[file] = uriStatus
			}
		} else if err == nil && (uriMeta.ContentHash != uriStatus.ContentHash || uriMeta.HasEmbedding != 1) {
			uriToBeIndexed[file] = uriStatus
			if !info.IsDir() {
				fileToBeIndexed[file] = uriStatus
			}
		} else if err != nil {
			logs.CtxInfo(ctx, "[localIndex] error get uri meta from uri, err: %v", err)
		}
	}
	initEvent.AddTeaParam(ckg_metrics.TeaParamComputeHashCost, time.Since(computeHashStartTime).Milliseconds())
	initEvent.AddTeaParam(ckg_metrics.TeaParamStatErrFileNum, len(statErrFiles))
	initEvent.AddTeaParam(ckg_metrics.TeaParamRelErrFileNum, len(relErrFiles))
	initEvent.AddTeaParam(ckg_metrics.TeaParamCreateInfoErrFileNum, len(createInfoErrFiles))
	initEvent.AddTeaParam(ckg_metrics.TeaParamCreateInfoErrFolderNum, len(createInfoErrFolders))

	// 每次新的请求，重新初始化一次 progress
	atomicValue := &atomic.Int64{}
	atomicValue.Store(int64(len(fileStats) - len(fileToBeIndexed)))
	building := &atomic.Bool{}
	building.Store(true)
	progress := &projectBuildProgress{
		totalFiles:        int64(len(fileStats)),
		builtFiles:        atomicValue,
		building:          building,
		successIndexFiles: &atomic.Int64{},
		failedIndexFiles:  &atomic.Int64{},
	}
	if bgIndex {
		m.bgProjectInitProgress.Store(project, progress)
	} else {
		m.projectInitProgress.Store(project, progress)
	}
	useV2Ignore := m.config.IsFeatureEnabled(ctx, userID, ckg_config.UseV2Ignore)
	util.SafeGo(ctx, func() {
		ctx := util.NewBackgroundContext(ctxvalues.LogIDDefault(ctx))

		// 获取远端未索引文件
		unIndexedURIs, err := m.getUnIndexFile(ctx, cli, knowledgebaseID, project, allFileURIs, userID)
		if err != nil {
			progress.building.Store(false)
			initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
			initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepGetUnIndexedFile)
			initEvent.Report(ctx, false)
			return
		}

		for _, uri := range unIndexedURIs {
			uriToBeIndexed[uri.AbsPath] = uri
		}

		allUriMetas, err := storage.GetAllURIMetaWithoutContent(ctx, storage.GetConn())
		if err != nil {
			progress.building.Store(false)
			initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
			initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepGetAllUriMeta)
			logs.CtxError(ctx, "error get all uri meta, err: %v", err)
			return
		}
		// 服务端限流策略
		// 如果 "并集(即将索引的文件 (uri), 本地已有的文件 (uri))" 超出限制，则只索引部分 “即将索引的文件”，直到和“强制索引文件数量的最大值” 相等
		// 注：以下逻辑与 remoteIndex 中相同
		alreadyIndexedUriMetaNum := len(allUriMetas)
		indexUriMetaNameSum := lo.Map(allUriMetas, func(m data_storage.StorageURIMeta, _ int) string { return m.Uri })
		indexUriMetaNameSum = lo.Union(indexUriMetaNameSum, lo.Keys(uriToBeIndexed))
		if len(indexUriMetaNameSum) > config.ForceIndexFileCountLimit {
			keys := lo.Keys(uriToBeIndexed)
			keysFixedNum := lo.Slice(keys, 0, config.ForceIndexFileCountLimit-alreadyIndexedUriMetaNum)
			uriToBeIndexed = lo.PickByKeys(uriToBeIndexed, keysFixedNum)
		}
		toBeIndexedFileNum := len(lo.Filter(lo.Keys(uriToBeIndexed), func(uri string, _ int) bool {
			_, ok := fileToBeIndexed[uri]
			return ok
		}))
		progress.builtFiles.Store(progress.totalFiles - int64(toBeIndexedFileNum))
		initEvent.AddTeaParam(ckg_metrics.TeaParamToIndexFilesCount, toBeIndexedFileNum)
		initEvent.AddTeaParam(ckg_metrics.TeaParamToIndexFilesCountDelta, len(files)-len(uriToBeIndexed))

		// CKG 限流策略
		// 统计当前 db 中已索引的文件的总行数
		// 后续判断，如果超过阈值，则不再索引任何文件
		currentIndexedTotalLine := 0
		for _, uriMeta := range allUriMetas {
			stat, ok := fileStats[uriMeta.Uri]
			if !ok {
				continue
			}
			currentIndexedTotalLine += stat.lineCount
		}

		var (
			fileToBeIndexedCnt   int
			folderToBeIndexedCnt int
		)

		wg := sync.WaitGroup{}

		fileLines := make([]int, 0)
		fileSizes := make([]int, 0)
		// 将需要索引的文件 & 文件夹加入索引队列
		for _, uri := range uriToBeIndexed {
			uri := uri
			info, err := m.fs.Stat(uri.AbsPath)
			if err != nil {
				logs.CtxError(ctx, "error get path stats, err: %v", err)
				continue
			}

			if info.IsDir() {
				folderToBeIndexedCnt++
				wg.Add(1)
				pushed := m.indexFolderTaskQueue.Push(ctx, tasks.NewIndexFolderTasks(userID, uri, func(ctx context.Context, uriData []*model.URIData) {
					defer func() {
						wg.Done()
					}()
					indexEvent := ckg_metrics.GetEvent(ctx)
					if len(uriData) == 0 || storage.IsDeleted() {
						indexEvent.AddTeaParam(ckg_metrics.TeaParamRemoteIndexEmptyUriData, len(uriData) == 0)
						indexEvent.AddTeaParam(ckg_metrics.TeaParamRemoteIndexEntityStorageDeleted, storage.IsDeleted())
						logs.CtxWarn(ctx, "folder init failed: %s, uriData size: %d, storage is deleted: %v", uri.AbsPath, len(uriData), storage.IsDeleted())
						return
					}
					if !storage.IsExistsOnDisk() {
						indexEvent.AddTeaParam(ckg_metrics.TeaParamRemoteIndexEntityStorageNotOnDisk, !storage.IsExistsOnDisk())
						logs.CtxWarn(ctx, "folder init failed, storage is not on disk: %v", !storage.IsExistsOnDisk())
						return
					}
					for _, data := range uriData {
						if err := data_storage.InsertStorage(ctx, storage, data); err != nil {
							indexEvent.AddTeaParam(ckg_metrics.TeaParamRemoteIndexUriDataInsertFailed, true)
							logs.CtxError(ctx, "insertStorage err is %v path is %v", err, uri.AbsPath)
							return
						}
					}
				}, cli, project, useV2Ignore, m.ignoreService, m.ignoreServiceV2, m.fs, storage, embeddingStorage), 1)
				if !pushed {
					wg.Done()
				}
			} else {
				// 判断接下来要索引的文件是否会使 “总行数超过阈值”，若超过阈值，则不索引该文件
				// 但需要保证还有能力索引更小的文件。
				// e.g. 当前已索引 1'400'000 行，下一个文件 100‘001 行，该文件不应索引，但其实还可索引后续文件
				if stat, ok := fileStats[uri.AbsPath]; ok {
					currentIndexedTotalLine += stat.lineCount
					if currentIndexedTotalLine > config.IndexFileLineLimit {
						currentIndexedTotalLine -= stat.lineCount // 消去当前文件行数，以索引后续文件
						continue
					}
				}
				fileToBeIndexedCnt++
				wg.Add(1)
				if stat, ok := fileStats[uri.AbsPath]; ok {
					fileLines = append(fileLines, stat.lineCount)
					fileSizes = append(fileSizes, stat.size)
				}

				pushed := m.indexFileTaskQueue.Push(ctx, tasks.NewIndexFileTasks(knowledgebaseID, userID, ckg_metrics.EventNameCKGInit, uri,
					func(ctx context.Context, uriData []*model.URIData) {
						defer func() {
							wg.Done()
							progress.builtFiles.Add(1)
						}()
						indexEvent := ckg_metrics.GetEvent(ctx)
						if len(uriData) == 0 || storage.IsDeleted() {
							indexEvent.AddTeaParam(ckg_metrics.TeaParamRemoteIndexEmptyUriData, len(uriData) == 0)
							indexEvent.AddTeaParam(ckg_metrics.TeaParamRemoteIndexEntityStorageDeleted, storage.IsDeleted())
							progress.failedIndexFiles.Add(1)
							logs.CtxWarn(ctx, "file init failed %s, uriData size: %d, storage: %v", uri.AbsPath, len(uriData), storage.IsDeleted())
							return
						}
						if !storage.IsExistsOnDisk() {
							indexEvent.AddTeaParam(ckg_metrics.TeaParamRemoteIndexEntityStorageNotOnDisk, !storage.IsExistsOnDisk())
							logs.CtxWarn(ctx, "file init failed, storage is not on disk: %v", !storage.IsExistsOnDisk())
							return
						}
						for _, data := range uriData {
							err := data_storage.InsertStorage(ctx, storage, data)
							if err != nil {
								indexEvent.AddTeaParam(ckg_metrics.TeaParamRemoteIndexUriDataInsertFailed, true)
								progress.failedIndexFiles.Add(1)
								logs.CtxError(ctx, "insertStorage err is %v path is %v", err, uri.AbsPath)
								return
							}
						}
						logs.CtxTrace(ctx, "file %s init succeed", uri.AbsPath)
						progress.successIndexFiles.Add(1)
					}, cli, project, fileToBeIndexedCnt, m.fs, storage), 1)
				if !pushed {
					wg.Done()
					progress.builtFiles.Add(1)
				}
			}
		}
		initEvent.AddTeaParam(ckg_metrics.TeaParamFileIndexCount, fileToBeIndexedCnt)
		initEvent.AddTeaParam(ckg_metrics.TeaParamFolderIndexCount, folderToBeIndexedCnt)
		if len(fileLines) > 0 {
			fileLineSum := lo.Reduce(fileLines, func(agg int, item int, index int) int { return agg + item }, 0)
			initEvent.AddTeaParam(ckg_metrics.TeaParamFileLineAvg, maths.AvgInts(fileLines...))
			initEvent.AddTeaParam(ckg_metrics.TeaParamFileLineMax, maths.MaxInts(fileLines...))
			initEvent.AddTeaParam(ckg_metrics.TeaParamFileLineSum, fileLineSum)
			initEvent.AddTeaParam(ckg_metrics.TeaParamFileSizeAvg, maths.AvgInts(fileSizes...))
			initEvent.AddTeaParam(ckg_metrics.TeaParamFileSizeMax, maths.MaxInts(fileSizes...))
		}

		util.SafeGo(ctx, func() {
			wg.Wait()
			defer initEvent.Report(ctx, true)

			progress.totalFiles = 0
			progress.building.Store(false)

			if _, err := m.fs.Stat(storage.GetStoragePath()); err != nil {
				if os.IsNotExist(err) {
					initEvent.AddTeaParam(ckg_metrics.TeaParamNoEntityStorageAtEnd, true)
					logs.CtxError(ctx, "[remoteIndex] init finished, but no entity storage at the end, storage %s, err: %v", storage.GetStoragePath(), err)
					return
				}
				initEvent.AddTeaParam(ckg_metrics.TeaParamUnknownEntityStorageErr, true)
				logs.CtxError(ctx, "[remoteIndex] init finished, but entity storage unknown error at the end of remote index, err: 5v", err)
				return
			}
			initEvent.AddTeaParam(ckg_metrics.TeaParamUriMetaCount, storage.GetURIMetaCount(ctx, storage.GetConn()))
			initEvent.AddTeaParam(ckg_metrics.TeaParamEntityCount, storage.GetEntityCount(ctx, storage.GetConn()))
			initEvent.AddTeaParam(ckg_metrics.TeaParamRelationCount, storage.GetRelationCount(ctx, storage.GetConn()))

			indexFinish <- bgIndex
			logs.CtxInfo(ctx, "[remoteIndex] project %s initialize finished, build time: %d ms, file indexed: %d, folder indexed: %d",
				project, time.Since(startTime).Milliseconds(), fileToBeIndexedCnt, folderToBeIndexedCnt)
		})
	})
	return nil
}
