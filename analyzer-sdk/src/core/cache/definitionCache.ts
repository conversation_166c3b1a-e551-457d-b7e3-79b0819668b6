import {LRUCache} from "lru-cache";
import {Logger} from "../../utils/logger";
import {ResolvedTypeInfo} from "../share/common";
import {EVENT_CATEGORY_CACHE, EVENT_NAME_FAILED, EVENT_VALUE_NO_CACHE, reportEvent} from "../../utils/event";

const definitionInfoLRUCache = new LRUCache<string, ResolvedTypeInfo>({
  max: 200,
  ttl: 60 * 60 * 1000,
  ttlAutopurge: true,
  updateAgeOnGet: true,
});

const EVENT_PROCESS_GET_DEFINITION_INFO = "get_definition_info";

export function clearCachedDefinitionInfo(): void {
  definitionInfoLRUCache.clear();
}

export function hasDefinitionInfoCached(defInfo: ResolvedTypeInfo): boolean {
  return definitionInfoLRUCache.has(defInfo.uniqueKeyString());
}

export function getCachedDefinitionInfoByKey(languageId: string, key: string): ResolvedTypeInfo {
  const cache = definitionInfoLRUCache.get(key);

  if (!cache) {
    Logger.warn(`getCachedDefinitionInfo: no cached data found for '${key}'`);
    reportEvent(languageId, EVENT_CATEGORY_CACHE, EVENT_PROCESS_GET_DEFINITION_INFO, EVENT_NAME_FAILED, EVENT_VALUE_NO_CACHE).then();
    return null;
  }

  return cache;
}

export function getAllCachedDefinitionInfo(): ResolvedTypeInfo[] {
  const result: ResolvedTypeInfo[] = [];

  definitionInfoLRUCache.forEach(value => {
    result.push(value);
  });

  return result;
}

export function updateDefinitionInfoCache(defInfo: ResolvedTypeInfo): string {
  const key = defInfo.uniqueKeyString();
  definitionInfoLRUCache.set(key, defInfo);
  return key;
}

export function getCachedDefinitionKey(defInfo: ResolvedTypeInfo): string {
  return defInfo.uniqueKeyString();
}