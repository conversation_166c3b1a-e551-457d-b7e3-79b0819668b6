package model

type VirtualProject struct {
	ProjectID string
	Uri       string
}

type ProjectType int32

const (
	ProjectTypeDefault ProjectType = 0
	ProjectTypeVirtual ProjectType = 1
)

type BuildStatus int

const (
	BuildStatusBuilding BuildStatus = 0
	BuildStatusFinished BuildStatus = 1
	BuildStatusFailed   BuildStatus = 2
)

type BuildFailedReason string

const (
	BuildFailedReasonContentInvalid      BuildFailedReason = "content_invalid"
	BuildFailedReasonContentSizeTooLarge BuildFailedReason = "content_size_too_large"
	BuildFailedReasonProjectSizeExceeded BuildFailedReason = "project_size_exceeded"
	BuildFailedReasonProjectItemExceeded BuildFailedReason = "project_item_exceeded"
	BuildFailedReasonOther               BuildFailedReason = "other"
)

// Document struct in vscode-types definition
type Document struct {
	Uri       string `json:"uri"`        // Canonical URI of the document
	Name      string `json:"name"`       // Name of the document, file name or website title, etc.
	Content   string `json:"content"`    // Content of the document, could be empty for files in fs
	ProjectID string `json:"project_id"` // Project ID of the document
}
