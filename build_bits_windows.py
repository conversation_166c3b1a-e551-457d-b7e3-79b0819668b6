import os
import json
import subprocess
import hashlib

# Change working directory to TARGETCODEPATH
target_code_path = os.environ.get('TARGETCODEPATH')
if not target_code_path:
    raise ValueError("TARGETCODEPATH environment variable is not set")
os.chdir(target_code_path)

# Create output directory if it doesn't exist
os.makedirs('output', exist_ok=True)

# Read SDK version from package.json
if os.environ.get('SCM_VERSION_ID') != None:
  SCM_BUILD_NUMBER = os.environ.get('SCM_VERSION_ID').split('.')[-1]
  CKG_SDK_VERSION = f"0.{SCM_BUILD_NUMBER}.0-test"
else:
  with open('./clients/ts/package.json') as f:
    package_data = json.load(f)
    CKG_SDK_VERSION = package_data['version']

print(f"CKG SDK version: {CKG_SDK_VERSION}")

def calculate_md5(filename):
  with open(filename, 'rb') as f:
    md5_hash = hashlib.md5()
    for chunk in iter(lambda: f.read(4096), b''):
      md5_hash.update(chunk)
  return md5_hash.hexdigest()

def upload_file(local_path, remote_path):
  access_key = os.environ.get('CUSTOM_CKG_ACCESS_KEY')
  base_url = "http://marscode-bucket-cn.tos-cn-north.byted.org/ckg_sdk_binary"
  url = f"{base_url}/{CKG_SDK_VERSION}/{remote_path}"
    
  subprocess.run([
    'curl', '-v', '-X', 'PUT', 
    url,
    '--upload-file', local_path,
    '-H', f'X-Tos-Access: {access_key}'
  ], check=True)

# Configure Git to use SSH for private repositories
subprocess.run(['git', 'config', '--global', 'url.ssh://******************/.insteadOf', 'https://code.byted.org/'], check=True)

# Configure Git user and email
subprocess.run(['git', 'config', '--global', 'user.name', 'zhuhang.leon'], check=True)
subprocess.run(['git', 'config', '--global', 'user.email', '<EMAIL>'], check=True)

# Download and write SSH key
try:
  subprocess.run([
    'curl', '-s', 'https://tosv.byted.org/obj/codegraph/ckg_key',
    '-o', os.path.expanduser('~/.ssh/id_rsa')
  ], check=True)
  # Set correct permissions for SSH key
  os.chmod(os.path.expanduser('~/.ssh/id_rsa'), 0o600)
except subprocess.CalledProcessError as e:
  print(f"Failed to download SSH key: {e}")
  raise

# check git user
subprocess.run(['ssh', '-T', 'code.byted.org'], check=True)

# Set Go environment variables
subprocess.run(['go', 'env', '-w', 'GO111MODULE=on'], check=True)
subprocess.run(['go', 'env', '-w', 'GOPROXY=https://go-mod-proxy.byted.org,https://proxy.golang.org,direct'], check=True)
subprocess.run(['go', 'env', '-w', 'GOPRIVATE=*.byted.org,*.everphoto.cn'], check=True)
subprocess.run(['go', 'env', '-w', 'GOSUMDB=sum.golang.google.cn'], check=True)

output_file = 'output/ckg_server_windows_x64.exe'
subprocess.run(['go', 'build', '-ldflags=-s -w', '-o', output_file, './codekg/cmd/server'], check=True)

md5_hash = calculate_md5(output_file)
with open(f'{output_file}.md5', 'w') as f:
  f.write(f'{md5_hash}  {output_file}\n')

commit_id = subprocess.run(['git', 'rev-parse', '--short=7', 'HEAD'], check=True, capture_output=True, text=True).stdout.strip()
with open(f'{output_file}.{commit_id}', 'w') as f:
  f.write(commit_id)
print(f"CKG commit id: {commit_id}")

# Upload files
upload_file(output_file, 'ckg_server_windows_x64.exe')
upload_file(f'{output_file}.md5', 'ckg_server_windows_x64.exe.md5')
upload_file(f'{output_file}.{commit_id}', f'ckg_server_windows_x64.exe.{commit_id}')

subprocess.run(['bit', 'env', 'set', 'BINARY_PATH', target_code_path+"/output"], check=True)
