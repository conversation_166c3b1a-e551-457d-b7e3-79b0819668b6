import {CkgA<PERSON>yzer, AnalyzerFeatureName} from "@byted-ckg/analyzer-sdk";
import {TextDocument} from 'vscode-languageserver-textdocument';
import {Position, Range} from 'vscode-languageserver-types';
import * as fs from 'fs';
import {localLogger} from "./log";

interface InitInfo {
  lspPort: number;
  language: string;
}

let initInfo: InitInfo = null;

export function updateInitInfo(port: number, language: string): void {
  if (!initInfo) {
    initInfo = {
      lspPort: port,
      language,
    };
  }
}

export async function handleInitAnalyzer(workspaceFolder: string): Promise<void> {
  await CkgAnalyzer.openFeature(AnalyzerFeatureName.ENABLE_AST_CACHE_ANALYSIS);
  await CkgAnalyzer.openFeature(AnalyzerFeatureName.ENABLE_DEFINITION_ANALYSIS);
  await CkgAnalyzer.openFeature(AnalyzerFeatureName.ENABLE_PSI_LSP_REQUEST);
  await CkgAnalyzer.openFeature(AnalyzerFeatureName.ENABLE_GO_DEFINITION_ANALYSIS);

  const lspProxy: CkgAnalyzer.LSPProxy = {
    execute: async (command: string, ...args: any[]) => {
      return sendLspRequest(command, ...args);
    },
  };

  const logger = {
    info: (msg: string): void => {
      localLogger.info(msg);
    },
    warn: (msg: string): void => {
      localLogger.warn(msg);
    },
    error: (msg: string): void => {
      localLogger.error(msg);
    },
  };

  await CkgAnalyzer.initAnalyzer({
    workspaceFolders: [{uri: `file://${workspaceFolder}`, name: "repo"}],
    workerJsDir: __dirname,
    logger,
    eventConsumer: null,
    featureChangeListener: null,
    ckg: null,
    treeSitterResourceDir: __dirname,
    lspProxy,
    psiProxy: null,
  });
}

export async function handleInitTreeSitterParser(): Promise<void> {
  const logger = {
    info: (msg: string): void => {
      localLogger.info(msg);
    },
    warn: (msg: string): void => {
      localLogger.warn(msg);
    },
    error: (msg: string): void => {
      localLogger.error(msg);
    },
  };

  await CkgAnalyzer.initTreeSitterParser(__dirname, logger);
}

async function sendLspRequest(command: string, ...args: any[]): Promise<any> {
  if (!initInfo) {
    localLogger.error(`the server is not initialized`);
    return [];
  }
  const url = `http://localhost:${initInfo.lspPort}/lsp/execute`;
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      language: initInfo.language,
      command,
      args,
    }),
  };

  try {
    const response = await fetch(url, options);
    if (!response.ok) {
      localLogger.error(`http response was not ok`);
      return [];
    }
    return await response.json();
  } catch (error) {
    localLogger.error(`fetch exception: ${error}`);
    return [];
  }
}

export async function handleGetDefinitionForRange(file: string, language: string, start: Position, end: Position): Promise<string> {
  const document = TextDocument.create(`file://${file}`, language, 0, fs.readFileSync(file).toString());
  return CkgAnalyzer.getDefinitionsForRange(document, start, end);
}

export async function handleGetDefinitionNodeRange(file: string, language: string, range: Range): Promise<string> {
  const document = TextDocument.create(`file://${file}`, language, 0, fs.readFileSync(file).toString());
  return JSON.stringify(CkgAnalyzer.getDefinitionNodeRange(document, range));
}