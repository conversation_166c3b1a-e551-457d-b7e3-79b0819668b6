import {Range} from "vscode-languageserver-types";
import type {SyntaxNode} from "web-tree-sitter";
import {DefNodeInfo, DefNodeKindEnum} from "../../share";
import {ResolvedTypeInfo} from "../../../share/common";
import {TreeSitterParser} from "../parser";
import {positionToPoint} from "../../../../utils/common";
import {Logger} from "../../../../utils/logger";

export enum TreeSitterTypeCategoryEnum {
  FUNCTION,
  TYPE,
  FILE,
  VARIABLE,
}
export abstract class TreeSitterHandler {
  abstract languageId: string;

  abstract funcTypes: string[];
  abstract typeTypes: string[];
  abstract fileTypes: string[];
  abstract varTypes: string[];

  getParentNodeWithTypeListForRange(node: SyntaxNode, range: Range, typeList: string[]): SyntaxNode {
    const targetNode = node.descendantForPosition(
      {row: range.start.line, column: range.start.character},
      {row: range.end.line, column: range.end.character},
    );
    return this.getParentNodeWithTypeListForNode(targetNode, typeList);
  }

  getParentNodeWithTypeListForNode(targetNode: SyntaxNode, typeList: string[]): SyntaxNode {
    let curNode = targetNode;
    while (curNode && !typeList.includes(curNode.type)) {
      curNode = curNode.parent;
    }

    return curNode;
  }

  getOutermostParentNodeWithTypeListForRange(node: SyntaxNode, range: Range, typeList: string[]): SyntaxNode {
    const targetNode = node.descendantForPosition(
      {row: range.start.line, column: range.start.character},
      {row: range.end.line, column: range.end.character},
    );
    return this.getOutermostParentNodeWithTypeListForNode(targetNode, typeList);
  }

  getOutermostParentNodeWithTypeListForNode(targetNode: SyntaxNode, typeList: string[]): SyntaxNode {
    let result = null;
    let curNode = targetNode;
    while (curNode) {
      if (typeList.includes(curNode.type)) {
        result = curNode;
      }
      curNode = curNode.parent;
    }

    return result;
  }

  getFuncSignatureString(node: SyntaxNode): string {
    const bodyNode = node.childForFieldName("body");
    if (!bodyNode) {
      return null;
    }

    return node.text.slice(0, bodyNode.startIndex - node.startIndex);
  }

  genResolvedTypeInfoFromDefNode(defNodeInfo: DefNodeInfo): ResolvedTypeInfo {
    const result = new ResolvedTypeInfo(
      defNodeInfo.uri,
      defNodeInfo.defNode ? defNodeInfo.defNode.startIndex : -1,
      defNodeInfo.name,
      defNodeInfo.kind,
      defNodeInfo.defNode ? defNodeInfo.defNode.text : defNodeInfo.name,
      defNodeInfo.subDefNodeInfo?.map(item => this.genResolvedTypeInfoFromDefNode(item)) ?? null,
    );

    if (defNodeInfo.defNode && defNodeInfo.kind === DefNodeKindEnum.Function) {
      const funcSignature = this.getFuncSignatureString(defNodeInfo.defNode);
      if (funcSignature) {
        result.addOptionalContent(ResolvedTypeInfo.OPTIONAL_CONTENT_FUNC_SIGNATURE, funcSignature);
      }
    }

    return result;
  }

  getElementInRangeWithTypeList(rootNode: SyntaxNode, range: Range, startIndex: number, endIndex: number, completelyIncluded: boolean, typeList: string[]): SyntaxNode[] {
    const queryString = `
[
${typeList.map(type => `  (${type})`).join("\n")}
] @element
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryString, rootNode, positionToPoint(range.start), positionToPoint(range.end));

    const candidates: SyntaxNode[] = [];
    for (const match of matchResult) {
      const nameNode = match.matches.get("element");

      if (nameNode.length === 1) {
        candidates.push(nameNode[0]);
      }
    }

    const result: SyntaxNode[] = [];
    for (const candidate of candidates) {
      let targetNode = null;
      let curNode = candidate;
      while (curNode) {
        if (typeList.includes(curNode.type)
          && (
            (completelyIncluded && (curNode.startIndex >= startIndex && curNode.endIndex <= endIndex))
            || (!completelyIncluded && !(curNode.startIndex >= endIndex || curNode.endIndex <= startIndex))
          )
        ) {
          targetNode = curNode;
        }
        curNode = curNode.parent;
      }

      if (targetNode && !result.map(item => item.id).includes(targetNode.id)) {
        result.push(targetNode);
      }
    }

    return result;
  }

  getIdentifierInRange(rootNode: SyntaxNode, range: Range, startIndex: number, endIndex: number, name: string): SyntaxNode[] {
    const queryString = `
(
  (_) @symbol
  (#eq? @symbol ${name})
)
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryString, rootNode, positionToPoint(range.start), positionToPoint(range.end));

    const result: SyntaxNode[] = [];
    for (const match of matchResult) {
      const nameNode = match.matches.get("symbol");

      if (nameNode.length === 1 && nameNode[0].startIndex >= startIndex && nameNode[0].endIndex <= endIndex && nameNode[0].namedChildren.length === 0) {
        result.push(nameNode[0]);
      }
    }

    return result;
  }

  getTreeSitterNodeTypes(category: TreeSitterTypeCategoryEnum): string[] {
    switch (category) {
      case TreeSitterTypeCategoryEnum.FUNCTION:
        return this.funcTypes;
      case TreeSitterTypeCategoryEnum.TYPE:
        return this.typeTypes;
      case TreeSitterTypeCategoryEnum.FILE:
        return this.fileTypes;
      case TreeSitterTypeCategoryEnum.VARIABLE:
        return this.varTypes;
      default:
        Logger.error(`Unknown TreeSitterTypeCategoryEnum: ${category}`);
        return [];
    }
  }

  getNotProcessTypeNode(_rootNode: SyntaxNode, _allTypeNodes: SyntaxNode[]): SyntaxNode[] {
    return [];
  }
}