package data_manager

import (
	"context"
	"os"
	"path/filepath"
	"runtime"
	"slices"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/data_storage/consts"
	"ide/ckg/codekg/components/env"
	bizErr "ide/ckg/codekg/components/error"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/tasks"
	"ide/ckg/codekg/components/validator"
	"ide/ckg/codekg/components/version_feature"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/util"

	"ide/ckg/codekg/components/logs"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/lang/maths"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

func (m *manager) InitProject(ctx context.Context, cli knowledgebase.Client, opt *InitProjectOption) error {
	var err error
	logs.CtxInfo(ctx, "InitProject start")

	config := m.config.GetTCCConfig(ctx, cli, opt.UserID)
	logs.CtxInfo(ctx, "codekg config %+v", config)
	abConfig := m.config.GetABConfig(ctx, cli, opt.UserID)
	logs.CtxInfo(ctx, "codekg ab config %+v", abConfig)

	p, ok := m.projectInitProgress.Load(opt.ProjectID)
	if ok && p.(*projectBuildProgress).building.Load() {
		logs.CtxInfo(ctx, "project %s is building, skip duplicate init request", opt.ProjectID)
		return nil // 该 project 正在构建，跳过
	}
	var bgBuilding = false
	bgp, ok := m.bgProjectInitProgress.Load(opt.ProjectID)
	if ok && bgp.(*projectBuildProgress).building.Load() {
		logs.CtxInfo(ctx, "background project %s is building, skip duplicate background init", opt.ProjectID)
		bgBuilding = true
	}
	// 项目的删除标识
	var cancelFlag atomic.Bool
	cancelFlag.Store(false)
	m.projectCancelFlag.Store(opt.ProjectID, &cancelFlag)

	m.projectTypes.Store(opt.ProjectID, model.ProjectTypeDefault)
	// 兼容之前 projectID 为绝对路径的情况
	m.projectUris.Store(opt.ProjectID, util.GetCanonicalURIFromURIOrPath(opt.ProjectID, false))

	logs.CtxInfo(ctx, "ckg_init start")
	// 开始 ckg_init 事件
	initEvent := ckg_metrics.NewEvent(ckg_metrics.EventNameCKGInit, opt.UserID, "", string(env.GetSourceProduct()))
	ctx = ckg_metrics.SetEvent(ctx, initEvent)
	initEvent.AddTeaParam(ckg_metrics.TeaParamProject, opt.ProjectID)
	initEvent.AddTeaParam(ckg_metrics.TeaParamInitIgnoreFileLimit, false)
	initEvent.AddTeaParam(ckg_metrics.TeaParamFileCountLimit, config.FileCountLimit)

	// 检查传入的project id是否合法，兜底校验误添加的Home目录、根目录等
	if !m.checkProjectID(ctx, opt.ProjectID, strings.Trim(config.DefaultOmitProjectRoots, "\n ")) {
		initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepCheckProjectID)
		initEvent.Report(ctx, false)
		return errors.WithMessagef(bizErr.ErrIllegalProjectID, "project id %s is illegal", opt.ProjectID)
	}

	// 每次初始化开始后，先清理一波冗余数据库
	// 本次清除失败了也可继续执行
	envDB, err := env.GetEnvStorage(ctx)
	if err != nil {
		initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepEnvStorage)
		initEvent.Report(ctx, false)
		logs.CtxError(ctx, "[InitProject] no env db, project: %s, err: %+v", opt.ProjectID, err)
		return err
	}
	if err := envDB.RemoveRedundantStorage(ctx, envDB.GetConn(), opt.StoragePath); err != nil {
		logs.CtxWarn(ctx, "[InitProject] RemoveRedundantStorage err is %v", err)
	}

	// 初始化 ignore service
	logs.CtxInfo(ctx, "ignore service start")
	initIgnoreServiceStartTime := time.Now()
	customizedIgnoreRule := ""
	if opt.CustomizedIgnoreFile != "" {
		data, err := m.fs.ReadFile(opt.CustomizedIgnoreFile)
		if err != nil {
			logs.CtxWarn(ctx, "failed to read customized ignore file, err: %v", err)
		} else {
			customizedIgnoreRule = string(data)
		}
	}
	useV2Ignore := m.config.IsFeatureEnabled(ctx, opt.UserID, ckg_config.UseV2Ignore)
	if useV2Ignore {
		err = m.ignoreServiceV2.InitRulesForProjectPath(ctx, opt.UserID, filepath.Clean(opt.ProjectID), config.DefaultIgnoreRules, customizedIgnoreRule)
	} else {
		err = m.ignoreService.AddProject(ctx, filepath.Clean(opt.ProjectID), config.DefaultIgnoreRules, customizedIgnoreRule)
	}
	if err != nil {
		initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepInitIgnoreService)
		initEvent.Report(ctx, false)
		logs.CtxError(ctx, "IgnoreService AddProject err project is %v, err: %+v", opt.ProjectID, err)
		return errors.WithMessagef(err, "IgnoreService AddProject err project is %v", opt.ProjectID)
	}
	initEvent.AddTeaParam(ckg_metrics.TeaParamInitIgnoreServiceCost, time.Since(initIgnoreServiceStartTime).Milliseconds())

	util.SafeGo(ctx, func() {
		err = m.InitRelationManager(ctx, cli, opt.ProjectID, opt.UserID)
		if err != nil {
			logs.CtxInfo(ctx, "[Init] InitRelationManager failed project is %s err is %+v", opt.ProjectID, err)
		} else {
			logs.CtxInfo(ctx, "[Init] InitRelationManager project is %s", opt.ProjectID)
		}
	})

	// 收集所有需要索引的文件，传入 FileCountLimit 作为上限。
	logs.CtxInfo(ctx, "collect all uri start")
	collectAllUrisStartTime := time.Now()
	files, err := m.collectAllURIs(ctx, opt.UserID, opt.ProjectID, config.FileCountLimit, config.FileSizeThreshold)
	if err != nil {
		initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepCollectFiles)
		initEvent.Report(ctx, false)
		logs.CtxError(ctx, "collectAllURIs err project is %v, err: %+v", opt.ProjectID, err)
		return errors.WithMessagef(err, "collectAllURIs err project is %v", opt.ProjectID)
	}
	slices.Sort(files)
	logs.CtxInfo(ctx, "%s has %d files", opt.ProjectID, len(files))
	initEvent.AddTeaParam(ckg_metrics.TeaParamTotalFilesCount, len(files))
	initEvent.AddTeaParam(ckg_metrics.TeaParamCollectAllUrisCost, time.Since(collectAllUrisStartTime).Milliseconds())

	// 尝试获取 2 种策略：1. 本地已有的；2. 可能进行的 A/B 实验 (如果没有命中 A/B，则返回 TCC 中的 baseline)
	// 1. 用户命中 A/B：
	// 		1.a 用户本地已有的 与 A/B 实验的不同：另建 A/B 实验的 db
	// 		1.b 用户本地已有的 与 A/B 实验的相同：复用已有 db
	// 2. 用户未命中 A/B：
	// 		2.a 用户本地已有的 与 TCC baseline 不同：另建符合 TCC baseline config 的 db
	// 		2.b 用户本地已有的 与 TCC baseline 相同：复用已有 db
	// 3. 用户曾命中 A/B，但已取消实验，正在回退，期望对齐 TCC baseline
	// 		3.a 用户本地已有的 与 TCC baseline 不同；另建符合 TCC baseline config 的 db
	//		3.b 用户本地已有的 与 TCC baseline 相同（非正常 case） / 本地无 db：复用已有 db 或新建 db
	// 因此仅判断条件不同，发现与本地已有策略不同后，行为一致。
	chunkingMethodLocal, embeddingModelLocal := m.config.GetLocalStrategyOrDefault(ctx, opt.UserID, opt.ProjectID)
	chunkingMethodTarget, embeddingModelTarget := m.config.GetTargetStrategy(ctx, cli, opt.UserID)
	storageVersionLocal := m.config.GetLocalStorageVersion(ctx, opt.ProjectID)
	storageVersionTarget := m.config.GetTargetStorageVersion(ctx, opt.UserID, env.EmbeddingStorageT)
	// 创建数据库并将所创建数据库的 path 记录到 env_codekg.db 中。
	// 该操作应锁住 storage 和 embeddingStorage，不能删除它们。用于清除冗余 db 的操作也上了 storage 和 embeddingStorage 锁。
	m.storageMu.Lock()
	m.embeddingStorageMu.Lock()
	var storage data_storage.Storage
	var embeddingStorage data_storage.EmbeddingStorage
	var bgNewStorage data_storage.Storage
	var bgNewEmbeddingStorage data_storage.EmbeddingStorage
	var bgErr error
	if chunkingMethodLocal == consts.NoChunkingMethodSinceNoEntityStorage && embeddingModelLocal == consts.NoEmbeddingModelSinceNoEmbeddingStorage && storageVersionLocal == consts.NoStorageVersion {
		// env_codekg.db 中不存在对应 project_id 记录，视为本地不存在实体库或向量库，直接使用 target 策略
		// 创建新的 db，且后续无需 switch db
		storage, embeddingStorage, err = m.getOrCreateStorage(ctx, opt.ProjectID, opt.ProjectURI, chunkingMethodTarget, embeddingModelTarget, env.EmbeddingStorageT, storageVersionTarget, true, opt.StoragePath, envDB)
	} else {
		// env_codekg.db 中有对应 project_id 记录，本地或许存在对应 db
		if m.needForceNew(ctx, envDB, opt.ProjectID, env.EmbeddingStorageT) {
			// 如果本来就需要重建现有 db（db 部分不存在、需要更换向量库），那直接使用 target 策略
			// 后续无需 switch db
			storage, embeddingStorage, err = m.getOrCreateStorage(ctx, opt.ProjectID, opt.ProjectURI, chunkingMethodTarget, embeddingModelTarget, env.EmbeddingStorageT, storageVersionTarget, true, opt.StoragePath, envDB)
		} else {
			// 如果本来无需重建 db，即当前 db 与 env_codekg.db 中 project_id 记录已对齐
			storage, embeddingStorage, err = m.getOrCreateStorage(ctx, opt.ProjectID, opt.ProjectURI, chunkingMethodLocal, embeddingModelLocal, env.EmbeddingStorageT, storageVersionLocal, false, opt.StoragePath, envDB)
			if !bgBuilding && m.needForceNewSinceStrategy(chunkingMethodLocal, embeddingModelLocal, chunkingMethodTarget, embeddingModelTarget, storageVersionLocal, storageVersionTarget) {
				// 如果当前没有 "正在后台索引 db" 的过程，且与 A/B || TCC baseline 无法对齐，后台创建新 db
				bgNewStorage, bgNewEmbeddingStorage, bgErr = m.getOrCreateStorage(ctx, opt.ProjectID, opt.ProjectURI, chunkingMethodTarget, embeddingModelTarget, env.EmbeddingStorageT, storageVersionTarget, true, opt.StoragePath, envDB)
				if bgErr != nil {
					initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
					initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepCreateABStorage)
					initEvent.Report(ctx, false)
					logs.CtxError(ctx, "[InitProject] getOrCreateStorage err when background create storage, project is %v, err: %+v", opt.ProjectID, err)
				}
			}
		}
	}
	if err != nil {
		defer m.storageMu.Unlock()
		defer m.embeddingStorageMu.Unlock()
		initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepCreateStorage)
		initEvent.Report(ctx, false)
		logs.CtxError(ctx, "[InitProject] getOrCreateStorage err project is %v, err: %+v", opt.ProjectID, err)
		return errors.WithMessagef(err, "getOrCreateStorage err project is %v", opt.ProjectID)
	}
	m.storages[opt.ProjectID] = storage
	m.embeddingStorages[opt.ProjectID] = embeddingStorage

	// 记录当前 project 到数据库
	v := version_feature.CKG_VERSION.String()
	err = envDB.UpsertProjectID(ctx, envDB.GetConn(), opt.ProjectID, opt.UserID,
		storage.GetStoragePath(), embeddingStorage.GetStoragePath(),
		storage.GetChunkingMethod(), embeddingStorage.GetEmbeddingModel(),
		v, env.EmbeddingStorageT, embeddingStorage.GetVersion(), model.ProjectType(opt.ProjectType), opt.ProjectURI)
	if err != nil {
		defer m.storageMu.Unlock()
		defer m.embeddingStorageMu.Unlock()
		initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepStoreDBPath)
		initEvent.Report(ctx, false)
		logs.CtxWarn(ctx, "[InitProject] UpsertProjectID project is %v, err is %v", opt.ProjectID, err)
		return errors.WithMessagef(err, "UpsertProjectID err project is %v", opt.ProjectID)
	}
	m.storageMu.Unlock()
	m.embeddingStorageMu.Unlock()

	indexFinish := make(chan bool, 10)
	if err = m.execIndex(ctx, cli, storage, embeddingStorage, opt.ProjectID, opt.UserID, files, false, indexFinish); err != nil {
		logs.CtxWarn(ctx, "[InitProject] execIndex err project is %v, err: %+v", opt.ProjectID, err)
		if bgNewStorage == nil || bgNewEmbeddingStorage == nil {
			return errors.WithMessagef(err, "execIndex err project is %v", opt.ProjectID)
		}
		// bgNewStorage != nil && bgNewEmbeddingStorage != nil，说明此时
		// 该用户命中 a/b 实验或在回退至 baseline
		// 虽然本地现有 db 的索引过程失败，但仍可将本应索引的 background db 直接赋值给 storages 和 embeddingStorages
		m.storageMu.Lock()
		m.embeddingStorageMu.Lock()
		m.storages[opt.ProjectID] = bgNewStorage
		m.embeddingStorages[opt.ProjectID] = bgNewEmbeddingStorage
		m.storageMu.Unlock()
		m.embeddingStorageMu.Unlock()
		logs.CtxInfo(ctx, "[InitProject] since userId %s still can exec index for background storage", opt.UserID)
	}
	// 对另建的 db 也开始索引
	if !bgBuilding && bgNewStorage != nil && bgNewEmbeddingStorage != nil {
		if err = m.execIndex(ctx, cli, bgNewStorage, bgNewEmbeddingStorage, opt.ProjectID, opt.UserID, files, true, indexFinish); err != nil {
			logs.CtxWarn(ctx, "[InitProject] background execIndex err project is %v, err: %+v", opt.ProjectID, err)
			return errors.WithMessagef(err, "background execIndex err project is %v", opt.ProjectID)
		}
	}

	// 最终清除冗余数据库
	util.SafeGo(ctx, func() {
		ctx := util.NewBackgroundContext(ctxvalues.LogIDDefault(ctx))
		checkPeriod := 20 * time.Second
		tick := time.NewTicker(checkPeriod)
		defer tick.Stop()
		for {
			select {
			case bgIndex, ok := <-indexFinish:
				if !ok {
					return
				}
				if bgNewStorage != nil && bgNewEmbeddingStorage != nil && !bgIndex {
					// 如果本次 Init 在后台创建 db，且本次并非 background indexing 的 indexFinish 触发，
					// 则不清除 db，交由 background indexing 结束后的 indexFinish 信号来触发并清除。
				} else {
					m.storageMu.Lock()
					m.embeddingStorageMu.Lock()
					if bgIndex {
						// bg index 结束，无缝切换 entity storage 和 embedding storage
						m.storages[opt.ProjectID] = bgNewStorage
						m.embeddingStorages[opt.ProjectID] = bgNewEmbeddingStorage
						// 记录当前 project 到数据库
						v := version_feature.CKG_VERSION.String()
						err = envDB.UpsertProjectID(ctx, envDB.GetConn(), opt.ProjectID, opt.UserID,
							bgNewStorage.GetStoragePath(), bgNewEmbeddingStorage.GetStoragePath(),
							bgNewStorage.GetChunkingMethod(), bgNewEmbeddingStorage.GetEmbeddingModel(),
							v, env.EmbeddingStorageT, bgNewEmbeddingStorage.GetVersion(), model.ProjectType(opt.ProjectType), opt.ProjectURI)
						if err != nil {
							initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
							initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepStoreABDBPath)
							initEvent.Report(ctx, false)
							logs.CtxWarn(ctx, "[InitProject] UpsertProjectID project is %v, err is %v", opt.ProjectID, err)
						}
					}
					if err := envDB.RemoveRedundantStorage(ctx, envDB.GetConn(), opt.StoragePath); err != nil {
						logs.CtxWarn(ctx, "[localIndex] RemoveRedundantStorage err is %v", err)
					}
					m.embeddingStorageMu.Unlock()
					m.storageMu.Unlock()
					return
				}
			case <-tick.C:
				logs.CtxInfo(ctx, "still indexing at background, project %s, entity db path %s, embedding db path %s",
					opt.ProjectID, storage.GetStoragePath(), embeddingStorage.GetStoragePath())
				tick.Stop()
				tick = time.NewTicker(checkPeriod)
			}
		}
	})
	return nil
}

func (m *manager) execIndex(ctx context.Context, cli knowledgebase.Client, storage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage,
	project, userId string, files []model.URI, bgIndex bool, indexFinish chan<- bool) error {
	if storage == nil || embeddingStorage == nil {
		return errors.New("storage or embedding storage is nil")
	}

	if project == "" || userId == "" {
		return errors.New("project or user ID is empty")
	}

	previousEmbeddingStorageType, ok := m.projects.Load(project)
	if ok {
		logs.CtxDebug(ctx, "[execIndex] the ckg process has already indexed project %d, using %s", project, previousEmbeddingStorageType)
		switch previousEmbeddingStorageType {
		case EmbeddingStorageTypeLocal:
			return m.localIndex(ctx, cli, storage, embeddingStorage, project, userId, files, bgIndex, indexFinish)
		case EmbeddingStorageTypeRemote:
		default:
			logs.CtxWarn(ctx, "[execIndex] previousEmbeddingStorageType illegal: %v", previousEmbeddingStorageType)
		}
	}
	// 除非 repo 之前用的是 remote index，否则彻底禁用 remote index，不允许新打开的项目使用 remote index
	return m.localIndex(ctx, cli, storage, embeddingStorage, project, userId, files, bgIndex, indexFinish)
}

func (m *manager) localIndex(ctx context.Context, cli knowledgebase.Client, storage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage,
	project, userID string, files []model.URI, bgIndex bool, indexFinish chan<- bool) error {
	var err error
	startTime := time.Now()
	initEvent := ckg_metrics.GetEvent(ctx)
	initEvent.AddTeaParam(ckg_metrics.TeaParamIndexType, EmbeddingStorageTypeLocal)
	initEvent.AddTeaParam(ckg_metrics.TeaParamChunkingMethod, storage.GetChunkingMethod())
	initEvent.AddTeaParam(ckg_metrics.TeaParamEmbeddingModel, embeddingStorage.GetEmbeddingModel())

	config := m.config.GetTCCConfig(ctx, cli, userID)
	logs.CtxInfo(ctx, "create local data storage")

	err = m.clearProjectOutdatedData(ctx, storage, embeddingStorage, files)
	if err != nil {
		initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepClearProjectNotExistData)
		initEvent.Report(ctx, false)
		return errors.WithMessagef(err, "clearProjectOutdatedData err project is %v", project)
	}
	m.projects.Store(project, EmbeddingStorageTypeLocal)

	logs.CtxInfo(ctx, "calculate all files that need indexing")
	// 计算所有需要 index 的文件
	fileToBeIndexed := make(map[string]*model.URIStatus)
	uriToBeIndexed := make(map[string]*model.URIStatus)
	fileStats := make(map[string]*fileInformation)
	computeHashStartTime := time.Now()

	statErrFiles, relErrFiles := make([]string, 0), make([]string, 0)
	createInfoErrFiles, createInfoErrFolders := make([]string, 0), make([]string, 0)
	logs.CtxInfo(ctx, "currently use local embedding storage? %v", env.LocalEmbedding)
	for idx, file := range files {
		if idx%1000 == 0 {
			var rtm runtime.MemStats
			runtime.ReadMemStats(&rtm)
			logs.CtxInfo(ctx, "[localIndex] before gc, mem stat, alloc: %.2f MB, in use: %.2f MB",
				float64(rtm.HeapAlloc)/1024/1024, float64(rtm.HeapInuse)/1024/1024)
			gcStartTime := time.Now()
			runtime.GC()
			logs.CtxInfo(ctx, "[localIndex] gc cost: %d ms", time.Since(gcStartTime).Milliseconds())
			runtime.ReadMemStats(&rtm)
			logs.CtxInfo(ctx, "[localIndex] after gc, mem stat, alloc: %.2f MB, in use: %.2f MB",
				float64(rtm.HeapAlloc)/1024/1024, float64(rtm.HeapInuse)/1024/1024)
		}

		info, err := m.fs.Stat(file)
		if err != nil {
			statErrFiles = append(statErrFiles, file)
			if os.IsNotExist(err) {
				logs.CtxInfo(ctx, "[localIndex] file is not exist path is %v", validator.EncryptPath(file))
				continue
			}
			logs.CtxError(ctx, "[localIndex] Stat err, file is %v, err is %v", validator.EncryptPath(file), err)
			continue
		}
		relPath, err := m.fs.Rel(project, file)
		if err != nil {
			relErrFiles = append(relErrFiles, file)
			logs.CtxError(ctx, "[localIndex] failed to relative path %s, project: %s", validator.EncryptPath(file), project)
			continue
		}
		var uriStatus *model.URIStatus
		if !info.IsDir() {
			fileInfo, err := m.createFileInfo(ctx, project, file)
			if err != nil {
				createInfoErrFiles = append(createInfoErrFiles, file)
				if os.IsNotExist(err) {
					logs.CtxInfo(ctx, "[localIndex] file %v not exist", file)
					continue
				}
				logs.CtxError(ctx, "[localIndex] createFileInfo err file is %v, err is %v", validator.EncryptPath(file), err)
				continue
			}
			if fileInfo.lineCount >= config.SingleFileLineThreshold {
				logs.CtxTrace(ctx, "[localIndex] file %s ignored, too long: %d", validator.EncryptPath(file), fileInfo.lineCount)
				continue
			}
			fileStats[file] = fileInfo
			uriStatus = &model.URIStatus{
				AbsPath:     file,
				RelPath:     relPath,
				ContentHash: fileInfo.hash,
				UniqueID:    fileInfo.uniqueID,
			}
		} else {
			folderInfo, err := m.createFolderInfo(ctx, file)
			if err != nil {
				createInfoErrFolders = append(createInfoErrFolders, file)
				if os.IsNotExist(err) {
					logs.CtxInfo(ctx, "[localIndex] folder %v not exist", validator.EncryptPath(file))
					continue
				}
				logs.CtxError(ctx, "[localIndex] createFolderInfo err folder is %v, err is %v", validator.EncryptPath(file), err)
				continue
			}
			uriStatus = &model.URIStatus{
				AbsPath:     file,
				RelPath:     relPath,
				ContentHash: folderInfo.hash,
			}
		}
		uriMeta, err := storage.GetURIMetaFromURIWithoutContent(ctx, storage.GetConn(), uriStatus)
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			uriToBeIndexed[file] = uriStatus
			if !info.IsDir() {
				fileToBeIndexed[file] = uriStatus
			}
		} else if err == nil && (uriMeta.ContentHash != uriStatus.ContentHash || uriMeta.HasEmbedding != 1) {
			uriToBeIndexed[file] = uriStatus
			if !info.IsDir() {
				fileToBeIndexed[file] = uriStatus
			}
		} else if err != nil {
			logs.CtxInfo(ctx, "[localIndex] error get uri meta from uri, err: %v", err)
		}
	}
	initEvent.AddTeaParam(ckg_metrics.TeaParamComputeHashCost, time.Since(computeHashStartTime).Milliseconds())
	initEvent.AddTeaParam(ckg_metrics.TeaParamStatErrFileNum, len(statErrFiles))
	initEvent.AddTeaParam(ckg_metrics.TeaParamRelErrFileNum, len(relErrFiles))
	initEvent.AddTeaParam(ckg_metrics.TeaParamCreateInfoErrFileNum, len(createInfoErrFiles))
	initEvent.AddTeaParam(ckg_metrics.TeaParamCreateInfoErrFolderNum, len(createInfoErrFolders))

	// 每次新的请求，重新初始化一次 progress
	atomicValue := &atomic.Int64{}
	atomicValue.Store(int64(len(fileStats) - len(fileToBeIndexed)))
	building := &atomic.Bool{}
	building.Store(true)
	progress := &projectBuildProgress{
		totalFiles:        int64(len(fileStats)),
		builtFiles:        atomicValue,
		building:          building,
		successIndexFiles: &atomic.Int64{},
		failedIndexFiles:  &atomic.Int64{},
	}
	if bgIndex {
		m.bgProjectInitProgress.Store(project, progress)
	} else {
		m.projectInitProgress.Store(project, progress)
	}

	useV2Ignore := m.config.IsFeatureEnabled(ctx, userID, ckg_config.UseV2Ignore)
	wg := sync.WaitGroup{}

	util.SafeGo(ctx, func() {
		ctx := util.NewBackgroundContext(ctxvalues.LogIDDefault(ctx))

		allUriMetas, err := storage.GetAllURIMetaWithoutContent(ctx, storage.GetConn())
		if err != nil {
			progress.building.Store(false)
			initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
			initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepGetAllUriMeta)
			logs.CtxError(ctx, "error get all uri meta, err: %v", err)
			return
		}
		// 服务端限流策略
		// 如果 "并集(即将索引的文件 (uri), 本地已有的文件 (uri))" 超出限制，则只索引部分 "即将索引的文件"，直到和"强制索引文件数量的最大值" 相等
		// 注：以下逻辑与 remoteIndex 中相同
		alreadyIndexedUriMetaNum := len(allUriMetas)
		indexUriMetaNameSum := lo.Map(allUriMetas, func(m data_storage.StorageURIMeta, _ int) string { return m.Uri })
		indexUriMetaNameSum = lo.Union(indexUriMetaNameSum, lo.Keys(uriToBeIndexed))
		if len(indexUriMetaNameSum) > config.ForceIndexFileCountLimit {
			keys := lo.Keys(uriToBeIndexed)
			keysFixedNum := lo.Slice(keys, 0, config.ForceIndexFileCountLimit-alreadyIndexedUriMetaNum)
			uriToBeIndexed = lo.PickByKeys(uriToBeIndexed, keysFixedNum)
		}
		toBeIndexedFileNum := len(lo.Filter(lo.Keys(uriToBeIndexed), func(uri string, _ int) bool {
			_, ok := fileToBeIndexed[uri]
			return ok
		}))
		progress.builtFiles.Store(progress.totalFiles - int64(toBeIndexedFileNum))
		initEvent.AddTeaParam(ckg_metrics.TeaParamToLocalIndexFilesCount, toBeIndexedFileNum)
		initEvent.AddTeaParam(ckg_metrics.TeaParamToLocalIndexFilesCountDelta, len(files)-len(uriToBeIndexed))

		// CKG 限流策略
		// 统计当前 db 中已索引的文件的总行数
		// 后续判断，如果超过阈值，则不再索引任何文件
		currentIndexedTotalLine := 0
		for _, uriMeta := range allUriMetas {
			stat, ok := fileStats[uriMeta.Uri]
			if !ok {
				continue
			}
			currentIndexedTotalLine += stat.lineCount
		}

		// 不从远端下载本地不存在的文件数据缓存
		var (
			fileToBeIndexedCnt   int
			folderToBeIndexedCnt int
		)

		wg := &wg

		fileLines := make([]int, 0)
		fileSizes := make([]int, 0)
		// 将需要索引的文件 & 文件夹加入索引队列
		var cancelCount = 0
		for _, uri := range uriToBeIndexed {
			uri := uri
			info, err := m.fs.Stat(uri.AbsPath)
			if err != nil {
				logs.CtxError(ctx, "error get path stats, err: %v", err)
				continue
			}
			cancelFlg, ok := m.projectCancelFlag.Load(project)
			if !ok || cancelFlg.(*atomic.Bool).Load() {
				cancelCount++
				continue
			}

			var pushed bool
			if info.IsDir() {
				folderToBeIndexedCnt++
				wg.Add(1)
				pushed = m.indexFolderTaskQueue.Push(ctx, tasks.NewIndexFolderTasks(userID, uri, func(ctx context.Context, uriData []*model.URIData) {
					defer func() {
						wg.Done()
					}()
					localIndexEvent := ckg_metrics.GetEvent(ctx)
					if len(uriData) == 0 || storage.IsDeleted() || embeddingStorage.IsDeleted() {
						localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmptyUriData, len(uriData) == 0)
						localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEntityStorageDeleted, storage.IsDeleted())
						localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbeddingStorageDeleted, embeddingStorage.IsDeleted())
						logs.CtxWarn(ctx, "folder init failed: %s, uriData size: %d, storage is deleted: %v, embedding storage is deleted: %v",
							validator.EncryptPath(uri.AbsPath), len(uriData), storage.IsDeleted(), embeddingStorage.IsDeleted())
						return
					}
					if !storage.IsExistsOnDisk() || !embeddingStorage.IsExistsOnDisk() {
						localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEntityStorageNotOnDisk, !storage.IsExistsOnDisk())
						localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbeddingStorageNotOnDisk, !embeddingStorage.IsExistsOnDisk())
						logs.CtxWarn(ctx, "folder init failed, storage is not on disk: %v, embedding storage is not on disk: %v",
							!storage.IsExistsOnDisk(), !embeddingStorage.IsExistsOnDisk())
						return
					}
					for _, data := range uriData {
						// folder 不需要计算 embedding
						err := data_storage.InsertStorage(ctx, storage, data)
						if err != nil {
							localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexUriDataInsertFailed, true)
							logs.CtxError(ctx, "insertStorage err is %v path is %v", err, validator.EncryptPath(uri.AbsPath))
							return
						}
						if err = data_storage.MarkFileAsEmbedded(ctx, storage, data); err != nil {
							localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexUpdateStatusFailed, true)
							logs.CtxError(ctx, "updateFileEmbeddingStatus err is %v path is %s", err, validator.EncryptPath(uri.AbsPath))
							return
						}
						if err := storage.UpdateURIMetaStatus(ctx, storage.GetConn(), data.Status.AbsPath, int(model.BuildStatusFinished)); err != nil {
							// localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexMarkFileAsEmbeddedErr, true)
							logs.CtxError(ctx, "markURIMetaAsIndexed err is %v path is %s", err, validator.EncryptPath(uri.AbsPath))
							progress.failedIndexFiles.Add(1)
							return
						}
					}
				}, cli, project, useV2Ignore, m.ignoreService, m.ignoreServiceV2, m.fs, storage, embeddingStorage), 1)
				if !pushed {
					wg.Done()
				}
			} else {
				// 判断接下来要索引的文件是否会使 “总行数超过阈值”，若超过阈值，则不索引该文件
				// 但需要保证还有能力索引更小的文件。
				// e.g. 当前已索引 1'400'000 行，下一个文件 100‘001 行，该文件不应索引，但其实还可索引后续文件
				if stat, ok := fileStats[uri.AbsPath]; ok {
					currentIndexedTotalLine += stat.lineCount
					if currentIndexedTotalLine > config.IndexFileLineLimit {
						currentIndexedTotalLine -= stat.lineCount // 消去当前文件行数，以索引后续文件
						continue
					}
				}
				fileToBeIndexedCnt++
				wg.Add(1)
				if stat, ok := fileStats[uri.AbsPath]; ok {
					fileLines = append(fileLines, stat.lineCount)
					fileSizes = append(fileSizes, stat.size)
				}

				var fallbackToSingleIndex = false
				if m.config.IsIndexFeatureEnabled(ctx, userID, ckg_config.EnableBatchIndex) {
					attr := model.IndexAttribute{ChunkingMethod: storage.GetChunkingMethod(), EmbeddingModel: embeddingStorage.GetEmbeddingModel()}
					mgr, _ := m.batchEmbeddingMgr.GetOrCreateProjectEmbeddingQueueMgr(userID, project, m.config, storage, embeddingStorage,
						cli, attr, m.batchLocalIndexEmbeddingTaskQueue, tasks.EmbeddingQueueMgrSetting{})
					pushed = m.batchLocalIndexSplitTaskQueue.Push(ctx, tasks.NewBatchLocalIndexSplitFileTask(userID, project, model.ProjectTypeDefault, m.config,
						uri, attr,
						&model.IndexTaskAttribute{},
						cli, useV2Ignore, m.ignoreService, m.ignoreServiceV2, m.fs, func(ctx context.Context, uriData []*model.URIData, embeddingData []*model.EmbeddingData, todos []*model.BatchEmbeddingMsg) {
							defer func() {
								wg.Done()
								progress.builtFiles.Add(1)
							}()
							err := data_storage.WriteChunkingAndEmbeddingData(ctx, storage, embeddingStorage, uri, uriData, embeddingData)
							if err != nil {
								// 日志已经在 WriteChunkingAndEmbeddingData 妥善输出了
								progress.failedIndexFiles.Add(1)
								return
							}
							progress.successIndexFiles.Add(1)
							logs.CtxDebug(ctx, "file %s split succeed", validator.EncryptPath(uri.AbsPath))
							// 切分结束，切分结果 & Abase 中 Embedding 缓存已分别写入本地实体库 & 向量库。
							// 将未命中缓存的 Embedding Content 写入到
							for _, todo := range todos {
								mgr.Push(ctx, todo)
							}
						}), 1)
					if !pushed {
						fallbackToSingleIndex = true
					}
				}
				if fallbackToSingleIndex || !m.config.IsIndexFeatureEnabled(ctx, userID, ckg_config.EnableBatchIndex) {
					pushed = m.localIndexFileTaskQueue.Push(ctx, tasks.NewLocalIndexFileTasks(storage, embeddingStorage, userID, ckg_metrics.EventNameCKGInit, uri,
						func(ctx context.Context, uriData []*model.URIData, embeddingData []*model.EmbeddingData) {
							defer func() {
								wg.Done()
								progress.builtFiles.Add(1)
							}()
							localIndexEvent := ckg_metrics.GetEvent(ctx)
							if len(uriData) == 0 || storage.IsDeleted() || embeddingStorage.IsDeleted() {
								localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmptyUriData, len(uriData) == 0)
								localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEntityStorageDeleted, storage.IsDeleted())
								localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbeddingStorageDeleted, embeddingStorage.IsDeleted())
								logs.CtxWarn(ctx, "file init failed %s, uriData size: %d, storage: %v, embedding storage: %v",
									validator.EncryptPath(uri.AbsPath), len(uriData), storage.IsDeleted(), embeddingStorage.IsDeleted())
								progress.failedIndexFiles.Add(1)
								return
							}
							if !storage.IsExistsOnDisk() || !embeddingStorage.IsExistsOnDisk() {
								localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEntityStorageNotOnDisk, !storage.IsExistsOnDisk())
								localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbeddingStorageNotOnDisk, !embeddingStorage.IsExistsOnDisk())
								logs.CtxWarn(ctx, "file init failed, storage is not on disk: %v, embedding storage is not on disk: %v",
									!storage.IsExistsOnDisk(), !embeddingStorage.IsExistsOnDisk())
								return
							}
							for _, data := range uriData {
								err := data_storage.InsertStorageWithEmbedding(ctx, storage, embeddingStorage, data)
								if err != nil {
									localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexUriDataInsertFailed, true)
									logs.CtxError(ctx, "insertStorage err is %v path is %v", err, validator.EncryptPath(uri.AbsPath))
									progress.failedIndexFiles.Add(1)
									return
								}
							}
							for _, data := range uriData {
								if err = data_storage.MarkFileAsEmbedded(ctx, storage, data); err != nil {
									localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexUpdateStatusFailed, true)
									logs.CtxError(ctx, "updateFileEmbeddingStatus err is %v path is %s", err, validator.EncryptPath(uri.AbsPath))
									progress.failedIndexFiles.Add(1)
									return
								}
							}
							if len(embeddingData) == 0 {
								progress.failedIndexFiles.Add(1)
								return
							}
							for _, eData := range embeddingData {
								if err := data_storage.InsertEmbeddingStorage(ctx, storage, embeddingStorage, eData); err != nil {
									localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexEmbeddingInsertFailed, true)
									progress.failedIndexFiles.Add(1)
									logs.CtxError(ctx, "insertEmbeddingStorage err is %v path is %v", err, validator.EncryptPath(uri.AbsPath))
									return
								}
							}

							for _, data := range uriData {
								if err := storage.UpdateURIMetaStatus(ctx, storage.GetConn(), data.Status.AbsPath, int(model.BuildStatusFinished)); err != nil {
									// localIndexEvent.AddTeaParam(ckg_metrics.TeaParamLocalIndexMarkFileAsEmbeddedErr, true)
									logs.CtxError(ctx, "markURIMetaAsIndexed err is %v path is %s", err, validator.EncryptPath(uri.AbsPath))
									progress.failedIndexFiles.Add(1)
									return
								}
							}
							progress.successIndexFiles.Add(1)
							logs.CtxTrace(ctx, "file %s init succeed", validator.EncryptPath(uri.AbsPath))
						}, cli, project, model.ProjectTypeDefault, useV2Ignore, m.ignoreService, m.ignoreServiceV2, m.config, m.fs, &model.IndexTaskAttribute{}), 1)
				}
				if !pushed {
					wg.Done()
					progress.builtFiles.Add(1)
				}
			}
		}
		initEvent.AddTeaParam(ckg_metrics.TeaParamFileIndexCount, fileToBeIndexedCnt)
		initEvent.AddTeaParam(ckg_metrics.TeaParamFolderIndexCount, folderToBeIndexedCnt)
		if len(fileLines) > 0 {
			fileLineSum := lo.Reduce(fileLines, func(agg int, item int, index int) int { return agg + item }, 0)
			initEvent.AddTeaParam(ckg_metrics.TeaParamFileLineAvg, maths.AvgInts(fileLines...))
			initEvent.AddTeaParam(ckg_metrics.TeaParamFileLineMax, maths.MaxInts(fileLines...))
			initEvent.AddTeaParam(ckg_metrics.TeaParamFileLineSum, fileLineSum)
			initEvent.AddTeaParam(ckg_metrics.TeaParamFileSizeAvg, maths.AvgInts(fileSizes...))
			initEvent.AddTeaParam(ckg_metrics.TeaParamFileSizeMax, maths.MaxInts(fileSizes...))
		}
		if cancelCount > 0 {
			logs.CtxInfo(ctx, "project cancel flag is true, total %d file does not push into queue", cancelCount)
		}

		util.SafeGo(ctx, func() {
			wg.Wait()
			defer initEvent.Report(ctx, false)

			progress.totalFiles = 0
			progress.building.Store(false)

			if _, err := m.fs.Stat(storage.GetStoragePath()); err != nil {
				if os.IsNotExist(err) {
					initEvent.AddTeaParam(ckg_metrics.TeaParamNoEntityStorageAtEnd, true)
					logs.CtxError(ctx, "[localIndex] init finished, but no entity storage at the end, storage %s, err: %v", storage.GetStoragePath(), err)
					return
				}
				initEvent.AddTeaParam(ckg_metrics.TeaParamUnknownEntityStorageErr, true)
				logs.CtxError(ctx, "[remoteIndex] init finished, but entity storage unknown error at the end of local index, err: 5v", err)
				return
			}
			if _, err := m.fs.Stat(embeddingStorage.GetStoragePath()); err != nil {
				if os.IsNotExist(err) {
					initEvent.AddTeaParam(ckg_metrics.TeaParamNoEmbeddingStorageAtEnd, true)
					logs.CtxError(ctx, "[localIndex] init finished, but no embedding storage at the end, embedding storage %s, err: %v", embeddingStorage.GetStoragePath(), err)
					return
				}
				initEvent.AddTeaParam(ckg_metrics.TeaParamUnknownEmbeddingStorageErr, true)
				logs.CtxError(ctx, "[remoteIndex] init finished, but embedding storage unknown error at the end of local index, err: 5v", err)
				return
			}
			initEvent.AddTeaParam(ckg_metrics.TeaParamUriMetaCount, storage.GetURIMetaCount(ctx, storage.GetConn()))
			initEvent.AddTeaParam(ckg_metrics.TeaParamEntityCount, storage.GetEntityCount(ctx, storage.GetConn()))
			initEvent.AddTeaParam(ckg_metrics.TeaParamRelationCount, storage.GetRelationCount(ctx, storage.GetConn()))

			indexFinish <- bgIndex
			logs.CtxInfo(ctx, "[localIndex] project %s initialize finished, build time: %d ms, file indexed: %d, folder indexed: %d",
				project, time.Since(startTime).Milliseconds(), progress.successIndexFiles.Load(), folderToBeIndexedCnt)
		})
	})
	return nil
}

func (m *manager) DeleteIndex(ctx context.Context, project string) error {
	// delete 的时候需要删除索引进度
	m.projectInitProgress.Delete(project)

	// 先取消改 project 的构建
	err := m.CancelIndex(ctx, project)
	if err != nil {
		logs.CtxError(ctx, "error cancel index before delete, err: %v", err)
		return errors.WithMessagef(err, "error delete index")
	}

	m.storageMu.Lock()
	defer m.storageMu.Unlock()

	// 删除 storage
	if storage, ok := m.storages[project]; ok {
		err := storage.Delete()
		if err != nil {
			logs.CtxError(ctx, "failed to delete storage, err: %v", err)
			return errors.WithMessagef(err, "error delete index")
		}
		delete(m.storages, project)
	}

	m.embeddingStorageMu.Lock()
	defer m.embeddingStorageMu.Unlock()
	// 删除 embedding storage
	if embeddingStorage, ok := m.embeddingStorages[project]; ok {
		err := embeddingStorage.Delete()
		if err != nil {
			logs.CtxError(ctx, "failed to delete embedding storage, err: %v", err)
			return errors.WithMessagef(err, "error delete index")
		}
		delete(m.embeddingStorages, project)
	}

	return nil
}

func (m *manager) CancelIndex(ctx context.Context, project string) error {
	// cancel 的时候也删除进度
	m.projectInitProgress.Delete(project)

	cancelFlag, ok := m.projectCancelFlag.Load(project)
	if ok {
		if cancelFlag.(*atomic.Bool).CompareAndSwap(false, true) {
			logs.CtxInfo(ctx, "set project %s cancelFlag success", project)
			m.projectCancelFlag.Delete(project)
		}
	} else {
		logs.CtxInfo(ctx, "set project %s cancelFlag failed, project cancel already", project)
	}

	removedFolderCount, err := m.indexFolderTaskQueue.RemoveIf(func(t tasks.Task) bool {
		return t.GetProject() == project
	})
	if err != nil {
		return errors.WithMessagef(err, "error cancel project, stage: remove folder tasks")
	}

	removedFileCount, err := m.indexFileTaskQueue.RemoveIf(func(t tasks.Task) bool {
		return t.GetProject() == project
	})
	if err != nil {
		return errors.WithMessagef(err, "error cancel project, stage: remove file tasks")
	}

	removedLocalIndexFileCount, err := m.localIndexFileTaskQueue.RemoveIf(func(t tasks.Task) bool {
		return t.GetProject() == project
	})
	if err != nil {
		return errors.WithMessagef(err, "error cancel project, stage: remove local index file tasks")
	}

	removedBatchLocalIndexSplitTaskCount, err := m.batchLocalIndexSplitTaskQueue.RemoveIf(func(t tasks.Task) bool {
		return t.GetProject() == project
	})
	if err != nil {
		return errors.WithMessagef(err, "error cancel project, stage: remove batch local split tasks")
	}

	removedBatchLocalIndexEmbeddingTaskCount, err := m.batchLocalIndexEmbeddingTaskQueue.RemoveIf(func(t tasks.Task) bool {
		return t.GetProject() == project
	})
	if err != nil {
		return errors.WithMessagef(err, "error cancel project, stage: remove batch local embedding tasks")
	}

	logs.CtxInfo(ctx, "cancel project, removed folder tasks: %d, removed file tasks: %d, removed local index file tasks: %d,"+
		" removed batch local index split tasks: %d, removed batch local index embedding tasks: %d",
		removedFolderCount, removedFileCount, removedLocalIndexFileCount,
		removedBatchLocalIndexSplitTaskCount, removedBatchLocalIndexEmbeddingTaskCount)
	return nil
}

func (m *manager) InitVirtualProject(ctx context.Context, cli knowledgebase.Client, projectID string, uri model.URI,
	userID string, loadFilesFromFs bool, globsToLoad []string) error {
	logs.CtxInfo(ctx, "init virtual project, projectID: %s, uri: %s, userID: %s, loadFilesFromFs: %v, globsToLoad: %v",
		projectID, uri, userID, loadFilesFromFs, globsToLoad)

	config := m.config.GetTCCConfig(ctx, cli, userID)
	logs.CtxInfo(ctx, "codekg config %+v", config)

	p, ok := m.projectInitProgress.Load(projectID)
	if ok && p.(*projectBuildProgress).building.Load() {
		logs.CtxInfo(ctx, "project %s is building, skip duplicate init request", projectID)
		return nil // 该 project 正在构建，跳过
	}

	logs.CtxInfo(ctx, "ckg_init virtual project start: %s, %s, %s, %v", projectID, validator.EncryptPath(uri), userID, loadFilesFromFs)
	// TODO(ldx): add tea initEvent

	envDB, err := env.GetEnvStorage(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[GetLocalStrategyOrDefault] no env db")
		return err
	}

	m.projects.Store(projectID, EmbeddingStorageTypeLocal)
	m.projectTypes.Store(projectID, model.ProjectTypeVirtual)
	if len(uri) == 0 {
		uri = util.GetCanonicalURIFromURIOrPath(projectID, true)
	}
	m.projectUris.Store(projectID, uri)

	// virtual project 的 storagePath 是全局的 storagePath
	storagePath := env.GetStoragePath()

	virtualProjectConfig := m.config.GetTCCConfig(ctx, cli, userID).VirtualProjectConfig

	chunkingMethodLocal, embeddingModelLocal := m.config.GetLocalStrategyOrDefault(ctx, userID, projectID)
	chunkingMethodTarget, embeddingModelTarget := m.config.GetVirtualProjectTargetStrategy(ctx, cli, userID)
	storageVersionLocal := m.config.GetLocalStorageVersion(ctx, projectID)
	storageVersionTarget := m.config.GetTargetStorageVersion(ctx, userID, env.EmbeddingStorageT)
	// 创建数据库并将所创建数据库的 path 记录到 env_codekg.db 中。
	// 该操作应锁住 storage 和 embeddingStorage，不能删除它们。用于清除冗余 db 的操作也上了 storage 和 embeddingStorage 锁。
	m.storageMu.Lock()
	m.embeddingStorageMu.Lock()
	defer m.storageMu.Unlock()
	defer m.embeddingStorageMu.Unlock()
	var storage data_storage.Storage
	var embeddingStorage data_storage.EmbeddingStorage
	var uriToIndexIds []int
	var urisToConvert []model.URI
	if chunkingMethodLocal == consts.NoChunkingMethodSinceNoEntityStorage && embeddingModelLocal == consts.NoEmbeddingModelSinceNoEmbeddingStorage {
		// env_codekg.db 中不存在对应 project_id 记录，视为本地不存在实体库或向量库，直接使用 target 策略
		// 创建新的 db，且后续无需 switch db
		storage, embeddingStorage, err = m.getOrCreateStorage(ctx, projectID, uri, chunkingMethodTarget, embeddingModelTarget, env.EmbeddingStorageT, storageVersionTarget, true, storagePath, envDB)
	} else {
		// env_codekg.db 中有对应 project_id 记录，本地或许存在对应 db
		if m.needForceNew(ctx, envDB, projectID, env.EmbeddingStorageT) {
			// 如果本来就需要重建现有 db（db 部分不存在、需要更换向量库），那直接使用 target 策略
			// 后续无需 switch db
			storage, embeddingStorage, err = m.getOrCreateStorage(ctx, projectID, uri, chunkingMethodTarget, embeddingModelTarget, env.EmbeddingStorageT, storageVersionTarget, true, storagePath, envDB)
		} else if m.needForceNewSinceStrategy(chunkingMethodLocal, embeddingModelLocal, chunkingMethodTarget, embeddingModelTarget, storageVersionLocal, storageVersionTarget) {
			// 切分算法、embedding 模型或者 db version 对不上，需要重新创建 DB。
			// #Doc 不会尝试前后台索引 - 平滑切换 storage
			storage, embeddingStorage, err = m.getOrCreateStorage(ctx, projectID, uri, chunkingMethodTarget, embeddingModelTarget, env.EmbeddingStorageT, storageVersionTarget, true, storagePath, envDB)
		} else {
			// 如果本来无需重建 db，即当前 db 与 env_codekg.db 中 project_id 记录已对齐
			storage, embeddingStorage, err = m.getOrCreateStorage(ctx, projectID, uri, chunkingMethodLocal, embeddingModelLocal, env.EmbeddingStorageT, storageVersionLocal, false, storagePath, envDB)
		}
	}
	if err != nil {
		// initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		// initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepCreateStorage)
		// initEvent.Report(ctx, false)
		logs.CtxError(ctx, "[InitVirtualProject] getOrCreateStorage err project is %v, err: %+v", projectID, err)
		return errors.WithMessagef(err, "getOrCreateStorage err project is %v", projectID)
	}
	m.storages[projectID] = storage
	m.embeddingStorages[projectID] = embeddingStorage

	// 如果 loadFilesFromFs 为 true，则从本地加载文件
	// 否则，从数据库中加载正在索引的 uri
	if loadFilesFromFs {
		uriToIndexIds, urisToConvert, err = m.loadAllUriMetaFromFS(ctx, projectID, uri, globsToLoad, storage, virtualProjectConfig.FileCountLimit, virtualProjectConfig.FileSizeLimit, virtualProjectConfig.TotalFileSizeLimit)
		if err != nil {
			logs.CtxError(ctx, "[InitVirtualProject] loadAllUriMetaFromFS err project is %v, err: %+v", projectID, err)
			return errors.WithMessagef(err, "loadAllUriMetaFromFS err project is %v", projectID)
		}

		logs.CtxInfo(ctx, "[InitVirtualProject] loadAllUriMetaFromFS success, urisToConvert: %v", urisToConvert)
	} else {
		uriToIndexIds, err = storage.GetAllIndexingUriIDs(ctx, storage.GetConn())
		if err != nil {
			logs.CtxError(ctx, "[InitVirtualProject] GetAllIndexingUriIDs err project is %v, err: %+v", projectID, err)
			return errors.WithMessagef(err, "GetAllIndexingUriIDs err project is %v", projectID)
		}
	}

	v := version_feature.CKG_VERSION.String()
	err = envDB.UpsertProjectID(ctx, envDB.GetConn(), projectID, userID,
		storage.GetStoragePath(), embeddingStorage.GetStoragePath(),
		storage.GetChunkingMethod(), embeddingStorage.GetEmbeddingModel(),
		v, env.EmbeddingStorageT, embeddingStorage.GetVersion(), model.ProjectTypeVirtual, uri)
	if err != nil {
		// initEvent.AddTeaParam(ckg_metrics.TeaParamIsFailed, true)
		// initEvent.AddTeaParam(ckg_metrics.TeaParamErrStep, initErrStepStoreDBPath)
		// initEvent.Report(ctx, false)
		logs.CtxWarn(ctx, "[InitProject] UpsertProjectID project is %v, err is %v", projectID, err)
		return errors.WithMessagef(err, "UpsertProjectID err project is %v", projectID)
	}

	return m.execIndexVirtualProject(ctx, cli, projectID, userID, uri, storage, embeddingStorage, uriToIndexIds, virtualProjectConfig)
}

func (m *manager) execIndexVirtualProject(ctx context.Context, cli knowledgebase.Client, projectID string, userID string, projectURI model.URI, storage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage, uriToIndexIds []int, virtualProjectConfig *model.VirtualProjectConfig) error {
	if virtualProjectConfig.DisableVirtualProjectIndexing {
		logs.CtxInfo(ctx, "virtual project indexing is disabled, project %s", projectID)
		return nil
	}

	if storage == nil || embeddingStorage == nil {
		return errors.New("storage or embedding storage is nil")
	}

	if projectID == "" || userID == "" || projectURI == "" {
		return errors.New("project ID, user ID or project URI is empty")
	}

	startTime := time.Now()
	initEvent := ckg_metrics.GetEvent(ctx)
	initEvent.AddTeaParam(ckg_metrics.TeaParamIndexType, EmbeddingStorageTypeLocal)

	// Validate storage paths
	if !storage.IsExistsOnDisk() || !embeddingStorage.IsExistsOnDisk() {
		return errors.New("storage or embedding storage does not exist on disk")
	}

	// 初始化进度跟踪
	atomicValue := &atomic.Int64{}
	building := &atomic.Bool{}
	building.Store(true)
	progress := &projectBuildProgress{
		totalFiles:        0, // 先设为0，后面会更新
		builtFiles:        atomicValue,
		building:          building,
		successIndexFiles: &atomic.Int64{},
		failedIndexFiles:  &atomic.Int64{},
	}

	m.projectInitProgress.Store(projectID, progress)

	wg := sync.WaitGroup{}

	util.SafeGo(ctx, func() {
		ctx := util.NewBackgroundContext(ctxvalues.LogIDDefault(ctx))

		// 1. 设置总文件数
		totalFiles := len(uriToIndexIds)
		logs.CtxInfo(ctx, "[InitVirtualProject] totalFiles: %d", totalFiles)
		progress.totalFiles = int64(totalFiles)
		progress.builtFiles.Store(0)

		logs.CtxInfo(ctx, "Starting virtual project indexing for %s with %d files", projectID, totalFiles)

		if totalFiles == 0 {
			logs.CtxInfo(ctx, "No files to index for project %s", projectID)
			progress.building.Store(false)
			initEvent.Report(ctx, true)
		}

		// 2. 分批处理ID
		const batchSize = 100
		for i := 0; i < len(uriToIndexIds); i += batchSize {
			end := i + batchSize
			if end > len(uriToIndexIds) {
				end = len(uriToIndexIds)
			}

			batchIDs := uriToIndexIds[i:end]

			// 3. 获取这批ID对应的完整URI元数据
			uriMetas, err := storage.GetURIMetaByIDs(ctx, storage.GetConn(), batchIDs)
			if err != nil {
				logs.CtxError(ctx, "Failed to get URI metas by IDs: %v", err)
				continue
			}

			// 4. 处理每个URI元数据
			for _, uriMeta := range uriMetas {
				absPath := uriMeta.Uri
				relPath := m.getFileRelaPathFromProjectID(projectID, uriMeta.UriCanonical)
				uri := &model.URIStatus{
					AbsPath:      absPath,
					RelPath:      relPath,
					UriCanonical: uriMeta.UriCanonical,
					ContentHash:  uriMeta.ContentHash,
					Content:      uriMeta.Content,
				}
				m.submitIndexVirtualTaskToQueue(ctx, &wg, userID, storage, embeddingStorage, projectID, cli, uri, progress, virtualProjectConfig)
			}

			// 记录处理进度
			processedCount := int(progress.builtFiles.Load())
			logs.CtxInfo(ctx, "Processed %d/%d files for project %s", processedCount, totalFiles, projectID)
		}

		// 等待所有任务完成
		util.SafeGo(ctx, func() {
			wg.Wait()

			defer func() {
				initEvent.Report(ctx, true)
			}()

			progress.building.Store(false)

			// 检查存储状态
			if _, err := m.fs.Stat(storage.GetStoragePath()); err != nil {
				if os.IsNotExist(err) {
					logs.CtxError(ctx, "[execIndexVirtualProject] init finished, but no entity storage at the end, storage %s, err: %v", storage.GetStoragePath(), err)
					return
				}
				logs.CtxError(ctx, "[execIndexVirtualProject] init finished, but entity storage unknown error at the end, err: %v", err)
				return
			}
			if _, err := m.fs.Stat(embeddingStorage.GetStoragePath()); err != nil {
				if os.IsNotExist(err) {
					logs.CtxError(ctx, "[execIndexVirtualProject] init finished, but no embedding storage at the end, embedding storage %s, err: %v", embeddingStorage.GetStoragePath(), err)
					return
				}
				logs.CtxError(ctx, "[execIndexVirtualProject] init finished, but embedding storage unknown error at the end, err: %v", err)
				return
			}

			logs.CtxInfo(ctx, "[execIndexVirtualProject] project %s initialize finished, build time: %d ms, file indexed: %d",
				projectID, time.Since(startTime).Milliseconds(), progress.successIndexFiles.Load())
			// initEvent.AddTeaParam(ckg_metrics.TeaParamFileIndexed, progress.successIndexFiles.Load())
		})
	})

	return nil
}

func (m *manager) checkProjectID(ctx context.Context, projectID string, omitProjectRoots string) bool {
	if projectID == "" {
		logs.CtxWarn(ctx, "projectID is empty")
		return false
	}

	if projectID == "\\" || projectID == "/" {
		logs.CtxWarn(ctx, "projectID is root dir, projectID: %s", projectID)
		return false
	}

	homeDir := os.Getenv("HOME")
	if projectID == homeDir {
		logs.CtxWarn(ctx, "projectID is home dir, projectID: %s", projectID)
		return false
	}
	if omitProjectRoots != "" {
		omitRoots := strings.Split(omitProjectRoots, "\n")
		if lo.Contains(omitRoots, projectID) {
			logs.CtxWarn(ctx, "projectID is in ignore list, projectID: %s", projectID)
			return false
		}
	}
	return true
}
