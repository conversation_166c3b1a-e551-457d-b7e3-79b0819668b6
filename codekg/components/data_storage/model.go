package data_storage

import (
	"ide/ckg/codekg/model"
	"time"
)

type InitProjectOption struct {
	ProjectType model.ProjectType
	Uri         string
}

type StorageEntity struct {
	ID         int              `gorm:"primaryKey;autoIncrement;column:id"`
	EntityID   string           `gorm:"type:varchar(1023);not null;column:entityID;index"`
	UriID      int              `gorm:"not null;column:uriID;index"`
	Name       string           `gorm:"type:varchar(1023);column:name;index"`
	Type       model.EntityType `gorm:"type:varchar(1023);not null;column:type"`
	Attributes string           `gorm:"type:text;column:attributes"`
}

func (StorageEntity) TableName() string {
	return "entity"
}

type CallerToCalleeAttribute struct {
	StartLine int32
	EndLine   int32
}

type CallerEntityResult struct {
	Entity *model.Entity
	Ranges []CallerToCalleeAttribute
}

type StorageURIMeta struct {
	ID           int        `gorm:"primaryKey;autoIncrement;column:id"`
	Uri          string     `gorm:"type:varchar(1023);not null;unique;column:uri;index"`
	ContentHash  string     `gorm:"type:string;not null;column:contentHash"`
	HasEmbedding int        `gorm:"not null;column:hasEmbedding;default:0"`
	Status       int        `gorm:"not null;column:status;default:1;index:idx_status"`
	CreateTime   *time.Time `gorm:"type:datetime;column:createTime"`
	UriCanonical string     `gorm:"type:varchar(1023);not null;column:uriCanonical;default:''"`
	Name         string     `gorm:"type:varchar(1023);not null;column:name;default:''"`
	Content      string     `gorm:"type:text;not null;column:content;default:''"`
}

func (StorageURIMeta) TableName() string {
	return "uri_meta"
}

type DocumentIndexStatus struct {
	Status model.BuildStatus
	Uri    string
	Name   string
}

type StorageRelation struct {
	ID         int                      `gorm:"primaryKey;autoIncrement;column:id"`
	UriID      int                      `gorm:"not null;column:uriID;index"`
	StartID    string                   `gorm:"type:varchar(1023);column:startID;index"`
	StartName  string                   `gorm:"size:255;not null;column:startName;index"`
	EndID      string                   `gorm:"type:varchar(1023);not null;column:endID;index"`
	EndName    string                   `gorm:"type:varchar(1023);not null;column:endName;index"`
	Type       model.EntityRelationType `gorm:"type:varchar(255);not null;column:type"`
	Attributes string                   `gorm:"type:text;column:attributes"`
}

func (StorageRelation) TableName() string {
	return "relation"
}

type StorageEntityAlias struct {
	ID       int    `gorm:"primaryKey;autoIncrement;column:id"`
	EntityID string `gorm:"type:varchar(255);not null;column:entityID;index"`
	UriID    int    `gorm:"not null;column:uriID;index"`
	Alias    string `gorm:"type:varchar(255);not null;column:alias"`
}

func (StorageEntityAlias) TableName() string {
	return "entity_alias"
}

type StorageEntityNotes struct {
	ID           int    `gorm:"primaryKey;autoIncrement;column:id"`
	EntityID     string `gorm:"type:varchar(1023);not null;column:entityID;unique_index:idx_entity_id"`
	EntityHash   string `gorm:"type:varchar(127);not null;column:entityHash"`
	Summary      string `gorm:"type:text;column:summary"`
	ElementNotes string `gorm:"type:text;column:elementNotes"`
}

func (StorageEntityNotes) TableName() string {
	return "entity_notes"
}

type StorageVectorToEntity struct {
	ID              int    `gorm:"primaryKey;autoIncrement;column:id"`
	StorageVectorID string `gorm:"type:varchar(1023);column:vectorID;index:idx_vector_id;not null"`
	StorageEntityID string `gorm:"type:varchar(1023);column:entityID;index:idx_entity_id;not null"`
}

func (StorageVectorToEntity) TableName() string { return "vector_to_entity" }
