package git_graph

import (
	"github.com/samber/lo"
	"math"
	"sort"
)

type IDType interface {
	getID() string
}

type WeightType interface {
	setWeight(float64)
	getWeight() float64
}

type Edge[T WeightType] struct {
	//from string
	//to   string
	info T
}

type Graph[N IDType, E WeightType] struct {
	nodes map[string]N
	edges map[string]map[string]Edge[E]
}

// 将结果转换为节点并排序
type NodeScore[N IDType] struct {
	node  N
	score float64
}

func NewGraph[N IDType, E WeightType]() *Graph[N, E] {
	return &Graph[N, E]{
		nodes: make(map[string]N),
		edges: make(map[string]map[string]Edge[E]),
	}
}

func (g *Graph[N, E]) GetNode(id string) N {
	return g.nodes[id]
}

func (g *Graph[N, E]) AddNode(n N) N {
	if _, ok := g.nodes[n.getID()]; !ok {
		g.nodes[n.getID()] = n
	}

	return g.nodes[n.getID()]
}

func (g *Graph[N, E]) GetEdge(from, to N) E {
	return g.edges[from.getID()][to.getID()].info
}

func (g *Graph[N, E]) GetEdges(from N) map[string]Edge[E] {
	return g.edges[from.getID()]
}

func (g *Graph[N, E]) AddEdge(from, to N, info E) {
	if m, ok1 := g.edges[from.getID()]; ok1 {
		if e, ok2 := m[to.getID()]; ok2 {
			e.info.setWeight(e.info.getWeight() + info.getWeight())
			return
		}
	} else {
		g.edges[from.getID()] = make(map[string]Edge[E])
	}

	g.edges[from.getID()][to.getID()] = Edge[E]{
		info: info,
	}
}

func (g *Graph[N, E]) NodeCount() int {
	return len(g.nodes)
}

func (g *Graph[N, E]) EdgeCount() int {
	count := 0
	for _, m := range g.edges {
		count += len(m)
	}
	return count
}

func (g *Graph[N, E]) PageRank() []NodeScore[N] {
	size := len(g.nodes)
	if size == 0 {
		return nil
	}

	// 创建节点ID到索引的映射
	nodesIntMap := make(map[string]int)
	intToNode := make([]N, size)
	for i, n := range lo.Values(g.nodes) {
		nodesIntMap[n.getID()] = i
		intToNode[i] = n
	}

	// 初始化邻接矩阵
	matrix := make([][]float64, size)
	for i := range matrix {
		matrix[i] = make([]float64, size)
	}

	// 构建邻接矩阵并计算出度
	outDegrees := make([]float64, size)
	for from, toMap := range g.edges {
		fromIdx := nodesIntMap[from]
		for to, edge := range toMap {
			toIdx := nodesIntMap[to]
			weight := edge.info.getWeight()
			matrix[fromIdx][toIdx] = weight
			outDegrees[fromIdx] += weight
		}
	}

	// 归一化邻接矩阵
	for i := 0; i < size; i++ {
		for j := 0; j < size; j++ {
			if outDegrees[i] > 0 {
				matrix[i][j] = matrix[i][j] / outDegrees[i]
			}
		}
	}

	// PageRank 参数
	dampingFactor := 0.85
	epsilon := 1e-5
	maxIterations := 100

	// 初始化 PageRank 向量
	pr := make([]float64, size)
	for i := range pr {
		pr[i] = 1.0 / float64(size)
	}

	// 迭代计算 PageRank
	for iter := 0; iter < maxIterations; iter++ {
		newPR := make([]float64, size)
		baseValue := (1 - dampingFactor) / float64(size)

		for i := 0; i < size; i++ {
			sum := 0.0
			for j := 0; j < size; j++ {
				sum += matrix[j][i] * pr[j]
			}
			newPR[i] = baseValue + dampingFactor*sum
		}

		// 检查是否收敛
		diff := 0.0
		for i := range pr {
			diff += math.Abs(newPR[i] - pr[i])
		}
		pr = newPR

		if diff < epsilon {
			break
		}
	}

	scores := make([]NodeScore[N], size)
	for i := range pr {
		scores[i] = NodeScore[N]{intToNode[i], pr[i]}
	}

	// 按分数降序排序
	sort.Slice(scores, func(i, j int) bool {
		return scores[i].score > scores[j].score
	})

	return scores
}

func (g *Graph[N, E]) getTotalWeight() float64 {
	total := 0.0
	for _, edges := range g.edges {
		for _, edge := range edges {
			total += edge.info.getWeight()
		}
	}
	return total
}
