import { beforeAll, describe, expect, it } from "vitest";
import { TreeSitterParser } from "../../parse/treesitter/parser";
import { LanguageIdEnum } from "../../parse/share";
import { SyntaxNode } from "web-tree-sitter";
import {
    getContainingClassByNode,
    getUsedIdentifierNodes,
    getClassConstructorNode,
    getClassHeaderString,
    getInheritanceClassIdentifierNode,
    getFuncMetaInfo,
    getIndentedNodeText,
} from "./python";


describe("test_getUsedIdentifierNodes", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
def test_transactions(earned, spent, expected):
    my_wallet = Wallet()
    my_wallet.add_cash(earned)
    my_wallet.spend_cash(spent)
    assert my_wallet.balance == expected
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
    const result = getUsedIdentifierNodes(tree.rootNode);
    expect(result.map((item) => item?.text ?? null)).toEqual([
        "test_transactions",
        "earned",
        "spent",
        "expected",
        "my_wallet",
        "Wallet",
        "my_wallet",
        "add_cash",
        "earned",
        "my_wallet",
        "spend_cash",
        "spent",
        "my_wallet",
        "balance",
        "expected"
    ]);
  });

  it("case2_normal", () => {
    const docText = `
def parse_object(self, exclude_fields=None, format_time=True, recursive=True):
        """
        反序列化cls对应的实例obj，并保存到dict中
        :param exclude_fields:
        :param format_time: 为True时将DateField和DateTimeField的值转化为字符串
        :param recursive: 对于外键是否递归解析，默认解析
        :return:
        """
        ret = dict()
        for field in self._meta.get_fields():
            field_name = field.name
            field_type = field.get_internal_type()
            if exclude_fields and (field_name in exclude_fields or field_type.startswith('ManyToMany')):
                continue

            # print [field_name, field_type, field,
            #        field.many_to_many, field.many_to_one, field.one_to_many, field.one_to_one]
            if field_type == 'ForeignKey':
                if field.one_to_many:
                    continue
                if recursive:
                    foreign_obj = getattr(self, field_name)
                    value = foreign_obj.parse_object()
                else:
                    value = field.value_from_object(self)
            elif field_type == 'DateField' and format_time:
                value = field.value_from_object(self)
                if value:
                    value = value.strftime(DATE_FORMAT)
            elif field_type == 'DateTimeField' and format_time:
                value = field.value_from_object(self)
                if value:
                    value = value.strftime(TIME_FORMAT)
            elif field_type == 'DecimalField':
                value = float(field.value_from_object(self))
            else:
                value = field.value_from_object(self)
            ret[field_name] = value

        return ret
`;
    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
    const results = getUsedIdentifierNodes(tree.rootNode);
    expect(results.length).toEqual(65);
  });

  it("case3_normal", () => {
    const docText = `
def get_sso_userinfo(access_token, host):
    """获取用户信息

    :param access_token:
    :param host:
    :return:
    """
    ret = dict()
    if IS_BOE:
        host = 'http://boe-m10n-health.byted.org'
        r = requests.get(host + '/api/user/sso_info?access_token={}&host={}'.format(access_token, host))
        print("r.json()['result']")
        print(r.json()['result'])
        return r.json()['result']

    try:
        if host not in OAUTH2_TOKEN:
            oauth2_token = OAUTH2_TOKEN['boe-m10n-health.byted.org']
        else:
            oauth2_token = OAUTH2_TOKEN[host]

        url = oauth2_token['api_url']
        params = {
            'access_token': access_token
        }
        r = requests.get(url, params=params)
        # if IS_BOE:  # 开发机线上断网问题
        #     re_url = 'https://cloudapi.bytedance.net/faas/services/tt9rm3/invoke/user_info'
        #     r = requests.post(re_url, data=json.dumps(params))
        # else:
        #     r = requests.get(url, params=params)
        ret = r.json()
    except Exception:
        pass
    finally:
        return ret
`;
     const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
     const results = getUsedIdentifierNodes(tree.rootNode);
     expect(results.length).toEqual(42);
  });

    it("case4_normal", () => {
        const docText = `
def prepare_test_case(dataset: Dataset, case_id: TestCaseId) -> PreparedTestCase:
    test_case = dataset.test_cases[case_id]
    generated_ut_json_key = dataset.meta_info[META_KEY_GENERATED_TEST]
    test_file_path = (dataset.repo_path / Path(test_case['relpath']).parent /
        Path(f"test_{case_id}.py"))

    if test_case[generated_ut_json_key] == "":
        test_case['prepared_test'] = ""
        dataset.test_cases[case_id] = test_case
        return PreparedTestCase(file_path=test_file_path, file_content="")


    content = TEST_FILE_HEADER_TEMPLATE.format(test_case[generated_ut_json_key])
    content = pythonhelper.fix_imports(dataset, test_file_path, content)
    if(content.endswith(RESTORE_SUFFIX)):
        pass
    test_case['prepared_test'] = content
    dataset.test_cases[case_id] = test_case
    return PreparedTestCase(
        file_path=test_file_path,
        file_content=content
    )
`;
        const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
        const results = getUsedIdentifierNodes(tree.rootNode);
        expect(results.length).toEqual(58);
    });
});

describe("test_getClassConstructorNode", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const docText = `
class Wallet(metaclass=ABCMeta):
    def __init__(self, initial_balance=0):
        """Initialize the Wallet with an optional initial balance."""
        self.balance = initial_balance
        self.test = 0
        cls.test=a
        if b.TEST_STR == "test":
            print("Hit")
            print(a.b.c())

    def add_cash(self, amount):
        """Add cash to the wallet."""
        self.balance += amount
        print(f"Added {amount} to the wallet. New balance: {self.balance}")

`;
    const constructorText = `
    def __init__(self, initial_balance=0):
        """Initialize the Wallet with an optional initial balance."""
        self.balance = initial_balance
        self.test = 0
        cls.test=a
        if b.TEST_STR == "test":
            print("Hit")
            print(a.b.c())
        `;
    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
    const result = getClassConstructorNode(tree.rootNode);
    expect(result.map((item) => item?.text ?? null)).toEqual([constructorText.trim()]);
  });

  it("case2_normal", () => {
    const docText = `
class Foo():
    def __new__(cls):
        return super(Foo, cls).__new__(cls)
`;
    const constructorText = `
    def __new__(cls):
        return super(Foo, cls).__new__(cls)
`;
    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
    const result = getClassConstructorNode(tree.rootNode);
    expect(result.map((item) => item?.text ?? null)).toEqual([constructorText.trim()]);
  });

  it("case3_normal", () => {
    const docText = `
class Foo():
    def __new__(cls):
        return super(Foo, cls).__new__(cls)

    def __init__(self):
        self.ok = True

    def __init__mustnotmatch(self):
        self.ok = True
`;
    const contructorArr = [
      `
    def __new__(cls):
        return super(Foo, cls).__new__(cls)
`,
      `
    def __init__(self):
        self.ok = True
`,
    ].map((str) => str.trim());
    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
    const result = getClassConstructorNode(tree.rootNode).map((item) => item?.text ?? null);

    expect(result).toEqual(expect.arrayContaining(contructorArr));
    expect(contructorArr).toEqual(expect.arrayContaining(result));
  });

    it("case4_complicated", () => {
        const docText = `
class BaseModel(models.Model):
    @classmethod
    def _do_query(cls, query_set=None, and_params=None, or_params=None, select_related=None, order_by=None,
                  exclude=False):
        """
        根据参数返回对应的QuerySet对象
        如下方例子，返回cls.objects.filter(age=18, name__icontains='tom').filter(Q(country='us') | Q(gender='male'))
        :param query_set: 为None时赋值为cls.objects.all()，否则直接使用
        :param and_params: None或dict对象，e.g：
            {
                'age': 18,
                'name__icontains': 'tom',
                ...
            }
        :param or_params: list对象，e.g:
            [
                {'country': 'us'},
                {'gender': 'male'}
            ]
        :param select_related: list对象
        :param order_by: list对象
        :param exclude: 为True时使用exclude方法，否则使用filter方法
        :return:
        """
        if not ((and_params is None or isinstance(and_params, dict)) and
                isinstance(or_params, (list, type(None))) and
                isinstance(select_related, (list, type(None))) and
                isinstance(order_by, (list, type(None)))):
            return None

        if query_set:
            qs = query_set
        else:
            qs = cls.objects.all()

        if exclude:
            func_name = 'exclude'
        else:
            func_name = 'filter'

        if and_params:
            qs = qs.__getattribute__(func_name)(**and_params)

        if or_params:
            q_objs = list()
            for arg in or_params:
                q_objs.append(Q(**arg))

            qs = qs.__getattribute__(func_name)(reduce(operator.or_, q_objs))

        if select_related:
            qs = qs.select_related(*select_related)
        if order_by:
            qs = qs.order_by(*order_by)

        ret = qs

        return ret

`;
        const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);

        const containingClass = getContainingClassByNode(tree.rootNode.children[0].children[4].children[0].children[1]);
        expect(containingClass.childForFieldName("name").text).toEqual("BaseModel");
        const header = getClassHeaderString(containingClass);
        expect(header).toEqual("class BaseModel(models.Model):\n    ");

        const result = getClassConstructorNode(containingClass);
        expect(result.length).toEqual(0);
    });

    it('case_function_in_function', () => {
        const docText = `
def json_response(func=None, need_auth=False, need_alarm=True):
    def actual_decorator(func):
        @functools.wraps(func)
        def inner(*args, **kw):
            ret = {
                "message": "",
                "timestamp": int(time() * 1000),
                "result": None,
                "code": 0,
            }

            try:
                request = args[0]  # WSGIRequest类型
                user_name = str(request.COOKIES.get("username", ""))

                # 生产环境metrics打点
                if IS_PRODUCT:
                    try:
                        metrics.emit_counter(
                            POINT.USER, 1, tags={"username": user_name}
                        )
                        _api = "/".join(
                            [_ for _ in request.path.split("/") if not _.isdigit()]
                        )
                        metrics.emit_counter(POINT.API, 1, tags={"api": _api})
                    except Exception:
                        pass

                ret["result"] = func(*args, **kw)

            except CustomException as _e:
                ret["code"] = _e.code
                ret["message"] = _e.value

            except Exception as _e:
                ret["code"] = -2
                _msg: list = get_traceback_info()
                _msg.append(f"error msg: {_e}")
                ret["message"] = _msg

            finally:
                # lark报警逻辑
                if ret["code"] == -2 and IS_PRODUCT and (not IS_PPE):  # 生产环境&预期之外的错误
                    log_id = request.META["x-tt-logid"]
                    is_ppe = request.META.get("x-use-ppe", 0)
                    if (not is_ppe) and need_alarm:
                        _alarm(user_name, log_id, ret["message"], request)
            return JsonResponse(ret, safe=False)

        return inner

    if func:
        return actual_decorator(func)
    else:
        return actual_decorator

        `
        const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
        const results = getUsedIdentifierNodes(tree.rootNode.namedChild(0));
        expect(results.length).toEqual(93);
    });

});
describe("test_getInheritanceClassIdentifierNode", () => {
    beforeAll(async () => {
        await TreeSitterParser.INSTANCE.init("./resources");
    });

    it("case1_normal", () => {
        const docText = `
class Wallet(WalletParentA,WalletParentB):
    def __init__(self, initial_balance=0):
        """Initialize the Wallet with an optional initial balance."""
        self.balance = initial_balance
`;

        const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
        const result = getInheritanceClassIdentifierNode(tree.rootNode);
        expect(result.map((item) => item?.text ?? null)).toEqual([
            "WalletParentA",
            "WalletParentB",
        ]);
    });

    it("case2_normal", () => {
        const docText = `
class Wallet(metaclass=ABCMeta):
    def __init__(self, initial_balance=0):
        """Initialize the Wallet with an optional initial balance."""
        self.balance = initial_balance
`;

        const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
        const result = getInheritanceClassIdentifierNode(tree.rootNode);
        expect(result.map((item) => item?.text ?? null)).toEqual([
            "metaclass=ABCMeta",
            "metaclass",
            "ABCMeta"
        ]);
    });
});

describe("test_getFuncMetaInfo", () => {
    beforeAll(async () => {
        await TreeSitterParser.INSTANCE.init("./resources");
    });

    it("case1_normal", () => {
        const docText = `
@retry
@jsonstring
def add_cash(self, amount):
    """Add cash to the wallet."""
    self.balance += amount
    print(f"Added {amount} to the wallet. New balance: {self.balance}")
`;

        const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
        const queue: SyntaxNode[] = [tree.rootNode];
        let targetNode = tree.rootNode;
        let found = false;
        while(queue.length>0 && !found){
            const node = queue.shift();
            for(const childNode of node.namedChildren){
                if(childNode.type === "function_definition"){
                    targetNode = childNode;
                    found=true;
                    break;
                }
                queue.push(childNode);
            }
        }
        if(found && targetNode!=tree.rootNode){
            const [funcName, funcBody, focalFunc] = getFuncMetaInfo(targetNode);
            expect(focalFunc).toEqual("@retry\n@jsonstring\ndef add_cash(self, amount):");
        }
    });
    it("case2_normal", () => {
        const docText = `
@dataclass
class Dataset:
    repo_path: Path
    output_path: Path
    meta_info: dict[str, Any]
    # read from local repo
    test_meta: TestCase
    test_cases: dict[str, TestCase]

    def __post_init__(self):
        if not isinstance(self.meta_info, dict):
            raise Exception("Require 'meta_info: dict[str, Any]'.")
`;

        const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
        const queue: SyntaxNode[] = [tree.rootNode];
        let targetNode = tree.rootNode;
        let found = false;
        while(queue.length>0 && !found){
            const node = queue.shift();
            for(const childNode of node.namedChildren){
                if(childNode.type === "function_definition"){
                    targetNode = childNode;
                    found=true;
                    break;
                }
                queue.push(childNode);
            }
        }
        if(found && targetNode!=tree.rootNode){
            const [funcName, funcBody, focalFunc] = getFuncMetaInfo(targetNode);
            expect(focalFunc).toEqual("def __post_init__(self):");
        }else{
            expect(() => throwError()).to.throw(Error, "Invalid_test");
        }
    });

});

describe("test_getIndentedNodeText", () => {
    beforeAll(async () => {
        await TreeSitterParser.INSTANCE.init("./resources");
    });

    it("case1_normal", () => {
        const docText = `
@dataclass
class Dataset:
    repo_path: Path
    output_path: Path
    meta_info: dict[str, Any]
    # read from local repo
    test_meta: TestCase
    test_cases: dict[str, TestCase]

    def __post_init__(self):
        if not isinstance(self.meta_info, dict):
            raise Exception("Require 'meta_info: dict[str, Any]'.")`;
        const expectedText = `
    def __post_init__(self):
        if not isinstance(self.meta_info, dict):
            raise Exception("Require 'meta_info: dict[str, Any]'.")`;
        const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
        const queue: SyntaxNode[] = [tree.rootNode];
        let targetNode = tree.rootNode;
        let found = false;
        while(queue.length>0 && !found){
            const node = queue.shift();
            for(const childNode of node.namedChildren){
                if(childNode.type === "function_definition"){
                    targetNode = childNode;
                    found=true;
                    break;
                }
                queue.push(childNode);
            }
        }
        if(found && targetNode!=tree.rootNode){
            const result = getIndentedNodeText(targetNode);
            expect(result).toEqual(expectedText.slice(1));
        }else{
            expect(() => throwError()).to.throw(Error, "Invalid_test");
        }
    });

    it("case2_normal", () => {
        const docText = `
@retry
@jsonstring
def add_cash(self, amount):
    """Add cash to the wallet."""
    self.balance += amount
    print(f"Added {amount} to the wallet. New balance: {self.balance}")
`;
        const expectedText = `
def add_cash(self, amount):
    """Add cash to the wallet."""
    self.balance += amount
    print(f"Added {amount} to the wallet. New balance: {self.balance}")
        `;
        const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
        const queue: SyntaxNode[] = [tree.rootNode];
        let targetNode = tree.rootNode;
        let found = false;
        while(queue.length>0 && !found){
            const node = queue.shift();
            for(const childNode of node.namedChildren){
                if(childNode.type === "function_definition"){
                    targetNode = childNode;
                    found=true;
                    break;
                }
                queue.push(childNode);
            }
        }
        if(found && targetNode!=tree.rootNode){
            const result = getIndentedNodeText(targetNode);
            expect(result).toEqual(expectedText.trim());
        }else{
            expect(() => throwError()).to.throw(Error, "Invalid_test");
        }
    });

    it("case3_normal", () => {
        const docText = `
def func_padding():
    pass
        
@retry
@jsonstring
def add_cash(self, amount):
    """Add cash to the wallet."""
    self.balance += amount
    print(f"Added {amount} to the wallet. New balance: {self.balance}")
`;
        const expectedText = `
@retry
@jsonstring
def add_cash(self, amount):
    """Add cash to the wallet."""
    self.balance += amount
    print(f"Added {amount} to the wallet. New balance: {self.balance}")
`;
        const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
        const queue: SyntaxNode[] = [tree.rootNode];
        let targetNode = tree.rootNode;
        let found = false;
        while(queue.length>0 && !found){
            const node = queue.shift();
            for(const childNode of node.namedChildren){
                if(childNode.type === "decorated_definition"){
                    targetNode = childNode;
                    found=true;
                    break;
                }
                queue.push(childNode);
            }
        }
        if(found && targetNode!=tree.rootNode){
            const result = getIndentedNodeText(targetNode);
            expect(result).toEqual(expectedText.trim());
        }else{
            expect(() => throwError()).to.throw(Error, "Invalid_test");
        }
    });
    it("case4_normal", () => {
        const docText = `
@dataclass
class Dataset:
    repo_path: Path
    output_path: Path
    meta_info: dict[str, Any]
    # read from local repo
    test_meta: TestCase
    test_cases: dict[str, TestCase]

    def __post_init__(self):
        if not isinstance(self.meta_info, dict):
            raise Exception("Require 'meta_info: dict[str, Any]'.")`;
        const expectedText = `
    def __post_init__(self):
        if not isinstance(self.meta_info, dict):
            raise Exception("Require 'meta_info: dict[str, Any]'.")`;
        const testNodes: SyntaxNode[] = [];
        const expectedResults = [
            "    repo_path: Path",
            "    output_path: Path",
            "    meta_info: dict[str, Any]",
            "    test_meta: TestCase",
            "    test_cases: dict[str, TestCase]",
        ];
        const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Python, docText);
        const queue: SyntaxNode[] = [tree.rootNode];
        let targetNode = tree.rootNode;
        let found = false;
        while(queue.length>0 && !found){
            const node = queue.shift();
            for(const childNode of node.namedChildren){
                if(childNode.type === "assignment"){
                    testNodes.push(childNode);
                }
                if(childNode.type === "function_definition"){
                    continue;
                }
                queue.push(childNode);
            }
        }
        const testResults: string[] = [];
        for(const targetNode of testNodes){
            testResults.push(getIndentedNodeText(targetNode));
        }
        expect(testResults).toEqual(expectedResults);
    });
});