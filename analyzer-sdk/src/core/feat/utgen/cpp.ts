import {TextDocument} from "vscode-languageserver-textdocument";
import {DocumentUri} from "vscode-languageserver-types";
import {getCacheFileAstTreeAllowCreate} from "../../cache/astCache";
import {TreeSitterParser} from "../../parse/treesitter/parser";
import {Logger} from "../../../utils/logger";
import {SyntaxNode, Tree} from "web-tree-sitter";
import {
  DefNodeInfo,
  DefNodeKindEnum,
  inWorkspaceLocationFilter,
  LanguageIdEnum,
  VarDeclNodeInfo,
} from "../../parse/share";
import {TreeSitterDispatcher} from "../../parse/treesitter/dispatcher";
import {TreeSitterFunctionalityEnum} from "../../parse/treesitter/share";
import {LSPParser} from "../../parse/lsp/parser";
import {getRootNode, isNodeWithType} from "../../../utils/tree";
import {convertToPlainObject} from "../../../utils/common";
import {getRelativePath} from "./utils";
import * as crypto from 'crypto';
import {getWorkspaceFolderPath} from "../../../utils/path";
import {PSIParser} from "../../parse/psi/parser";
import {getConfig} from "../../common/config";
import {FunctionalityExecutor} from "../../parse/functionality/executor";
import {FunctionalityEnum} from "../../parse/functionality/share";

interface ContextInfo {
  getMD5(): string;
}

class TypeContextInfo implements ContextInfo {
  name: string;
  file: string;
  namespace: string;
  kind: string;
  code: string;
  fields: string[];
  constructors: string[];
  targetFunc: string;

  constructor(name: string, file: string, namespace: string, kind: string) {
    this.name = name;
    this.file = file;
    this.namespace = namespace;
    this.kind = kind;
  }

  withCode(code: string): TypeContextInfo {
    this.code = code;
    return this;
  }

  withFields(fields: string[]): TypeContextInfo {
    this.fields = fields;
    return this;
  }

  withConstructors(constructors: string[]): TypeContextInfo {
    this.constructors = constructors;
    return this;
  }

  withTargetFunc(targetFunc: string): TypeContextInfo {
    this.targetFunc = targetFunc;
    return this;
  }

  getMD5(): string {
    const jsonStr = JSON.stringify(convertToPlainObject(this));
    return crypto.createHash('md5').update(jsonStr).digest('hex');
  }

  toOutputString(): string {
    const result: string[] = [];

    switch (this.kind) {
      case DefNodeKindEnum.Class:
      case DefNodeKindEnum.Struct:
        result.push(`${this.kind} ${this.name} {`);
        if (this.fields) {
          result.push(...this.fields);
        }
        if (this.constructors) {
          result.push(...this.constructors);
        }
        if (this.targetFunc) {
          result.push(this.targetFunc);
        }
        result.push("}");
        break;
      case DefNodeKindEnum.Enum:
      case DefNodeKindEnum.Alias:
        if (this.code) {
          result.push(this.code);
        }
        break;
    }

    return result.join("\n");
  }
}

class FuncContextInfo implements ContextInfo {
  name: string;
  file: string;
  namespace: string;
  body: string;

  constructor(name: string, file: string, namespace: string) {
    this.name = name;
    this.file = file;
    this.namespace = namespace;
  }

  withBody(body: string): FuncContextInfo {
    this.body = body;
    return this;
  }

  getMD5(): string {
    const jsonStr = JSON.stringify(convertToPlainObject(this));
    return crypto.createHash('md5').update(jsonStr).digest('hex');
  }
}

class VariableContextInfo implements ContextInfo {
  name: string;
  file: string;
  namespace: string;
  code: string;

  constructor(name: string, file: string, namespace: string) {
    this.name = name;
    this.file = file;
    this.namespace = namespace;
  }

  withCode(code: string): VariableContextInfo {
    this.code = code;
    return this;
  }

  getMD5(): string {
    const jsonStr = JSON.stringify(convertToPlainObject(this));
    return crypto.createHash('md5').update(jsonStr).digest('hex');
  }
}

class ContextResult {
  fileName: string;
  imports: string[];
  alias: string[];
  name: string;
  signature: string;
  body: string;
  namespace: string;

  classInfo: TypeContextInfo;
  superClassInfo: TypeContextInfo[];
  returnTypeInfo: TypeContextInfo[];
  paramTypeInfo: TypeContextInfo[];
  usedTypeInfo: TypeContextInfo[];
  usedFuncInfo: FuncContextInfo[];
  usedOutsideVariableInfo: VariableContextInfo[];

  withName(name: string): ContextResult {
    this.name = name;
    return this;
  }

  withFileName(fileName: string): ContextResult {
    this.fileName = fileName;
    return this;
  }

  withImports(imports: string[]): ContextResult {
    this.imports = imports;
    return this;
  }

  withAlias(alias: string[]): ContextResult {
    this.alias = alias;
    return this;
  }

  withSignature(signature: string): ContextResult {
    this.signature = signature;
    return this;
  }

  withBody(body: string): ContextResult {
    this.body = body;
    return this;
  }

  withNamespace(namespace: string): ContextResult {
    this.namespace = namespace;
    return this;
  }

  withClassInfo(classInfo: TypeContextInfo): ContextResult {
    this.classInfo = classInfo;
    return this;
  }

  withSuperClassInfo(superClassInfo: TypeContextInfo[]): ContextResult {
    this.superClassInfo = this.removeRepeatedInfo(superClassInfo) as TypeContextInfo[];
    return this;
  }

  withReturnTypeInfo(returnTypeInfo: TypeContextInfo[]): ContextResult {
    this.returnTypeInfo = returnTypeInfo;
    return this;
  }

  withParamTypeInfo(paramTypeInfo: TypeContextInfo[]): ContextResult {
    this.paramTypeInfo = this.removeRepeatedInfo(paramTypeInfo) as TypeContextInfo[];
    return this;
  }

  withUsedTypeInfo(usedTypeInfo: TypeContextInfo[]): ContextResult {
    this.usedTypeInfo = this.removeRepeatedInfo(usedTypeInfo) as TypeContextInfo[];
    return this;
  }

  addUsedTypeInfo(usedTypeInfo: TypeContextInfo[]): ContextResult {
    const newInfo: TypeContextInfo[] = [];
    newInfo.push(...this.usedTypeInfo);
    newInfo.push(...usedTypeInfo);
    this.usedTypeInfo = this.removeRepeatedInfo(newInfo) as TypeContextInfo[];
    return this;
  }

  withUsedFuncInfo(usedFuncInfo: FuncContextInfo[]): ContextResult {
    this.usedFuncInfo = this.removeRepeatedInfo(usedFuncInfo) as FuncContextInfo[];
    return this;
  }

  withUsedOutsideVariableInfo(usedOutsideVariableInfo: VariableContextInfo[]): ContextResult {
    this.usedOutsideVariableInfo = this.removeRepeatedInfo(usedOutsideVariableInfo) as VariableContextInfo[];
    return this;
  }

  toOriginJsonString(): string {
    return JSON.stringify(convertToPlainObject(this), null, 2);
  }

  toOutputJsonString(): string {
    interface namespaceContext {
      namespace: string;
      context: string[];
    }

    const namespaceContextToString = (namespaceContext: namespaceContext): string => {
      const result: string[] = [];

      if (namespaceContext.namespace === "") {
        result.push(...namespaceContext.context);
      } else {
        result.push(`namespace ::${namespaceContext.namespace} {`);
        result.push(...namespaceContext.context);
        result.push(`} // namespace ::${namespaceContext.namespace}`);
      }

      return result.join("\n");
    };

    interface fileContext {
      filename: string;
      namespaceContext: Map<string, namespaceContext>;
    }

    const fileContextToString = (fileContext: fileContext): string => {
      const result: string[] = [];

      fileContext.namespaceContext.forEach(value => {
        result.push(namespaceContextToString(value));
      });

      return result.join("\n");
    };


    const contextMap = new Map<string, fileContext>();
    const addTypeContextInfoToMap = (typeContextInfo: TypeContextInfo): void => {
      if (!contextMap.has(typeContextInfo.file)) {
        contextMap.set(typeContextInfo.file, {
          filename: typeContextInfo.file,
          namespaceContext: new Map<string, namespaceContext>(),
        });
      }
      if (!contextMap.get(typeContextInfo.file).namespaceContext.has(typeContextInfo.namespace)) {
        contextMap.get(typeContextInfo.file).namespaceContext.set(typeContextInfo.namespace, {
          namespace: typeContextInfo.namespace,
          context: [],
        });
      }
      contextMap.get(typeContextInfo.file).namespaceContext.get(typeContextInfo.namespace).context.push(typeContextInfo.toOutputString());
    };
    const addFuncVarContextInfoToMap = (contextInfo: FuncContextInfo | VariableContextInfo): void => {
      if (!contextMap.has(contextInfo.file)) {
        contextMap.set(contextInfo.file, {
          filename: contextInfo.file,
          namespaceContext: new Map<string, namespaceContext>(),
        });
      }
      if (!contextMap.get(contextInfo.file).namespaceContext.has(contextInfo.namespace)) {
        contextMap.get(contextInfo.file).namespaceContext.set(contextInfo.namespace, {
          namespace: contextInfo.namespace,
          context: [],
        });
      }
      if (contextInfo instanceof FuncContextInfo) {
        contextMap.get(contextInfo.file).namespaceContext.get(contextInfo.namespace).context.push(contextInfo.body);
      }
      if (contextInfo instanceof VariableContextInfo) {
        contextMap.get(contextInfo.file).namespaceContext.get(contextInfo.namespace).context.push(contextInfo.code);
      }
    };

    if (this.classInfo) {
      addTypeContextInfoToMap(this.classInfo);
    }
    if (this.superClassInfo) {
      this.superClassInfo.forEach(item => addTypeContextInfoToMap(item));
    }
    if (this.returnTypeInfo) {
      this.returnTypeInfo.forEach(item => addTypeContextInfoToMap(item));
    }
    if (this.paramTypeInfo) {
      this.paramTypeInfo.forEach(item => addTypeContextInfoToMap(item));
    }
    if (this.usedTypeInfo) {
      this.usedTypeInfo.forEach(item => addTypeContextInfoToMap(item));
    }
    if (this.usedFuncInfo) {
      this.usedFuncInfo.forEach(item => addFuncVarContextInfoToMap(item));
    }
    if (this.usedOutsideVariableInfo) {
      this.usedOutsideVariableInfo.forEach(item => addFuncVarContextInfoToMap(item));
    }

    const contextByFilename: { filename: string, context: string }[] = [];
    contextMap.forEach((value, key) => {
      contextByFilename.push({
        filename: key,
        context: fileContextToString(value),
      });
    });

    const result = {
      symbol_name: this.namespace === "" ? this.name : `${this.namespace}::${this.name}`,
      file_includes: this.imports,
      file_using_alias_stmts: this.alias,
      kind: this.classInfo ? "Method" : "Function",
      context_by_filename: contextByFilename,
      definition_list: [{
        signature: this.signature,
        definition: this.body,
        definition_namespace: this.namespace,
        filename: this.fileName,
      }],
    };

    return JSON.stringify(result, null, 2);
  }

  private removeRepeatedInfo(infos: ContextInfo[]): ContextInfo[] {
    const result: ContextInfo[] = [];

    const md5Map = new Map<string, ContextInfo>();
    for (const info of infos) {
      const md5 = info.getMD5();
      if (!md5Map.has(md5)) {
        md5Map.set(md5, info);
        result.push(info);
      }
    }

    return result;
  }
}

export async function doExtractUnitTestContextForCpp(document: TextDocument, startIndex: number, endIndex: number): Promise<string> {
  Logger.info(`doExtractUnitTestContextForCpp: '${document.uri}' (${startIndex}, ${endIndex})`);

  const result = new ContextResult();

  const workspaceFolderPath = getWorkspaceFolderPath();

  if (document.languageId !== LanguageIdEnum.Cpp) {
    Logger.error(`doExtractUnitTestContextForCpp: languageId should be '${LanguageIdEnum.Cpp}', got '${document.languageId}'`);
    return "{}";
  }

  if (!PSIParser.INSTANCE.initialized() && !LSPParser.INSTANCE.initialized()) {
    Logger.error(`doExtractUnitTestContextForJS: PSIParser and LSPParser are not initialized`);
    return "{}";
  }

  if (!workspaceFolderPath) {
    Logger.error(`doExtractUnitTestContextForCpp: workspaceFolderPath is empty`);
    return "{}";
  }

  const tree = getCacheFileAstTreeAllowCreate(document);
  if (!tree) {
    Logger.error(`doExtractUnitTestContextForCpp: failed to parse Tree for document '${document.uri}'`);
    return "{}";
  }

  const start = Date.now();

  const selectRange = {start: document.positionAt(startIndex), end: document.positionAt(endIndex)};

  const funcDeclNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(LanguageIdEnum.Cpp, TreeSitterFunctionalityEnum.GetFuncParentNodeForRange, tree.rootNode, selectRange);
  if (!funcDeclNode) {
    Logger.error(`doExtractUnitTestContextForCpp: failed to get funcDeclNode for document '${document.uri}' with range [${startIndex}, ${endIndex}]`);
    return "{}";
  }

  result.withFileName(getRelativePath(document.uri, workspaceFolderPath));

  const importResult: string[] = TreeSitterDispatcher.INSTANCE.dispatch(LanguageIdEnum.Cpp, TreeSitterFunctionalityEnum.GetImportFileOrModule, tree.rootNode);
  if (importResult) {
    result.withImports(importResult.map(item => `#include ${item}`));
  }

  const aliasResult: string[] = TreeSitterDispatcher.INSTANCE.dispatch(LanguageIdEnum.Cpp, TreeSitterFunctionalityEnum.GetValidAliasInfoInFile, funcDeclNode);
  if (aliasResult) {
    result.withAlias(Array.from(new Set(aliasResult.map(item => `using ${item}`))));
  }

  const funcNameNode = getFuncNameNode(funcDeclNode);
  if (funcNameNode) {
    result.withName(funcNameNode.text);
  }

  const signatureResult: string = TreeSitterDispatcher.INSTANCE.dispatch(LanguageIdEnum.Cpp, TreeSitterFunctionalityEnum.GetFuncSignatureString, funcDeclNode);
  if (signatureResult) {
    result.withSignature(signatureResult);
  }

  result.withBody(funcDeclNode.text);

  result.withNamespace(getNamespaceInfo(funcDeclNode));

  const classTypeInfo = await getClassTypeInfo(funcDeclNode, document, tree);
  if (classTypeInfo) {
    result.withClassInfo(await getTypeInfoFromDefNode(classTypeInfo, workspaceFolderPath, funcDeclNode));

    const superClassInfos: TypeContextInfo[] = [];
    for (const superClassTypeInfo of await getSuperClassTypeInfos(classTypeInfo, workspaceFolderPath)) {
      const typeContextInfo = await getTypeInfoFromDefNode(superClassTypeInfo, workspaceFolderPath);
      if (typeContextInfo) {
        superClassInfos.push(typeContextInfo);
      }
    }
    result.withSuperClassInfo(superClassInfos);
  }

  const returnInfos: TypeContextInfo[] = [];
  for (const returnTypeInfo of await getReturnTypeInfos(document.uri, funcDeclNode)) {
    if (returnTypeInfo) {
      const typeContextInfo = await getTypeInfoFromDefNode(returnTypeInfo, workspaceFolderPath);
      if (typeContextInfo) {
        returnInfos.push(typeContextInfo);
      }
    }
  }
  result.withReturnTypeInfo(returnInfos);

  const paramInfos: TypeContextInfo[] = [];
  for (const paramTypeInfo of await getParamTypeInfos(document.uri, funcDeclNode)) {
    if (paramTypeInfo) {
      const typeContextInfo = await getTypeInfoFromDefNode(paramTypeInfo, workspaceFolderPath);
      if (typeContextInfo) {
        paramInfos.push(typeContextInfo);
      }
    }
  }
  result.withParamTypeInfo(paramInfos);

  if (Date.now() - start > getConfig("analysis.utgen.cpp.timeoutMs")) {
    Logger.warn(`doExtractUnitTestContextForCpp: timeout for function '${funcNameNode?.text ?? ""}', return partial result`);
    return result.toOutputJsonString();
  }

  const usedTypeInfos: TypeContextInfo[] = [];
  const usedTypeInfosResult = await getUsedTypeInfos(document.uri, funcDeclNode, start);
  for (const usedTypeInfo of usedTypeInfosResult.data) {
    const typeContextInfo = await getTypeInfoFromDefNode(usedTypeInfo, workspaceFolderPath);
    if (typeContextInfo) {
      usedTypeInfos.push(typeContextInfo);
    }
  }
  result.withUsedTypeInfo(usedTypeInfos);

  if (usedTypeInfosResult.timeout) {
    Logger.warn(`doExtractUnitTestContextForCpp: timeout for function '${funcNameNode?.text ?? ""}', return partial result`);
    return result.toOutputJsonString();
  }

  const usedFuncInfos: FuncContextInfo[] = [];
  const usedFuncInfosResult = await getUsedFuncInfos(document.uri, funcDeclNode, workspaceFolderPath, start);
  for (const usedFuncInfo of usedFuncInfosResult.data) {
    const funcContextInfo = getFuncInfoFromDefNode(usedFuncInfo, workspaceFolderPath);
    if (funcContextInfo) {
      usedFuncInfos.push(funcContextInfo);
    }
  }
  result.withUsedFuncInfo(usedFuncInfos);

  if (usedFuncInfosResult.timeout) {
    Logger.warn(`doExtractUnitTestContextForCpp: timeout for function '${funcNameNode?.text ?? ""}', return partial result`);
    return result.toOutputJsonString();
  }

  const usedOutsideVariableInfos: VariableContextInfo[] = [];
  const usedOutsideVariableInfosResult = await getUsedOutsideVariableInfos(document.uri, funcDeclNode, start);
  for (const usedVariableInfo of usedOutsideVariableInfosResult.data) {
    const variableContextInfo = getVariableInfoFromDeclNode(usedVariableInfo, workspaceFolderPath);
    if (variableContextInfo) {
      usedOutsideVariableInfos.push(variableContextInfo);
    }
    if (usedVariableInfo.declNode.type === "enumerator") {
      const enumTypeNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(LanguageIdEnum.Cpp, TreeSitterFunctionalityEnum.GetParentNodeWithTypeListForNode, usedVariableInfo.declNode, ["enum_specifier"]);
      const enumTypeDefInfo = {
        languageId: LanguageIdEnum.Cpp,
        uri: usedVariableInfo.uri,
        kind: DefNodeKindEnum.Enum,
        name: enumTypeNode.childForFieldName("name")?.text ?? "",
        defNode: enumTypeNode,
        subDefNodeInfo: null,
      };
      const enumTypeContextInfo = await getTypeInfoFromDefNode(enumTypeDefInfo, workspaceFolderPath);
      result.addUsedTypeInfo([enumTypeContextInfo]);
    }
  }
  result.withUsedOutsideVariableInfo(usedOutsideVariableInfos);

  if (usedOutsideVariableInfosResult.timeout) {
    Logger.warn(`doExtractUnitTestContextForCpp: timeout for function '${funcNameNode?.text ?? ""}', return partial result`);
    return result.toOutputJsonString();
  }

  return result.toOutputJsonString();
}

export function getNamespaceInfo(node: SyntaxNode): string {
  const result: string[] = [];

  let curNode = node;
  while (curNode) {
    const range = {
      start: {
        line: curNode.startPosition.row,
        character: curNode.startPosition.column,
      },
      end: {
        line: curNode.endPosition.row,
        character: curNode.endPosition.column,
      },
    };
    const namespaceNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(LanguageIdEnum.Cpp, TreeSitterFunctionalityEnum.GetParentNodeWithTypeListForRange, getRootNode(node), range, ["namespace_definition"]);
    if (namespaceNode && namespaceNode.childForFieldName("name")) {
      result.push(namespaceNode.childForFieldName("name").text);
      curNode = namespaceNode.parent;
    } else {
      curNode = null;
    }
  }

  return result.reverse().join("::");
}

async function getClassTypeInfo(funcDeclNode: SyntaxNode, textDocument: TextDocument, ast: Tree): Promise<DefNodeInfo> {
  const declaratorNode = getFunctionDeclaratorNode(funcDeclNode)?.childForFieldName("declarator");
  if (declaratorNode) {
    if (declaratorNode.type === "field_identifier") {
      const funcRange = {
        start: textDocument.positionAt(funcDeclNode.startIndex),
        end: textDocument.positionAt(funcDeclNode.endIndex),
      };
      const typeDeclNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(LanguageIdEnum.Cpp, TreeSitterFunctionalityEnum.GetTypeParentNodeForRange, ast.rootNode, funcRange);
      if (typeDeclNode && isNodeWithType(typeDeclNode, ["class_specifier", "struct_specifier"])) {
        return TreeSitterDispatcher.INSTANCE.dispatch(LanguageIdEnum.Cpp, TreeSitterFunctionalityEnum.GenTypeInfoFromDefNode, textDocument.uri, typeDeclNode);
      }
    } else if (declaratorNode.type === "qualified_identifier") {
      const scopeNode = declaratorNode.childForFieldName("scope");
      if (scopeNode) {
        const defNodeInfos: DefNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
          FunctionalityEnum.GetTypeDefinition,
          LanguageIdEnum.Cpp,
          textDocument.uri,
          scopeNode,
          null,
          inWorkspaceLocationFilter,
        );
        if (defNodeInfos && defNodeInfos.length === 1 && [DefNodeKindEnum.Class, DefNodeKindEnum.Struct].includes(defNodeInfos[0].kind)) {
          return defNodeInfos[0];
        }
      }
    }
  }

  return null;
}

export function getFunctionDeclaratorNode(node: SyntaxNode): SyntaxNode {
  const bodyNode = node.childForFieldName("body");
  if (!bodyNode) {
    return null;
  }

  const queryStr = `
(function_declarator) @func_decl
`;

  const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Cpp, queryStr, node);

  for (const match of matchResult) {
    const declNode = match.matches.get("func_decl");

    if (declNode.length === 1 && declNode[0].endIndex <= bodyNode.startIndex) {
      return declNode[0];
    }
  }

  return null;
}

export function getFuncNameNode(node: SyntaxNode): SyntaxNode {
  let curNode = getFunctionDeclaratorNode(node)?.childForFieldName("declarator");
  while (curNode && !["identifier", "field_identifier", "operator_name"].includes(curNode.type)) {
    curNode = curNode.childForFieldName("name");
  }

  return curNode;
}

async function getSuperClassTypeInfos(classTypeInfo: DefNodeInfo, workspaceFolderPath: string): Promise<DefNodeInfo[]> {
  const result: DefNodeInfo[] = [];

  const superClassNameNode = getSuperClassNameNode(classTypeInfo.defNode);
  if (superClassNameNode) {
    const defNodeInfos: DefNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
      FunctionalityEnum.GetTypeDefinition,
      LanguageIdEnum.Cpp,
      classTypeInfo.uri,
      superClassNameNode,
      null,
      inWorkspaceLocationFilter,
    );
    if (defNodeInfos && defNodeInfos.length === 1 && [DefNodeKindEnum.Class, DefNodeKindEnum.Struct].includes(defNodeInfos[0].kind)) {
      result.push(defNodeInfos[0]);
      result.push(...(await getSuperClassTypeInfos(defNodeInfos[0], workspaceFolderPath)));
    }
  }

  return result;
}

export function getSuperClassNameNode(node: SyntaxNode): SyntaxNode {
  const baseClassQueryStr = `
(class_specifier
  (base_class_clause) @base_class_clause
)
`;

  const baseClassMatchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Cpp, baseClassQueryStr, node);

  for (const baseClassMatch of baseClassMatchResult) {
    const baseClassNodes = baseClassMatch.matches.get("base_class_clause");

    if (baseClassNodes.length === 1) {
      const identifierQueryStr = `
(type_identifier) @base_class_name
`;

      const identifierMatchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Cpp, identifierQueryStr, baseClassNodes[0]);

      for (const identifierMatch of identifierMatchResult) {
        const nameNodes = identifierMatch.matches.get("base_class_name");

        if (nameNodes.length === 1) {
          return nameNodes[0];
        }
      }
    }
  }

  return null;
}

async function getReturnTypeInfos(uri: DocumentUri, node: SyntaxNode): Promise<DefNodeInfo[]> {
  const result: DefNodeInfo[] = [];

  const returnTypeNameNodes = getReturnTypeNameNodes(node);

  for (const returnTypeNameNode of returnTypeNameNodes) {
    if (returnTypeNameNode) {
      const defNodeInfos: DefNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
        FunctionalityEnum.GetTypeDefinition,
        LanguageIdEnum.Cpp,
        uri,
        returnTypeNameNode,
        null,
        inWorkspaceLocationFilter,
      );
      if (defNodeInfos && defNodeInfos.length === 1) {
        result.push(defNodeInfos[0]);
      }
    }
  }

  return result;
}

export function getReturnTypeNameNodes(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  const typeNode = node.childForFieldName("type");
  if (typeNode) {
    const queryStr = `
(type_identifier) @return_type_name
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Cpp, queryStr, typeNode);

    for (const match of matchResult) {
      const nameNodes = match.matches.get("return_type_name");

      if (nameNodes && nameNodes.length === 1) {
        result.push(nameNodes[0]);
      }
    }
  }

  return result;
}

async function getParamTypeInfos(uri: DocumentUri, node: SyntaxNode): Promise<DefNodeInfo[]> {
  const result: DefNodeInfo[] = [];

  const paramTypeNameNodes = getParamTypeNameNodes(node);
  for (const paramTypeNameNode of paramTypeNameNodes) {
    if (paramTypeNameNode) {
      const defNodeInfos: DefNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
        FunctionalityEnum.GetTypeDefinition,
        LanguageIdEnum.Cpp,
        uri,
        paramTypeNameNode,
        null,
        inWorkspaceLocationFilter,
      );
      if (defNodeInfos && defNodeInfos.length === 1) {
        result.push(defNodeInfos[0]);
      }
    }
  }

  return result;
}

export function getParamTypeNameNodes(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  const parametersNode = getFunctionDeclaratorNode(node)?.childForFieldName("parameters");
  for (const parameterNode of parametersNode?.namedChildren ?? []) {
    if (parameterNode.type === "parameter_declaration") {
      const queryStr = `
(type_identifier) @param_type_name
`;

      const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Cpp, queryStr, parameterNode);
      for (const match of matchResult) {
        const nameNodes = match.matches.get("param_type_name");
        if (nameNodes && nameNodes.length === 1) {
          result.push(nameNodes[0]);
        }
      }
    }
  }

  return result;
}

async function getUsedTypeInfos(uri: DocumentUri, node: SyntaxNode, start: number): Promise<{
  data: DefNodeInfo[],
  timeout: boolean
}> {
  const result: DefNodeInfo[] = [];

  const typeNameNodes = getUsedTypeNameNodes(node);
  for (const typeNameNode of typeNameNodes) {
    const defNodeInfos: DefNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
      FunctionalityEnum.GetTypeDefinition,
      LanguageIdEnum.Cpp,
      uri,
      typeNameNode,
      null,
      inWorkspaceLocationFilter,
    );
    if (defNodeInfos && defNodeInfos.length === 1 && [DefNodeKindEnum.Class, DefNodeKindEnum.Struct, DefNodeKindEnum.Enum].includes(defNodeInfos[0].kind)) {
      result.push(defNodeInfos[0]);
    }

    if (Date.now() - start > getConfig("analysis.utgen.cpp.timeoutMs")) {
      return {data: result, timeout: true};
    }
  }

  return {data: result, timeout: false};
}

export function getUsedTypeNameNodes(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  const bodyNode = node.childForFieldName("body");
  if (bodyNode) {
    const queryStr = `
[
  (type_identifier) @type_name
  (namespace_identifier) @type_name
]
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Cpp, queryStr, bodyNode);

    for (const match of matchResult) {
      const nameNodes = match.matches.get("type_name");

      if (nameNodes.length === 1) {
        result.push(nameNodes[0]);
      }
    }
  }

  return result;
}

async function getUsedFuncInfos(uri: DocumentUri, node: SyntaxNode, workspaceFolderPath: string, start: number): Promise<{
  data: DefNodeInfo[],
  timeout: boolean
}> {
  const result: DefNodeInfo[] = [];

  const funcNameNodes = getUsedFuncNameNodes(node);
  for (const funcNameNode of funcNameNodes) {
    const defNodeInfos: DefNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
      FunctionalityEnum.GetInvokeDefinition,
      LanguageIdEnum.Cpp,
      uri,
      funcNameNode,
      inWorkspaceLocationFilter,
    );

    if (defNodeInfos && defNodeInfos.length === 1) {
      result.push(defNodeInfos[0]);
    }

    if (Date.now() - start > getConfig("analysis.utgen.cpp.timeoutMs")) {
      return {data: result, timeout: true};
    }
  }

  return {data: result, timeout: false};
}

export function getUsedFuncNameNodes(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  const bodyNode = node.childForFieldName("body");
  if (bodyNode) {
    const queryStr = `
(call_expression) @invoke
`;

    const invokeMatchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Cpp, queryStr, bodyNode);

    for (const match of invokeMatchResult) {
      const invokeNodes = match.matches.get("invoke");

      if (invokeNodes.length === 1) {
        const functionNode = invokeNodes[0].childForFieldName("function");
        if (functionNode) {
          const nameName = getInvokeNameNodeHelper(functionNode);
          if (nameName) {
            result.push(nameName);
          }
        }
      }
    }
  }

  return result;
}

function getInvokeNameNodeHelper(node: SyntaxNode): SyntaxNode {
  switch (node.type) {
    case "identifier":
      return node;
    case "qualified_identifier":
      const nameNode = node.childForFieldName("name");
      if (nameNode) {
        return getInvokeNameNodeHelper(nameNode);
      } else {
        return null;
      }
    case "field_expression":
      return node.childForFieldName("field");
    case "template_function":
      return node.childForFieldName("name");
    default:
      return null;
  }
}

async function getUsedOutsideVariableInfos(uri: DocumentUri, node: SyntaxNode, start: number): Promise<{
  data: VarDeclNodeInfo[],
  timeout: boolean
}> {
  const result: VarDeclNodeInfo[] = [];

  const varNameNodes = getUsedOutsideVariableNameNodes(node);
  for (const varNameNode of varNameNodes) {
    const defNodeInfos: VarDeclNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
      FunctionalityEnum.GetVarDeclaration,
      LanguageIdEnum.Cpp,
      uri,
      varNameNode,
      inWorkspaceLocationFilter,
    );
    if (defNodeInfos && defNodeInfos.length === 1) {
      result.push(defNodeInfos[0]);
    }

    if (Date.now() - start > getConfig("analysis.utgen.cpp.timeoutMs")) {
      return {data: result, timeout: true};
    }
  }

  return {data: result, timeout: false};
}

export function getUsedOutsideVariableNameNodes(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  const bodyNode = node.childForFieldName("body");
  if (bodyNode) {
    const excludeNames = [
      ...getUsedFuncNameNodes(node).map(item => item.text),
      ...getParameterNameNodes(node).map(item => item.text),
      ...getVariableDeclNameNodes(node).map(item => item.text),
      ...getLambdaParameterNameNodes(node).map(item => item.text),
    ];

    const queryStr = `
(identifier) @identifier
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Cpp, queryStr, bodyNode);

    for (const match of matchResult) {
      const nameNodes = match.matches.get("identifier");

      if (nameNodes.length === 1 && !excludeNames.includes(nameNodes[0].text)) {
        result.push(nameNodes[0]);
      }
    }
  }

  return result;
}

export function getParameterNameNodes(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  const parametersNode = getFunctionDeclaratorNode(node)?.childForFieldName("parameters");
  for (const parameterNode of parametersNode?.namedChildren ?? []) {
    if (parameterNode.type === "parameter_declaration") {
      const queryStr = `
(identifier) @param_name
`;

      const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Cpp, queryStr, parameterNode);

      const nameNodes = matchResult[0]?.matches.get("param_name");
      if (nameNodes && nameNodes.length === 1) {
        result.push(nameNodes[0]);
      }
    }
  }

  return result;
}

export function getVariableDeclNameNodes(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  const bodyNode = node.childForFieldName("body");
  if (bodyNode) {
    const declaratorQueryStr = `
(declaration
  declarator: (_) @declarator
)
`;

    const declaratorMatchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Cpp, declaratorQueryStr, bodyNode);

    for (const declaratorMatch of declaratorMatchResult) {
      const declaratorNodes = declaratorMatch.matches.get("declarator");

      if (declaratorNodes.length === 1) {
        let queryFromNode: SyntaxNode;
        if (declaratorNodes[0].type === "init_declarator") {
          queryFromNode = declaratorNodes[0].childForFieldName("declarator");
        } else {
          queryFromNode = declaratorNodes[0];
        }

        if (queryFromNode) {
          const identifierQueryStr = `
(identifier) @identifier
`;

          const identifierMatchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Cpp, identifierQueryStr, queryFromNode);

          for (const identifierMatch of identifierMatchResult) {
            const nameNodes = identifierMatch.matches.get("identifier");

            if (nameNodes.length === 1) {
              result.push(nameNodes[0]);
            }
          }
        }
      }
    }
  }

  return result;
}

export function getLambdaParameterNameNodes(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  const bodyNode = node.childForFieldName("body");
  if (bodyNode) {
    const queryStr = `
(lambda_expression
  declarator: (abstract_function_declarator
    parameters: (parameter_list
      (parameter_declaration
        declarator: (identifier) @name_var
      )
    )
  )
)
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Cpp, queryStr, bodyNode);

    for (const match of matchResult) {
      const nameNodes = match.matches.get("name_var");

      if (nameNodes.length === 1) {
        result.push(nameNodes[0]);
      }
    }
  }

  return result;
}

async function getTypeInfoFromDefNode(defNodeInfo: DefNodeInfo, workspaceFolderPath: string, targetFuncNode?: SyntaxNode): Promise<TypeContextInfo> {
  const classContextInfo = new TypeContextInfo(defNodeInfo.name, getRelativePath(defNodeInfo.uri, workspaceFolderPath), getNamespaceInfo(defNodeInfo.defNode), defNodeInfo.kind);

  switch (defNodeInfo.kind) {
    case DefNodeKindEnum.Class:
    case DefNodeKindEnum.Struct:
      classContextInfo.withFields(getFieldDefNodes(defNodeInfo.defNode).map(item => item.text));

      const constructorDefNodes: SyntaxNode[] = [];
      constructorDefNodes.push(...getConstructorDefNodes(defNodeInfo.defNode));
      const constructorDeclNodes = getConstructorDeclNodes(defNodeInfo.defNode);
      for (const constructorDeclNode of constructorDeclNodes) {
        const defNodeInfos: DefNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
          FunctionalityEnum.GetInvokeDefinition,
          LanguageIdEnum.Cpp,
          defNodeInfo.uri,
          defNodeInfo.defNode,
          inWorkspaceLocationFilter,
        );

        if (defNodeInfos && defNodeInfos.length === 1 && [DefNodeKindEnum.Class, DefNodeKindEnum.Struct].includes(defNodeInfos[0].kind)) {
          constructorDefNodes.push(defNodeInfos[0].defNode);
        } else {
          constructorDefNodes.push(constructorDeclNode);
        }
      }

      classContextInfo.withConstructors(constructorDefNodes.map(item => item.text));

      if (targetFuncNode) {
        classContextInfo.withTargetFunc(targetFuncNode.text);
      }
      break;
    case DefNodeKindEnum.Enum:
    case DefNodeKindEnum.Alias:
      classContextInfo.withCode(defNodeInfo.defNode.text);
      break;
  }

  return classContextInfo;
}

export function getFieldDefNodes(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  if (node.type === "class_specifier") {
    const bodyNode = node.childForFieldName("body");
    if (bodyNode) {
      for (const child of bodyNode.namedChildren) {
        if (child.type === "field_declaration") {
          if (child.childForFieldName("declarator")?.type === "field_identifier") {
            result.push(child);
          }
        }
      }
    }
  }

  return result;
}

export function getConstructorDefNodes(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  if (node.type === "class_specifier") {
    const className = node.childForFieldName("name")?.text;
    if (className) {
      for (const funcDefNode of node.descendantsOfType("function_definition")) {
        const funcName = getFuncNameNode(funcDefNode)?.text;
        if (funcName && funcName === className) {
          result.push(funcDefNode);
        }
      }
    }
  }

  return result;
}

export function getConstructorDeclNodes(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];

  if (node.type === "class_specifier") {
    const queryStr = `
(declaration
  declarator: (function_declarator) @func_decl
)
`;

    const className = node.childForFieldName("name")?.text;
    if (className) {
      const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Cpp, queryStr, node);

      for (const match of matchResult) {
        const declNode = match.matches.get("func_decl");

        if (declNode.length === 1) {
          const funcName = declNode[0].childForFieldName("declarator")?.text;
          if (funcName && funcName === className) {
            result.push(declNode[0]);
          }
        }
      }
    }
  }

  return result;
}

function getFuncInfoFromDefNode(defNodeInfo: DefNodeInfo, workspaceFolderPath: string): FuncContextInfo {
  const funcContextInfo = new FuncContextInfo(defNodeInfo.name, getRelativePath(defNodeInfo.uri, workspaceFolderPath), getNamespaceInfo(defNodeInfo.defNode));

  funcContextInfo.withBody(defNodeInfo.defNode.text);

  return funcContextInfo;
}

export function getVariableInfoFromDeclNode(valDeclNodeInfo: VarDeclNodeInfo, workspaceFolderPath: string): VariableContextInfo {
  const variableContextInfo = new VariableContextInfo(valDeclNodeInfo.name, getRelativePath(valDeclNodeInfo.uri, workspaceFolderPath), getNamespaceInfo(valDeclNodeInfo.declNode));

  variableContextInfo.withCode(valDeclNodeInfo.declNode.text);

  return variableContextInfo;
}