import {Range, TextDocument} from "vscode-languageserver-textdocument";
import {TreeSitterParser} from "../../parse/treesitter/parser";
import {TreeSitterDispatcher} from "../../parse/treesitter/dispatcher";
import {TreeSitterFunctionalityEnum} from "../../parse/treesitter/share";

export function doGetCodeStructNodeForKeywords(document: TextDocument, keywords: { text: string, range: Range }[]): {
  keyword: { text: string, range: Range },
  codeStruct: { text: string, range: Range }
}[] {
  const resultList = [];

  const tree = TreeSitterParser.INSTANCE.parse(document.languageId, document.getText());
  if (!tree) {
    for (const keyword of keywords) {
      resultList.push({
        keyword,
        codeStruct: null,
      });
    }
    return resultList;
  }

  for (const keyword of keywords) {
    const range = {
      start: keyword.range.start,
      end: keyword.range.start,
    };
    const codeStructNode = TreeSitterDispatcher.INSTANCE.dispatch(document.languageId, TreeSitterFunctionalityEnum.GetParentNodeForKeywordSearch, tree.rootNode, range);
    if (codeStructNode) {
      resultList.push({
        keyword,
        codeStruct: {
          text: codeStructNode.text,
          range: {
            start: {
              line: codeStructNode.startPosition.row,
              character: codeStructNode.startPosition.column,
            },
            end: {
              line: codeStructNode.endPosition.row,
              character: codeStructNode.endPosition.column,
            },
          },
        },
      });
    } else {
      resultList.push({
        keyword,
        codeStruct: null,
      });
    }
  }

  return resultList;
}