package import_analysis

import (
	"context"
	"encoding/json"
	"fmt"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/protocol"
	"os"
	"path/filepath"
	"strings"

	"ide/ckg/codekg/components/logs"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

type ImportAnalyzer struct {
	Ctx        context.Context
	ProjectID  model.URI
	Storage    data_storage.Storage
	FileSystem file_system.FileSystem

	File            string
	ImportStatement string
}

func NewImportAnalyzer(ctx context.Context, projectID string, storage data_storage.Storage, fileSystem file_system.FileSystem, file string, importStatement string) *ImportAnalyzer {
	return &ImportAnalyzer{
		Ctx:             ctx,
		ProjectID:       projectID,
		Storage:         storage,
		FileSystem:      fileSystem,
		File:            file,
		ImportStatement: importStatement,
	}
}

func (p *ImportAnalyzer) Analyze() (*protocol.ImportAnalysisResult, error) {
	return p.analyzeGo()
}

func (p *ImportAnalyzer) AnalyzeFiles(files []string) (*protocol.ImportAnalysisResult, error) {
	return p.analyzeGoFiles(files)
}

func (p *ImportAnalyzer) analyzeGo() (*protocol.ImportAnalysisResult, error) {
	pkgPath, err := p.getPkgPath(p.getGoModuleInfo())
	if err != nil {
		return nil, fmt.Errorf("failed to getPkgPath: %s", err.Error())
	}

	result, err := p.getPkgCodeInfo(pkgPath)
	if err != nil {
		return nil, fmt.Errorf("failed to getPkgCodeInfo: %s", err.Error())
	}

	return result, nil
}

func (p *ImportAnalyzer) analyzeGoFiles(files []string) (*protocol.ImportAnalysisResult, error) {
	result, err := p.getFilesImportInfo(files)
	if err != nil {
		return nil, fmt.Errorf("failed to getFilesImportInfo: %s", err.Error())
	}

	return result, nil
}

func (p *ImportAnalyzer) getPkgPath(moduleInfo [2]*string) (string, error) {
	if moduleInfo[0] == nil || moduleInfo[1] == nil {
		return "", fmt.Errorf("failed to parse information from go.mod")
	}

	importStringSplit := strings.Split(strings.TrimSpace(p.ImportStatement), " ")
	importPkgString := importStringSplit[len(importStringSplit)-1]
	pkgPath := strings.Trim(importPkgString, "\"")

	return fmt.Sprintf("%s%s", *moduleInfo[0], strings.TrimPrefix(strings.TrimPrefix(pkgPath, *moduleInfo[1]), "/")), nil
}

func (p *ImportAnalyzer) getGoModuleInfo() [2]*string {
	var moduleInfo [2]*string

	goModFile, err := p.getGoModFile()
	if err != nil {
		logs.CtxError(p.Ctx, "failed to getGoModFile: %s", err.Error())
		return moduleInfo
	}

	relativePath := strings.TrimPrefix(strings.TrimPrefix(p.FileSystem.Dir(goModFile), p.ProjectID), "/")
	moduleInfo[0] = &relativePath
	moduleInfo[1] = p.getModuleNameFromGoModFile(goModFile)

	return moduleInfo
}

func (p *ImportAnalyzer) getGoModFile() (model.URI, error) {
	result := ""

	err := p.FileSystem.Walk(p.ProjectID, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() && !strings.HasPrefix(filepath.Join(p.ProjectID, p.File), path) {
			return file_system.SkipDir
		}

		if strings.HasSuffix(path, "go.mod") && strings.HasPrefix(filepath.Join(p.ProjectID, p.File), p.FileSystem.Dir(path)) {
			if len(result) < len(path) {
				result = path
			}
		}

		return nil
	})

	if err != nil {
		return "", fmt.Errorf("failed to Walk: %s", err.Error())
	}
	if result == "" {
		return "", fmt.Errorf("failed to find go.mod file")
	}

	return result, nil
}

func (p *ImportAnalyzer) getRelativePathFromGoModFile(modFileEntityID string) *string {
	relativePath := strings.TrimSuffix(strings.TrimPrefix(modFileEntityID, "F."), "go.mod")
	return &relativePath
}

func (p *ImportAnalyzer) getModuleNameFromGoModFile(goModFilePath string) *string {
	content, _ := os.ReadFile(goModFilePath)
	lines := strings.Split(string(content), "\n")
	if len(lines) == 0 {
		return nil
	} else {
		moduleName := strings.TrimSuffix(strings.TrimPrefix(strings.TrimSpace(lines[0]), "module "), " ")
		return &moduleName
	}
}

type GoFileResult struct {
	FilePath    string            `json:"file_path"`
	FileContent string            `json:"file_content"`
	SymbolList  []*GoSymbolResult `json:"symbol_list"`
}

type GoSymbolResult struct {
	SymbolName string `json:"symbol_name"`
	SymbolType string `json:"symbol_type"`
	SymbolInfo string `json:"symbol_info"`
}

func (p *ImportAnalyzer) getPkgCodeInfo(pkgPath string) (*protocol.ImportAnalysisResult, error) {
	_, err := p.Storage.GetEntityByEntityID(p.Ctx, p.Storage.GetConn(), pkgPath)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &protocol.ImportAnalysisResult{
				Message: "PACKAGE_NOT_FOUND",
			}, nil
		}
		return nil, fmt.Errorf("failed to GetEntityByEntityID: %s", err.Error())
	}

	entities, err := p.Storage.GetEndEntitiesInRelationByStartIDAndType(p.Ctx, p.Storage.GetConn(), pkgPath, model.FileToFile)
	if err != nil {
		return nil, fmt.Errorf("failed to GetEntitiesInRelationByStartIDAndType: %s", err.Error())
	}

	var result []*GoFileResult
	for _, entity := range entities {
		if entity == nil {
			continue
		}

		if entity.Type == model.File {
			fileResult, err := p.getFileResult(entity)
			if err != nil {
				logs.CtxError(p.Ctx, "failed to getFileResult: %s", err.Error())
				continue
			}
			result = append(result, fileResult)
		}
	}

	data, err := json.Marshal(result)
	if err != nil {
		return nil, fmt.Errorf("failed to Marshal: %s", err.Error())
	}

	return &protocol.ImportAnalysisResult{
		Message: "",
		Data:    string(data),
	}, nil
}

func (p *ImportAnalyzer) getFileResult(entity *model.Entity) (*GoFileResult, error) {
	result := &GoFileResult{
		FilePath:   strings.TrimPrefix(entity.ID, "F."),
		SymbolList: make([]*GoSymbolResult, 0),
	}

	fullContent, err := os.ReadFile(entity.URI)
	if err != nil {
		return nil, fmt.Errorf("failed to ReadFile: %s", err.Error())
	}
	result.FileContent = string(fullContent)

	classEntities, err := p.Storage.GetStartEntitiesInRelationByEndIDAndType(p.Ctx, p.Storage.GetConn(), entity.ID, model.ClassToFile)
	if err != nil {
		return nil, fmt.Errorf("failed to GetStartEntitiesInRelationByEndIDAndType: %s", err.Error())
	}

	for _, classEntity := range classEntities {
		if p.isInterface(classEntity) {
			symbolInfo := &GoSymbolResult{
				SymbolName: classEntity.Name,
				SymbolType: "interface",
			}

			content := strings.Join(classEntity.GetContent(), "\n")
			if content == "" {
				logs.CtxError(p.Ctx, "failed to get content for struct '%s'", classEntity.Name)
				continue
			}

			symbolInfo.SymbolInfo = content

			result.SymbolList = append(result.SymbolList, symbolInfo)
		} else {
			symbolInfo := &GoSymbolResult{
				SymbolName: classEntity.Name,
				SymbolType: "struct",
			}

			content := strings.Join(classEntity.GetContent(), "\n")
			if content == "" {
				logs.CtxError(p.Ctx, "failed to get content for struct '%s'", classEntity.Name)
				continue
			}

			classMethodEntities, err := p.Storage.GetClassMethodEntities(p.Ctx, p.Storage.GetConn(), classEntity.ID)
			if err != nil {
				return nil, fmt.Errorf("failed to GetClassMethodEntities: %s", err.Error())
			}

			symbolInfo.SymbolInfo = content
			result.SymbolList = append(result.SymbolList, symbolInfo)

			//var classMethodSignatures []string
			for _, classMethodEntity := range classMethodEntities {
				signature := classMethodEntity.GetSignature()
				if signature == "" {
					logs.CtxError(p.Ctx, "failed to get signature for class method '%s'", classMethodEntity.Name)
					continue
				}

				//classMethodSignatures = append(classMethodSignatures, signature)

				methodSymbolInfo := &GoSymbolResult{
					SymbolName: classEntity.Name + "." + classMethodEntity.Name,
					SymbolType: "member_function",
					SymbolInfo: signature,
				}
				result.SymbolList = append(result.SymbolList, methodSymbolInfo)
			}

			//symbolInfo.SymbolInfo = content + "\n" + strings.Join(classMethodSignatures, "\n")
			//result.SymbolList = append(result.SymbolList, symbolInfo)
		}
	}

	methodEntities, err := p.Storage.GetStartEntitiesInRelationByEndIDAndType(p.Ctx, p.Storage.GetConn(), entity.ID, model.MethodToFile)
	if err != nil {
		return nil, fmt.Errorf("failed to GetStartEntitiesInRelationByEndIDAndType: %s", err.Error())
	}

	for _, methodEntity := range methodEntities {
		symbolInfo := &GoSymbolResult{
			SymbolName: methodEntity.Name,
			SymbolType: "function",
		}

		signature := methodEntity.GetSignature()
		if signature != "" {
			symbolInfo.SymbolInfo = signature
		} else {
			logs.CtxError(p.Ctx, "failed to get signature for method '%s'", methodEntity.Name)
			continue
		}

		result.SymbolList = append(result.SymbolList, symbolInfo)
	}

	return result, nil
}

func (p *ImportAnalyzer) isInterface(entity *model.Entity) bool {
	contentLines := entity.GetContent()
	if len(contentLines) > 0 {
		return strings.Contains(contentLines[0], "interface")
	} else {
		return false
	}
}

func (p *ImportAnalyzer) getFilesImportInfo(paths []string) (*protocol.ImportAnalysisResult, error) {
	entityMap := make(map[string]*model.Entity)
	for _, path := range paths {
		queryEntityId := fmt.Sprintf("F.%s", path)
		entity, err := p.Storage.GetEntityByEntityID(p.Ctx, p.Storage.GetConn(), queryEntityId)
		if err != nil {
			entityMap[path] = nil
		} else {
			entityMap[path] = entity
		}
	}

	var result []*GoFileResult
	for path, entity := range entityMap {
		if entity == nil {
			result = append(result, &GoFileResult{
				FilePath:    path,
				FileContent: "",
				SymbolList:  nil,
			})
		} else if entity.Type == model.File {
			fileResult, err := p.getFileResult(entity)
			if err != nil {
				logs.CtxError(p.Ctx, "failed to getFileResult: %s", err.Error())
				continue
			}
			result = append(result, fileResult)
		}
	}

	data, err := json.Marshal(result)
	if err != nil {
		return nil, fmt.Errorf("failed to Marshal: %s", err.Error())
	}

	return &protocol.ImportAnalysisResult{
		Message: "",
		Data:    string(data),
	}, nil
}
