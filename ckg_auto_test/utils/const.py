import os

# 流水线构建的二进制下载信息变量名
binary_download_message = 'binary_download_path'

# 测试仓库代码仓以及commit ID
repo_url1 = '******************:ide/github_xManager.git'
repo_url1_commit_id = '37dea6f9143933e2e4ac3359c7c760da85ce2b6a'

repo_url2 = '******************:ide/github_practical_python.git'
repo_url2_commit_id = '40738576bf725c1f5ea3dbc5667972661d15c207'

# 获取当前工作目录
current_working_directory = os.getcwd()

# 测试代码仓库路径
repo_path1 = os.path.join(current_working_directory, 'ckg_auto_test', 'test')
repo_path2 = os.path.join(current_working_directory, 'ckg_auto_test', 'test2')

# ckg二进制下载路径
download_binary_path = os.path.join(current_working_directory, 'ckg_auto_test', 'binary')

# ckg 启动参数
storage_path = os.path.join(current_working_directory, 'ckg_auto_test', 'storage')
port = 56642

# package.json相对路径
relative_path = 'clients/ts/package.json'

# os type
binary_linux_arm64 = "ckg_server_linux_arm64"
binary_linux_x64 = "ckg_server_linux_x64"
binary_darwin_arm64 = "ckg_server_darwin_arm64"
binary_darwin_x64 = "ckg_server_darwin_x64"
binary_win_x64 = "ckg_server_windows_x64.exe"

# jwt_token
jwt_token = 'df93f539abfd1e96d56da7d5825598c2'
jwt_token_url = 'https://cloud.bytedance.net/auth/api/v1/jwt/'

user_id = 'test'

# repo1预期成功索引文件数
repo1_success_index_files = 44

# repo2预期成功索引文件数
repo2_success_index_files = 219

# 仓库同步功能测试用例
java_code1 = """public class HelloWorld {
    public static void printHelloWorld() {
        System.out.println("Hello, World!");
    }
    public static void main(String[] args) {
        printHelloWorld();
    }
}
"""
java_code2 = """public class HelloWorld {
    public static void main(String[] args) {
        printHelloWorld();
    }
}
"""
test_java_name = 'hello.java'

# 召回设置的user_message
user_message1 = '请解释下Solutions/1_33/pcost.py文件'
user_message2 = '请解释下Solutions/4_4/fileparse.py文件中的parse_csv'

folder_path1 = os.path.join(repo_path2, 'Solutions', '1_33')
entity_num = 1
retrieve_entity_num = 1

# 召回功能预期的召回信息(适配windows entity id的路径)
except_entity_id1 = os.path.join('F.Solutions', '1_33', 'pcost.py')
except_entity_id2_linux = 'M.Solutions/4_4.fileparse.parse_csv'
except_entity_id2_windows = os.path.join('M.Solutions', '4_4','fileparse.parse_csv')
except_entity_id3 = os.path.join('F.Solutions', '4_4', 'fileparse.py')
except_relation_file_path = os.path.join(repo_path2, 'Solutions', '4_4', 'fileparse.py')
except_method_name = 'parse_csv'
except_file_path = os.path.join(repo_path2, 'Solutions', '4_4', 'fileparse.py')

# 召回功能构造编辑info信息
editor_info_file_path = os.path.join(repo_path2, 'Solutions', '1_33', 'pcost.py')
editor_info_select_code_content = """with open(filename, 'rt') as f:"""
editor_info_select_code_start_line = 10
editor_info_select_code_end_line = 11