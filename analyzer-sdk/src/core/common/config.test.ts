import {describe, expect, it} from "vitest";
import {Config, getConfig, doSetConfigById} from "./config";

describe("test_doSetConfig", () => {
  it("case1_normal", async () => {
    doSetConfigById("parse.lsp.timeoutMs", 998);

    expect(Config.INSTANCE.parse.lsp.timeoutMs).toEqual(998);
  });
});

describe("test_getConfig", () => {
  it("case1_normal", async () => {
    Config.INSTANCE.parse.lsp.timeoutMs = 998;

    expect(getConfig("parse.lsp.timeoutMs")).toEqual(998);
  });
  it("case2_not_found_1", async () => {
    Config.INSTANCE.parse.lsp.timeoutMs = 998;

    try {
      getConfig("parse.lsp.timeout");
      expect(false).toBe(true);
    } catch (e) {
      expect(e.message).toEqual("configId parse.lsp.timeout not found");
    }
  });

  it("case2_not_found_2", async () => {
    Config.INSTANCE.parse.lsp.timeoutMs = 998;

    try {
      getConfig("parse.ab.timeoutMs");
      expect(false).toBe(true);
    } catch (e) {
      expect(e.message).toEqual("configId parse.ab.timeoutMs not found");
    }
  });
});