package knowledgebase

import (
	"bytes"
	"github.com/samber/lo"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/util"
	"strings"
)

func createFileAliasEntity(entityID string, uri string) []*model.AliasEntity {
	var aliasEntity = make([]*model.AliasEntity, 0)
	var aliasNames = make([]string, 0)
	subDir := util.GetSubdirectory(uri)
	for subDir != "" && (strings.Contains(subDir, "\\") || strings.Contains(subDir, "/")) {
		aliasNames = append(aliasNames, subDir)
		subDir = util.GetSubdirectory(subDir)
	}

	for _, alias := range aliasNames {
		aliasEntity = append(aliasEntity, &model.AliasEntity{
			EntityID: entityID,
			Alias:    alias,
		})
	}

	return aliasEntity
}

func getAliasEntity(attributes map[string]interface{}, entityID string) []*model.AliasEntity {
	aliasName := make([]string, 0)
	aliasEntity := make([]*model.AliasEntity, 0)
	if v, ok := attributes[model.AttributeLabelAliasName]; ok {
		if value, ok := v.([]interface{}); ok {
			for _, item := range value {
				if s, ok := item.(string); ok { // 对单个元素进行类型断言
					aliasName = append(aliasName, s)
				}
			}
		}
	}

	for _, alias := range aliasName {
		aliasEntity = append(aliasEntity, &model.AliasEntity{
			EntityID: entityID,
			Alias:    alias,
		})
	}

	return aliasEntity
}

func trimPrefixAndSpace(data []byte, prefix []byte) []byte {
	data = bytes.TrimPrefix(data, prefix)
	if len(data) > 0 && data[0] == ' ' {
		data = data[1:]
	}
	return data
}

func ConvertKBEmbeddingToEmbedding(e *Embedding) *model.Embedding {
	return &model.Embedding{
		Embedding: lo.Map(e.Embedding, func(item float64, _ int) float32 { return float32(item) }),
	}
}

func GetSplitFile(filePath, content string) *SplitFile {
	return &SplitFile{
		Content:   content,
		Path:      filePath,
		ChunkSize: 1500,
	}
}
