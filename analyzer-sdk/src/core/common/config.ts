import { Logger } from "../../utils/logger";

export class Config {
  static INSTANCE = new Config();

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {}

  cache = {
    ast: {
      maxLineCount: 10_000,
    },
  };
  parse = {
    common: {
      timeoutInterval: 10 * 1000,
    },
    lsp: {
      maxRequestCount: 1,
      waitInterval: 10,
      timeoutMs: 500,
    },
    psi: {
      maxRequestCount: 1,
      waitInterval: 10,
      timeoutMs: 500,
    },
  };
  analysis = {
    definition: {
      completeAnalysisMaxLength: 2_000,
      partialAnalysisLines: 10,
      partialAnalysisDebounce: 1000,
      toAnalyzerMaxSymbolCount: 100,
    },
    utgen: {
      python: {
        timeoutMs: 4_000,
      },
      cpp: {
        timeoutMs: 3_000,
      },
      javascript: {
        timeoutMs: 3_000,
      },
    },
  };
}

export function doSetConfigById(configId: string, value: any): void {
  if (configId) {
    const configIdSplit = configId.split(".");
    let cur: any = Config.INSTANCE;
    for (let i = 0; i < configIdSplit.length; i++) {
      if (cur[configIdSplit[i]] !== null && cur[configIdSplit[i]] !== undefined) {
        if (i === configIdSplit.length - 1) {
          cur[configIdSplit[i]] = value;
        } else {
          cur = cur[configIdSplit[i]];
        }
      } else {
        Logger.error(`doSetConfigById: configId ${configId} not found`);
        return;
      }
    }
    Logger.info(`doSetConfigById: ${configId} = ${value}`);
  }
}

export function getConfig(configId: string): any {
  if (configId) {
    const configIdSplit = configId.split(".");
    let cur: any = Config.INSTANCE;
    for (let i = 0; i < configIdSplit.length; i++) {
      if (cur[configIdSplit[i]] !== null && cur[configIdSplit[i]] !== undefined) {
        cur = cur[configIdSplit[i]];
      } else {
        throw new Error(`configId ${configId} not found`);
      }
    }

    return cur;
  }

  return null;
}

export enum FeatureName {
  DISABLE_LOG = "disable_log",
  DISABLE_EVENT = "disable_event",

  ENABLE_AST_CACHE_ANALYSIS = "enable_ast_cache_analysis",
  ENABLE_DEFINITION_ANALYSIS = "enable_definition_analysis",

  ENABLE_GO_DEFINITION_ANALYSIS = "enable_go_definition_analysis",
  ENABLE_TS_DEFINITION_ANALYSIS = "enable_ts_definition_analysis",
  ENABLE_JAVA_DEFINITION_ANALYSIS = "enable_java_definition_analysis",

  ENABLE_PSI_LSP_REQUEST = "enable_psi_lsp_request",
  ENABLE_PSI_LSP_TIMEOUT_INTERVAL = "enable_psi_lsp_timeout_interval",
}

export class FeatureGateManager {
  static INSTANCE = new FeatureGateManager();

  private features = new Set<FeatureName>();

  private cachedFeatures = new Map<string, Set<FeatureName>>();

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {}

  openFeature(feature: FeatureName): void {
    this.features.add(feature);
    Logger.info(`FeatureGateManager.openFeature: ${feature}`);
  }

  closeFeature(feature: FeatureName): void {
    this.features.delete(feature);
    Logger.info(`FeatureGateManager.closeFeature: ${feature}`);
  }

  isFeatureOpen(feature: FeatureName): boolean {
    return this.features.has(feature);
  }

  getOpenedFeature(): FeatureName[] {
    return Array.from(this.features);
  }

  cacheFeature(flag: string): void {
    this.cachedFeatures.set(flag, this.features);
    this.features = new Set<FeatureName>();
  }

  restoreFeature(flag: string): void {
    const features = this.cachedFeatures.get(flag);
    if (features) {
      this.features = features;
    }
  }
}