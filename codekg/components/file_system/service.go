package file_system

import (
	"io/fs"
	"os"
	"path/filepath"
)
import "ide/ckg/codekg/model"

var (
	RealFS  *realFileSystem = &realFileSystem{}
	SkipDir                 = fs.SkipDir
)

//go:generate go run go.uber.org/mock/mockgen -destination mock_file_system/file_system_mock.go -package file_system_mock . FileSystem
type FileSystem interface {
	Walk(project model.URI, fn filepath.WalkFunc) error
	Stat(file model.URI) (os.FileInfo, error)
	Lstat(file model.URI) (os.FileInfo, error)
	Rel(basePath string, targetPath string) (string, error)
	ReadDir(name string) ([]os.DirEntry, error)
	Dir(path string) string
	ReadFile(name string) ([]byte, error)
	Create(name string) (*os.File, error)
	Abs(path string) (string, error)
	Base(path string) string
	OpenFile(name string, flag int, perm os.FileMode) (*os.File, error)
	Remove(name string) error
	RemoveAll(path string) error
}

type realFileSystem struct{}

func (f *realFileSystem) Remove(file string) error {
	return os.Remove(file)
}

func (f *realFileSystem) RemoveAll(path string) error { return os.RemoveAll(path) }

func (f *realFileSystem) OpenFile(name string, flag int, perm os.FileMode) (*os.File, error) {
	return os.OpenFile(name, flag, perm)
}

func (f *realFileSystem) Abs(path string) (string, error) {
	return filepath.Abs(path)
}

func (f *realFileSystem) Base(path string) string {
	return filepath.Base(path)
}

func (f *realFileSystem) Walk(project model.URI, fn filepath.WalkFunc) error {
	return filepath.Walk(project, fn)
}

func (f *realFileSystem) Stat(file model.URI) (os.FileInfo, error) {
	return os.Stat(file)
}

func (f *realFileSystem) Lstat(file model.URI) (os.FileInfo, error) {
	return os.Lstat(file)
}

func (f *realFileSystem) Rel(basePath string, targetPath string) (string, error) {
	return filepath.Rel(basePath, targetPath)
}

func (f *realFileSystem) ReadDir(dir string) ([]os.DirEntry, error) {
	return os.ReadDir(dir)
}

func (f *realFileSystem) Dir(path string) string {
	return filepath.Dir(path)
}

func (f *realFileSystem) ReadFile(file string) ([]byte, error) {
	return os.ReadFile(file)
}

func (f *realFileSystem) Create(name string) (*os.File, error) {
	return os.Create(name)
}
