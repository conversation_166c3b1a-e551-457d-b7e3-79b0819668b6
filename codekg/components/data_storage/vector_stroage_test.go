package data_storage

import (
	"context"
	"fmt"
	"github.com/stretchr/testify/require"
	"ide/ckg/codekg/model"
	"testing"
)

var (
	ctx  = context.Background()
	docs = []*model.EmbeddingDocument{
		{
			ID:      "1",
			Content: "package main import \"fmt\"  func main() {  fmt.Println(\"Hello, World!\")  }",
			Embedding: &model.Embedding{
				Embedding: []float32{1, 2, 3, 4, 5, 6, 7, 8, 9, 10},
			},
			Metadata: map[string]any{
				"file": "test.go",
			},
		},
		{
			ID:      "2",
			Content: "package main import \"fmt\"  func calcu[T int | float64](a, b T) T {  return a + b  }",
			Embedding: &model.Embedding{
				Embedding: []float32{1, 2, 3, 4, 5, 6, 7, 8, 10, 9},
			},
			Metadata: map[string]any{
				"file": "calcu.go",
			},
		},
		{
			ID:      "3",
			Content: "package test import \"testing\"  func TestAddEmbeddingDocument(t *testing.T) {  t.Log(\"test\")  }",
			Embedding: &model.Embedding{
				Embedding: []float32{1, 2, 3, 5, 4, 6, 7, 8, 10, 9},
			},
			Metadata: map[string]any{
				"file": "test.go",
			},
		},
		{
			ID:      "4",
			Content: "package test",
			Embedding: &model.Embedding{
				Embedding: []float32{10, 2, 3, 4, 5, 6, 7, 8, 1, 9},
			},
			Metadata: map[string]any{
				"file": "test.go",
			},
		},
	}
)

func TestChromemGo(t *testing.T) {
	csg, err := CreateChromemGoStorage(ctx, ".", "ckg")
	if err != nil {
		t.Fail()
		return
	}
	option := NewAddEmbeddingDocumentOptionBuilder().
		Build()
	_ = csg.AddEmbeddingDocument(ctx, docs[0], option)
	_ = csg.AddEmbeddingDocument(ctx, docs[1], option)
	_ = csg.AddEmbeddingDocument(ctx, docs[3], option)
	d, err := csg.GetById(ctx, "1", Collection_Embedding)
	if err != nil {
		t.Fail()
		return
	}
	fmt.Println(d)
	var res []*model.EmbeddingQueryResult
	whereMetadata := make(map[string]any)
	whereMetadata["file"] = "test.go"
	aQueryOption := NewEmbeddingQueryOptions().
		SetWhereMetadata(whereMetadata).
		Build()
	bQueryOption := NewEmbeddingQueryOptions().
		Build()
	cQueryOption := NewEmbeddingQueryOptions().
		SetWhereMetadata(whereMetadata).
		Build()
	if res, err = csg.QueryByEmbedding(ctx, docs[2].Embedding, 2, aQueryOption); err != nil {
		t.Fail()
		return
	}
	require.Len(t, res, 2)
	if res, err = csg.QueryByEmbedding(ctx, docs[2].Embedding, 3, bQueryOption); err != nil {
		t.Fail()
		return
	}
	require.Len(t, res, 3)
	if res, err = csg.QueryByEmbedding(ctx, docs[2].Embedding, 3, cQueryOption); err != nil {
		t.Fail()
		return
	}
	require.Len(t, res, 2)

	// delete "1"
	deleteOption := NewDeleteDocumentOptionBuilder().Build()
	if err = csg.DeleteDocumentsByIds(ctx, deleteOption, "1"); err != nil {
		t.Fail()
		return
	}
	if res, err = csg.QueryByEmbedding(ctx, docs[2].Embedding, 2, aQueryOption); err != nil {
		t.Fail()
		return
	}
	require.Len(t, res, 1)
	if res, err = csg.QueryByEmbedding(ctx, docs[2].Embedding, 3, bQueryOption); err != nil {
		t.Fail()
		return
	}
	require.Len(t, res, 2)
	if res, err = csg.QueryByEmbedding(ctx, docs[2].Embedding, 3, cQueryOption); err != nil {
		t.Fail()
		return
	}
	require.Len(t, res, 1)

}
