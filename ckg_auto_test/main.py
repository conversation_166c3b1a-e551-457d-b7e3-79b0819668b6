from utils import const
from utils import util
from repo_clone import clone_repo
import test_case
import os
import json

if __name__ == "__main__":
    key_id = None
    url = None
    binary_name = None
    CKG_SDK_VERSION = None
    try:
        download_message = os.environ.get(const.binary_download_message)
        if download_message is not None:
            print(f'download_message: {download_message}')
            # 将单引号转成双引号
            download_message = download_message.replace("'", '"')
            json_data = json.loads(download_message)
            url, binary_name = util.get_download_url(json_data)
            if binary_name == '' or url == '':
                print('get binary download failed')
                exit(1)
        else:
            print('binary_download_message is empty')
            exit(1)

        CKG_SDK_VERSION = util.get_ckg_version()
        print(f"CKG SDK version: {CKG_SDK_VERSION}")
        if CKG_SDK_VERSION == '':
            print('CKG SDK version is empty')
            exit(1)

        # 杀掉目标port端口的进程
        util.kill_process_by_port(const.port)
        download_name = util.download_binary_file(url, binary_name)
        print(f"download binary name: {binary_name}")
        if download_name == '':
            print('download binary failed')
            exit(1)

        # 克隆仓库
        result = clone_repo.clone_repo(const.repo_url2, const.repo_path2, const.repo_url2_commit_id)
        if not result:
            print('clone repo2 failed')
            exit(1)
        result = clone_repo.clone_repo(const.repo_url1, const.repo_path1, const.repo_url1_commit_id)
        if not result:
            print('clone repo1 failed')
            exit(1)
        result = test_case.auto_test_1(download_name, [const.repo_path1], CKG_SDK_VERSION)
        if not result:
            print('auto_test_1 failed')
            exit(1)
        print('auto_test_1 success')
        result = test_case.auto_test_2(const.repo_path1, const.repo1_success_index_files)
        if not result:
            print('auto_test_2 failed')
            exit(1)
        print('auto_test_2 success')
        result = test_case.auto_test_3(const.repo_path1, download_name)
        if not result:
            print('auto_test_3 failed')
            exit(1)
        print('auto_test_3 success')
        result = test_case.auto_test_4()
        if not result:
            print('auto_test_4 failed')
            exit(1)
        print('auto_test_4 success')
        result = test_case.auto_test_5()
        if not result:
            print('auto_test_5 failed')
            exit(1)
        print('auto_test_5 success')
        result = test_case.auto_test_6()
        if not result:
            print('auto_test_6 failed')
            exit(1)
        print('auto_test_6 success')
        result = test_case.auto_test_7(const.repo_path1)
        if not result:
            print('auto_test_7 failed')
            exit(1)
        print('auto_test_7 success')
        result = test_case.auto_test_8(const.repo_path1)
        if not result:
            print('auto_test_8 failed')
            exit(1)
        print('auto_test_8 success')
        result = test_case.auto_test_9([const.repo_path2])
        if not result:
            print('auto_test_9 failed')
            exit(1)
        print('auto_test_9 success')
        result = test_case.auto_test_10([const.repo_path2])
        if not result:
            print('auto_test_10 failed')
            exit(1)
        print('auto_test_10 success')
        result = test_case.auto_test_11([const.repo_path2])
        if not result:
            print('auto_test_11 failed')
            exit(1)
        print('auto_test_11 success')
    except Exception as e:
        print(f"ckg auto test error: {str(e)}")
        exit(1)
    finally:
        util.release_ckg()
