import {isMainThread} from "worker_threads";
import {MainHandler} from "../worker/main";
import {FeatureGateManager, FeatureName, getConfig} from "../core/common/config";

let lastTimeoutTimestamp: number = null;

export async function updateLastTimeoutTimestamp(timestamp: number): Promise<void> {
  lastTimeoutTimestamp = timestamp;

  if (isMainThread) {
    await MainHandler.INSTANCE.updateLastTimeoutTimestamp({timestamp});
  }
}

export function getLastTimeoutTimestamp(): number {
  return lastTimeoutTimestamp;
}

export function isInTimeoutInterval(): boolean {
  return FeatureGateManager.INSTANCE.isFeatureOpen(FeatureName.ENABLE_PSI_LSP_TIMEOUT_INTERVAL) && lastTimeoutTimestamp !== null && Date.now() - lastTimeoutTimestamp < getConfig("parse.common.timeoutInterval");
}