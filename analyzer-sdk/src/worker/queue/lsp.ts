import {DocumentUri, Position} from "vscode-languageserver-types";

export interface LspTask {
  command: string;
  languageId: string;
  uri: DocumentUri;
  position: Position;
}

export interface LspTaskInQueue extends LspTask {
  promise: {resolve: any, reject: any},
}

export const lspQueue: LspTaskInQueue[] = [];

export async function addWorkerLspTask(task: LspTask): Promise<any> {
  return new Promise<any>((resolve, reject) => {
    const taskInQueue = {...task, promise: {resolve, reject}};
    lspQueue.push(taskInQueue);
  });
}