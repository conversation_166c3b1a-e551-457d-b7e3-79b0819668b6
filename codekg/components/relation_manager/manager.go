package relation_manager

import (
	"context"
	"ide/ckg/codekg/components/ckg_config"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/components/relation_manager/manager_v1"
	"ide/ckg/codekg/components/relation_manager/manager_v2"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/protocol"
)

type RelationManager interface {
	HandleDocumentChange(ctx context.Context, entity *model.Entity, timestamp int64) error
	HandleCursorMove(ctx context.Context, cursorMoveFilePath string, cursorMoveLine int32) error
	RetrieveRelevantContextFromInteraction(ctx context.Context, cli knowledgebase.Client, processedQuery *model.ProcessedQuery, request *protocol.RetrieveRelevantSnippetRequest, tccConfig *model.CodeKGTCCConfig, recallNum int) ([]*protocol.Snippet, error)
}

func NewRelationManager(project string, userID string, config *ckg_config.Config, getStorage func() data_storage.Storage, getEmbeddingStorage func() data_storage.EmbeddingStorage, version string) RelationManager {
	var relationManager RelationManager
	switch version {
	case "default":
		// default version is v1
		relationManager = manager_v1.NewManager(
			project,
			getStorage,
			getEmbeddingStorage,
		)
	case "v1":
		relationManager = manager_v1.NewManager(
			project,
			getStorage,
			getEmbeddingStorage,
		)
	case "v2":
		relationManager = manager_v2.NewManager(
			project,
			userID,
			config,
			getStorage,
			getEmbeddingStorage,
		)
	case "":
		relationManager = nil
	}

	if relationManager == nil {
		logs.CtxInfo(context.Background(), "RelationManager is nil, version: %s", version)
		return nil
	}
	return relationManager
}
