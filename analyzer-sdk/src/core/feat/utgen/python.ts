import {TextDocument, Range} from "vscode-languageserver-textdocument";
import {Logger} from "../../../utils/logger";
import {
  LanguageIdEnum,
  VarDeclNodeInfo,
} from "../../parse/share";
import {LSPParser} from "../../parse/lsp/parser";
import {getCacheFileAstTreeAllowCreate} from "../../cache/astCache";
import {TreeSitterDispatcher} from "../../parse/treesitter/dispatcher";
import {TreeSitterFunctionalityEnum} from "../../parse/treesitter/share";
import {SyntaxNode} from "web-tree-sitter";
import {getRootNode} from "../../../utils/tree";
import {getRelativePath} from "./utils";
import {TreeSitterParser} from "../../parse/treesitter/parser";
import {convertToPlainObject, FILE_URI_PREFIX} from "../../../utils/common";
import {getWorkspaceFolderPath} from "../../../utils/path";
import {PSIParser} from "../../parse/psi/parser";
import {FunctionalityExecutor} from "../../parse/functionality/executor";
import {FunctionalityEnum} from "../../parse/functionality/share";
import {Location} from "vscode-languageserver-types";

function getContextId(uri: string, startIndex: number, endIndex: number): string {
  return `${uri}:${startIndex}:${endIndex}`;
}

class NodeContext {
  id: string;
  uri: string;
  lineno: number;
  startIndex: number;
  endIndex: number;
  name: string;
  body: string;

  constructor(uri: string, lineno: number, startIndex: number, endIndex: number) {
    this.uri = uri;
    this.lineno = lineno;
    this.id = getContextId(uri, startIndex, endIndex);
  }

  withName(name: string): NodeContext {
    this.name = name;
    return this;
  }

  toJsonString(): string {
    return JSON.stringify(convertToPlainObject(this), null, 2);
  }
}

class FunctionNodeContext extends NodeContext {
  signature: string;

  constructor(uri: string, lineno: number, startIndex: number, endIndex: number, body: string) {
    super(uri, lineno, startIndex, endIndex);
    this.body = body;
  }

  withSignature(signature: string): FunctionNodeContext {
    this.signature = signature.trim();
    return this;
  }
}

class ClassNodeContext extends NodeContext {
  header: string;
  variables: Set<string>;
  functions: Map<string, FunctionNodeContext>;
  functionArray: FunctionNodeContext[];

  constructor(uri: string, lineno: number, startIndex: number, endIndex: number) {
    super(uri, lineno, startIndex, endIndex);
    this.variables = new Set<string>();
    this.functions = new Map<string, FunctionNodeContext>();
    this.functionArray = [];
  }

  withHeader(header: string): NodeContext {
    this.header = header.trim();
    return this;
  }

  addVariableContext(varString: string): ClassNodeContext {
    this.variables.add(varString);
    return this;
  }

  addFunctionContext(functionNodeContext: FunctionNodeContext): ClassNodeContext {
    this.functions.set(functionNodeContext.id, functionNodeContext);
    return this;
  }

  getFunctionArray(): ClassNodeContext {
    this.functionArray.push(...this.functions.values());
    this.functionArray.sort((a, b) => a.lineno - b.lineno);
    return this;
  }
}

class ModuleContext {
  uri: string;
  relPath: string;
  imports: Set<string>;
  variables: Set<string>;
  classes: Map<string, ClassNodeContext>;
  classArray: ClassNodeContext[];
  functions: Map<string, FunctionNodeContext>;
  functionArray: FunctionNodeContext[];

  constructor(uri: string) {
    this.uri = uri;
    this.imports = new Set<string>();
    this.variables = new Set<string>();
    this.classes = new Map<string, ClassNodeContext>();
    this.functions = new Map<string, FunctionNodeContext>();
    this.classArray = [];
    this.functionArray = [];
  }

  withImports(imports: string[]): ModuleContext {
    imports.forEach(item => this.imports.add(item));
    return this;
  }

  withRelPath(relPath: string): ModuleContext {
    this.relPath = relPath;
    return this;
  }

  addVariableContext(varString: string): ModuleContext {
    this.variables.add(varString);
    return this;
  }

  addFunctionContext(functionNodeContext: FunctionNodeContext): ModuleContext {
    this.functions.set(functionNodeContext.id, functionNodeContext);
    return this;
  }

  addClassContext(classNodeContext: ClassNodeContext): ModuleContext {
    if (!this.classes.has(classNodeContext.id)) {
      this.classes.set(classNodeContext.id, classNodeContext);
    }
    return this;
  }

  getClassContext(classContextId: string): ClassNodeContext {
    if (this.classes.has(classContextId)) {
      return this.classes.get(classContextId);
    }
    return null;
  }

  toJsonString(): string {
    return JSON.stringify(convertToPlainObject(this), null, 2);
  }

  getFunctionArray(): ModuleContext {
    this.functionArray.push(...this.functions.values());
    this.functionArray.sort((a, b) => a.lineno - b.lineno);
    return this;
  }

  getClassArray(): ModuleContext {
    this.classArray.push(...this.classes.values());
    this.classArray.sort((a, b) => a.lineno - b.lineno);
    return this;
  }
}

class Context {
  funcName: string;
  focalFunc: string;
  funcBody: string;
  startIndex: number;
  endIndex: number;
  lineno: number;
  uri: string;
  relPath: string;
  containingClassId: string;
  containingClassName: string;
  modules: Map<string, ModuleContext>;
  moduleArray: ModuleContext[];

  errMsg: string;

  constructor() {
    this.modules = new Map<string, ModuleContext>();
    this.moduleArray = [];
  }

  withError(errMsg: string): Context {
    this.errMsg = errMsg;
    return this;
  }

  withInfo(funcName: string, focalFunc: string, funcBody: string): Context {
    this.funcName = funcName;
    this.focalFunc = focalFunc;
    this.funcBody = funcBody;
    return this;
  }

  withUri(uri: string): Context {
    this.uri = uri;
    return this;
  }

  withRelPath(relPath: string): Context {
    this.relPath = relPath;
    return this;
  }

  withLinenoAndIndex(lineno: number, startIndex: number, endIndex: number): Context {
    this.lineno = lineno;
    this.startIndex = startIndex;
    this.endIndex = endIndex;
    return this;
  }

  withClassId(classId: string): Context {
    this.containingClassId = classId;
    return this;
  }

  withClassName(className: string): Context {
    this.containingClassName = className;
    return this;
  }

  addModuleContext(moduleContext: ModuleContext): Context {
    this.modules.set(moduleContext.uri, moduleContext);
    return this;
  }

  getModuleContext(moduleUri: string): ModuleContext {
    if (this.modules.has(moduleUri)) {
      return this.modules.get(moduleUri);
    }
    return null;
  }

  getModuleArray(): Context {
    this.moduleArray.push(...this.modules.values());
    return this;
  }

  deDup(): Context {
    if (this.uri && this.startIndex && this.endIndex) {
      const targetFuncId = getContextId(this.uri, this.startIndex, this.endIndex);
      const moduleContext = this.modules.get(this.uri);
      if (moduleContext) {
        if (!this.containingClassId) {
          moduleContext.functions.delete(targetFuncId);
        } else {
          for (const [_, classContextInfo] of moduleContext.classes) {
            if (classContextInfo.functions.has(targetFuncId)) {
              classContextInfo.functions.delete(targetFuncId);
            }
          }
        }
      }
    }
    return this;
  }

  toJsonString(): string {
    return JSON.stringify(convertToPlainObject(this), null, 2);
  }

  toPromptReadyJSON(): string {
    for (const [_, moduleContext] of this.modules) {
      let targetClassContext = null;
      for (const [__, classContext] of moduleContext.classes) {
        // A hacky way to handle the classContext does not contain any class variables or class methods
        if (classContext.functions.size === 0 && classContext.variables.size === 0) {
          classContext.variables.add("    pass");
        }
        classContext.getFunctionArray();
        classContext.functions = null;
        // If the target function is a class method
        if (classContext.id === this.containingClassId) {
          classContext.functionArray.push(new FunctionNodeContext(this.uri, this.lineno, this.startIndex, this.endIndex, this.funcBody));
          targetClassContext = classContext;
        }
      }
      // If the target function is a class method, we need to make the corresponding classContext to be the last one in the moduleContext
      if (targetClassContext) {
        moduleContext.classes.delete(this.containingClassId);
      }
      moduleContext.getClassArray();
      moduleContext.classes = null;
      if (targetClassContext) {
        moduleContext.classArray.push(targetClassContext);
      }
      moduleContext.getFunctionArray();
      moduleContext.functions = null;
    }
    // we need to make the moduleCotext of the target function to be the last one in the Context object.
    const targetModuleContext = this.modules.get(this.uri);
    this.modules.delete(this.uri);
    this.getModuleArray();
    this.modules = null;
    if (this.containingClassId === "") {
      targetModuleContext.functionArray.push(new FunctionNodeContext(this.uri, this.lineno, this.startIndex, this.endIndex, this.funcBody));
    }
    // Sort the ModuleContext to put the 3rd party module reference in the beginning
    this.moduleArray.sort((a, b) => a.relPath.localeCompare(b.relPath));
    this.moduleArray.push(targetModuleContext);
    return JSON.stringify(convertToPlainObject(this), null, 2);
  }
}

export async function doExtractUnitTestContextForPython(document: TextDocument, startIndex: number, endIndex: number): Promise<string> {
  Logger.info(`doExtractUnitTestContextForPython: '${document.uri}' (${startIndex}, ${endIndex})`);

  const result = new Context();

  const workspaceFolderPath = getWorkspaceFolderPath();

  if (document.languageId !== LanguageIdEnum.Python) {
    Logger.error(`doExtractUnitTestContextForPython: languageId should be '${LanguageIdEnum.Python}', got '${document.languageId}'`);
    return "{}";
  }

  if (!PSIParser.INSTANCE.initialized() && !LSPParser.INSTANCE.initialized()) {
    Logger.error(`doExtractUnitTestContextForPython: PSIParser and LSPParser are not initialized`);
    return "{}";
  }

  if (!workspaceFolderPath) {
    Logger.error(`doExtractUnitTestContextForPython: workspaceFolderPath is empty`);
    return "{}";
  }

  const ast = getCacheFileAstTreeAllowCreate(document);
  if (!ast) {
    Logger.error(`doExtractContextForPython: failed to parse Tree for document '${document.uri}'`);
    return "{}";
  }

  const selectRange = {start: document.positionAt(startIndex), end: document.positionAt(endIndex)};

  let funcDeclNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(LanguageIdEnum.Python, TreeSitterFunctionalityEnum.GetFuncParentNodeForRange, ast.rootNode, selectRange);
  if (!funcDeclNode) {
    Logger.error(`doExtractContextForPython: failed to get parent funcDeclNode for document '${document.uri}' with range [${startIndex}, ${endIndex}]`);
    funcDeclNode = getFirstFuncDeclNode(ast.rootNode, selectRange, startIndex, endIndex);
    if (!funcDeclNode) {
      Logger.error(`doExtractContextForPython: failed to get first child funcDeclNode for document '${document.uri}' with range [${startIndex}, ${endIndex}]`);
      return "{}";
    }
  }
  const [funcName, funcBody, focalFunc] = getFuncMetaInfo(funcDeclNode);
  result.withUri(document.uri);
  result.withRelPath(getRelativePath(document.uri, workspaceFolderPath));
  result.withLinenoAndIndex(funcDeclNode.startPosition.row, funcDeclNode.startIndex, funcDeclNode.endIndex);
  result.withInfo(funcName, focalFunc, funcBody);

  const targetModuleContext = getRefModuleContext(result, workspaceFolderPath, document.uri, funcDeclNode);
  const importResult: string[] = TreeSitterDispatcher.INSTANCE.dispatch(LanguageIdEnum.Python, TreeSitterFunctionalityEnum.GetImportFileOrModule, ast.rootNode);
  if (importResult) { // 这里的 importResult 是 import 语句的字符串，比如 'import os' or 'from xx import yy'
    targetModuleContext.withImports(importResult);
  }
  const parentClassNode = getContainingClassByNode(funcDeclNode);
  if (parentClassNode) {
    const targetClassContext = getRefClassContext(result, workspaceFolderPath, document.uri, parentClassNode);
    result.withClassId(targetClassContext.id);
    result.withClassName(parentClassNode.childForFieldName("name").text);
  } else {
    result.withClassId("");
  }
  let targetNode = funcDeclNode;
  const funcParentNode = getDecoratorNodeByNode(funcDeclNode);
  if (funcParentNode) {
    targetNode = funcParentNode;
  }
  const nodeModuleMap = new Map<string, SyntaxNode[]>([[document.uri, [targetNode]]]);
  await bfs(result, workspaceFolderPath, nodeModuleMap, 2);
  return result.deDup().toPromptReadyJSON();
}

async function bfs(context: Context, workspaceFolderPath: string, nodesMap: Map<string, SyntaxNode[]>, depth: number) {
  const queue: Map<string, SyntaxNode[]>[] = [nodesMap];
  const visitedSyntaxNode = new Set<string>();
  let currentDepth = 1;
  while (queue.length > 0 && currentDepth <= depth) {
    const queueSize = queue.length;
    for (let i = 0; i < queueSize; i++) {
      // Dequeue the first element from the queue
      const currentNodesMap: Map<string, SyntaxNode[]> = queue.shift()!;
      for (const [moduleUri, nodesInModule] of currentNodesMap) {
        for (const node of nodesInModule) {
          const nodeId = getContextId(moduleUri, node.startIndex, node.endIndex);
          if (!visitedSyntaxNode.has(nodeId)) {
            const nextLevelSyntaxNodesMap = await buildContextForFuncDefNode(context, workspaceFolderPath, moduleUri, node);
            // Enqueue the map<string, SyntaxNode[]>
            queue.push(nextLevelSyntaxNodesMap);
            visitedSyntaxNode.add(nodeId);
          }
        }
      }
    }
    currentDepth += 1;
  }
}

async function buildContextForFuncDefNode(context: Context, workspaceFolderPath: string, nodeUri: string, node: SyntaxNode): Promise<Map<string, SyntaxNode[]>> {
  const nextLevelSyntaxNode = new Map<string, SyntaxNode[]>();
  // 1) Find ALL identifiers appear in the function definition, if function has a decorated_definition, then ALL identifiers appear in the decorated_definition, e.g., function calls, constant variables, class instances, etc.
  const usedIdentifierNodes = getUsedIdentifierNodes(node);
  // 2) Find the identifier definitions through LSP and remove the possible definitions inside the target node body, e.g., expression_statement, inside function definition, etc.
  let usedDefNodesInfo = await getLSPDefVarNodeInfos(usedIdentifierNodes, nodeUri);
  usedDefNodesInfo = usedDefNodesInfo.filter(defNodeInfo => !isChildrenOfTarget(defNodeInfo, nodeUri, node));
  // 3) Loop through the found definitions and build Context accordingly, e.g., FunctionNodeContext, ClassNodeContext and ModuleContext
  for (const defNodeInfo of usedDefNodesInfo) {
    const defSyntaxNode = defNodeInfo.declNode;
    const defSyntaxNodeString = defSyntaxNode.text;
    const refModuleUri = defNodeInfo.uri;
    if (!nextLevelSyntaxNode.has(refModuleUri)) {
      nextLevelSyntaxNode.set(refModuleUri, []);
    }
    const refModuleContext = getRefModuleContext(context, workspaceFolderPath, refModuleUri, defSyntaxNode);
    if (defSyntaxNode.type === "class_definition") {
      const refClassContext = getRefClassContext(context, workspaceFolderPath, refModuleUri, defSyntaxNode);
      if (!refClassContext) {
        Logger.warn(`${defSyntaxNodeString} is referred as 'class_definition', but corresponding ClassNodeContext is null!`);
      } else {
        refModuleContext.addClassContext(refClassContext);
        nextLevelSyntaxNode.get(refModuleUri).push(...getInheritanceClassIdentifierNode(defSyntaxNode));
      }
    } else {
      const defSyntaxNodeParentClassNode = getContainingClassByNode(defSyntaxNode);
      if (defSyntaxNodeParentClassNode) {
        const refClassContext = getRefClassContext(context, workspaceFolderPath, refModuleUri, defSyntaxNodeParentClassNode);
        switch (defSyntaxNode.type) {
          case "assignment":
            if (!isFunctionScopeVar(defSyntaxNode)) {
              refClassContext.addVariableContext(getIndentedNodeText(defSyntaxNode));
              // we add the assignment node inside a class definition for next level processing(inside bfs() call)
              nextLevelSyntaxNode.get(refModuleUri).push(defSyntaxNode);
            } else {
              Logger.info(`${defSyntaxNodeString} is defined inside a class method`);
            }
            break;
          case "function_definition":
            const funcContext = getRefFunctionContext(refModuleUri, defSyntaxNode);
            refClassContext.addFunctionContext(funcContext);
            break;
        }
        // we only add the superclass identifiers for next level processing(inside bfs() call)
        nextLevelSyntaxNode.get(refModuleUri).push(...getInheritanceClassIdentifierNode(defSyntaxNodeParentClassNode));
      } else {
        switch (defSyntaxNode.type) {
          case "assignment":
            refModuleContext.addVariableContext(getIndentedNodeText(defSyntaxNode));
            // we add assignment node for next level processing(inside bfs() call)
            nextLevelSyntaxNode.get(refModuleUri).push(defSyntaxNode);
            break;
          case "function_definition":
            const funcContext = getRefFunctionContext(refModuleUri, defSyntaxNode);
            refModuleContext.addFunctionContext(funcContext);
            break;
        }
      }
    }
  }
  return nextLevelSyntaxNode;
}

function getRefModuleContext(context: Context, workspaceFolderPath: string, nodeUri: string, refSyntaxNode: SyntaxNode): ModuleContext {
  if (!context.getModuleContext(nodeUri)) {
    const newModuleContext = new ModuleContext(nodeUri);
    newModuleContext.withImports(TreeSitterDispatcher.INSTANCE.dispatch(LanguageIdEnum.Python, TreeSitterFunctionalityEnum.GetImportFileOrModule, getRootNode(refSyntaxNode)));
    if (nodeUri.startsWith(workspaceFolderPath)) {
      newModuleContext.withRelPath(getRelativePath(nodeUri, workspaceFolderPath));
    } else {
      newModuleContext.withRelPath(nodeUri.slice(FILE_URI_PREFIX.length));
    }
    context.addModuleContext(newModuleContext);
  }
  return context.getModuleContext(nodeUri);
}

function getRefClassContext(context: Context, workspaceFolderPath: string, nodeUri: string, refClassNode: SyntaxNode): ClassNodeContext {
  const refModule = getRefModuleContext(context, workspaceFolderPath, nodeUri, refClassNode);
  const classNodeContextId = getContextId(nodeUri, refClassNode.startIndex, refClassNode.endIndex);
  if (!refModule.getClassContext(classNodeContextId)) {
    const newClassNodeContext = new ClassNodeContext(nodeUri, refClassNode.startPosition.row, refClassNode.startIndex, refClassNode.endIndex);
    newClassNodeContext.withHeader(getClassHeaderString(refClassNode));
    // add constructor functions into the ClassNodeContext
    const constructorFunctions = getClassConstructorNode(refClassNode);
    constructorFunctions.forEach(constructorFunctionNode => {
      newClassNodeContext.addFunctionContext(getRefFunctionContext(nodeUri, constructorFunctionNode));
    });
    // add class variables into the ClassNodeContext
    const classVariables = getClassVariableNodes(refClassNode);
    classVariables.forEach(classVariableNode => {
      newClassNodeContext.addVariableContext(getIndentedNodeText(classVariableNode));
    });
    refModule.addClassContext(newClassNodeContext);
  }
  return refModule.getClassContext(classNodeContextId);
}

function getRefFunctionContext(nodeUri: string, node: SyntaxNode): FunctionNodeContext {
  const [funcName, funcBody, focalFunc] = getFuncMetaInfo(node);
  const funcContext = new FunctionNodeContext(nodeUri, node.startPosition.row, node.startIndex, node.endIndex, funcBody);
  funcContext.withSignature(focalFunc);
  funcContext.withName(funcName);
  return funcContext;
}

async function getLSPDefVarNodeInfos(nodes: SyntaxNode[], uri: string): Promise<VarDeclNodeInfo[]> {
  const result: VarDeclNodeInfo[] = [];
  const visitedNodes = new Set<string>();
  for (const node of nodes) {
    const defNodeInfos: VarDeclNodeInfo[] = await FunctionalityExecutor.INSTANCE.execute(
      FunctionalityEnum.GetVarDeclaration,
      LanguageIdEnum.Python,
      uri,
      node,
      function (location: Location): boolean {
        return location.uri.startsWith(getWorkspaceFolderPath()) || location.uri.includes("site-packages");
      },
    );

    if (defNodeInfos && defNodeInfos.length === 1) {
      const nodeId = getContextId(defNodeInfos[0].uri, defNodeInfos[0].declNode.startIndex, defNodeInfos[0].declNode.endIndex);
      // defNodeInfos deduplication
      if (visitedNodes.has(nodeId)) {
        continue;
      }
      switch (defNodeInfos[0].declNode.type) {
        case "assignment":
          if (defNodeInfos[0].declNode.text.includes("=")) {
            result.push(defNodeInfos[0]);
          }
          break;
        case "class_definition":
          result.push(defNodeInfos[0]);
          break;
        case "function_definition":
          result.push(defNodeInfos[0]);
          break;
        default:
          Logger.info(`doExtractContextForPython: unhandled identifier type: ${defNodeInfos[0].declNode.type}`);
      }
      visitedNodes.add(nodeId);
    }
  }

  return result;
}

export function getFuncMetaInfo(node: SyntaxNode): [string, string, string] {
  let funcName = "", funcBody = "", focalFunc = "";
  const queryStr = `
[
  (decorated_definition
  (decorator) (function_definition)) @decorated_definition
]`;
  if (node.type === "function_definition") {
    funcName = node.childForFieldName("name").text;
    const funcBodyNode = node.childForFieldName("body");
    funcBody = getIndentedNodeText(node);
    focalFunc = node.text.slice(0, funcBodyNode.startIndex - node.startIndex).trim();
    // check if current function_definition has decorators
    const decoratorNode: SyntaxNode = getDecoratorNodeByNode(node);
    if (decoratorNode) {
      const captureResult = TreeSitterParser.INSTANCE.queryAndCaptureForNode(
        LanguageIdEnum.Python,
        queryStr,
        decoratorNode,
      );
      if (captureResult && captureResult.length === 1) {
        funcBody = getIndentedNodeText(captureResult[0].node);
        focalFunc = captureResult[0].node.text.slice(0, funcBodyNode.startIndex - captureResult[0].node.startIndex).trim();
      }
    }
  }

  return [funcName, funcBody, focalFunc];
}

function getClassVariableNodes(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];
  const queryStr = `
(class_definition
  body:
    (block
      (expression_statement 
        (assignment) @assignment
      )
    )
)
`;
  const matchedResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(
    LanguageIdEnum.Python,
    queryStr,
    node,
  );
  matchedResult.forEach(match => {
    result.push(...match.matches.get("assignment"));
  });
  return result;
}

export function getClassConstructorNode(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];
  const funcQueryStr = `
(class_definition
  body: (block
    (function_definition
      name: (identifier) @function_name
      (#match? @function_name "^(__init__|__new__|__post_init__)$")
    ) @function_definition
  )
)
`;
  const matchedResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(
    LanguageIdEnum.Python,
    funcQueryStr,
    node,
  );
  matchedResult.forEach(match => {
    result.push(...match.matches.get("function_definition"));
  });
  return result;
}

export function getUsedIdentifierNodes(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];
  const queryStr = `
  [
    ((identifier) @identifier
    (#not-any-of? @identifier "self" "cls"))
  ]
`;
  const captureResult = TreeSitterParser.INSTANCE.queryAndCaptureForNode(
    LanguageIdEnum.Python,
    queryStr,
    node,
  );
  captureResult.forEach(capture => {
    result.push(capture.node);
  });
  return result;
}

export function getClassHeaderString(node: SyntaxNode): string {
  if (node.type === "class_definition") {
    const bodyNode = node.childForFieldName("body");
    if (!bodyNode) {
      return null;
    }
    const decoratorNode: SyntaxNode = getDecoratorNodeByNode(node);
    if (decoratorNode) {
      return decoratorNode.text.slice(0, bodyNode.startIndex - decoratorNode.startIndex);
    }
    return node.text.slice(0, bodyNode.startIndex - node.startIndex);
  }

  return null;
}

export function getContainingClassByNode(targetNode: SyntaxNode): SyntaxNode {
  return TreeSitterDispatcher.INSTANCE.dispatch(LanguageIdEnum.Python, TreeSitterFunctionalityEnum.GetTypeParentNodeForNode, targetNode);
}

export function getDecoratorNodeByNode(targetNode: SyntaxNode): SyntaxNode {
  return TreeSitterDispatcher.INSTANCE.dispatch(LanguageIdEnum.Python, TreeSitterFunctionalityEnum.GetParentNodeWithTypeListForNode, targetNode, ["decorated_definition"]);
}

export function getInheritanceClassIdentifierNode(node: SyntaxNode): SyntaxNode[] {
  const result: SyntaxNode[] = [];
  const queryStr = `
  [
    (class_definition
      name: (identifier)  
      superclasses: (argument_list (_ (identifier) @identifier))
    )
    (class_definition
      name: (identifier)  
      superclasses: (argument_list (_) @identifier)
    )
  ]
  `;
  const matchedResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(
    LanguageIdEnum.Python,
    queryStr,
    node,
  );
  matchedResult.forEach(match => {
    result.push(...match.matches.get("identifier"));
  });
  return result;
}

function isFunctionScopeVar(targetNode: SyntaxNode): boolean {
  const functionDefNode = TreeSitterDispatcher.INSTANCE.dispatch(LanguageIdEnum.Python, TreeSitterFunctionalityEnum.GetFuncParentNodeForNode, targetNode);
  return !!functionDefNode;

}

function isChildrenOfTarget(defNodeInfo: VarDeclNodeInfo, uri: string, targetNode: SyntaxNode): boolean {
  if (defNodeInfo.uri !== uri) {
    return false;
  }
  return defNodeInfo.declNode.startIndex >= targetNode.startIndex && defNodeInfo.declNode.endIndex <= targetNode.endIndex;
}

export function getIndentedNodeText(node: SyntaxNode): string {
  if (!node) {
    return "";
  }
  if (!node.parent || node.startPosition.column === 0) {
    return node.text;
  }
  let ancestorNode = node.parent;
  while (ancestorNode && ancestorNode.startPosition.row === node.startPosition.row) {
    ancestorNode = ancestorNode.parent;
  }
  const candidateText = ancestorNode.text.slice(0, node.startIndex - ancestorNode.startIndex);
  const padding = candidateText.slice(candidateText.lastIndexOf("\n") + 1);
  return !!padding ? padding + node.text : node.text;
}

function getFirstFuncDeclNode(node: SyntaxNode, range: Range, startIndex: number, endIndex: number): SyntaxNode | null {
  const parentNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(LanguageIdEnum.Python, TreeSitterFunctionalityEnum.GetParentNodeForKeywordSearch, node, range);
  const result: SyntaxNode[] = [];
  const queryStr = `
     definition: (function_definition) @func_definition
  `;
  const captureResult = TreeSitterParser.INSTANCE.queryAndCaptureForNode(
    LanguageIdEnum.Python,
    queryStr,
    parentNode,
  );
  captureResult.forEach(capture => {
    if (capture.node.endIndex >= startIndex && capture.node.endIndex <= endIndex || capture.node.startIndex >= startIndex && capture.node.startIndex <= endIndex) {
      result.push(capture.node);
    }
  });
  return result.length > 0 ? result[0] : null;
}