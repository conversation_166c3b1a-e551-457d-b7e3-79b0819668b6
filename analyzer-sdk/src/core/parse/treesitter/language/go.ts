import {Point, SyntaxNode, Tree} from "web-tree-sitter";
import {TreeSitterParser} from "../parser";
import {DefNodeInfo, DefNodeKindEnum, LanguageIdEnum} from "../../share";
import {Range} from "vscode-languageserver-textdocument";
import {TreeSitter<PERSON>andler} from "./common";
import {DocumentUri} from "vscode-languageserver-types";
import {getTreeAndDocumentFromUri} from "../../../cache/astCache";
import {ResolvedTypeInfo} from "../../../share/common";

export class GoTreeSitterHandler extends TreeSitterHandler {
  static INSTANCE = new GoTreeSitterHandler();

  languageId = LanguageIdEnum.Go;

  funcTypes = ["function_declaration", "method_declaration", "method_spec", "func_literal"];
  typeTypes = ["type_declaration"];
  fileTypes = ["source_file"];
  varTypes = ["var_spec", "const_spec", "parameter_declaration", "field_declaration", "short_var_declaration"];

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
    super();
  }

  getDeclFunctionInFile(uri: DocumentUri, tree: Tree): DefNodeInfo[] {
    const result: DefNodeInfo[] = [];

    const queryStr = `
[
  (function_declaration
    name: (identifier) @symbol_name
  )
  (method_declaration
    name: (field_identifier) @symbol_name
  )
] @func_decl
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, tree.rootNode);

    for (const match of matchResult) {
      const declNode = match.matches.get("func_decl");
      const nameNode = match.matches.get("symbol_name");

      if (declNode.length === 1 && nameNode.length === 1) {
        const defNodeInfo: DefNodeInfo = {
          languageId: this.languageId,
          uri,
          kind: DefNodeKindEnum.Function,
          name: nameNode[0].text,
          defNode: declNode[0],
          subDefNodeInfo: null,
        };

        result.push(defNodeInfo);
      }
    }

    return result;
  }

  getInvokeInFile(node: SyntaxNode, startPosition?: Point, endPosition?: Point): SyntaxNode[] {
    const result: SyntaxNode[] = [];

    const queryStr = `
(call_expression
  [
    (identifier) @symbol_name
    (selector_expression
      (field_identifier) @symbol_name
    )
  ]
)
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, node, startPosition, endPosition);

    for (const match of matchResult) {
      const nameNode = match.matches.get("symbol_name");

      if (nameNode.length === 1 && nameNode[0].startPosition) {
        result.push(nameNode[0]);
      }
    }

    return result;
  }

  getTypeInFile(node: SyntaxNode, startPosition?: Point, endPosition?: Point): SyntaxNode[] {
    const result: SyntaxNode[] = [];

    const queryStr = `
(type_identifier) @symbol_name
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, node, startPosition, endPosition);

    for (const match of matchResult) {
      const nameNode = match.matches.get("symbol_name");

      if (nameNode.length === 1) {
        result.push(nameNode[0]);
      }
    }

    return result;
  }

  getDeclVariableInFile(node: SyntaxNode, startPosition?: Point, endPosition?: Point): SyntaxNode[] {
    const result: SyntaxNode[] = [];

    const queryStr = `
[
  (var_spec
    name: (identifier) @symbol_name
  )
  (const_spec
    name: (identifier) @symbol_name
  )
  (parameter_declaration
    name: (identifier) @symbol_name
  )
  (field_declaration
    name: (field_identifier) @symbol_name
  )
  (short_var_declaration
    left: (expression_list
      (identifier) @symbol_name
    )
  )
]
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, node, startPosition, endPosition);

    for (const match of matchResult) {
      const nameNode = match.matches.get("symbol_name");

      if (nameNode.length === 1) {
        result.push(nameNode[0]);
      }
    }

    return result;
  }

  getUsageVariableInFile(node: SyntaxNode, startPosition?: Point, endPosition?: Point): SyntaxNode[] {
    const result: SyntaxNode[] = [];

    const invokeNodeIdInFile = this.getInvokeInFile(node, startPosition, endPosition).map(node => node.id);
    const declVarNodeIdInFile = this.getDeclVariableInFile(node, startPosition, endPosition).map(node => node.id);

    const pkgIds: string[] = [];

    const pkgIdQueryStr = `
(package_identifier) @pkg_id
`;

    const pkgIdMatchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, pkgIdQueryStr, node, startPosition, endPosition);

    for (const pkgIdMatch of pkgIdMatchResult) {
      const pkgIdNode = pkgIdMatch.matches.get("pkg_id");

      if (pkgIdNode.length === 1) {
        pkgIds.push(pkgIdNode[0].text);
      }
    }

    const queryStr = `
[
  (identifier) @symbol_name
  (field_identifier) @symbol_name
]
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, node, startPosition, endPosition);

    for (const match of matchResult) {
      const nameNode = match.matches.get("symbol_name");

      if (nameNode.length === 1
        && !invokeNodeIdInFile.includes(nameNode[0].id)
        && nameNode[0].parent?.type !== "function_declaration"
        && !declVarNodeIdInFile.includes(nameNode[0].id)
        && !pkgIds.includes(nameNode[0].text)
      ) {
        result.push(nameNode[0]);
      }
    }

    return result;
  }

  getVarTypeNameNode(node: SyntaxNode, varName: string): { node: SyntaxNode, typeString: string, index: number } {
    switch (node.type) {
      case "var_spec":
      case "parameter_declaration":
      case "field_declaration":
        return {node: this.getTypeNameNodeHelper(node.childForFieldName("type")), typeString: null, index: -1};
      case "const_spec":
        const nodeFromType = this.getTypeNameNodeHelper(node.childForFieldName("type"));
        if (nodeFromType) {
          return {node: nodeFromType, typeString: null, index: -1};
        }
        return this.getTypeFromValueHelper(node.childForFieldName("value")?.namedChild(0));
      case "short_var_declaration":
        return this.getShortVarDeclVarTypeNameNode(node, varName);
    }

    return {node: null, typeString: null, index: -1};
  }

  private getTypeFromValueHelper(node: SyntaxNode): { node: SyntaxNode, typeString: string, index: number } {
    if (node) {
      switch (node.type) {
        case "interpreted_string_literal":
          return {node: null, typeString: "string", index: -1};
        case "float_literal":
          return {node: null, typeString: "float", index: -1};
        case "int_literal":
          return {node: null, typeString: "int", index: -1};
        case "unary_expression":
          return this.getTypeFromValueHelper(node.childForFieldName("operand"));
        default:
          const typeNameNode = this.getTypeNameNodeHelper(node);
          return {node: typeNameNode, typeString: null, index: -1};
      }
    }

    return {node: null, typeString: null, index: -1};
  }

  private getShortVarDeclVarTypeNameNode(node: SyntaxNode, varName: string): {
    node: SyntaxNode,
    typeString: string,
    index: number
  } {
    const leftNodes = node.childForFieldName("left").namedChildren;
    const rightNodes = node.childForFieldName("right").namedChildren;

    let leftIndex = -1;
    for (let i = 0; i < leftNodes.length; i++) {
      if (varName === leftNodes[i].text) {
        leftIndex = i;
        break;
      }
    }

    if (leftIndex !== -1) {
      if (rightNodes.length === 1 && rightNodes[0].type === "call_expression") {
        const functionNode = rightNodes[0].childForFieldName("function");
        if (functionNode) {
          if (functionNode.type === "identifier") {
            if (functionNode.text === "new") {
              const argTypeNode = rightNodes[0].childForFieldName("arguments")?.namedChild(0);
              if (argTypeNode) {
                if (argTypeNode.type === "type_identifier") {
                  return {node: argTypeNode, typeString: null, index: -1};
                } else if (argTypeNode.type === "qualified_type") {
                  return {node: argTypeNode.childForFieldName("name"), typeString: null, index: -1};
                }
              }
            } else if (functionNode.text === "make") {
              const argTypeNode = rightNodes[0].childForFieldName("arguments")?.namedChild(0);
              if (argTypeNode) {
                return {node: this.getTypeNameNodeHelper(argTypeNode), typeString: null, index: -1};
              }
            } else {
              return {node: functionNode, typeString: null, index: leftIndex};
            }
          } else if (functionNode.type === "selector_expression") {
            const fieldNode = functionNode.childForFieldName("field");
            if (fieldNode) {
              return {node: fieldNode, typeString: null, index: leftIndex};
            }
          }
        }
      } else if (leftNodes.length === rightNodes.length) {
        return {node: this.getTypeNameNodeHelper(rightNodes[leftIndex]), typeString: null, index: -1};
      }
    }

    return {node: null, typeString: null, index: -1};
  }

  getParentNodeForKeywordSearch(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, [
      ...this.funcTypes,
      ...this.typeTypes,
      ...this.fileTypes,
    ]);
  }

  getFuncParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.funcTypes);
  }

  getFuncParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.funcTypes);
  }

  getTypeParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.typeTypes);
  }

  getTypeParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.typeTypes);
  }

  getVarParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.varTypes);
  }

  getVarParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.varTypes);
  }

  getMethodInRange(rootNode: SyntaxNode, range: Range, startIndex: number, endIndex: number, completelyIncluded: boolean): SyntaxNode[] {
    return this.getElementInRangeWithTypeList(rootNode, range, startIndex, endIndex, completelyIncluded, this.funcTypes);
  }

  getReturnTypeNodes(node: SyntaxNode): SyntaxNode[] {
    const result: SyntaxNode[] = [];

    if (this.funcTypes.includes(node.type)) {
      const resultNode = node.childForFieldName("result");
      if (resultNode) {
        if (resultNode.type === "parameter_list") {
          for (const child of resultNode.namedChildren) {
            result.push(this.getTypeNameNodeHelper(child.childForFieldName("type")));
          }
        } else {
          result.push(this.getTypeNameNodeHelper(resultNode));
        }
      }
    }

    return result;
  }

  getSubTypeNodes(node: SyntaxNode): SyntaxNode[] {
    if (node) {
      switch (node.type) {
        case "pointer_type":
          return [this.getTypeNameNodeHelper(node.namedChild(0))];
        case "slice_type":
          return [this.getTypeNameNodeHelper(node.childForFieldName("element"))];
        case "map_type":
          return [this.getTypeNameNodeHelper(node.childForFieldName("key")), this.getTypeNameNodeHelper(node.childForFieldName("value"))];
      }
    }

    return [];
  }

  getTypeFullString(node: SyntaxNode): string {
    if (node.parent && node.parent.type === "qualified_type") {
      return node.parent.text;
    } else {
      return node.text;
    }
  }

  getVarNameNodeFromVarDeclNode(node: SyntaxNode): SyntaxNode {
    switch (node.type) {
      case "var_spec":
      case "const_spec":
      case "parameter_declaration":
      case "field_declaration":
        return node.childForFieldName("name");
      case "short_var_declaration":
        // TODO: handle the case of short var declaration
        return null;
      default:
        return null;
    }
  }

  override getFuncSignatureString(node: SyntaxNode): string {
    if (node.type === "function_declaration" || node.type === "method_declaration") {
      return super.getFuncSignatureString(node);
    } else {
      return node.text;
    }
  }

  override genResolvedTypeInfoFromDefNode(defNodeInfo: DefNodeInfo): ResolvedTypeInfo {
    const result = super.genResolvedTypeInfoFromDefNode(defNodeInfo);

    if (defNodeInfo.kind === DefNodeKindEnum.Struct) {
      const structMethodContent = this.genStructMethodSignatureContent(defNodeInfo);
      if (structMethodContent !== "") {
        result.addOptionalContent(ResolvedTypeInfo.OPTIONAL_CONTENT_METHODS_IN_CLASS, structMethodContent);
      }
    }

    return result;
  }

  getImportInfo(rootNode: SyntaxNode): any {
    const importSpecNodes: SyntaxNode[] = [];

    for (const rootChild of rootNode.namedChildren) {
      if (rootChild.type === "import_declaration") {
        for (const importChild of rootChild.namedChildren) {
          if (importChild.type === "import_spec") {
            importSpecNodes.push(importChild);
          }
          if (importChild.type === "import_spec_list") {
            for (const importListChild of importChild.namedChildren) {
              if (importListChild.type === "import_spec") {
                importSpecNodes.push(importListChild);
              }
            }
          }
        }
      }
    }

    const result = new Map<string, string>();

    for (const specNode of importSpecNodes) {
      const name = specNode.childForFieldName("name")?.text ?? null;
      const path = specNode.childForFieldName("path")?.text.replace(/^"|"$/g, "") ?? null;

      if (path) {
        if (name) {
          result.set(name, path);
        } else {
          const pathSplit = path.split("/");
          if (pathSplit.length > 0) {
            result.set(pathSplit[pathSplit.length - 1], path);
          }
        }
      }
    }

    return result;
  }

  private getTypeNameNodeHelper(node: SyntaxNode): SyntaxNode {
    if (node) {
      // TODO: handle the case of generic type
      switch (node.type) {
        case "type_identifier":
        case "slice_type":
        case "map_type":
          return node;
        case "pointer_type":
          return this.getTypeNameNodeHelper(node.namedChild(0));
        case "qualified_type":
          return this.getTypeNameNodeHelper(node.childForFieldName("name"));
        case "composite_literal":
          return this.getTypeNameNodeHelper(node.childForFieldName("type"));
        case "unary_expression":
          return this.getTypeNameNodeHelper(node.childForFieldName("operand"));
      }
    }

    return null;
  }

  private genStructMethodSignatureContent(defNodeInfo: DefNodeInfo): string {
    const result: string[] = [];

    const tree = getTreeAndDocumentFromUri(defNodeInfo.uri, this.languageId).tree;
    if (!tree) {
      return result.join("\n");
    }

    const queryStr = `
(source_file
  (method_declaration) @method_decl
)
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, tree.rootNode);
    for (const match of matchResult) {
      const declNode = match.matches.get("method_decl");

      if (declNode.length === 1) {
        const receiverTypeNode = declNode[0].childForFieldName("receiver")?.namedChild(0)?.childForFieldName("type");
        if (receiverTypeNode) {
          let typeName = "";
          if (receiverTypeNode.type === "type_identifier") {
            typeName = receiverTypeNode.text;
          } else if (receiverTypeNode.type === "pointer_type") {
            typeName = receiverTypeNode.namedChild(0)?.text ?? "";
          }

          if (typeName === defNodeInfo.name) {
            result.push(this.getFuncSignatureString(declNode[0]));
          }
        }
      }
    }

    return result.join("\n");
  }
}