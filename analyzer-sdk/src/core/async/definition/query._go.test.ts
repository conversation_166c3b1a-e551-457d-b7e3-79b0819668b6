import {beforeAll, describe, expect, it, vi} from "vitest";
import {createDocument, wait} from "../../../utils/common";
import {DefNodeKindEnum, LanguageIdEnum} from "../../parse/share";
import {TreeSitterParser} from "../../parse/treesitter/parser";
import {PSIParser} from "../../parse/psi/parser";
import type {DocumentUri, Position} from "vscode-languageserver-types";
import * as astCache from "../../cache/astCache";
import * as utilsPath from "../../../utils/path";
import {queryDefinitionForPosition} from "./query";
import {ResolvedTypeInfo} from "../../share/common";
import {doEditorSelectionListener, doFileActiveListener, doFileEditListener} from "../../common/listener";
import {completeUpdateCachedTree} from "../../cache/astCache";
import {FeatureGateManager, FeatureName, getConfig} from "../../common/config";
import {setEnableLocalLogging} from "../../../utils/logger";

describe("test_queryDefinitionForPosition", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
    FeatureGateManager.INSTANCE.openFeature(FeatureName.ENABLE_AST_CACHE_ANALYSIS)
    FeatureGateManager.INSTANCE.openFeature(FeatureName.ENABLE_DEFINITION_ANALYSIS)
    FeatureGateManager.INSTANCE.openFeature(FeatureName.ENABLE_GO_DEFINITION_ANALYSIS)
    setEnableLocalLogging(true)
  });

  it("case1_after_complete_update", async () => {
    const docText = `
type T struct {
}
func f(t *T) {
}
`;

    const document = createDocument(docText, LanguageIdEnum.Go, "file:///test1.go");
    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);

    const workspacePathMock = vi.spyOn(utilsPath, "getWorkspaceFolderPath").mockImplementation(() => {
      return "file:///";
    });
    const psiIns = PSIParser.INSTANCE
    const psiInitMock = vi.spyOn(psiIns, "initialized").mockImplementation(() => {
      return true;
    });
    const psiExecMock = vi.spyOn(psiIns, "execute").mockImplementation(async (command: string, _languageId: string, uri: DocumentUri, position: Position) => {
      if (command === "jetbrains.getDefinition") {
        if (uri.toString() === "file:///test1.go") {
          if (position.line === 3 && position.character === 10) {
            return [{
              uri: "file:///test1.go",
              range: {start: {line: 1, character: 5}, end: {line: 1, character: 6}},
            }];
          }
        }
      }
      return [];
    });
    const getTreeDocMock = vi.spyOn(astCache, "getTreeAndDocumentFromUri").mockImplementation(() => {
      return {tree, document};
    });

    await doFileActiveListener(document);
    await wait(50);

    const result = queryDefinitionForPosition(document, {line: 3, character: 10});

    expect(result).toEqual({
      symbolName: "T",
      definitionKey: "struct@file:///test1.go@T",
      typeInfo: new ResolvedTypeInfo("file:///test1.go", 1, "T", DefNodeKindEnum.Struct, "type T struct {\n}", null),
    });

    workspacePathMock.mockRestore();
    psiInitMock.mockRestore();
    psiExecMock.mockRestore();
    getTreeDocMock.mockRestore();
  });

  it("case2_after_incremental_updated", async () => {
    const docText = `
type TTT1TTT struct {
}
type TTT2TTT struct {
}
func f(t *TTT1TTT) {
  t.f()
}
`;

    const document = createDocument(docText, LanguageIdEnum.Go, "file:///test2.go");
    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);

    const workspacePathMock = vi.spyOn(utilsPath, "getWorkspaceFolderPath").mockImplementation(() => {
      return "file:///";
    });
    const psiIns = PSIParser.INSTANCE
    const psiInitMock = vi.spyOn(psiIns, "initialized").mockImplementation(() => {
      return true;
    });
    const psiExecMock1 = vi.spyOn(psiIns, "execute").mockImplementation(async (command: string, _languageId: string, uri: DocumentUri, position: Position) => {
      if (command === "jetbrains.getDefinition") {
        if (uri.toString() === "file:///test2.go") {
          if (position.line === 5 && position.character === 10) {
            return [{
              uri: "file:///test2.go",
              range: {start: {line: 1, character: 5}, end: {line: 1, character: 12}},
            }];
          }
          if (position.line === 6 && position.character === 2) {
            return [{
              uri: "file:///test2.go",
              range: {start: {line: 5, character: 7}, end: {line: 5, character: 8}},
            }];
          }
        }
      }
      if (command === "jetbrains.getUsage") {
        if (uri.toString() === "file:///test2.go") {
          if (position.line === 5 && position.character === 7) {
            return [{
              uri: "file:///test2.go",
              range: {start: {line: 6, character: 2}, end: {line: 6, character: 3}},
            }];
          }
        }
      }
      return [];
    });
    const getTreeDocMock1 = vi.spyOn(astCache, "getTreeAndDocumentFromUri").mockImplementation(() => {
      return {tree, document};
    });

    await doFileActiveListener(document);
    await wait(50);

    psiExecMock1.mockRestore();
    getTreeDocMock1.mockRestore();

    const docText2 = `
type TTT1TTT struct {
}
type TTT2TTT struct {
}
func f(t *TTT2TTT) {
  t.f()
}
`;

    const document2 = createDocument(docText2, LanguageIdEnum.Go, "file:///test2.go");
    const tree2 = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText2);

    const psiExecMock2 = vi.spyOn(psiIns, "execute").mockImplementation(async (command: string, _languageId: string, uri: DocumentUri, position: Position) => {
      if (command === "jetbrains.getDefinition") {
        if (uri.toString() === "file:///test2.go") {
          if (position.line === 5 && position.character === 10) {
            return [{
              uri: "file:///test2.go",
              range: {start: {line: 3, character: 5}, end: {line: 3, character: 12}},
            }];
          }
          if (position.line === 6 && position.character === 2) {
            return [{
              uri: "file:///test2.go",
              range: {start: {line: 5, character: 7}, end: {line: 5, character: 8}},
            }];
          }
        }
      }
      if (command === "jetbrains.getUsage") {
        if (uri.toString() === "file:///test2.go") {
          if (position.line === 5 && position.character === 7) {
            return [{
              uri: "file:///test2.go",
              range: {start: {line: 6, character: 2}, end: {line: 6, character: 3}},
            }];
          }
        }
      }
      return [];
    });
    const getTreeDocMock2 = vi.spyOn(astCache, "getTreeAndDocumentFromUri").mockImplementation(() => {
      return {tree: tree2, document: document2};
    });


    await doFileEditListener({
      document: document2,
      contentChanges: [{
        position: {line: 5, character: 13},
        removeLength: 1,
        text: "2",
      }],
    });
    await wait(50);

    const result1 = queryDefinitionForPosition(document2, {line: 5, character: 10});

    expect(result1).toEqual({
      symbolName: "TTT2TTT",
      definitionKey: "struct@file:///test2.go@TTT2TTT",
      typeInfo: new ResolvedTypeInfo("file:///test2.go", 25, "TTT2TTT", DefNodeKindEnum.Struct, "type TTT2TTT struct {\n}", null),
    });

    const result2 = queryDefinitionForPosition(document2, {line: 5, character: 7});

    expect(result2).toEqual({
      symbolName: "t",
      definitionKey: "struct@file:///test2.go@TTT2TTT",
      typeInfo: new ResolvedTypeInfo("file:///test2.go", 25, "TTT2TTT", DefNodeKindEnum.Struct, "type TTT2TTT struct {\n}", null),
    });

    const result3 = queryDefinitionForPosition(document2, {line: 6, character: 2});

    expect(result3).toEqual({
      symbolName: "t",
      definitionKey: "struct@file:///test2.go@TTT2TTT",
      typeInfo: new ResolvedTypeInfo("file:///test2.go", 25, "TTT2TTT", DefNodeKindEnum.Struct, "type TTT2TTT struct {\n}", null),
    })

    workspacePathMock.mockRestore();
    psiInitMock.mockRestore();
    psiExecMock2.mockRestore();
    getTreeDocMock2.mockRestore();
  });

  it("case3_after_partial_updated", async () => {
    const docText = `
type T struct {
}
func f(t *T) {
}
`;

    const document = createDocument(docText, LanguageIdEnum.Go, "file:///test3.go");
    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);

    const workspacePathMock = vi.spyOn(utilsPath, "getWorkspaceFolderPath").mockImplementation(() => {
      return "file:///";
    });
    const psiIns = PSIParser.INSTANCE
    const psiInitMock = vi.spyOn(psiIns, "initialized").mockImplementation(() => {
      return true;
    });
    const psiExecMock = vi.spyOn(psiIns, "execute").mockImplementation(async (command: string, _languageId: string, uri: DocumentUri, position: Position) => {
      if (command === "jetbrains.getDefinition") {
        if (uri.toString() === "file:///test3.go") {
          if (position.line === 3 && position.character === 10) {
            return [{
              uri: "file:///test3.go",
              range: {start: {line: 1, character: 5}, end: {line: 1, character: 6}},
            }];
          }
        }
      }
      return [];
    });
    const getTreeDocMock = vi.spyOn(astCache, "getTreeAndDocumentFromUri").mockImplementation(() => {
      return {tree, document};
    });

    await completeUpdateCachedTree(document)
    await doEditorSelectionListener({
      document,
      selectionRange: {
        start: {line: 5, character: 0},
        end: {line: 5, character: 0},
      },
    });
    await wait(getConfig("analysis.definition.partialAnalysisDebounce") + 20);

    const result = queryDefinitionForPosition(document, {line: 3, character: 10});

    expect(result).toEqual({
      symbolName: "T",
      definitionKey: "struct@file:///test3.go@T",
      typeInfo: new ResolvedTypeInfo("file:///test3.go", 1, "T", DefNodeKindEnum.Struct, "type T struct {\n}", null),
    });

    workspacePathMock.mockRestore();
    psiInitMock.mockRestore();
    psiExecMock.mockRestore();
    getTreeDocMock.mockRestore();
  });

  it("case4_after_incremental_updated_multi_change", async () => {
    const docText = `
type T1 struct {
}
type T2 struct {
}
func f(t1 *T1, t2 *T2) {
  t1.f1()
  t2.f2()
  t1.f1()
  t2.f2()
}
`;

    const document = createDocument(docText, LanguageIdEnum.Go, "file:///test4.go");
    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText);

    const workspacePathMock = vi.spyOn(utilsPath, "getWorkspaceFolderPath").mockImplementation(() => {
      return "file:///";
    });
    const psiIns = PSIParser.INSTANCE
    const psiInitMock = vi.spyOn(psiIns, "initialized").mockImplementation(() => {
      return true;
    });
    const psiExecMock1 = vi.spyOn(psiIns, "execute").mockImplementation(async (command: string, _languageId: string, uri: DocumentUri, position: Position) => {
      if (command === "jetbrains.getDefinition") {
        if (uri.toString() === "file:///test4.go") {
          if (position.line === 5 && position.character === 11) {
            return [{
              uri: "file:///test4.go",
              range: {start: {line: 1, character: 5}, end: {line: 1, character: 7}},
            }];
          }
          if (position.line === 5 && position.character === 19) {
            return [{
              uri: "file:///test4.go",
              range: {start: {line: 3, character: 5}, end: {line: 3, character: 7}},
            }];
          }
          if (position.line === 7 && position.character === 2) {
            return [{
              uri: "file:///test4.go",
              range: {start: {line: 5, character: 15}, end: {line: 5, character: 17}},
            }];
          }
          if (position.line === 9 && position.character === 2) {
            return [{
              uri: "file:///test4.go",
              range: {start: {line: 5, character: 15}, end: {line: 5, character: 17}},
            }];
          }
        }
      }
      return [];
    });
    const getTreeDocMock1 = vi.spyOn(astCache, "getTreeAndDocumentFromUri").mockImplementation(() => {
      return {tree, document};
    });

    await doFileActiveListener(document);
    await wait(50);

    psiExecMock1.mockRestore();
    getTreeDocMock1.mockRestore();

    const docText2 = `
type T1 struct {
}
type T2 struct {
}
func f(tt1111111111 *T1, t2 *T2) {
  tt1111111111.f1()
  t2.f2()
  tt1111111111.f1()
  t2.f2()
}
`;

    const document2 = createDocument(docText2, LanguageIdEnum.Go, "file:///test4.go");
    const tree2 = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Go, docText2);

    const psiExecMock2 = vi.spyOn(psiIns, "execute").mockImplementation(async () => {
      return [];
    });
    const getTreeDocMock2 = vi.spyOn(astCache, "getTreeAndDocumentFromUri").mockImplementation(() => {
      return {tree: tree2, document: document2};
    });

    await doFileEditListener({
      document: document2,
      contentChanges: [
        {
          position: {line: 5, character: 8},
          removeLength: 1,
          text: "t1111111111",
        },
        {
          position: {line: 6, character: 3},
          removeLength: 1,
          text: "t1111111111",
        },
        {
          position: {line: 8, character: 3},
          removeLength: 1,
          text: "t1111111111",
        },
      ],
    });
    await wait(50);

    const result1 = queryDefinitionForPosition(document2, {line: 5, character: 25});

    expect(result1).toEqual({
      symbolName: "t2",
      definitionKey: "struct@file:///test4.go@T2",
      typeInfo: new ResolvedTypeInfo("file:///test4.go", 20, "T2", DefNodeKindEnum.Struct, "type T2 struct {\n}", null),
    });

    const result2 = queryDefinitionForPosition(document2, {line: 7, character: 2});

    expect(result2).toEqual({
      symbolName: "t2",
      definitionKey: "struct@file:///test4.go@T2",
      typeInfo: new ResolvedTypeInfo("file:///test4.go", 20, "T2", DefNodeKindEnum.Struct, "type T2 struct {\n}", null),
    });

    const result3 = queryDefinitionForPosition(document2, {line: 9, character: 3});

    expect(result3).toEqual({
      symbolName: "t2",
      definitionKey: "struct@file:///test4.go@T2",
      typeInfo: new ResolvedTypeInfo("file:///test4.go", 20, "T2", DefNodeKindEnum.Struct, "type T2 struct {\n}", null),
    });

    workspacePathMock.mockRestore();
    psiInitMock.mockRestore();
    psiExecMock2.mockRestore();
    getTreeDocMock2.mockRestore();
  });
});