#!/bin/bash
set -e

mkdir -p output
CKG_SDK_VERSION=$(grep -o '"version": *"[^"]*"' ./clients/ts/package.json | sed 's/"version": *"\([^"]*\)"/\1/')
echo "CKG SDK version: ${CKG_SDK_VERSION}"
echo "Build build target: ${CUSTOM_CKG_TARGET}"

COMMIT_ID=$(git rev-parse --short=7 HEAD)
echo "Commit ID: ${COMMIT_ID}"

if [ "${CUSTOM_CKG_TARGET}" == "linux_arm64" ]; then
  GOOS=linux GOARCH=arm64 CGO_ENABLED=1 CC=aarch64-linux-gnu-gcc CXX=aarch64-linux-gnu-g++ go build -ldflags="-s -w" -o output/ckg_server_linux_arm64 ./codekg/cmd/server
  md5sum output/ckg_server_linux_arm64 > output/ckg_server_linux_arm64.md5
  echo ${COMMIT_ID} > output/ckg_server_linux_arm64.${COMMIT_ID}
  curl -v -X PUT http://marscode-bucket-cn.tos-cn-north.byted.org/ckg_sdk_binary/${CKG_SDK_VERSION}/ckg_server_linux_arm64 --upload-file ./output/ckg_server_linux_arm64 -H "X-Tos-Access: ${CUSTOM_CKG_ACCESS_KEY}"
  curl -v -X PUT http://marscode-bucket-cn.tos-cn-north.byted.org/ckg_sdk_binary/${CKG_SDK_VERSION}/ckg_server_linux_arm64.md5 --upload-file ./output/ckg_server_linux_arm64.md5 -H "X-Tos-Access: ${CUSTOM_CKG_ACCESS_KEY}"
  curl -v -X PUT http://marscode-bucket-cn.tos-cn-north.byted.org/ckg_sdk_binary/${CKG_SDK_VERSION}/ckg_server_linux_arm64.${COMMIT_ID} --upload-file ./output/ckg_server_linux_arm64.${COMMIT_ID} -H "X-Tos-Access: ${CUSTOM_CKG_ACCESS_KEY}"
elif [ "${CUSTOM_CKG_TARGET}" == "linux_x64" ]; then
  go build -ldflags="-s -w" -o output/ckg_server_linux_x64 ./codekg/cmd/server
  md5sum output/ckg_server_linux_x64 > output/ckg_server_linux_x64.md5
  echo ${COMMIT_ID} > output/ckg_server_linux_x64.${COMMIT_ID}
  curl -v -X PUT http://marscode-bucket-cn.tos-cn-north.byted.org/ckg_sdk_binary/${CKG_SDK_VERSION}/ckg_server_linux_x64 --upload-file "./output/ckg_server_linux_x64" -H "X-Tos-Access: ${CUSTOM_CKG_ACCESS_KEY}"
  curl -v -X PUT http://marscode-bucket-cn.tos-cn-north.byted.org/ckg_sdk_binary/${CKG_SDK_VERSION}/ckg_server_linux_x64.md5 --upload-file ./output/ckg_server_linux_x64.md5 -H "X-Tos-Access: ${CUSTOM_CKG_ACCESS_KEY}"
  curl -v -X PUT http://marscode-bucket-cn.tos-cn-north.byted.org/ckg_sdk_binary/${CKG_SDK_VERSION}/ckg_server_linux_x64.${COMMIT_ID} --upload-file ./output/ckg_server_linux_x64.${COMMIT_ID} -H "X-Tos-Access: ${CUSTOM_CKG_ACCESS_KEY}"
fi
