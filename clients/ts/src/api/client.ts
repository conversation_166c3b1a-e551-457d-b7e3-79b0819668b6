import * as grpc from "@grpc/grpc-js";
import { CodeKGClient } from "../protocol/codekg_grpc_pb";
import {
  CancelIndexRequest,
  CancelIndexResponse,
  CodeSnippet,
  CurrentEditorInfo, CursorMoveRequest,
  DeleteIndexRequest,
  DeleteIndexResponse,
  DocumentRequest,
  DocumentResponse,
  Document,
  Entity,
  FilesImportAnalysisRequest,
  GetBuildStatusRequest,
  GetBuildStatusResponse,
  GetDocumentsIndexStatusRequest,
  GetDocumentsIndexStatusResponse,
  ImportAnalysisRequest,
  ImportAnalysisResponse,
  InitRequest,
  InitResponse,
  InitVirtualProjectsRequest,
  InitVirtualProjectsResponse,
  IsCKGEnabledForNonWorkspaceScenarioRequest,
  IsCKGEnabledForNonWorkspaceScenarioResponse,
  IsVersionMatchedRequest,
  IsVersionMatchedResponse,
  Project,
  Range,
  RefreshTokenRequest,
  RefreshTokenResponse,
  RetrieveEntityRequest,
  RetrieveEntityResponse,
  RetrieveRelationRequest,
  RetrieveRelationResponse,
  RetrieveRelevantSnippetRequest,
  RetrieveRelevantSnippetResponse,
  SearchCKGDBRequest,
  SearchCKGDBResponse,
  VirtualProject,
} from "../protocol/codekg_pb";

export type DocumentAction =
  | "documentSelect"
  | "documentCreate"
  | "documentDelete"
  | "documentChange";

export class CKGClient {
  private _client: CodeKGClient;
  readonly connectionTimeoutMS = 100000;

  // eslint-disable-next-line @typescript-eslint/no-useless-constructor,@typescript-eslint/no-empty-function
  constructor() {}

  public static async create(host: string, port: number): Promise<CKGClient> {
    const that = new CKGClient();
    that._client = await that._initGRPCClient(host, port);
    return that;
  }

  private async _initGRPCClient(host: string, port: number): Promise<CodeKGClient> {
    const maxMessageSize = 512 * 1024 * 1024;
    const addrSchema = `${host}:${port}`;
    const client = new CodeKGClient(
      addrSchema,
      grpc.credentials.createInsecure(),
      {
        "grpc.max_receive_message_length": maxMessageSize,
      },
    );
    await new Promise<void>((resolve, reject) => {
      client.waitForReady(this.connectionTimeoutMS + Date.now(), err => {
        if (err) {
          return reject(err);
        }
        resolve();
      });
    });
    return client;
  }

  async init(params: InitRequest.AsObject): Promise<InitResponse.AsObject> {
    const req = new InitRequest();
    req.setProjectIdsList(params.projectIdsList);
    req.setUserId(params.userId);
    req.setIgnoreFileLimit(params.ignoreFileLimit);
    params.projectsList.forEach(project => {
      const p = new Project();
      p.setProjectId(project.projectId);
      p.setStoragePath(project.storagePath);
      req.addProjects(p);
    });
    const res = await this._invoke<InitResponse>(req, "init");
    return res?.toObject();
  }

  async initVirtualProjects(params: InitVirtualProjectsRequest.AsObject): Promise<InitVirtualProjectsResponse.AsObject> {
    const req = new InitVirtualProjectsRequest();
    req.setUserId(params.userId);
    req.setLoadFilesFromFs(params.loadFilesFromFs);
    params.projectsList.forEach(project => {
      const p = new VirtualProject();
      p.setProjectId(project.projectId);
      p.setUri(project.uri);
      p.setRelativeGlobsToLoadList(project.relativeGlobsToLoadList);
      req.addProjects(p);
    });
    const res = await this._invoke<InitVirtualProjectsResponse>(req, "initVirtualProjects");
    return res?.toObject();
  }

  async refreshToken(userId: string, token: string): Promise<RefreshTokenResponse.AsObject> {
    const req = new RefreshTokenRequest();
    req.setToken(token);
    req.setUserId(userId); // 设置用户ID到请求对象中
    const res = await this._invoke<RefreshTokenResponse>(req, "refreshToken");
    return res?.toObject();
  }

  async retrieveEntity(
    params: RetrieveEntityRequest.AsObject,
  ): Promise<RetrieveEntityResponse.AsObject> {
    const req = new RetrieveEntityRequest();
    req.setProjectIdsList(params.projectIdsList);
    req.setUserMessage(params.userMessage);
    if (params.editorInfo) {
      req.setEditorInfo(this.getEditorInfo(params.editorInfo));
    }
    req.setUserId(params.userId);
    req.setSessionId(params.sessionId);
    req.setEntityNum(params.entityNum);
    if (params.intent) {
      req.setIntent(params.intent);
    }
    if (params.folderPathsList && params.folderPathsList.length > 0) {
      req.setFolderPathsList(params.folderPathsList);
    }
    if (params.expectEntityTypesList && params.expectEntityTypesList.length > 0) {
      req.setExpectEntityTypesList(params.expectEntityTypesList);
    }
    const res = await this._invoke<RetrieveEntityResponse>(
      req,
      "retrieveEntity",
    );
    return res?.toObject();
  }

  async retrieveRelevantSnippet(
    params: RetrieveRelevantSnippetRequest.AsObject,
  ): Promise<RetrieveRelevantSnippetResponse.AsObject> {
    const req = new RetrieveRelevantSnippetRequest();
    req.setProjectId(params.projectId);
    req.setUserQuery(params.userQuery);
    req.setWorkspace(params.workspace);
    req.setVersion(params.version);
    params.selectedFoldersList?.forEach(item => {
      req.addSelectedFolders(item);
    });
    params.selectedFilesList?.forEach(item => {
      req.addSelectedFiles(item);
    });
    params.selectedCodesList?.forEach(item => {
      const code = new CodeSnippet();
      code.setFilePath(item.filePath);
      code.setStartLine(item.startLine);
      code.setEndLine(item.endLine);
      code.setContent(item.content);
      req.addSelectedCodes(code);
    });
    if (params.currentEditor) {
      req.setCurrentEditor(this.getEditorInfo(params.currentEditor));
    }

    req.setUserId(params.userId);
    req.setSessionId(params.sessionId);
    const res = await this._invoke<RetrieveRelevantSnippetResponse>(
      req,
      "retrieveRelevantSnippet",
    );
    return res?.toObject();
  }

  getEditorInfo(params: CurrentEditorInfo.AsObject): CurrentEditorInfo {
    const editorInfo = new CurrentEditorInfo();
    editorInfo.setFilePath(params.filePath);
    editorInfo.setProjectId(params.projectId);
    editorInfo.setUserFileContent(params.userFileContent);
    editorInfo.setCursorLine(params.cursorLine);
    if (params.selectCodeRange) {
      const selectCodeRange = new Range();
      selectCodeRange.setStartLine(params.selectCodeRange.startLine);
      selectCodeRange.setEndLine(params.selectCodeRange.endLine);
      editorInfo.setSelectCodeRange(selectCodeRange);
    }
    editorInfo.setSelectCodeContent(params.selectCodeContent);
    if (params.visibleCodeRange) {
      const visibleCodeRange = new Range();
      visibleCodeRange.setStartLine(params.visibleCodeRange.startLine);
      visibleCodeRange.setEndLine(params.visibleCodeRange.endLine);
      editorInfo.setVisibleCodeRange(visibleCodeRange);
    }
    editorInfo.setVisibleCodeContent(params.visibleCodeContent);
    return editorInfo;
  }

  async retrieveRelation(
    params: RetrieveRelationRequest.AsObject,
  ): Promise<RetrieveRelationResponse.AsObject> {
    const req = new RetrieveRelationRequest();
    params.entitiesByUserMessageList.forEach(item => {
      const entity = new Entity();
      entity.setProjectId(item.projectId);
      entity.setEntityId(item.entityId);
      req.addEntitiesByUserMessage(entity);
    });
    if (params.editorInfo) {
      req.setEditorInfo(this.getEditorInfo(params.editorInfo));
    }
    req.setUserId(params.userId);
    const res = await this._invoke<RetrieveRelationResponse>(
      req,
      "retrieveRelation",
    );
    return res?.toObject();
  }

  async batchFileAction(
    action: DocumentAction,
    userId: string,
    paths: string[],
  ): Promise<DocumentResponse.AsObject> {
    const req = new DocumentRequest();
    req.setFilePathsList(paths);
    req.setUserid(userId);
    const res = await this._invoke<DocumentResponse>(req, action);
    return res?.toObject();
  }

  async documentAction(
    action: DocumentAction,
    userId: string,
    path: string,
    uri?: string,
    name?: string,
    content?: string,
    projectId?: string,
  ): Promise<DocumentResponse.AsObject> {
    const req = new DocumentRequest();
    req.setFilePathsList([path]);
    req.setUserid(userId);
    const doc = new Document();
    if (uri){
      doc.setUri(uri);
      doc.setName(name);
      doc.setContent(content);
      doc.setProjectId(projectId);
      req.setDocumentsList([doc]);
    }
    const res = await this._invoke<DocumentResponse>(req, action);
    return res?.toObject();
  }

  async cursorMove(projectId: string, path: string, userId: string, line: number, version: string): Promise<void> {
    const req = new CursorMoveRequest();
    req.setProjectId(projectId);
    req.setFile(path);
    req.setUserId(userId);
    req.setLine(line);
    req.setVersion(version);
    await this._invoke<void>(req, "cursorMove");
  }

  async getBuildStatus(projectIds?: string[]): Promise<GetBuildStatusResponse.AsObject> {
    const req = new GetBuildStatusRequest();
    if (projectIds && projectIds.length > 0) {
      req.setProjectIdsList(projectIds);
    }
    const res = await this._invoke<GetBuildStatusResponse>(
      req,
      "getBuildStatus",
    );
    return res?.toObject();
  }

  async getDocumentsIndexStatus(projectIds: string[]): Promise<GetDocumentsIndexStatusResponse.AsObject> {
    const req = new GetDocumentsIndexStatusRequest();
    req.setProjectIdsList(projectIds);
    const res = await this._invoke<GetDocumentsIndexStatusResponse>(
      req,
      "getDocumentsIndexStatus",
    );
    return res?.toObject();
  }

  async isVersionMatched(version: string): Promise<IsVersionMatchedResponse.AsObject> {
    const req = new IsVersionMatchedRequest();
    req.setVersion(version);
    const res = await this._invoke<IsVersionMatchedResponse>(
      req,
      "isVersionMatched",
    );
    return res?.toObject();
  }

  async cancelIndex(
    params: CancelIndexRequest.AsObject,
  ): Promise<CancelIndexResponse.AsObject> {
    const req = new CancelIndexRequest();
    req.setProjectId(params.projectId);
    const res = await this._invoke<CancelIndexResponse>(req, "cancelIndex");
    return res?.toObject();
  }

  async deleteIndex(
    params: DeleteIndexRequest.AsObject,
  ): Promise<DeleteIndexResponse.AsObject> {
    const req = new DeleteIndexRequest();
    req.setProjectId(params.projectId);
    const res = await this._invoke<DeleteIndexResponse>(req, "deleteIndex");
    return res?.toObject();
  }

  async importAnalysis(
    params: ImportAnalysisRequest.AsObject,
  ): Promise<ImportAnalysisResponse.AsObject> {
    const req = new ImportAnalysisRequest();
    req.setProjectId(params.projectId);
    req.setFile(params.file);
    req.setImportStatement(params.importStatement);
    const res = await this._invoke<ImportAnalysisResponse>(
      req,
      "importAnalysis",
    );
    return res?.toObject();
  }

  async filesImportAnalysis(
    params: FilesImportAnalysisRequest.AsObject,
  ): Promise<ImportAnalysisResponse.AsObject> {
    const req = new FilesImportAnalysisRequest();
    req.setProjectId(params.projectId);
    req.setRelaFilesList(params.relaFilesList);
    const res = await this._invoke<ImportAnalysisResponse>(
      req,
      "filesImportAnalysis",
    );
    return res?.toObject();
  }

  async searchCKGDB(
    params: SearchCKGDBRequest.AsObject,
  ): Promise<SearchCKGDBResponse.AsObject> {
    const req = new SearchCKGDBRequest();
    req.setProjectId(params.projectId);
    req.setSymbolIdentifier(params.symbolIdentifier);
    req.setSymbolRegex(params.symbolRegex);
    req.setTypesList(params.typesList);
    req.setEntityNum(params.entityNum);
    const res = await this._invoke<SearchCKGDBResponse>(req, "searchCKGDB");
    return res?.toObject();
  }

  async isCKGEnabledForNonWorkspaceScenario(userId: string): Promise<string> {
    const req = new IsCKGEnabledForNonWorkspaceScenarioRequest();
    req.setUserId(userId);
    const res = await this._invoke<IsCKGEnabledForNonWorkspaceScenarioResponse>(
      req,
      "isCKGEnabledForNonWorkspaceScenario",
    );
    const isEnable = res?.getIsEnabled() ?? false;
    if (!isEnable) {
      return "";
    } else {
      return res?.getVersion() ?? "";
    }
  }

  private async _invoke<T>(req: any, method: string): Promise<T> {
    const metaData = new grpc.Metadata({ waitForReady: true });
    return new Promise<T>((resolve, reject) => {
      this._client[method](req, metaData, (err: grpc.ServiceError, res) => {
        if (err || !res) {
          return reject(err);
        }
        resolve(res);
      });
    });
  }
}
