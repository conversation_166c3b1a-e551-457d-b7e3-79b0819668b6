package ignore_rule_checker

import (
	"context"
	"fmt"
	"github.com/denormal/go-gitignore"
	"github.com/samber/lo"
	"ide/ckg/codekg/components/ignore_service"
	"os"
	"path/filepath"
	"strings"
	"time"
)

const (
	// 用户自定义的 ignore 规则
	TCCIgnoreRule  = "TCCIgnoreRuleChecker"
	GitIgnoreRule  = "GitIgnoreRuleChecker"
	TraeIgnoreRule = "TraceIgnoreRuleChecker"

	TCCIgnoreRuleRank  = 305
	GitIgnoreRuleRank  = 310
	TraeIgnoreRuleRank = 360
)

// TCCIgnoreRuleChecker 是 TCC 上配置的默认 ignore 规则。
// 有些规则不是 “按照目录名忽略”、“按照文件名忽略” 以及 “按照后缀忽略” 那么简单，
// e.g. **/.ssh/id_*，因此需要额外配置。
type TCCIgnoreRuleChecker struct {
	tccIgnores map[string]map[string]gitignore.GitIgnore
	is         IgnoreServiceV2
	expire     time.Time
}

func NewTCCIgnoreRuleChecker() *TCCIgnoreRuleChecker {
	return &TCCIgnoreRuleChecker{
		tccIgnores: make(map[string]map[string]gitignore.GitIgnore),
		expire:     time.Now(),
	}
}

func (t *TCCIgnoreRuleChecker) Name() CheckerName                   { return TCCIgnoreRule }
func (t *TCCIgnoreRuleChecker) Rank() int                           { return TCCIgnoreRuleRank }
func (t *TCCIgnoreRuleChecker) Kind() CheckerKind                   { return All }
func (t *TCCIgnoreRuleChecker) SetIgnoreService(is IgnoreServiceV2) { t.is = is }
func (t *TCCIgnoreRuleChecker) AddRules(_ context.Context, _, _ string, _ []IgnoreRule) error {
	return nil
}
func (t *TCCIgnoreRuleChecker) UpdateRules(ctx context.Context, userId string, base string, rules []IgnoreRule) error {
	if _, ok := t.tccIgnores[userId]; !ok {
		t.tccIgnores[userId] = make(map[string]gitignore.GitIgnore)
	}
	finalRule := strings.TrimSpace(lo.Reduce(rules, func(agg string, rule IgnoreRule, _ int) string {
		return lo.Ternary(len(rule) == 0, agg, fmt.Sprintf("%s\n%s", agg, string(rule)))
	}, ""))
	t.tccIgnores[userId][base] = gitignore.New(strings.NewReader(finalRule), base, nil)
	return nil
}
func (t *TCCIgnoreRuleChecker) UpdateRulesFromTCC(ctx context.Context, userId string, base string) error {
	// 判断是否超时
	if time.Now().Before(t.expire) {
		return nil
	} else {
		t.expire = time.Now().Add(5 * time.Minute)
	}
	// 获取 TCC 中的默认配置
	config, cli := t.is.GetConfig(), t.is.GetClient()
	if config == nil || cli == nil {
		return nil
	}
	tcc := config.GetTCCConfig(ctx, cli, userId)
	// 在默认 ignore rule 中添加该用户 & 该 project 的忽略规则
	if _, ok := t.tccIgnores[userId]; !ok {
		t.tccIgnores[userId] = make(map[string]gitignore.GitIgnore)
	}
	t.tccIgnores[userId][base] = gitignore.New(strings.NewReader(tcc.DefaultIgnoreRules), base, nil)
	return nil
}
func (t *TCCIgnoreRuleChecker) IsIgnore(ctx context.Context, userId string, base string, path string, info os.FileInfo) (ignored bool, skip error, err error) {
	base, path = filepath.Clean(base), filepath.Clean(path)
	if path == base {
		// 如果 path 和 base 相等，不应该判断是否被忽略。
		// gitignore 库有 bug，会导致 panic。
		return false, nil, nil
	}
	if _, ok := t.tccIgnores[userId]; !ok {
		// 如果 userCustomizedIgnores 没有该 userId，说明本 Checker 不应判断该文件是否被忽略
		// 所以统一返回 false
		return false, nil, nil
	}
	if _, ok := t.tccIgnores[userId][base]; !ok {
		// 同上
		return false, nil, nil
	}
	ig := t.tccIgnores[userId][base]
	if strings.HasPrefix(path, base) && ig.Ignore(path) {
		// 如果是 dir 且被忽略，返回 skipDir
		return true, lo.Ternary(info.IsDir(), filepath.SkipDir, nil), nil
	}
	return false, nil, nil
}

type GitIgnoreRuleChecker struct {
	gitignoreService map[string]ignore_service.IgnoreService
	is               IgnoreServiceV2
}

func NewGitIgnoreRuleChecker() *GitIgnoreRuleChecker {
	return &GitIgnoreRuleChecker{
		gitignoreService: make(map[string]ignore_service.IgnoreService),
	}
}

func (g *GitIgnoreRuleChecker) Name() CheckerName                   { return GitIgnoreRule }
func (g *GitIgnoreRuleChecker) Rank() int                           { return GitIgnoreRuleRank }
func (g *GitIgnoreRuleChecker) Kind() CheckerKind                   { return All }
func (g *GitIgnoreRuleChecker) SetIgnoreService(is IgnoreServiceV2) { g.is = is }
func (g *GitIgnoreRuleChecker) AddRules(_ context.Context, _, _ string, _ []IgnoreRule) error {
	return nil
}
func (g *GitIgnoreRuleChecker) UpdateRules(ctx context.Context, userId string, base string, _ []IgnoreRule) error {
	if _, ok := g.gitignoreService[userId]; !ok {
		g.gitignoreService[userId] = ignore_service.NewIgnoreService(".gitignore")
	}
	return g.gitignoreService[userId].AddProject(ctx, filepath.Clean(base), "", "")
}
func (g *GitIgnoreRuleChecker) UpdateRulesFromTCC(_ context.Context, _, _ string) error { return nil }
func (g *GitIgnoreRuleChecker) IsIgnore(ctx context.Context, userId, _ string, path string, info os.FileInfo) (ignored bool, skip error, err error) {
	if _, ok := g.gitignoreService[userId]; !ok {
		g.gitignoreService[userId] = ignore_service.NewIgnoreService(".gitignore")
	}
	if ignored, err = g.gitignoreService[userId].IsIgnored(ctx, filepath.Clean(path)); err != nil {
		return ignored, nil, err
	}
	// 如果是 dir 且被忽略，返回 skipDir
	return ignored, lo.Ternary(ignored && info.IsDir(), filepath.SkipDir, nil), nil
}
func (g *GitIgnoreRuleChecker) GetIgnoreService(_ context.Context, userId string) ignore_service.IgnoreService {
	if _, ok := g.gitignoreService[userId]; !ok {
		g.gitignoreService[userId] = ignore_service.NewIgnoreService(".gitignore")
	}
	return g.gitignoreService[userId]
}

// TraeIgnoreRuleChecker 的 userCustomizedIgnores 第一层 key 为 user，第二层为 project
type TraeIgnoreRuleChecker struct {
	userCustomizedIgnores map[string]map[string]gitignore.GitIgnore
	is                    IgnoreServiceV2
}

func NewTraeIgnoreRuleChecker() *TraeIgnoreRuleChecker {
	return &TraeIgnoreRuleChecker{userCustomizedIgnores: make(map[string]map[string]gitignore.GitIgnore)}
}

func (t *TraeIgnoreRuleChecker) Name() CheckerName                   { return TraeIgnoreRule }
func (t *TraeIgnoreRuleChecker) Rank() int                           { return TraeIgnoreRuleRank }
func (t *TraeIgnoreRuleChecker) Kind() CheckerKind                   { return All }
func (t *TraeIgnoreRuleChecker) SetIgnoreService(is IgnoreServiceV2) { t.is = is }
func (t *TraeIgnoreRuleChecker) AddRules(_ context.Context, _, _ string, _ []IgnoreRule) error {
	return nil
}

// UpdateRules 每次都根据 rules 重置用户自定义的忽略规则。
// e.g. 如果传入 rules 为空，则不忽略任何文件或文件夹。
func (t *TraeIgnoreRuleChecker) UpdateRules(ctx context.Context, userId string, base string, rules []IgnoreRule) error {
	if _, ok := t.userCustomizedIgnores[userId]; !ok {
		t.userCustomizedIgnores[userId] = make(map[string]gitignore.GitIgnore)
	}
	finalRule := strings.TrimSpace(lo.Reduce(rules, func(agg string, rule IgnoreRule, _ int) string {
		return lo.Ternary(len(rule) == 0, agg, fmt.Sprintf("%s\n%s", agg, string(rule)))
	}, ""))
	t.userCustomizedIgnores[userId][base] = gitignore.New(strings.NewReader(finalRule), base, nil)
	return nil
}

// UpdateRulesFromTCC of TraeIgnoreRuleChecker，base 应该是项目根目录
func (t *TraeIgnoreRuleChecker) UpdateRulesFromTCC(_ context.Context, _, _ string) error { return nil }

func (t *TraeIgnoreRuleChecker) IsIgnore(ctx context.Context, userId string, base string, path string, info os.FileInfo) (ignored bool, skip error, err error) {
	base, path = filepath.Clean(base), filepath.Clean(path)
	if path == base {
		// 如果 path 和 base 相等，不应该判断是否被忽略。
		// gitignore 库有 bug，会导致 panic。
		return false, nil, nil
	}
	if _, ok := t.userCustomizedIgnores[userId]; !ok {
		// 如果 userCustomizedIgnores 没有该 userId，说明本 Checker 不应判断该文件是否被忽略
		// 所以统一返回 false
		return false, nil, nil
	}
	if _, ok := t.userCustomizedIgnores[userId][base]; !ok {
		// 同上
		return false, nil, nil
	}
	ig := t.userCustomizedIgnores[userId][base]
	if strings.HasPrefix(path, base) && ig.Ignore(path) {
		// 如果是 dir 且被忽略，返回 skipDir
		return true, lo.Ternary(info.IsDir(), filepath.SkipDir, nil), nil
	}
	return false, nil, nil
}

func ConvertStrToRules(content string) []IgnoreRule {
	return lo.Map(
		lo.Filter(strings.Split(content, "\n"), func(s string, _ int) bool { return len(s) != 0 }),
		func(c string, _ int) IgnoreRule { return IgnoreRule(strings.TrimSpace(c)) },
	)
}
