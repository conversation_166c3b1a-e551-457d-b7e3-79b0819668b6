package main

import (
	"byted-ckg/analyzer-offline/manager"
	"byted-ckg/analyzer-offline/util"
	"context"
	"fmt"
	"net/http"

	"byted-ckg/analyzer-offline/handler"

	"code.byted.org/gopkg/logs/v2"
)

func main() {
	ctx := context.Background()

	initTreeSitterParserHandler()

	http.HandleFunc("/init_with_language_server", handler.InitWithLanguageServerHandler)
	http.HandleFunc("/analyzer/get_definition_for_range", handler.GetDefinitionForRangeHandler)
	http.HandleFunc("/analyzer/get_definition_node_range", handler.GetDefinitionNodeRangeHandler)
	http.HandleFunc("/server/definition", handler.ServerDefinitionHandler)
	http.HandleFunc("/server/reference", handler.ServerReferenceHandler)

	http.HandleFunc("/lsp/execute", handler.LspExecuteHandler)

	if err := http.ListenAndServe(fmt.Sprintf(":%d", util.GetServerPort()), nil); err != nil {
		logs.CtxInfo(ctx, "failed to start HTTP server: %s", err.Error())
	}
}

func initTreeSitterParserHandler() {
	ctx := context.Background()

	if manager.GetAnalyzerManager().IsStarted() {
		logs.CtxInfo(ctx, "analyzer is already started, shutdown it")
		if err := manager.GetAnalyzerManager().Shutdown(); err != nil {
			msg := fmt.Sprintf("failed to shutdown analyzer: %s", err.Error())
			logs.CtxError(ctx, msg)
			return
		}
	}

	if err := manager.GetAnalyzerManager().Start(); err != nil {
		msg := fmt.Sprintf("failed to start analyzer: %s", err.Error())
		logs.CtxError(ctx, msg)
		return
	}

	if err := manager.GetAnalyzerManager().InitializeTreeSitterParser(); err != nil {
		msg := fmt.Sprintf("failed to initialize tree-sitter parser: %s", err.Error())
		logs.CtxError(ctx, msg)
		return
	}

	logs.CtxInfo(ctx, "Init tree-sitter parser success")
}
