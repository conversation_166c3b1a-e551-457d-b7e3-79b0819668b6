import {beforeAll, describe, expect, it} from "vitest";
import {TreeSitterParser} from "../../parse/treesitter/parser";
import {createDocument} from "../../../utils/common";
import {LanguageIdEnum} from "../../parse/share";
import {doGetMethodInRange} from "./methodInRange";
import {setEnableLocalLogging} from "../../../utils/logger";

describe("test_doGetMethodInRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
    setEnableLocalLogging(true);
  });

  it("case1_completely_included", async () => {
    const docText = `
func (p *T) m1() {
  var a int
  var b int
}

func (p *T) m2() {
  var a int
  var b int
}

func f1(t *p.T) {
  var a int
  
  var v2 = func(t *p.T) {
    var a int
    var b int
  }
  
  var b int
}

var v1 = func(t *p.T) {
  var a int
  var b int
}
`;

    const document = createDocument(docText, LanguageIdEnum.Go, "file:///test.go");

    const range1 = {
      start: {
        line: 3,
        character: 3,
      },
      end: {
        line: 16,
        character: 5,
      },
    };

    const result1 = doGetMethodInRange(document, range1, true);

    expect(result1).toEqual([
      {
        text: `
func (p *T) m2() {
  var a int
  var b int
}
`.trim(),
        range: {
          start: {
            line: 6,
            character: 0,
          },
          end: {
            line: 9,
            character: 1,
          },
        },
      },
    ]);

    const range2 = {
      start: {
        line: 3,
        character: 3,
      },
      end: {
        line: 19,
        character: 5,
      },
    };

    const result2 = doGetMethodInRange(document, range2, true);

    expect(result2).toEqual([
      {
        text: `
func (p *T) m2() {
  var a int
  var b int
}
`.trim(),
        range: {
          start: {
            line: 6,
            character: 0,
          },
          end: {
            line: 9,
            character: 1,
          },
        },
      },
      {
        text: `
func(t *p.T) {
    var a int
    var b int
  }
`.trim(),
        range: {
          start: {
            line: 14,
            character: 11,
          },
          end: {
            line: 17,
            character: 3,
          },
        },
      },
    ]);
  });

  it("case2_not_completely_included", async () => {
    const docText = `
func (p *T) m1() {
  var a int
  var b int
}

func (p *T) m2() {
  var a int
  var b int
}

func f1(t *p.T) {
  var a int
  
  var v2 = func(t *p.T) {
    var a int
    var b int
  }
  
  var b int
}

var v1 = func(t *p.T) {
  var a int
  var b int
}
`;

    const document = createDocument(docText, LanguageIdEnum.Go, "file:///test.go");

    const range = {
      start: {
        line: 3,
        character: 3,
      },
      end: {
        line: 16,
        character: 5,
      },
    };

    const result = doGetMethodInRange(document, range, false);

    expect(result).toEqual([
      {
        text: `
func (p *T) m1() {
  var a int
  var b int
}
`.trim(),
        range: {
          start: {
            line: 1,
            character: 0,
          },
          end: {
            line: 4,
            character: 1,
          },
        },
      },
      {
        text: `
func (p *T) m2() {
  var a int
  var b int
}
`.trim(),
        range: {
          start: {
            line: 6,
            character: 0,
          },
          end: {
            line: 9,
            character: 1,
          },
        },
      },
      {
        text: `
func f1(t *p.T) {
  var a int
  
  var v2 = func(t *p.T) {
    var a int
    var b int
  }
  
  var b int
}
`.trim(),
        range: {
          start: {
            line: 11,
            character: 0,
          },
          end: {
            line: 20,
            character: 1,
          },
        },
      },
    ]);
  });

  it("case3_select_function_name", async () => {
    const docText = `
func fff1() {
}

func fff2() {
}

func fff3() {
}
`;

    const document = createDocument(docText, LanguageIdEnum.Go, "file:///test.go");

    const range = {
      start: {
        line: 4,
        character: 5,
      },
      end: {
        line: 4,
        character: 9,
      },
    };

    const result = doGetMethodInRange(document, range, false);

    expect(result).toEqual([
      {
        text: `
func fff2() {
}
`.trim(),
        range: {
          start: {
            line: 4,
            character: 0,
          },
          end: {
            line: 5,
            character: 1,
          },
        },
      },
    ]);
  });
});