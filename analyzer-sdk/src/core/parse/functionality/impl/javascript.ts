import {CommonFunctionalityExecutor} from "./common";
import {DefNodeInfo, DefNodeKindEnum, LanguageIdEnum, ParserEnum, VarDeclNodeInfo} from "../../share";
import {DocumentUri, Location} from "vscode-languageserver-types";
import {SyntaxNode} from "web-tree-sitter";
import {TreeSitterDispatcher} from "../../treesitter/dispatcher";
import {TreeSitterFunctionalityEnum} from "../../treesitter/share";

export class JavascriptFunctionalityExecutor extends CommonFunctionalityExecutor {
  static INSTANCE = new JavascriptFunctionalityExecutor();

  languageId = LanguageIdEnum.Javascript;

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
    super();
  }

  async getInvokeDefinition(parses: ParserEnum[], uri: DocumentUri, invokeNameNode: SyntaxNode, resultFilter: (location: Location) => boolean): Promise<DefNodeInfo[]> {
    const result: DefNodeInfo[] = [];

    for (const node of await this.getDefinitionNode(parses, uri, invokeNameNode, resultFilter)) {
      const realDefNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(this.languageId, TreeSitterFunctionalityEnum.GetParentNodeWithTypeListForNode, node.node, ["function_declaration", "method_definition", "generator_function_declaration", "variable_declarator", "required_parameter", "public_field_definition"]);

      if (realDefNode) {
        let typeKind = DefNodeKindEnum.Unknown;
        switch (node.node.type) {
          case "function_declaration":
          case "method_definition":
          case "generator_function_declaration":
            typeKind = DefNodeKindEnum.Function;
            break;
          case "variable_declarator":
          case "required_parameter":
          case "public_field_definition":
            typeKind = DefNodeKindEnum.VarDecl;
            break;
        }
        result.push({
          languageId: this.languageId,
          uri: node.uri,
          kind: typeKind,
          name: invokeNameNode.text,
          defNode: node.node,
          subDefNodeInfo: null,
        });
      }
    }

    return result;
  }

  async getVarDeclaration(parses: ParserEnum[], uri: DocumentUri, varNameNode: SyntaxNode, resultFilter: (location: Location) => boolean): Promise<VarDeclNodeInfo[]> {
    const nodes = await this.getDefinitionNode(parses, uri, varNameNode, resultFilter);

    return nodes.map(node => {
      const realDeclNode: SyntaxNode = TreeSitterDispatcher.INSTANCE.dispatch(this.languageId, TreeSitterFunctionalityEnum.GetVarParentNodeForNode, node.node);

      if (realDeclNode) {
        return {
          languageId: this.languageId,
          uri: node.uri,
          kind: DefNodeKindEnum.VarDecl,
          name: varNameNode.text,
          declNode: realDeclNode,
        };
      } else {
        return null;
      }
    }).filter(item => item !== null);
  }
}