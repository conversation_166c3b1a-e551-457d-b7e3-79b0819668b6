package local_splitter

import (
	"context"
	"sync"

	kb "ide/ckg/codekg/components/knowledgebase"
	"ide/ckg/codekg/components/splitter/local_splitter/code"
	lsmodel "ide/ckg/codekg/components/splitter/local_splitter/model"
	"ide/ckg/codekg/components/splitter/local_splitter/text"
	"ide/ckg/codekg/components/splitter/local_splitter/text/langchain"
	splittermodel "ide/ckg/codekg/components/splitter/model"
	"ide/ckg/codekg/model"
)

// localSplitterImpl manages all registered Splitters
type localSplitterImpl struct {
	languageSplitters map[model.LanguageType]lsmodel.LocalSplitter
	defaultSplitter   lsmodel.LocalSplitter
}

var globalSplitter *localSplitterImpl
var once sync.Once

func getDefaultLocalSplitter() *localSplitterImpl {
	once.Do(func() {
		globalSplitter = newLocalSplitter()
		globalSplitter.initializeDefaultSplitters()
	})
	return globalSplitter
}

// newLocalSplitter creates a new Splitter dispatcher
func newLocalSplitter() *localSplitterImpl {
	return &localSplitterImpl{
		languageSplitters: make(map[model.LanguageType]lsmodel.LocalSplitter),
	}
}

// registerSplitterForLanguage registers a Splitter for a specific language
func (ls *localSplitterImpl) registerSplitterForLanguage(language model.LanguageType, splitter lsmodel.LocalSplitter) {
	ls.languageSplitters[language] = splitter
}

// setDefaultSplitter sets the default Splitter to use when language is not supported
func (ls *localSplitterImpl) setDefaultSplitter(splitter lsmodel.LocalSplitter) {
	ls.defaultSplitter = splitter
}

// getSplitterForLanguage returns the appropriate Splitter for a given language
func (ls *localSplitterImpl) getSplitterForLanguage(language model.LanguageType) (lsmodel.LocalSplitter, error) {
	splitter, ok := ls.languageSplitters[language]
	if !ok {
		if ls.defaultSplitter != nil {
			return ls.defaultSplitter, nil
		}
		return nil, splittermodel.ErrUnsupportedLanguage
	}

	return splitter, nil
}

// split splits files into text segments
func (ls *localSplitterImpl) split(ctx context.Context, opt *splittermodel.LocalSplitOption,
	files []*kb.SplitFile) (*kb.SplitFilesResponse, error) {
	results := make(map[string]*kb.SplitFileResult_)
	failedResults := make(map[string]string)

	// 如果使用Langchain分割器，并且提供了LangchainOpt
	if opt.LocalSubtype == splittermodel.LocalSubtypeLangchainText && opt.LangchainOpt != nil {
		// 使用Langchain分割器
		langchainOpt := opt.LangchainOpt

		// 使用Langchain分割器
		return langchain.SplitFilesWithLangchainOpt(ctx, langchainOpt, files)
	}

	for _, file := range files {
		if file == nil {
			continue
		}

		language := model.LanguageText
		if opt.ContentType != splittermodel.ContentTypeText {
			language = model.DetectLanguage(file.Path, []byte(file.Content))
		}

		// 根据ContentType选择不同的处理方式
		if opt.ContentType == splittermodel.ContentTypeCode {
			// 处理代码文件
			// TODO: 实现代码分割器
			failedResults[file.Path] = "code splitting not implemented yet"
			continue
		}

		splitter, err := ls.getSplitterForLanguage(language)
		if err != nil {
			failedResults[file.Path] = err.Error()
			continue
		}

		result, err := splitter.SplitFile(ctx, file)
		if err != nil {
			failedResults[file.Path] = err.Error()
			continue
		}

		results[file.Path] = result
	}

	return &kb.SplitFilesResponse{
		Results:     results,
		FailedFiles: failedResults,
	}, nil
}

// initializeDefaultSplitters initializes the default Splitters
func (ls *localSplitterImpl) initializeDefaultSplitters() {
	textSplitter := text.NewTextSplitter(nil)
	supportedLanguages := textSplitter.SupportedLanguages()
	for lang, supported := range supportedLanguages {
		if supported {
			ls.registerSplitterForLanguage(lang, textSplitter)
		}
	}

	codeSplitter := code.NewCodeSplitter()
	supportedLanguages = codeSplitter.SupportedLanguages()
	for lang, supported := range supportedLanguages {
		if supported {
			ls.registerSplitterForLanguage(lang, codeSplitter)
		}
	}

	ls.setDefaultSplitter(textSplitter)
}

func splitInMainThread(ctx context.Context, opt *splittermodel.LocalSplitOption, files []*kb.SplitFile) (*kb.SplitFilesResponse, error) {
	splitter := getDefaultLocalSplitter()
	return splitter.split(ctx, opt, files)
}

// Split is kept for backward compatibility
func Split(ctx context.Context, opt *splittermodel.LocalSplitOption, files []*kb.SplitFile) (*kb.SplitFilesResponse, error) {
	return splitWithWorkerPool(ctx, files, opt.ContentType == splittermodel.ContentTypeText)
}
