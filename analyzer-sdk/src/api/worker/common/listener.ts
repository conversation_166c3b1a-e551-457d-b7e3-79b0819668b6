import type {TextDocument} from 'vscode-languageserver-textdocument';
import {EditorSelectionInfo, type FileEditInfo} from '../../../utils/common';
import {Logger} from "../../../utils/logger";
import {<PERSON>Hand<PERSON>} from "../../../worker/main";

export async function fileActiveListener(document: TextDocument): Promise<void> {
  try {
    await MainHandler.INSTANCE.fileActiveListener({document});
  } catch (e) {
    Logger.error(`fileActiveListener: exception for '${document.uri}': ${e}`);
    Logger.error(e.stack);
  }
}

export async function fileEditListener(editInfo: FileEditInfo): Promise<void> {
  try {
    await MainHandler.INSTANCE.fileEditListener(editInfo);
  } catch (e) {
    Logger.error(`fileEditListener: exception for '${editInfo.document.uri}': ${e}`);
    Logger.error(e.stack);
  }
}

export async function editorSelectionListener(selectionInfo: EditorSelectionInfo): Promise<void> {
  try {
    await MainHandler.INSTANCE.editorSelectionListener(selectionInfo);
  } catch (e) {
    Logger.error(`editorSelectionListener: exception for '${selectionInfo.document.uri}': ${e}`);
    Logger.error(e.stack);
    return null;
  }
}