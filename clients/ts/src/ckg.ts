import os from "os";
import {CKGClient} from "./api/client";
import data from "../package.json";
import {
  AccessBeforeInitError,
  AccessDeadProcessError,
  APIErrorCode,
  BinaryExecCheckError,
  CKGStartErrorCode,
  DownloadRetryMaxError,
  errorMap,
  GlibcVersionError,
  KilledError,
  LockReleaseError,
  NetworkError,
  ProjectNotIndexedError,
  UnknownError,
  UnsupportedOSArchError,
  UserEnvUnsupportedCKG,
} from "./errors/errors";
import {CKG_API, CKGEventManager, CKGStartEventData, ClientAPIEventData} from "./metrics/event";
import {Logger, setClientLogger} from "./metrics/logger";
import {CKGProcess, CKGProcessStatus, SourceProductType} from "./process/process";
import {Code} from "./protocol/codekg_pb";
import {
  CodeInfo,
  CurrentEditorInfo,
  Entity,
  EntityType,
  ImportAnalysisResponse,
  ProjectBuildStatus,
  Reference,
  RelevantSnippets,
  VirtualProjectInfo,
  SnippetInfo,
  SnippetWithScoreInfo,
} from "./typings";
import {getPortByHost} from "./utils/util";
import { FileEvent, FileEventCollector, FileEventType } from "utils/file_event_collector";

// 插件传入参数
export interface CKGOptions {
  // 命令行参数， storagePath必要
  host?: string;
  appID?: string; // 插件传入
  storagePath: string;
  binaryRootDir: string;
  channelType: string;  // 插件传入，表示运行插件的产品，e.g. VsCode, GoLand, IDEA 之类
  ideVersion?: string; // 插件传入
  versionCode?: string; // 插件传入
  region?: string; // 插件传入
  extensionVersion?: string; // 插件传入
  sourceProduct: SourceProductType; // 插件传入
  localEmbedding?: string; // 插件传入
  embeddingStorageType?: string; // 插件传入

  // 初始化方式，是否开启守护进程, todo
  isDaemon?: boolean;

  userId: string;
  logger?: Logger;
  getToken?: () => Promise<string>;
  ppe?: string;
  merlin?: boolean;
}

// 对外暴露的 CKG 能力
export class CKG {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  private static INSTANCE: CKG;
  private _opts: CKGOptions;
  protected _port: number;

  private _timerToken: NodeJS.Timeout;
  private _timerMonitor: NodeJS.Timeout;
  private _ckgClient: CKGClient;
  private _ckgProcess: CKGProcess;

  private _eventManager: CKGEventManager = new CKGEventManager();
  private indexedProjects: Set<string> = new Set();
  private _fileCollector = new FileEventCollector(this.handleDocumentEvents.bind(this), 1500, 1000);

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() { }

  public static getInstance(): CKG {
    if (!this.INSTANCE) {
      this.INSTANCE = new CKG();
      return this.INSTANCE;
    }
    return this.INSTANCE;
  }

  get port(): number {
    return this._port;
  }

  public static isEnabled(): boolean {
    return this.INSTANCE && this.INSTANCE.isInitialized();
  }

  public static async init(opts: CKGOptions): Promise<void> {
    const startTime = Date.now();
    const instance = this.getInstance();

    // logger注册
    opts.logger.info("registerClientLogger");
    setClientLogger(opts.logger);

    Logger.info(`ckg init with opts=${JSON.stringify(opts)}`);
    instance._opts = opts;
    instance._port = await getPortByHost();

    // 创建 ckg 进程
    if (instance._ckgProcess === undefined || instance._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING) {
      // 本函数未捕捉 createProcess 内抛出的异常，因此 createProcess 中也有埋点上报
      try {
        await instance.createProcess();
      } catch (err) {
        let errorCode: number;
        let errorMsg: string;
        if (err instanceof DownloadRetryMaxError) {
          errorCode = CKGStartErrorCode.DownloadRetryMaxError;
          errorMsg = `CKG Start error (download) ${err.message}`;
        } else if (err instanceof NetworkError) {
          errorCode = CKGStartErrorCode.NetworkError;
          errorMsg = `CKG Start error (download) ${err.message}`;
        } else if (err instanceof LockReleaseError) {
          errorCode = CKGStartErrorCode.LockReleaseError;
          errorMsg = `CKG Start error (download) ${err.message}`;
        } else if (err instanceof UnsupportedOSArchError) {
          errorCode = CKGStartErrorCode.UnsupportedOSArchError;
          errorMsg = `CKG Start error (download) ${err.message}`;
        } else if (err.toString().includes("ETIMEDOUT")) {
          errorCode = CKGStartErrorCode.TimeoutError;
          errorMsg = `CKG Start error (download) ${err.message}`;
        } else if (err.toString().includes("Client network socket disconnected")) {
          errorCode = CKGStartErrorCode.DisconnectBeforeTLSError;
          errorMsg = `CKG Start error (download) ${err.message}`;
        } else if (err.toString().includes("ENOTFOUND")) {
          errorCode = CKGStartErrorCode.DNSError;
          errorMsg = `CKG Start error (download) ${err.message}`;
        } else if (err.toString().includes("EPERM")) {
          errorCode = CKGStartErrorCode.NoPermissionError;
          errorMsg = `CKG Start error (download) ${err.message}`;
        } else if (err instanceof BinaryExecCheckError) {
          errorCode = CKGStartErrorCode.BinaryExecCheckError;
          errorMsg = `CKG Start error (binary check) ${err.message}`;
        } else if (err instanceof GlibcVersionError) {
          errorCode = CKGStartErrorCode.GlibcVersionError;
          errorMsg = `CKG Start error (binary check) ${err.message}`;
        } else if (err instanceof UserEnvUnsupportedCKG) {
          errorCode = CKGStartErrorCode.UserEnvUnsupportedCKG;
          errorMsg = `CKG Start error (binary check) ${err.message}`;
        } else if (err.toString().includes("retry 3 times, process start failed")) {
          errorCode = CKGStartErrorCode.CKGBinaryStartError;
          errorMsg = `CKG Start error (process start) ${err.message}`;
        } else {
          errorCode = CKGStartErrorCode.UnknownError;
          errorMsg = `CKG Start error (unknown error) ${err.message}`;
        }
        instance._eventManager.reportEvent({
          extension_version: instance._opts.extensionVersion,
          ide_version: instance._opts.ideVersion,
          ai_backend_host: instance._opts.host,
          ckg_version: data.version,
          start_status: false,
          create_client_failed: false,
          os_type: os.type(),
          region: instance._opts.region,
          source_product: instance._opts.sourceProduct,
          error_code: errorCode,
          error_msg: errorMsg,
          create_process_cost: Date.now() - startTime,
          create_client_cost: 0,
        });
        throw err;
      }

    }

    // 等待进程启动
    while (true) {
      const status = instance._ckgProcess.getStatus();
      if (status === CKGProcessStatus.RUNNING) {
        break;
      } else if (status === CKGProcessStatus.TERMINATED) {
        instance._eventManager.reportEvent({
          ide_version: instance._opts.ideVersion,
          extension_version: instance._opts.ideVersion,
          ai_backend_host: instance._opts.host,
          ckg_version: data.version,
          start_status: false,
          create_client_failed: false,
          os_type: os.type(),
          region: opts.region,
          source_product: opts.sourceProduct,
          error_code: CKGStartErrorCode.CKGTerminated,
          create_process_cost: Date.now() - startTime,
          create_client_cost: 0,
        });
        throw new UnknownError(`ckg process is terminated`); // init 的 exit 之一
      } else if (status === CKGProcessStatus.KILLED) {
        instance._eventManager.reportEvent({
          ide_version: instance._opts.ideVersion,
          extension_version: instance._opts.ideVersion,
          ai_backend_host: instance._opts.host,
          ckg_version: data.version,
          start_status: false,
          create_client_failed: false,
          os_type: os.type(),
          region: opts.region,
          source_product: opts.sourceProduct,
          error_code: CKGStartErrorCode.CKGKilledError,
          create_process_cost: Date.now() - startTime,
          create_client_cost: 0,
        });
        throw new KilledError(`ckg process is killed`); // init 的 exit 之一
      }
      await new Promise(resolve => setTimeout(resolve, 3000)); // 每 3s 检测一次
      Logger.info(`ckg process is starting`);
    }
    // 注册子进程退出
    try {
      process.on('exit', () => instance._ckgProcess.kill());
    } catch (e) {
      Logger.error(`register process exit event error, error: ${e}`);
    }
    Logger.info(`ckg process is started, try to create rpc client`);
    const createProcessCost = Date.now() - startTime;
    instance._eventManager.reportEvent({
      ide_version: instance._opts.ideVersion,
      extension_version: instance._opts.ideVersion,
      ai_backend_host: instance._opts.host,
      ckg_version: data.version,
      start_status: true,
      create_client_failed: false,
      os_type: os.type(),
      region: opts.region,
      source_product: opts.sourceProduct,
      create_process_cost: createProcessCost,
      create_client_cost: 0,
    });

    // client 创建重试 3 次
    const createClientStartTime = Date.now();
    for (let i = 0; i < 3; i++) {
      try {
        instance._ckgClient = await CKGClient.create("127.0.0.1", instance._port);
        await instance.refreshToken();
        instance._eventManager.reportEvent({
          ide_version: instance._opts.ideVersion,
          extension_version: instance._opts.ideVersion,
          ai_backend_host: instance._opts.host,
          ckg_version: data.version,
          start_status: true,
          create_client_failed: false,
          os_type: os.type(),
          region: opts.region,
          source_product: opts.sourceProduct,
          create_process_cost: createProcessCost,
          create_client_cost: Date.now() - createClientStartTime,
        });
        return; // init 的 exit 之一，之前需要打点
      } catch (e) {
        Logger.error(`create ckg client error, error: ${e}, retry count: ${i}`);
        await new Promise(resolve => setTimeout(resolve, 5 * 1000)); // 5s 后重试
      }
    }
    instance._eventManager.reportEvent({
      ide_version: instance._opts.ideVersion,
      extension_version: instance._opts.ideVersion,
      ai_backend_host: instance._opts.host,
      ckg_version: data.version,
      start_status: true,
      create_client_failed: true,
      os_type: os.type(),
      region: opts.region,
      source_product: opts.sourceProduct,
      create_process_cost: createProcessCost,
      create_client_cost: Date.now() - createClientStartTime,
    });
    throw new UnknownError("retry 3 times, create ckg client error"); // init 的 exit 之一，之前需要打点
  }

  private isInitialized(): boolean {
    return this._ckgClient !== undefined && this._ckgProcess !== undefined && this._ckgProcess.getStatus() === CKGProcessStatus.RUNNING;
  }

  async isCKGEnabledForNonWorkspaceScenario(): Promise<string> {
    if (this._ckgClient === undefined || this._ckgProcess === undefined) {
      throw new AccessBeforeInitError(
        "CKG client or process is not initialized",
      );
    }

    if (this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING) {
      throw new AccessDeadProcessError("CKG process is not running");
    }

    return this._ckgClient.isCKGEnabledForNonWorkspaceScenario(this._opts.userId);
  }

  // ckg api
  // 仓库初始化
  async indexWorkspace(
    projectsList: Array<string>,
    ignoreFileLimit = false,
  ): Promise<void> {
    const reportData: ClientAPIEventData = {
      ckg_client_api: CKG_API.INIT,
      api_result_code: 0,
      source_product: this._opts.sourceProduct,
      os_type: os.type(),
    };

    if (this._ckgClient === undefined || this._ckgProcess === undefined) {
      reportData.api_result_code = APIErrorCode.AccessBeforeInitError;
      this._eventManager.reportEvent(reportData);
      throw new AccessBeforeInitError(
        "CKG client or process is not initialized",
      );
    }

    if (this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING) {
      // 异常行为，需要上报
      reportData.api_result_code = APIErrorCode.AccessDeadProcessError;
      this._eventManager.reportEvent(reportData);
      throw new AccessDeadProcessError("CKG process is not running");
    }

    Logger.info(`CKG init start`);
    const res = await this._ckgClient.init({
      projectIdsList: projectsList,
      userId: this._opts.userId,
      ignoreFileLimit,
      projectsList: [],
    });


    // 初始化失败，则CKG不生效
    if (res?.code?.valueOf() === Code.SUCCEED.valueOf()) {
      projectsList.forEach(project => {
        this.indexedProjects.add(project);
      });
      reportData.api_result_code = APIErrorCode.Success;
      this._eventManager.reportEvent(reportData);
    } else {
      const errorInfo = errorMap.get(res?.code?.valueOf());
      if (errorInfo) {
        this.reportErrorEvent(CKG_API.INIT, reportData, errorInfo.apiCode, errorInfo.message);
      } else {
        this.reportErrorEvent(CKG_API.INIT, reportData, APIErrorCode.UnknownError, "Unknown error");
      }
    }
  }

  async cancelIndex(projectID: string): Promise<void> {
    const reportData: ClientAPIEventData = {
      ckg_client_api: CKG_API.DELETE_INDEX,
      api_result_code: 0,
      source_product: this._opts.sourceProduct,
      os_type: os.type(),
    };

    if (!this.indexedProjects.has(projectID)) {
      Logger.info(`project ${projectID} not indexed, skip cancel index process`);
      return;
    }

    if (this._ckgClient === undefined || this._ckgProcess === undefined) {
      reportData.api_result_code = APIErrorCode.AccessBeforeInitError;
      this._eventManager.reportEvent(reportData);
      throw new AccessBeforeInitError(
        "CKG client or process is not initialized",
      );
    }

    if (this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING) {
      // 异常行为，需要上报
      reportData.api_result_code = APIErrorCode.AccessDeadProcessError;
      this._eventManager.reportEvent(reportData);
      throw new AccessDeadProcessError("CKG process is not running");
    }

    Logger.info(`CKG cancel index start`);
    const res = await this._ckgClient.cancelIndex({
      projectId: projectID,
    });
    if (res?.code?.valueOf() !== Code.SUCCEED.valueOf()) {
      const errorInfo = errorMap.get(res?.code?.valueOf());
      if (errorInfo) {
        this.reportErrorEvent(CKG_API.DELETE_INDEX, reportData, errorInfo.apiCode, errorInfo.message);
      } else {
        this.reportErrorEvent(CKG_API.DELETE_INDEX, reportData, APIErrorCode.UnknownError, "Unknown error");
      }

    } else {
      reportData.api_result_code = APIErrorCode.Success;
    }
    this._eventManager.reportEvent(reportData);
  }

  async deleteIndex(projectID: string): Promise<void> {
    const reportData: ClientAPIEventData = {
      ckg_client_api: CKG_API.DELETE_INDEX,
      api_result_code: 0,
      source_product: this._opts.sourceProduct,
      os_type: os.type(),
    };

    if (!this.indexedProjects.has(projectID)) {
      Logger.info(`project ${projectID} not indexed, skip delete index`);
      return;
    }

    if (this._ckgClient === undefined || this._ckgProcess === undefined) {
      reportData.api_result_code = APIErrorCode.AccessBeforeInitError;
      this._eventManager.reportEvent(reportData);
      throw new AccessBeforeInitError(
        "CKG client or process is not initialized",
      );
    }

    if (this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING) {
      // 异常行为，需要上报
      reportData.api_result_code = APIErrorCode.AccessDeadProcessError;
      this._eventManager.reportEvent(reportData);
      throw new AccessDeadProcessError("CKG process is not running");
    }

    Logger.info(`CKG delete index start`);
    const res = await this._ckgClient.deleteIndex({
      projectId: projectID,
    });
    if (res?.code?.valueOf() !== Code.SUCCEED.valueOf()) {
      const errorInfo = errorMap.get(res?.code?.valueOf());
      if (errorInfo) {
        this.reportErrorEvent(CKG_API.DELETE_INDEX, reportData, errorInfo.apiCode, errorInfo.message);
      } else {
        this.reportErrorEvent(CKG_API.DELETE_INDEX, reportData, APIErrorCode.UnknownError, "Unknown error");
      }
    } else {
      this.indexedProjects.delete(projectID);
      reportData.api_result_code = APIErrorCode.Success;
    }
    this._eventManager.reportEvent(reportData);
  }

  async retrieveEntity(
    projectIDsList: Array<string>,
    userMessage: string,
    intent: string,
    sessionId: string,
    folderPathsList?: Array<string>,
    editorInfo?: CurrentEditorInfo,
    entityNum?: number,
  ): Promise<[Array<Entity>, Array<string>, Array<string>, Array<string>]> {
    const reportData: ClientAPIEventData = {
      ckg_client_api: CKG_API.RETRIEVE_ENTITY,
      api_result_code: 0,
      source_product: this._opts.sourceProduct,
      os_type: os.type(),
    };

    if (this._ckgClient === undefined || this._ckgProcess === undefined) {
      reportData.api_result_code = APIErrorCode.AccessBeforeInitError;
      this._eventManager.reportEvent(reportData);
      throw new AccessBeforeInitError(
        "CKG client or process is not initialized",
      );
    }

    if (this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING) {
      reportData.api_result_code = APIErrorCode.AccessDeadProcessError;
      this._eventManager.reportEvent(reportData);
      throw new AccessDeadProcessError("CKG process is not running");
    }

    const projectIDs = [];
    projectIDsList.forEach(project => {
      if (this.indexedProjects.has(project)) {
        projectIDs.push(project);
      }
    });
    if (projectIDs.length === 0) {
      reportData.api_result_code = APIErrorCode.ProjectNotIndexedError;
      this._eventManager.reportEvent(reportData);
      throw new ProjectNotIndexedError("CKG is not enable");
    }

    Logger.info(`CKG retrieve entity start`);
    const res = await this._ckgClient.retrieveEntity({
      projectIdsList: projectIDsList,
      userMessage,
      editorInfo,
      intent,
      folderPathsList,
      userId: this._opts.userId,
      sessionId,
      expectEntityTypesList: [],
      entityNum: entityNum ?? 0,
    });
    if (res?.code?.valueOf() !== Code.SUCCEED.valueOf()) {
      const errorInfo = errorMap.get(res?.code?.valueOf());
      if (errorInfo) {
        this.reportErrorEvent(CKG_API.RETRIEVE_ENTITY, reportData, errorInfo.apiCode, errorInfo.message);
      } else {
        this.reportErrorEvent(CKG_API.RETRIEVE_ENTITY, reportData, APIErrorCode.UnknownError, "Unknown error");
      }
    } else {
      reportData.api_result_code = APIErrorCode.Success;
    }

    this._eventManager.reportEvent(reportData);
    return [res.entitiesByUserMessageList, res.queryEmbeddingModelsList, res.vectorEmbeddingModelsList, res.entityChunkingMethodsList];
  }

  async retrieveRelation(
    entitiesByUserMessageList: Array<Entity>,
    editorInfo?: CurrentEditorInfo,
  ): Promise<[string, Array<Reference>, string]> {
    const reportData: ClientAPIEventData = {
      ckg_client_api: CKG_API.RETRIEVE_RELATION,
      api_result_code: 0,
      source_product: this._opts.sourceProduct,
      os_type: os.type(),
    };

    if (this._ckgClient === undefined || this._ckgProcess === undefined) {
      reportData.api_result_code = APIErrorCode.AccessBeforeInitError;
      this._eventManager.reportEvent(reportData);
      throw new AccessBeforeInitError(
        "CKG client or process is not initialized",
      );
    }

    if (this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING) {
      reportData.api_result_code = APIErrorCode.AccessDeadProcessError;
      this._eventManager.reportEvent(reportData);
      throw new AccessDeadProcessError("CKG process is not running");
    }

    Logger.info(`CKG retrieve relation start`);
    const res = await this._ckgClient.retrieveRelation({
      entitiesByUserMessageList,
      editorInfo,
      userId: this._opts.userId,
    });
    if (res?.code?.valueOf() !== Code.SUCCEED.valueOf()) {
      const errorInfo = errorMap.get(res?.code?.valueOf());
      if (errorInfo) {
        this.reportErrorEvent(CKG_API.RETRIEVE_RELATION, reportData, errorInfo.apiCode, errorInfo.message);
      } else {
        this.reportErrorEvent(CKG_API.RETRIEVE_RELATION, reportData, APIErrorCode.UnknownError, "Unknown error");
      }
    } else {
      reportData.api_result_code = APIErrorCode.Success;
    }

    return [res.jsonResult, res.referencesList, res.errMetric];
  }

  async retrieveRelevantSnippet(
    projectId: string,
    userQuery: string,
    workspace: boolean,
    selectedFolders: string[],
    selectedFiles: string[],
    selectedCodes: CodeInfo[],
    version: string,
    currentEditorInfo?: CurrentEditorInfo,
    sessionId?: string,
  ): Promise<RelevantSnippets> {
    const reportData: ClientAPIEventData = {
      ckg_client_api: CKG_API.RETRIEVE_RELEVANT_SNIPPET,
      api_result_code: 0,
      source_product: this._opts.sourceProduct,
      os_type: os.type(),
    };

    if (this._ckgClient === undefined || this._ckgProcess === undefined) {
      reportData.api_result_code = APIErrorCode.AccessBeforeInitError;
      this._eventManager.reportEvent(reportData);
      throw new AccessBeforeInitError(
        "CKG client or process is not initialized",
      );
    }

    if (this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING) {
      reportData.api_result_code = APIErrorCode.AccessDeadProcessError;
      this._eventManager.reportEvent(reportData);
      throw new AccessDeadProcessError("CKG process is not running");
    }

    Logger.info(`CKG retrieve relevant snippet start`);
    const res = await this._ckgClient.retrieveRelevantSnippet({
      projectId,
      userQuery,
      workspace,
      selectedFoldersList: selectedFolders,
      selectedFilesList: selectedFiles,
      selectedCodesList: selectedCodes,
      currentEditor: currentEditorInfo,
      version,
      userId: this._opts.userId,
      sessionId,
    });
    if (res?.code?.valueOf() !== Code.SUCCEED.valueOf()) {
      const errorInfo = errorMap.get(res?.code?.valueOf());
      if (errorInfo) {
        this.reportErrorEvent(CKG_API.RETRIEVE_RELEVANT_SNIPPET, reportData, errorInfo.apiCode, errorInfo.message);
      } else {
        const errorMessage = res?.error.message ?? "Unknown error";
        this.reportErrorEvent(CKG_API.RETRIEVE_RELEVANT_SNIPPET, reportData, APIErrorCode.UnknownError, errorMessage);
      }
    } else {
      reportData.api_result_code = APIErrorCode.Success;
    }

    return {
      snippetsByWorkspace: res.snippetsByWorkspaceList,
      snippetsBySelectedFolders: res.snippetsByFolderList,
      snippetsBySelectedFiles: res.snippetsByFileList,
      snippetsBySelectedCodes: res.snippetsByCodeList,
      snippetsByCurrentEditor: res.snippetsByCurrentFileList,
      snippetsByInteraction: res.snippetsByInteractionList,
    }
  }

  async handleDocumentEvents(fileEvents: FileEvent[]) {
    const deletes: string[] = [];
    const creates: string[] = [];
    const changes: string[] = [];
    for (const event of fileEvents) {
      if (event.event === FileEventType.Delete) {
        deletes.push(event.path);
      } else if (event.event === FileEventType.Create) {
        creates.push(event.path);
      } else if (event.event === FileEventType.Change) {
        changes.push(event.path);
      }
    }

    if (deletes.length > 0) {
      await this._ckgClient.batchFileAction(
        "documentDelete",
        this._opts.userId,
        deletes,
      );
    }

    if (creates.length > 0) {
      await this._ckgClient.batchFileAction(
        "documentCreate",
        this._opts.userId,
        creates,
      );
    }

    if (changes.length > 0) {
      await this._ckgClient.batchFileAction(
        "documentChange",
        this._opts.userId,
        changes,
      );
    }
  }

  async documentCreate(absolutePath: string, uri?: string, name?: string, content?: string, projectId?: string): Promise<void> {
    if (
      this._ckgClient === undefined ||
      this._ckgProcess === undefined ||
      this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING
    ) {
      return;
    }

    Logger.info(`CKG document create, ${absolutePath}`);
    this._fileCollector.handleFileChange(absolutePath, FileEventType.Create);
  }

  async documentSelect(absolutePath: string): Promise<void> {
    if (
      this._ckgClient === undefined ||
      this._ckgProcess === undefined ||
      this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING
    ) {
      return;
    }

    Logger.info(`CKG document select, ${absolutePath}`);
    await this._ckgClient.documentAction(
      "documentSelect",
      this._opts.userId,
      absolutePath,
    );
  }

  async documentDelete(absolutePath: string): Promise<void> {
    if (
      this._ckgClient === undefined ||
      this._ckgProcess === undefined ||
      this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING
    ) {
      return;
    }

    Logger.info(`CKG document delete, ${absolutePath}`);
    this._fileCollector.handleFileChange(absolutePath, FileEventType.Delete);
  }

  async documentChange(absolutePath: string): Promise<void> {
    if (
      this._ckgClient === undefined ||
      this._ckgProcess === undefined ||
      this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING
    ) {
      return;
    }

    Logger.info(`CKG document change, ${absolutePath}`);
    this._fileCollector.handleFileChange(absolutePath, FileEventType.Change);
  }

  async cursorMove(projectId: string, absolutePath: string, userId: string, line: number, version: string): Promise<void> {
    if (
      this._ckgClient === undefined ||
      this._ckgProcess === undefined ||
      this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING
    ) {
      return;
    }

    Logger.info(`CKG record user ${userId} cursor move to, ${absolutePath}, ${line}, ${version}`);
    await this._ckgClient.cursorMove(projectId, absolutePath, userId, line, version);
  }

  async importAnalysis(
    projectId: string,
    file: string,
    importStatement: string,
  ): Promise<ImportAnalysisResponse> {
    if (
      this._ckgClient === undefined ||
      this._ckgProcess === undefined ||
      this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING
    ) {
      return;
    }

    Logger.info(
      `CKG import analysis, import: ${importStatement}, file: ${file}, project: ${projectId}`,
    );
    return this._ckgClient.importAnalysis({
      projectId,
      file,
      importStatement,
    });
  }

  async fileImportAnalysis(
    projectId: string,
    relaFilesList: string[],
  ): Promise<ImportAnalysisResponse> {
    if (
      this._ckgClient === undefined ||
      this._ckgProcess === undefined ||
      this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING
    ) {
      return;
    }

    Logger.info(
      `CKG import analysis for files, files: ${relaFilesList}, project: ${projectId}`,
    );
    return this._ckgClient.filesImportAnalysis({
      projectId,
      relaFilesList,
    });
  }

  async searchCKGDB(
    projectId: string,
    symbolIdentifier: string,
    symbolRegex: string,
    entityNum?: number,
    types?: EntityType[],
  ): Promise<[string, Reference[]]> {
    Logger.info(
      `searchCKGDB, project: ${projectId}, symbol: ${symbolIdentifier}, type: ${types}`,
    );
    Logger.info(`${this._ckgClient}, ${this._ckgProcess}, ${this._ckgProcess.getStatus()}`);
    if (
      this._ckgClient === undefined ||
      this._ckgProcess === undefined ||
      this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING
    ) {
      return ["", []];
    }

    Logger.info(
      `searchCKGDB, project: ${projectId}, symbol: ${symbolIdentifier}, type: ${types}`,
    );
    const res = await this._ckgClient.searchCKGDB({
      projectId,
      symbolIdentifier,
      symbolRegex,
      typesList: types ?? [],
      entityNum: entityNum ?? 20,
    });
    if (res.error) {
      throw new Error(res.error.message);
    }
    return [res.jsonResult, res.referencesList];
  }

  async getCKGBuildStatus(projectIds?: string[]): Promise<Array<[string, ProjectBuildStatus]>> {
    const reportData: ClientAPIEventData = {
      ckg_client_api: CKG_API.GET_BUILD_STATUS,
      api_result_code: 0,
      source_product: this._opts.sourceProduct,
      os_type: os.type(),
    };

    if (this._ckgClient === undefined || this._ckgProcess === undefined) {
      reportData.api_result_code = APIErrorCode.AccessBeforeInitError;
      this._eventManager.reportEvent(reportData);
      throw new AccessBeforeInitError(
        "CKG client or process is not initialized",
      );
    }

    if (this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING) {
      reportData.api_result_code = APIErrorCode.AccessDeadProcessError;
      this._eventManager.reportEvent(reportData);
      throw new AccessDeadProcessError("CKG process is not running");
    }

    const res = await this._ckgClient.getBuildStatus(projectIds);
    if (res?.code?.valueOf() !== Code.SUCCEED.valueOf()) {
      const errorInfo = errorMap.get(res?.code?.valueOf());
      if (errorInfo) {
        this.reportErrorEvent(CKG_API.GET_BUILD_STATUS, reportData, errorInfo.apiCode, errorInfo.message);
      } else {
        this.reportErrorEvent(CKG_API.GET_BUILD_STATUS, reportData, APIErrorCode.UnknownError, "Unknown error");
      }
    } else {
      reportData.api_result_code = APIErrorCode.Success;
    }

    return res?.statusMap;
  }

  private async refreshToken(): Promise<void> {
    const reportData: ClientAPIEventData = {
      ckg_client_api: CKG_API.REFRESH_TOKEN,
      api_result_code: 0,
      source_product: this._opts.sourceProduct,
      os_type: os.type(),
    };

    if (this._ckgClient === undefined || this._ckgProcess === undefined) {
      reportData.api_result_code = APIErrorCode.AccessBeforeInitError;
      this._eventManager.reportEvent(reportData);
      return;
    }

    if (this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING) {
      reportData.api_result_code = APIErrorCode.AccessDeadProcessError;
      this._eventManager.reportEvent(reportData);
      return;
    }

    // 如果有getToken函数传入
    if (this._opts.getToken) {
      const res = await this._ckgClient.refreshToken(
        this._opts.userId,
        await this._opts.getToken(),
      );
      if (res?.code?.valueOf() !== Code.SUCCEED.valueOf()) {
        reportData.api_result_code = APIErrorCode.UnknownError;
        this._eventManager.reportEvent(reportData);
      } else {
        reportData.api_result_code = APIErrorCode.Success;
        this._eventManager.reportEvent(reportData);
      }

      // update every 4 min
      this._timerToken = setInterval(async () => {
        const res = await this._ckgClient.refreshToken(
          this._opts.userId,
          await this._opts.getToken(),
        );
        if (res?.code !== 0) {
          reportData.api_result_code = APIErrorCode.UnknownError;
          this._eventManager.reportEvent(reportData);
        } else {
          reportData.api_result_code = APIErrorCode.Success;
          this._eventManager.reportEvent(reportData);
        }
      }, 240_000).unref();
    }
  }

  // 进程管理 todo
  private async createProcess(): Promise<void> {
    // init config
    Logger.info("init config");
    const ckgProcess = new CKGProcess({
      channelType: this._opts.channelType,
      binaryRootDir: this._opts.binaryRootDir,
      commandArgs: {
        host: this._opts.host,
        appID: this._opts.appID,
        storagePath: this._opts.storagePath,
        port: this._port,
        ideVersion: this._opts.ideVersion,
        versionCode: this._opts.versionCode,
        region: this._opts.region,
        extensionVersion: this._opts.extensionVersion,
        sourceProduct: this._opts.sourceProduct,
        limitCpu: 1,
        isMerlin: this._opts.merlin ? "true" : "false",

        localEmbedding: this._opts.localEmbedding,
        userID: this._opts.userId,
        embeddingStorageType: this._opts.embeddingStorageType,
      },
    });

    let downloadHost = "https://lf-cdn.marscode.cn";
    if (this._opts.merlin) {
      downloadHost = "https://tosv.byted.org"; // merlin 用户使用内网进行下载
    }

    // binary download
    Logger.info("binary download");
    try {
      await ckgProcess.ckgBinaryDownload(downloadHost);
    } catch (err) {
      Logger.error(`CodeKG error downloading ckg binary, error ${err}`);
      throw err;
    }

    // exec check
    try {
      await ckgProcess.ckgBinaryExecCheck();
    } catch (err) {
      Logger.error(`CodeKG error exec ckg binary, error ${err}`);
      throw err;
    }

    // process start，
    Logger.info("process start");
    let retry: number;
    for (retry = 0; retry < 3; retry++) {
      if (await ckgProcess.start(this._opts.isDaemon, this._opts.ppe)) {
        // CKG 启动成功
        this._ckgProcess = ckgProcess;
        return;
      }
      Logger.warn(`process start error, retry:${retry}`);
    }
    Logger.error(`process start failed`);
    throw new UnknownError(`retry 3 times, process start failed`);
  }

  async killProcess(): Promise<boolean> {
    if ((await this.getProcessStatus()) === CKGProcessStatus.RUNNING) {
      // 如果进程状态为运行中，则终止进程
      await this._ckgProcess.kill();
      return true;
    } else {
      return false;
    }
  }

  // ckg进程状态
  async getProcessStatus(): Promise<CKGProcessStatus> {
    if (this._ckgProcess === undefined) {
      return CKGProcessStatus.NOTEXIST;
    }

    return this._ckgProcess.getStatus();
  }

  async registerClientEventConsumer(
    clientConsumer: (data: ClientAPIEventData) => void,
  ): Promise<void> {
    this._eventManager.setClientEventConsumer(clientConsumer);
  }

  async registerStartEventConsumer(
    startConsumer: (data: CKGStartEventData) => void,
  ): Promise<void> {
    this._eventManager.setCkgStartEventConsumer(startConsumer);
  }

  reportErrorEvent(api: CKG_API, reportData: ClientAPIEventData, code: number, message: string): void {
    reportData.api_result_code = code;
    this._eventManager.reportEvent(reportData);
    Logger.error(`${api} error: ${message}`);
    const errorClass = errorMap.get(code)?.errorClass;
    throw new errorClass(message);
  }

  // 初始化虚拟项目
  async initVirtualProjects(
    projectsList: Array<VirtualProjectInfo>,
    loadFilesFromFs = false,
  ): Promise<void> {
    const reportData: ClientAPIEventData = {
      ckg_client_api: CKG_API.INIT_VIRTUAL_PROJECTS,
      api_result_code: 0,
      source_product: this._opts.sourceProduct,
      os_type: os.type(),
    };

    if (this._ckgClient === undefined || this._ckgProcess === undefined) {
      reportData.api_result_code = APIErrorCode.AccessBeforeInitError;
      this._eventManager.reportEvent(reportData);
      throw new AccessBeforeInitError(
        "CKG client or process is not initialized",
      );
    }

    if (this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING) {
      // 异常行为，需要上报
      reportData.api_result_code = APIErrorCode.AccessDeadProcessError;
      this._eventManager.reportEvent(reportData);
      throw new AccessDeadProcessError("CKG process is not running");
    }

    Logger.info(`CKG init virtual projects start`);
    const res = await this._ckgClient.initVirtualProjects({
      projectsList,
      userId: this._opts.userId,
      loadFilesFromFs,
    });

    // 初始化失败，则CKG不生效
    if (res?.code?.valueOf() === Code.SUCCEED.valueOf()) {
      reportData.api_result_code = APIErrorCode.Success;
      this._eventManager.reportEvent(reportData);
    } else {
      const errorInfo = errorMap.get(res?.code?.valueOf());
      if (errorInfo) {
        this.reportErrorEvent(CKG_API.INIT_VIRTUAL_PROJECTS, reportData, errorInfo.apiCode, errorInfo.message);
      } else {
        this.reportErrorEvent(CKG_API.INIT_VIRTUAL_PROJECTS, reportData, APIErrorCode.UnknownError, "Unknown error");
      }
    }
  }

  async getDocumentsIndexStatus(projectIds: string[]): Promise<Record<string, any>> {
    const reportData: ClientAPIEventData = {
      ckg_client_api: CKG_API.GET_DOCUMENTS_INDEX_STATUS,
      api_result_code: 0,
      source_product: this._opts.sourceProduct,
      os_type: os.type(),
    };

    if (this._ckgClient === undefined || this._ckgProcess === undefined) {
      reportData.api_result_code = APIErrorCode.AccessBeforeInitError;
      this._eventManager.reportEvent(reportData);
      throw new AccessBeforeInitError(
        "CKG client or process is not initialized",
      );
    }

    if (this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING) {
      reportData.api_result_code = APIErrorCode.AccessDeadProcessError;
      this._eventManager.reportEvent(reportData);
      throw new AccessDeadProcessError("CKG process is not running");
    }

    const res = await this._ckgClient.getDocumentsIndexStatus(projectIds);
    if (res?.code?.valueOf() !== Code.SUCCEED.valueOf()) {
      const errorInfo = errorMap.get(res?.code?.valueOf());
      if (errorInfo) {
        this.reportErrorEvent(CKG_API.GET_DOCUMENTS_INDEX_STATUS, reportData, errorInfo.apiCode, errorInfo.message);
      } else {
        this.reportErrorEvent(CKG_API.GET_DOCUMENTS_INDEX_STATUS, reportData, APIErrorCode.UnknownError, "Unknown error");
      }
    } else {
      reportData.api_result_code = APIErrorCode.Success;
    }

    return res?.projectsStatusMap;
  }

  async isVersionMatched(version: string): Promise<boolean> {
    const reportData: ClientAPIEventData = {
      ckg_client_api: CKG_API.IS_VERSION_MATCHED,
      api_result_code: 0,
      source_product: this._opts.sourceProduct,
      os_type: os.type(),
    };

    if (this._ckgClient === undefined || this._ckgProcess === undefined) {
      reportData.api_result_code = APIErrorCode.AccessBeforeInitError;
      this._eventManager.reportEvent(reportData);
      throw new AccessBeforeInitError(
        "CKG client or process is not initialized",
      );
    }

    if (this._ckgProcess.getStatus() !== CKGProcessStatus.RUNNING) {
      reportData.api_result_code = APIErrorCode.AccessDeadProcessError;
      this._eventManager.reportEvent(reportData);
      throw new AccessDeadProcessError("CKG process is not running");
    }

    const res = await this._ckgClient.isVersionMatched(version);
    if (res?.code?.valueOf() !== Code.SUCCEED.valueOf()) {
      const errorInfo = errorMap.get(res?.code?.valueOf());
      if (errorInfo) {
        this.reportErrorEvent(CKG_API.IS_VERSION_MATCHED, reportData, errorInfo.apiCode, errorInfo.message);
      } else {
        this.reportErrorEvent(CKG_API.IS_VERSION_MATCHED, reportData, APIErrorCode.UnknownError, "Unknown error");
      }
    } else {
      reportData.api_result_code = APIErrorCode.Success;
    }

    return res?.matched;
  }
}

export { SourceProductType };
