package user_storage

import "time"

type StorageUserInformation struct {
	ID              int    `gorm:"primaryKey;autoIncrement;column:id"`
	ProjectIDs      string `gorm:"type:text;column:project_ids"`
	Token           string `gorm:"type:string;not null;column:token"`
	UserID          string `gorm:"type:string;not null;column:user_id;unique"`
	KnowledgebaseID string `gorm:"type:string;column:knowledgebase_id"`
}

func (StorageUserInformation) TableName() string {
	return "user_information"
}

type StorageProjectID struct {
	ID              int        `gorm:"primaryKey;autoIncrement;column:id"`
	ProjectPath     string     `gorm:"type:string;not null;column:project_id;unique"`
	CodeKGDBPath    string     `gorm:"type:string;column:codekg_db_path;unique"`
	EmbeddingDBPath string     `gorm:"type:string;column:embedding_db_path;unique"`
	EmbeddingDBType int        `gorm:"type:int;column:embedding_db_type;not null;default:0"`
	DBVersion       int        `gorm:"type:string;column:db_version;not null;default:1"`
	CKGVersion      string     `gorm:"type:string;column:ckg_version"`
	ChunkingMethod  string     `gorm:"type:string;column:chunking_method;not null;default:'v1'"`
	EmbeddingModel  string     `gorm:"type:string;column:embedding_model;not null;default:'codekg_bge_m3'"`
	CreateTime      *time.Time `gorm:"type:datetime;column:create_time"`
	ShrinkTime      *time.Time `gorm:"type:datetime;column:shrink_time"`
	InitTime        *time.Time `gorm:"type:datetime;column:init_time"`
	ProjectType     int32      `gorm:"type:int;column:project_type;not null;default:0"`
	ProjectURI      string     `gorm:"type:string;column:project_uri;not null;default:''"`
}

func (StorageProjectID) TableName() string {
	return "project_id"
}
