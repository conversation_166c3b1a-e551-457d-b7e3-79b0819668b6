package handler

import (
	"code.byted.org/gopkg/logs/v2"
	"context"
	"encoding/json"
	"fmt"
	"go.lsp.dev/protocol"
	"io"
	"net/http"

	"byted-ckg/analyzer-offline/manager"
)

type LspExecuteRequestData struct {
	Language string        `json:"language"`
	Command  string        `json:"command"`
	Args     []interface{} `json:"args"`
}

func LspExecuteHandler(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	if r.Method != http.MethodPost {
		msg := fmt.Sprintf("unsupported request method, required POST, got %s", r.Method)
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusMethodNotAllowed)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		msg := fmt.Sprintf("failed to read request body: %s", err.Error())
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	var data LspExecuteRequestData
	if err = json.Unmarshal(body, &data); err != nil {
		msg := fmt.Sprintf("failed to read body data: %s", err.Error())
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusBadRequest)
		return
	}

	uri := data.Args[0].(string)
	position := data.Args[1].(map[string]interface{})
	line := uint32(position["line"].(float64))
	character := uint32(position["character"].(float64))

	var result []protocol.Location
	switch data.Command {
	case "vscode.executeDefinitionProvider":
		if result, err = manager.GetLanguageServerManager().ExecuteDefinition(data.Language, protocol.DocumentURI(uri), protocol.Position{Line: line, Character: character}); err != nil {
			logs.CtxError(ctx, "failed to execute definition: %s", err.Error())
		}
	case "vscode.executeReferenceProvider":
		if result, err = manager.GetLanguageServerManager().ExecuteReference(data.Language, protocol.DocumentURI(uri), protocol.Position{Line: line, Character: character}); err != nil {
			logs.CtxError(ctx, "failed to execute reference: %s", err.Error())
		}
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	if err = json.NewEncoder(w).Encode(result); err != nil {
		msg := fmt.Sprintf("failed to write response: %s", err.Error())
		logs.CtxError(ctx, msg)
		http.Error(w, msg, http.StatusInternalServerError)
		return
	}
}
