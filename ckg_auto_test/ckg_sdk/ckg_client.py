import grpc
from . import codekg_pb2
from . import codekg_pb2_grpc


class CKGClient:
    def __init__(self, address):
        self.channel = grpc.insecure_channel(address)
        self.stub = codekg_pb2_grpc.CodeKGStub(self.channel)

    def close(self):
        self.channel.close()

    def refresh_token(self, token, user_id):
        return self.stub.RefreshToken(codekg_pb2.RefreshTokenRequest(token = token, user_id = user_id))

    def init_project(self, project_ids, user_id):
        return self.stub.Init(codekg_pb2.InitRequest(project_ids = project_ids, user_id = user_id))

    def is_version_matched(self, version):
        return self.stub.IsVersionMatched(codekg_pb2.IsVersionMatchedRequest(version = version))

    def get_build_status(self):
        return self.stub.GetBuildStatus(codekg_pb2.Empty())

    def file_create(self, file_paths, user_id):
        return self.stub.DocumentCreate(codekg_pb2.DocumentRequest(file_paths = file_paths, userID = user_id))

    def file_change(self, file_paths, user_id):
        return self.stub.DocumentChange(codekg_pb2.DocumentRequest(file_paths = file_paths, userID = user_id))

    def file_delete(self, file_paths, user_id):
        return self.stub.DocumentDelete(codekg_pb2.DocumentRequest(file_paths = file_paths, userID = user_id))

    def cancel_index(self, project_id):
        return self.stub.CancelIndex(codekg_pb2.CancelIndexRequest(project_id = project_id))

    def delete_index(self, project_id):
        return self.stub.DeleteIndex(codekg_pb2.DeleteIndexRequest(project_id = project_id))

    def retrieve_entity(self, project_ids, user_id, user_message, folder_paths = None, entity_num = None, expect_entity_types = None, editor_info = None):
        return self.stub.RetrieveEntity(codekg_pb2.RetrieveEntityRequest(project_ids = project_ids,
                                                                         user_id = user_id,
                                                                         user_message = user_message,
                                                                         folder_paths = folder_paths,
                                                                         entity_num = entity_num,
                                                                         expect_entity_types = expect_entity_types,
                                                                         editor_info = editor_info))

    def retrieve_relation(self, entities_by_user_message, user_id, editor_info = None):
        return self.stub.RetrieveRelation(codekg_pb2.RetrieveRelationRequest(entities_by_user_message = entities_by_user_message,
                                                                             editor_info = editor_info,
                                                                             user_id = user_id))
