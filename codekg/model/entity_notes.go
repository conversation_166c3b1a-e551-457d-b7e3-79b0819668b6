package model

type EntityNotes struct {
	ID           string
	Hash         string
	Summary      string
	ElementNotes map[string]interface{}
}

func CreateEntityNotes(id string, hash string, summary string, elementNotes map[string]interface{}) *EntityNotes {
	if elementNotes == nil {
		elementNotes = make(map[string]interface{})
	}
	return &EntityNotes{
		ID:           id,
		Hash:         hash,
		Summary:      summary,
		ElementNotes: elementNotes,
	}
}
