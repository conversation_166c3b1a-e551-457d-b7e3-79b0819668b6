import {describe, expect, it} from "vitest";
import {genDocState, getExtentPoint, spliceCompletionText, getMatchingSuffixLength} from './share';
import type {TextDocument, Position} from 'vscode-languageserver-textdocument';
import {createDocument} from "../../../../utils/common";

describe("test_genDocState", () => {
  it("case1_positionLineSuffix_is_empty", () => {
    const docText = `
func main() {
  fmt.Println(
}
`;

    const textDocument = createDocument(docText, "go", "file:///test.go");
    const position = {line: 2, character: 14} as Position;

    const result = genDocState(textDocument as TextDocument, position);

    const expectedDocState = {
      position,
      positionIndexInDoc: textDocument.offsetAt(position),
      positionLine: "  fmt.Println(\n",
      positionLinePrefix: "  fmt.Println(",
      positionLineSuffix: "",
    };

    expect(result).toEqual(expectedDocState);
  });
  it("case2_positionLineSuffix_is_not_empty", () => {
    const docText = `
func main() {
  fmt.Println();
}
`;

    const textDocument = createDocument(docText, "go", "file:///test.go");
    const position = {line: 2, character: 14} as Position;

    const result = genDocState(textDocument as TextDocument, position);

    const expectedDocState = {
      position,
      positionIndexInDoc: textDocument.offsetAt(position),
      positionLine: "  fmt.Println();\n",
      positionLinePrefix: "  fmt.Println(",
      positionLineSuffix: ");",
    };

    expect(result).toEqual(expectedDocState);
  });
  it("case3_positionLineSuffix_starts_with_blank", () => {
    const docText = `
func main() {
  fmt.Println(  ); 
}
`;

    const textDocument = createDocument(docText, "go", "file:///test.go");
    const position = {line: 2, character: 14} as Position;

    const result = genDocState(textDocument as TextDocument, position);

    const expectedDocState = {
      position,
      positionIndexInDoc: textDocument.offsetAt(position),
      positionLine: "  fmt.Println(  ); \n",
      positionLinePrefix: "  fmt.Println(",
      positionLineSuffix: "  );",
    };

    expect(result).toEqual(expectedDocState);
  });
});

describe("test_getExtentPoint", () => {
  it("case1_normal", () => {
    const docText = `
func main() {
  fmt.Println();
}
`;
    const result = getExtentPoint(docText);

    expect(result).toEqual({row: 4, column: 0});
  });
  it("case2_empty_input", () => {
    const docText = "";
    const result = getExtentPoint(docText);

    expect(result).toEqual({row: 0, column: 0});
  });
});

describe("test_spliceCompletionText", () => {
  it("case1_suffixLengthToRemove_is_0", () => {
    const docText = `
func main() {
  fmt.Println(
}
`;
    const textDocument = createDocument(docText, "go", "file:///test.go");
    const position = {line: 2, character: 14} as Position;

    const docState = genDocState(textDocument, position);

    const completionText = '"Hello, world!");';

    const suffixLengthToRemove = 0;

    const result = spliceCompletionText(docText, completionText, docState, suffixLengthToRemove);

    const expectedTextWithCompletion = `
func main() {
  fmt.Println("Hello, world!");
}
`;

    const docAfterCompletion = createDocument(expectedTextWithCompletion, "go", "file:///test.go");

    expect(result.textAfterModification).toEqual(expectedTextWithCompletion);
    expect(result.edit).toEqual({
      startIndex: docAfterCompletion.offsetAt({line: 2, character: 14}),
      startPosition: {row: 2, column: 14},
      oldEndIndex: docAfterCompletion.offsetAt({line: 2, character: 14}),
      oldEndPosition: {row: 2, column: 14},
      newEndIndex: docAfterCompletion.offsetAt({line: 2, character: 31}),
      newEndPosition: {row: 2, column: 31},
    });
  });
});

describe("test_getMatchingSuffixLength", () => {
  it("case1_normal", () => {
    const docText = `
func main() {
  fmt.Println();
}
`;
    const textDocument = createDocument(docText, "go", "file:///test.go");
    const position = {line: 2, character: 14} as Position;

    const docState = genDocState(textDocument, position);

    const completionText = '"Hello, world!");';

    const result = getMatchingSuffixLength(completionText, docState);

    expect(result).toEqual(2);
  });

  it("case2_no_suffix_matching", () => {
    const docText = `
func main() {
  fmt.Println(
}
`;
    const textDocument = createDocument(docText, "go", "file:///test.go");
    const position = {line: 2, character: 14} as Position;

    const docState = genDocState(textDocument, position);

    const completionText = '"Hello, world!");';

    const result = getMatchingSuffixLength(completionText, docState);

    expect(result).toEqual(0);
  });
});