package model

type CKGVariables struct {
	IsCKG       bool         `json:"is_ckg"`
	CKGEntities []*CKGEntity `json:"ckg_entities"`
}

type CKGReferences struct {
	Path      string `json:"uri"`
	StartLine int32  `json:"start_line"`
	EndLine   int32  `json:"end_line"`
}

type CKGEntity struct {
	Type                     EntityType                `json:"type"`
	URI                      *string                   `json:"uri"`
	ID                       *string                   `json:"id"`
	CKGFile                  *CKGFile                  `json:"ckg_file"`
	CKGFolderTree            *CKGFolderTree            `json:"ckg_folder_tree"`
	CKGFolderUsefulFileInfos *CKGFolderUsefulFileInfos `json:"ckg_folder_useful_file_infos"`
	CKGComment               *CKGComment               `json:"ckg_comment"`
	CKGCodeContent           *CKGCodeContent           `json:"ckg_code_content"`
	CKGCalleeSummary         *CKGCalleeSummary         `json:"ckg_callee_summary"`
	CKGCallerSummary         *CKGCallerSummary         `json:"ckg_caller_summary"`
	CKGClassMemberSummary    *CKGClassMemberSummary    `json:"ckg_class_member_summary"`
	CKGClassMethodSummary    *CKGClassMethodSummary    `json:"ckg_class_method_summary"`
	CKGClassTestFunctions    *CKGClassTestFunctions    `json:"ckg_class_test_functions"`
	CKGTestCallerSummary     *CKGTestCallerSummary     `json:"ckg_test_caller_summary"`
}

type CKGFile struct {
	Content string `json:"content"`
}

type CKGFolderTree struct {
	FolderTree string `json:"folder_tree"`
}

type CKGFileInfo struct {
	Name    string `json:"name"`
	Content string `json:"content"`
}

type CKGFolderUsefulFileInfos struct {
	FileInfos []*CKGFileInfo `json:"file_infos"`
}

type CKGComment struct {
	EntityType EntityType `json:"entity_type"`
	Comment    string     `json:"comment"`
}

type CKGCodeContent struct {
	EntityType EntityType `json:"entity_type"`
	Body       string     `json:"body"`
}

type CKGCallee struct {
	URI             string `json:"uri"`
	MethodSignature string `json:"method_signature"`
	Comment         string `json:"comment"`
}

type CKGCalleeSummary struct {
	Callees []*CKGCallee `json:"callees"`
}

type CKGCaller struct {
	URI             string `json:"uri"`
	MethodSignature string `json:"method_signature"`
	Comment         string `json:"comment"`
	CodeSnippet     string `json:"code_snippet"`
}

type CKGCallerSummary struct {
	Callers []*CKGCaller `json:"callers"`
}

type CKGClassMember struct {
	Code string `json:"code"`
}

type CKGClassMemberSummary struct {
	Members []*CKGClassMember `json:"members"`
}

type CKGMethod struct {
	URI             string `json:"uri"`
	MethodSignature string `json:"method_signature"`
	Comment         string `json:"comment"`
}

type CKGClassMethodSummary struct {
	Methods []*CKGMethod `json:"methods"`
}

type CKGClassTestFunc struct {
	Code string `json:"code"`
}

type CKGClassTestFunctions struct {
	TargetID string              `json:"target_id"`
	Tests    []*CKGClassTestFunc `json:"tests"`
}

type CKGTestCaller struct {
	URI             string `json:"uri"`
	MethodSignature string `json:"method_signature"`
	Comment         string `json:"comment"`
	Code            string `json:"code"`
}

type CKGTestCallerSummary struct {
	HasTestFunction bool             `json:"has_test_function"`
	TestCallers     []*CKGTestCaller `json:"test_callers"`
}

type CKGRelevantFileNameByKeyword struct {
	RelevantFilenames string `json:"relevant_filenames"`
}

type CKGRelevantSymbolByKeyword struct {
	RelevantSymbols string `json:"relevant_symbols"`
}
