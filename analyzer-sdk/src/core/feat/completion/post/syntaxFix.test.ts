import {beforeAll, describe, expect, it} from "vitest";
import {
  analyzeMissingBracket,
  doSyntaxFix,
  generateNewCompletionText,
} from "./syntaxFix";
import {completeUpdateCachedTree} from "../../../cache/astCache";
import type {Position} from "vscode-languageserver-types";
import {TreeSitterParser} from "../../../parse/treesitter/parser";
import {createDocument} from "../../../../utils/common";
import {TimeoutError} from "../../../../utils/error";

describe("test_analyzeMissingBracket", () => {
  it("case1_missing_bracket", () => {
    const docText = `
func f() {
  if i > 0 {
    if i > 0 {
      if i > 0 {
        fmt.Println()
  }
}
`;

    const textDocument = createDocument(docText, "go", "file:///test.go");
    const startIndex = textDocument.offsetAt({line: 0, character: 9});
    const endIndex = textDocument.offsetAt({line: 7, character: 2});
    const targetIndex = textDocument.offsetAt({line: 5, character: 21});

    const result = analyzeMissingBracket(textDocument, startIndex, endIndex, targetIndex);

    expect(result).toEqual([
      {bracket: "}", indent: "      ", newLine: true},
      {bracket: "}", indent: "    ", newLine: true},
    ]);
  });
  it("case2_not_missing_bracket", () => {
    const docText = `
func f() {
  if i > 0 {
    if i > 0 {
      if i > 0 {
        fmt.Println()
      }
    }
  }
}
`;

    const textDocument = createDocument(docText, "go", "file:///test.go");
    const startIndex = textDocument.offsetAt({line: 0, character: 9});
    const endIndex = textDocument.offsetAt({line: 9, character: 2});
    const targetIndex = textDocument.offsetAt({line: 5, character: 21});

    const result = analyzeMissingBracket(textDocument, startIndex, endIndex, targetIndex);

    expect(result).toEqual([]);
  });
});

describe("test_doSyntaxFix", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_no_syntax_error", async () => {
    const docText = `
func main() {
  if 
}
`;
    const textDocument = createDocument(docText, "go", "file:///test.go");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 2, character: 5} as Position;

    const completionText = "i > 0 {\n  }\n";

    const result = doSyntaxFix(textDocument, position, completionText, 20);

    expect(result).toEqual({
      completionText,
      suffixLengthToRemove: 0,
      modified: false,
    });
  });

  it("case2_add_missing_bracket", async () => {
    const docText = `
func main() {
  if 
}
`;
    const textDocument = createDocument(docText, "go", "file:///test.go");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 2, character: 5} as Position;

    const completionText = "i > 0 {\n    if i > 0 {\n      if i > 0 {\n        fmt.Println()";

    const result = doSyntaxFix(textDocument, position, completionText, 20);

    const expectedCompletionText = "i > 0 {\n    if i > 0 {\n      if i > 0 {\n        fmt.Println()\n      }\n    }\n  }";

    expect(result).toEqual({
      completionText: expectedCompletionText,
      suffixLengthToRemove: 0,
      modified: true,
    });
  });

  it("case3_add_missing_bracket_in_same_line", async () => {
    const docText = `
export function f(): Result {
  
}  
`;
    const textDocument = createDocument(docText, "typescript", "file:///test.ts");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 2, character: 2} as Position;

    const completionText = "return generateResult({\n    value: {\n      k1: v1,\n      k2: v2,";

    const result = doSyntaxFix(textDocument, position, completionText, 20);

    const expectedCompletionText = "return generateResult({\n    value: {\n      k1: v1,\n      k2: v2,\n    }\n  })";

    expect(result).toEqual({
      completionText: expectedCompletionText,
      suffixLengthToRemove: 0,
      modified: true,
    });
  });

  it("case4_remove_suffix_bracket", async () => {
    const docText = `
export function f(): Result {
  
}  
`;
    const textDocument = createDocument(docText, "typescript", "file:///test.ts");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 2, character: 2} as Position;

    const completionText = "return generateResult()))}";

    const result = doSyntaxFix(textDocument, position, completionText, 20);

    const expectedCompletionText = "return generateResult()";

    expect(result).toEqual({
      completionText: expectedCompletionText,
      suffixLengthToRemove: 0,
      modified: true,
    });
  });

  it("case5_timeout", async () => {
    const docText = `
export function f(): Result {
  
}  
`;
    const textDocument = createDocument(docText, "typescript", "file:///test.ts");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 2, character: 2} as Position;

    const completionText = "return generateResult()))))))))))))))))))))))))))))}";

    try {
      doSyntaxFix(textDocument, position, completionText, 1);
      expect(false).toEqual(true);
    } catch (e) {
      expect(e).toBeInstanceOf(TimeoutError);
    }
  });

  it("case6_add_missing_bracket_for_prefix", async () => {
    const docText = `
export function f(): Result {
  if (
}  
`;
    const textDocument = createDocument(docText, "typescript", "file:///test.ts");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 2, character: 6} as Position;

    const completionText = "a === 0) {\n    return null;";

    const result = doSyntaxFix(textDocument, position, completionText, 20);

    const expectedCompletionText = "a === 0) {\n    return null;\n  }";

    expect(result).toEqual({
      completionText: expectedCompletionText,
      suffixLengthToRemove: 0,
      modified: true,
    });
  });

  it("case7_add_missing_bracket_in_middle_of_line", async () => {
    const docText = `
export function f(): Result {
  if ()
}  
`;
    const textDocument = createDocument(docText, "typescript", "file:///test.ts");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 2, character: 6} as Position;

    const completionText = "a === 0) {\n    return null;";

    const result = doSyntaxFix(textDocument, position, completionText, 20);

    const expectedCompletionText = "a === 0) {\n    return null;\n  }";

    expect(result).toEqual({
      completionText: expectedCompletionText,
      suffixLengthToRemove: 1,
      modified: true,
    });
  })

  it("case8_no_syntax_error_not_remove_suffix", async () => {
    const docText = `
export function f(): Result {
  if (a === 0 && ) {
    return null;
  }
}
`;
    const textDocument = createDocument(docText, "typescript", "file:///test.ts");
    await completeUpdateCachedTree(textDocument);

    const position = {line: 2, character: 17} as Position;

    const completionText = "returnNull()";

    const result = doSyntaxFix(textDocument, position, completionText, 20);

    const expectedCompletionText = "returnNull()";

    expect(result).toEqual({
      completionText: expectedCompletionText,
      suffixLengthToRemove: 0,
      modified: false,
    });
  })
});

describe("test_generateNewCompletionText", () => {
  it("case1_normal", () => {
    const completionText = "  if (i) {";

    const result = generateNewCompletionText(completionText, [
      {bracket: "}", indent: "  ", newLine: true},
    ]);

    expect(result).toEqual("  if (i) {\n  }");
  });

  it("case2_more_than_one_in_a_line", () => {
    const completionText = "firstline ({\n  secondline ({\n";

    const result = generateNewCompletionText(completionText, [
      {bracket: "}", indent: "  ", newLine: true},
      {bracket: ")", indent: "  ", newLine: false},
      {bracket: "}", indent: "", newLine: true},
      {bracket: ")", indent: "", newLine: false},
    ]);

    expect(result).toEqual("firstline ({\n  secondline ({\n\n  })\n})");
  });
});