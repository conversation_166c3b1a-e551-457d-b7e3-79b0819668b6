package data_manager

import (
	"context"
	"ide/ckg/codekg/components/ckg_metrics"
	"ide/ckg/codekg/components/data_storage"
	"ide/ckg/codekg/components/data_storage/consts"
	"ide/ckg/codekg/components/data_storage/user_storage"
	bizErr "ide/ckg/codekg/components/error"
	"ide/ckg/codekg/components/file_system"
	"ide/ckg/codekg/components/validator"
	"ide/ckg/codekg/model"
	"ide/ckg/codekg/util"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync/atomic"
	"time"

	"gorm.io/gorm"

	"ide/ckg/codekg/components/logs"

	"github.com/pkg/errors"
	"github.com/samber/lo"
)

func (m *manager) GetEntityStorage(ctx context.Context, project model.URI) (data_storage.Storage, error) {
	m.storageMu.Lock()
	defer m.storageMu.Unlock()

	storage, ok := m.storages[project]
	if !ok {
		return nil, errors.WithMessagef(bizErr.ErrNotMatchStorage, "project %s cannot get entity storage", project)
	}

	return storage, nil
}

func (m *manager) GetLocalEmbeddingStorage(ctx context.Context, project model.URI) (data_storage.EmbeddingStorage, error) {
	m.embeddingStorageMu.Lock()
	defer m.embeddingStorageMu.Unlock()

	storage, ok := m.embeddingStorages[project]
	if !ok {
		return nil, errors.WithMessagef(bizErr.ErrNotMatchStorage, "project %s cannot get embedding storage", project)
	}

	return storage, nil
}

func (m *manager) GetProjectEmbeddingType(ctx context.Context, project model.URI) EmbeddingStorageType {
	embeddingType, ok := m.projects.Load(project)
	if !ok {
		return EmbeddingStorageTypeLocal
	}

	return embeddingType.(EmbeddingStorageType)
}

func (m *manager) GetProjectType(ctx context.Context, projectID string) model.ProjectType {
	projectType, ok := m.projectTypes.Load(projectID)
	if !ok {
		return model.ProjectTypeDefault
	}
	return projectType.(model.ProjectType)
}

func (m *manager) getOrCreateStorage(ctx context.Context, project model.URI, projectURI model.URI, chunkingMethod, embeddingModel string,
	t consts.EmbeddingStorageType, storageVersion consts.StorageVersion,
	forceNew bool, storagePath string, envStorage *user_storage.UserStorage) (data_storage.Storage, data_storage.EmbeddingStorage, error) {

	entityStorage, err := m.getOrCreateEntityStorage(ctx, project, projectURI, chunkingMethod, storageVersion, forceNew, storagePath, envStorage)
	if err != nil {
		return nil, nil, err
	}
	embeddingStorage, err := m.getOrCreateEmbeddingStorage(ctx, project, embeddingModel, t, storageVersion, forceNew, storagePath, envStorage)
	if err != nil {
		return nil, nil, err
	}
	return entityStorage, embeddingStorage, nil
}

func (m *manager) getOrCreateEntityStorage(ctx context.Context, project model.URI, projectURI model.URI, chunkingMethod string, version consts.StorageVersion,
	forceNew bool, storagePath string, envStorage *user_storage.UserStorage) (data_storage.Storage, error) {
	var entityStorage data_storage.Storage
	var ok bool
	var err error
	if entityStorage, ok = m.storages[project]; !ok || entityStorage == nil || !entityStorage.IsExistsOnDisk() {
		projectType := m.GetProjectType(ctx, project)
		entityStorage, err = data_storage.Create(ctx, envStorage, storagePath, project, projectURI, projectType, chunkingMethod, version, forceNew)
		if err != nil {
			logs.CtxError(ctx, "[getOrCreateEntityStorage] Create error: %v", err)
			return nil, errors.WithMessagef(bizErr.ErrNotMatchStorage, "project %s cannot get storage, storagePath: %s", project, storagePath)
		}
	}
	return entityStorage, nil
}

func (m *manager) getOrCreateEmbeddingStorage(ctx context.Context, project model.URI, embeddingModel string, t consts.EmbeddingStorageType, version consts.StorageVersion,
	forceNew bool, storagePath string, envStorage *user_storage.UserStorage) (data_storage.EmbeddingStorage, error) {
	var embeddingStorage data_storage.EmbeddingStorage
	var ok bool
	var err error
	if embeddingStorage, ok = m.embeddingStorages[project]; !ok || embeddingStorage == nil || !embeddingStorage.IsExistsOnDisk() {
		embeddingStorage, err = data_storage.CreateEmbeddingStorage(ctx, envStorage, storagePath, project, embeddingModel, t, version, forceNew)
		if err != nil {
			logs.CtxError(ctx, "[getOrCreateEmbeddingStorage] CreateEmbeddingStorage error: %v", err)
			return nil, errors.WithMessagef(bizErr.ErrNotMatchStorage, "project %s cannot get embedding storage, storagePath: %s", project, storagePath)
		}
	}
	return embeddingStorage, nil
}

// needForceNewSinceStrategy 仅判断 chunking / embedding model / storage version 等不同导致的另建数据库行为。
// 构建过程中，原数据库不删除，等待 (新 chunking / embedding model / storage version) 数据库构建完成后无缝切换。
// 如果 env codekg 中已有 (chunking || embedding model || storage version) 与传入的不同，
// 则需另建数据库
func (m *manager) needForceNewSinceStrategy(chunkingMethodLocal, embeddingModelLocal, chunkingMethodAB, embeddingModelAB string, storageVersionLocal, storageVersionTarget consts.StorageVersion) bool {
	return chunkingMethodLocal != chunkingMethodAB || embeddingModelLocal != embeddingModelAB || storageVersionLocal != storageVersionTarget
}

// needForceNew 仅判断 env_codekg.db 中是否存在某 project_id 记录，使得该记录的实体库 & 向量库都实际存在于本地磁盘。
// 该 API 不关心
func (m *manager) needForceNew(ctx context.Context, envStorage *user_storage.UserStorage, project model.URI, t consts.EmbeddingStorageType) bool {
	storageProjectId, err := envStorage.GetProjectIDByProjectPath(ctx, envStorage.GetConn(), project)
	if err != nil {
		return true
	}
	// 实体库不存在，重建数据库
	if len(storageProjectId.CodeKGDBPath) != 0 {
		if _, err := file_system.RealFS.Stat(storageProjectId.CodeKGDBPath); err != nil {
			if os.IsNotExist(err) {
				logs.CtxWarn(ctx, "[GetStoragePathsFromUserStorage] codekg db not existed at %s, err: %v", storageProjectId.CodeKGDBPath, err)
			}
			return true
		}
	}
	// 向量库类型不匹配，重建数据库
	if t.Int() != storageProjectId.EmbeddingDBType {
		logs.CtxInfo(ctx, "[GetStoragePathsFromUserStorage] embedding db type not matched, type in env storage: %d, curr type: %s", storageProjectId.EmbeddingDBType, t)
		return true
	}
	// 向量库不存在，重建数据库
	if len(storageProjectId.EmbeddingDBPath) != 0 {
		if _, err := file_system.RealFS.Stat(storageProjectId.EmbeddingDBPath); err != nil {
			if os.IsNotExist(err) {
				logs.CtxWarn(ctx, "[GetStoragePathsFromUserStorage] embedding db not existed at %s, err: %v", storageProjectId.EmbeddingDBPath, err)
			}
			return true
		}
	}
	logs.CtxInfo(ctx, "get storage smoothly")
	return false
}

func (m *manager) getStorageFromURIStatus(file *model.URIStatus) (data_storage.Storage, data_storage.EmbeddingStorage) {
	m.storageMu.Lock()
	defer m.storageMu.Unlock()
	var storageKeyCandidate = ""
	var storage data_storage.Storage = nil
	// 找到能匹配的最长 path
	for key, value := range m.storages {
		if len(key) > len(storageKeyCandidate) && strings.HasPrefix(file.AbsPath, key) {
			storageKeyCandidate = key
			storage = value
		}
	}
	m.embeddingStorageMu.Lock()
	defer m.embeddingStorageMu.Unlock()
	var embeddingKeyCandidate = ""
	var embeddingStorage data_storage.EmbeddingStorage = nil
	for key, value := range m.embeddingStorages {
		if len(key) > len(embeddingKeyCandidate) && strings.HasPrefix(file.AbsPath, key) {
			embeddingKeyCandidate = key
			embeddingStorage = value
		}
	}
	return storage, embeddingStorage
}

func (m *manager) deleteEntityData(ctx context.Context, storage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage, uriMeta *data_storage.StorageURIMeta) error {
	err := storage.GetConn().Transaction(func(tx *gorm.DB) error {
		var err error
		err = storage.DeleteURIMetaFromURI(ctx, tx, &model.URIStatus{
			AbsPath:     uriMeta.Uri,
			RelPath:     "",
			ContentHash: "",
			UniqueID:    "",
		})
		if err != nil {
			return err
		}

		// 首先确定待删除的 entities 和 vectors 的 ID
		entities, err := storage.SearchEntitiesByURIMeta(ctx, tx, uriMeta)
		if err != nil {
			logs.CtxError(ctx, "SearchEntitiesByURIMeta err %v", err)
			return err
		}
		option := data_storage.NewDeleteDocumentOptionBuilder().Build()
		if embeddingStorage.GetVersion() == consts.StorageVersionV1 {
			if err = embeddingStorage.DeleteDocumentsByEntity(ctx, entities, storage, tx, option); err != nil {
				return err
			}
		} else if embeddingStorage.GetVersion() == consts.StorageVersionV2 {
			entityIds := lo.Map(entities, func(e data_storage.StorageEntity, _ int) string { return e.EntityID })
			vectors, err := storage.SearchVectorIDsByEntityIDs(ctx, tx, entityIds)
			if err != nil {
				logs.CtxError(ctx, "SearchVectorIDsByEntityIDs err %v", err)
				return err
			}
			// 先在向量库中删除，再删除 EntityStorage 中的关系
			err = embeddingStorage.DeleteDocumentsByIds(ctx, option, vectors...)
			if err != nil {
				return err
			}
		}

		// 先删除 vector_to_entity，再删除 entity，因为需要先根据 uriMeta.Uri 找到对应的 entity 中的 entities，
		// 这样才能先在 vector_to_entity 中删除对应的记录。
		if err = storage.DeleteVectorToEntityFromURIMeta(ctx, tx, uriMeta); err != nil {
			logs.CtxError(ctx, "DeleteVectorToEntityFromURIMeta err %v", err)
			return err
		}
		err = storage.DeleteEntitiesFromURIMeta(ctx, tx, uriMeta)
		if err != nil {
			logs.CtxError(ctx, "DeleteEntitiesFromURIMeta err %v", err)
			return err
		}
		err = storage.DeleteRelationsFromURIMeta(ctx, tx, uriMeta)
		if err != nil {
			logs.CtxError(ctx, "DeleteRelationsFromURIMeta err %v", err)
			return err
		}
		err = storage.DeleteAliasFromURIMeta(ctx, tx, uriMeta)
		if err != nil {
			logs.CtxError(ctx, "DeleteAliasFromURIMeta err %v", err)
			return err
		}
		return err
	})
	return err
}

func (m *manager) clearProjectOutdatedData(ctx context.Context, storage data_storage.Storage, embeddingStorage data_storage.EmbeddingStorage, filesToIndex []string) error {
	initEvent := ckg_metrics.GetEvent(ctx)
	uriMetas, err := storage.GetAllURIMetaWithoutContent(ctx, storage.GetConn())
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logs.CtxInfo(ctx, "GetAllURIMeta record not found")
			return nil
		}
		logs.CtxError(ctx, "clearProjectOutdatedData err is %v", err)
		return err
	}

	files := make(map[string]bool)
	for _, f := range filesToIndex {
		files[f] = true
	}

	deleteTime, deleteNum := time.Now(), 0
	for _, uriMeta := range uriMetas {
		cancelFlg, ok := m.projectCancelFlag.Load(storage.GetProjectID())
		if !ok || cancelFlg.(*atomic.Bool).Load() {
			logs.CtxInfo(ctx, "project %s init already canceled, skip deleteEntityData", storage.GetProjectID())
			initEvent.AddTeaParam(ckg_metrics.TeaParamClearProjectOutdatedNum, deleteNum)
			initEvent.AddTeaParam(ckg_metrics.TeaParamClearProjectOutdatedTime, time.Since(deleteTime).Milliseconds())
			return nil
		}

		curUriMeta := uriMeta
		if _, ok := files[uriMeta.Uri]; !ok {
			err = m.deleteEntityData(ctx, storage, embeddingStorage, &curUriMeta)
			if err != nil {
				logs.CtxWarn(ctx, "deleteEntityData err is %v", err)
				initEvent.AddTeaParam(ckg_metrics.TeaParamClearProjectOutdatedNum, deleteNum)
				initEvent.AddTeaParam(ckg_metrics.TeaParamClearProjectOutdatedTime, time.Since(deleteTime).Milliseconds())
				return nil
			} else {
				deleteNum++
				logs.CtxTrace(ctx, "remove outdated index data, uri is %v", uriMeta.Uri)
			}
		}
	}
	initEvent.AddTeaParam(ckg_metrics.TeaParamClearProjectOutdatedNum, deleteNum)
	initEvent.AddTeaParam(ckg_metrics.TeaParamClearProjectOutdatedTime, time.Since(deleteTime).Milliseconds())
	return nil
}

// loadAllUriMetaFromFS 从 uri 中加载所有 uri meta，包括 content
func (m *manager) loadAllUriMetaFromFS(ctx context.Context, projectID string, projectURI model.URI, globs []string, storage data_storage.Storage, fileCountLimit int, fileSizeLimit int64, totalFileSizeLimit int64) (uriToIndexIds []int, uriToConvert []model.URI, err error) {
	defer func() {
		if rec := recover(); rec != nil {
			err = errors.WithMessagef(bizErr.ErrPanic, "loadAllUriMetaFromFS panic is %v", rec)
		}
	}()

	uriToIndexIds = make([]int, 0)
	uriToConvert = make([]model.URI, 0)

	projectAbsPath := util.GetFilePathFromURI(projectURI, true)
	if projectAbsPath == "" {
		err = errors.New("not local project")
		return
	}

	filePathsToAdd := make([]string, 0)
	folderPathsToAdd := make([]string, 0)
	processedGlobMap := make(map[string]bool)
	relaPathAdded := make(map[string]bool)

	for _, globStr := range globs {
		relaPath := globStr
		if strings.HasSuffix(relaPath, "**.txt") {
			relaPath = strings.TrimSuffix(relaPath, "**.txt")
		} else if strings.HasSuffix(relaPath, "**.md") {
			relaPath = strings.TrimSuffix(relaPath, "**.md")
		} else if strings.HasSuffix(relaPath, "*.txt") {
			relaPath = strings.TrimSuffix(relaPath, "*.txt")
		} else if strings.HasSuffix(relaPath, "*.md") {
			relaPath = strings.TrimSuffix(relaPath, "*.md")
		}

		if runtime.GOOS == "windows" {
			relaPath = strings.ReplaceAll(relaPath, "\\", "/")
		}

		relaPath = strings.TrimSuffix(relaPath, "/")
		if relaPathAdded[relaPath] {
			continue
		}
		relaPathAdded[relaPath] = true

		fullPath := filepath.Join(projectAbsPath, relaPath)
		stat, err := m.fs.Stat(fullPath)
		if err != nil {
			continue
		}
		if stat.IsDir() {
			if processedGlobMap[fullPath] {
				continue
			}
			processedGlobMap[fullPath] = true
			folderPathsToAdd = append(folderPathsToAdd, fullPath)
		} else {
			if processedGlobMap[fullPath] {
				continue
			}
			processedGlobMap[fullPath] = true
			filePathsToAdd = append(filePathsToAdd, fullPath)
		}
	}

	if len(globs) == 0 {
		stat, err := m.fs.Stat(projectAbsPath)
		if err != nil {
			return nil, nil, err
		}
		if stat.IsDir() {
			folderPathsToAdd = append(folderPathsToAdd, projectAbsPath)
		} else {
			filePathsToAdd = append(filePathsToAdd, projectAbsPath)
		}
	}

	stopCollectionLimit := 0
	if fileCountLimit != 0 {
		stopCollectionLimit = lo.Ternary(fileCountLimit > 10000, 10000, fileCountLimit)
	}

	// 处理文件并加载到数据库
	var totalSize int64 = 0
	var processedCount int = 0

	const batchSize = 16 // 每批处理 16 个文件写入 db
	var currentBatch []*model.URIStatus
	var currentBatchSize int64 = 0
	var processedMap = make(map[string]bool)
	// 收集处理失败的文件信息
	var failedFiles []*model.URIStatus

	// 记录处理失败的文件
	recordFailedFile := func(path string, reason model.BuildFailedReason) {
		fileURI := util.GetCanonicalURIFromFilePath(path, true)
		relPath, err := m.fs.Rel(projectAbsPath, path)
		if err != nil {
			logs.CtxError(ctx, "Failed to get relative path for %s: %v", path, err)
			relPath = path
		}

		// 创建包含错误信息的 URI 状态，但不保存文件内容
		failedStatus := &model.URIStatus{
			AbsPath:      path,
			RelPath:      relPath,
			UriCanonical: fileURI,
			// 在 Name 字段中存储标准化的错误原因
			Name:    string(reason),
			Content: "",
		}

		failedFiles = append(failedFiles, failedStatus)
		logs.CtxWarn(ctx, "File %s failed to process: %s", path, reason)
	}

	// 处理一批文件函数
	processBatch := func() error {
		if len(currentBatch) == 0 {
			return nil
		}

		err := storage.GetConn().Transaction(func(tx *gorm.DB) error {
			for _, uriStatus := range currentBatch {
				uriMeta, err := storage.UpsertUriMeta(ctx, tx, uriStatus)
				if err != nil {
					logs.CtxError(ctx, "Failed to upsert URI meta for %s: %v", uriStatus.UriCanonical, err)
					return err
				}
				uriToIndexIds = append(uriToIndexIds, uriMeta.ID)
			}
			return nil
		})

		if err != nil {
			logs.CtxError(ctx, "Failed to process batch: %v", err)
			return err
		}

		// 清空当前批次
		currentBatch = nil
		currentBatchSize = 0
		return nil
	}

	breakFlag := false
	for _, folderPath := range folderPathsToAdd {
		folderProcessCount := 0

		err = m.fs.Walk(folderPath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				logs.CtxError(ctx, "file walk param err != nil, err: %v", err)
				return err
			}

			// 检查路径是否已处理，避免重复处理
			if processedMap[path] {
				logs.CtxInfo(ctx, "file path %v is already processed", path)
				if info.IsDir() {
					return filepath.SkipDir
				}
				return nil
			}
			processedMap[path] = true

			if folderProcessCount >= 1000 {
				return filepath.SkipAll
			}
			folderProcessCount++

			if info.IsDir() && path != folderPath {
				return filepath.SkipDir
			}

			// 处理普通文件
			if !info.IsDir() {

				// 检查是否需要转换格式
				if validator.IsSupportedFileFormatToConvert(ctx, path) {
					// 使用标准格式构建URI
					fileURI := util.GetCanonicalURIFromFilePath(path, true)
					uriToConvert = append(uriToConvert, model.URI(fileURI))
					//logs.CtxInfo(ctx, "file %s is supported file format to convert", path)
					return nil
				}

				// 检查文件大小限制
				if fileSizeLimit > 0 && info.Size() > fileSizeLimit {
					logs.CtxInfo(ctx, "File %s exceeds size limit %d, skipping", path, fileSizeLimit)
					recordFailedFile(path, model.BuildFailedReasonContentSizeTooLarge)
					return nil
				}

				//// 只检测后缀名
				if !strings.HasSuffix(path, ".txt") && !strings.HasSuffix(path, ".md") {
					//logs.CtxWarn(ctx, "file %s not valid document type", path)
					//ckg_metrics.GetEvent(ctx).AddParam(ckg_metrics.TeaParamFileSkipIndex, path)
					//recordFailedFile(path, model.BuildFailedReasonContentInvalid)
					return nil
				}

				// 检查处理文件数量限制
				if fileCountLimit > 0 && processedCount >= fileCountLimit {
					logs.CtxInfo(ctx, "Reached file count limit %d, stopping", fileCountLimit)
					recordFailedFile(path, model.BuildFailedReasonProjectItemExceeded)
					return nil
				}

				// 检查总大小限制
				if totalFileSizeLimit > 0 && totalSize+info.Size() > totalFileSizeLimit {
					logs.CtxInfo(ctx, "Total size limit %d reached, stopping", totalFileSizeLimit)
					recordFailedFile(path, model.BuildFailedReasonProjectSizeExceeded)
					return nil
				}

				// 直接处理符合条件的文件
				fileInformation, err := m.createFileInfo(ctx, projectAbsPath, path)
				if err != nil {
					//logs.CtxError(ctx, "Failed to create file info for %s: %v", path, err)
					recordFailedFile(path, model.BuildFailedReasonContentInvalid)
					return nil
				}

				// 构建标准URI格式
				fileURI := util.GetCanonicalURIFromFilePath(path, true)

				// 计算相对路径
				relPath, err := m.fs.Rel(projectAbsPath, path)
				if err != nil {
					//logs.CtxError(ctx, "Failed to get relative path for %s: %v", path, err)
					relPath = path // 如果无法获取相对路径，使用绝对路径
				}

				uriStatus := &model.URIStatus{
					AbsPath:      path,
					RelPath:      relPath, // 添加相对路径
					ContentHash:  fileInformation.hash,
					UriCanonical: fileURI,
					Name:         info.Name(),
				}

				// 将此文件添加到当前批次
				currentBatch = append(currentBatch, uriStatus)
				currentBatchSize += info.Size()

				totalSize += info.Size()
				processedCount++

				// 如果当前批次达到了批处理大小，处理这一批
				if len(currentBatch) >= batchSize {
					if err := processBatch(); err != nil {
						return err
					}
				}

				// 检查是否达到收集上限
				if stopCollectionLimit != 0 && processedCount >= stopCollectionLimit {
					breakFlag = true
					return filepath.SkipAll
				}
			}

			return nil
		})

		if breakFlag {
			break
		}
	}

	for _, path := range filePathsToAdd {
		info, err := m.fs.Stat(path)
		if err != nil {
			// logs.CtxError(ctx, "Failed to get file info for %s: %v", filePath, err)
			continue
		}

		if info.IsDir() {
			continue
		}

		// 检查是否需要转换格式
		if validator.IsSupportedFileFormatToConvert(ctx, path) {
			// 使用标准格式构建URI
			fileURI := util.GetCanonicalURIFromFilePath(path, true)
			uriToConvert = append(uriToConvert, model.URI(fileURI))
			// logs.CtxInfo(ctx, "file %s is supported file format to convert", path)
			continue
		}

		// 检查文件大小限制
		if fileSizeLimit > 0 && info.Size() > fileSizeLimit {
			logs.CtxInfo(ctx, "File %s exceeds size limit %d, skipping", path, fileSizeLimit)
			recordFailedFile(path, model.BuildFailedReasonContentSizeTooLarge)
			continue
		}

		// 检查处理文件数量限制
		if fileCountLimit > 0 && processedCount >= fileCountLimit {
			// logs.CtxInfo(ctx, "Reached file count limit %d, stopping", fileCountLimit)
			recordFailedFile(path, model.BuildFailedReasonProjectItemExceeded)
			continue
		}

		// 检查总大小限制
		if totalFileSizeLimit > 0 && totalSize+info.Size() > totalFileSizeLimit {
			// logs.CtxInfo(ctx, "Total size limit %d reached, stopping", totalFileSizeLimit)
			recordFailedFile(path, model.BuildFailedReasonProjectSizeExceeded)
			continue
		}

		//// 只检测后缀名
		if !strings.HasSuffix(path, ".txt") && !strings.HasSuffix(path, ".md") {
			// ckg_metrics.GetEvent(ctx).AddParam(ckg_metrics.TeaParamFileSkipIndex, path)
			//recordFailedFile(path, model.BuildFailedReasonContentInvalid)
			continue
		}

		// 直接处理符合条件的文件
		fileInformation, err := m.createFileInfo(ctx, projectAbsPath, path)
		if err != nil {
			logs.CtxError(ctx, "Failed to create file info for %s: %v", path, err)
			recordFailedFile(path, model.BuildFailedReasonContentInvalid)
			continue
		}

		// 构建标准URI格式
		fileURI := util.GetCanonicalURIFromFilePath(path, true)

		// 计算相对路径
		relPath, err := m.fs.Rel(projectAbsPath, path)
		if err != nil {
			logs.CtxError(ctx, "Failed to get relative path for %s: %v", path, err)
			relPath = path // 如果无法获取相对路径，使用绝对路径
		}

		uriStatus := &model.URIStatus{
			AbsPath:      path,
			RelPath:      relPath, // 添加相对路径
			ContentHash:  fileInformation.hash,
			UriCanonical: fileURI,
			Name:         info.Name(),
		}

		// 将此文件添加到当前批次
		currentBatch = append(currentBatch, uriStatus)
		currentBatchSize += info.Size()

		totalSize += info.Size()
		processedCount++

		// 如果当前批次达到了批处理大小，处理这一批
		if len(currentBatch) >= batchSize {
			if err := processBatch(); err != nil {
				continue
			}
		}

		// 检查是否达到收集上限
		if stopCollectionLimit != 0 && processedCount >= stopCollectionLimit {
			breakFlag = true
			break
		}
	}

	if err != nil {
		return
	}

	// 处理最后剩余的批次
	if len(currentBatch) > 0 {
		if batchErr := processBatch(); batchErr != nil {
			err = batchErr
			return
		}
	}

	// 处理失败的文件统一写入数据库
	if len(failedFiles) > 0 {
		logs.CtxInfo(ctx, "Writing %d failed files to database", len(failedFiles))
		err = storage.GetConn().Transaction(func(tx *gorm.DB) error {
			for _, failedStatus := range failedFiles {
				_, err := storage.UpsertUriMeta(ctx, tx, failedStatus)
				if err != nil {
					logs.CtxError(ctx, "Failed to upsert failed URI meta for %s: %v", failedStatus.UriCanonical, err)
					return err
				}
				// 失败的文件不需要添加到待索引列表，但需要设置索引状态为失败
				err = storage.UpdateURIMetaStatus(ctx, tx, failedStatus.AbsPath, int(model.BuildStatusFailed))
				if err != nil {
					logs.CtxError(ctx, "Failed to update URI meta status for %s: %v", failedStatus.UriCanonical, err)
					return err
				}
			}
			return nil
		})

		if err != nil {
			logs.CtxError(ctx, "Failed to write failed files to database: %v", err)
			// 这里不返回错误，让成功处理的文件信息能够正常返回
		} else {
			logs.CtxInfo(ctx, "Successfully wrote %d failed files to database", len(failedFiles))
		}
	}

	logs.CtxInfo(ctx, "Loaded %d files with total size %d bytes for project %s, marked %d files as failed",
		processedCount, totalSize, projectID, len(failedFiles))

	// 返回需要索引的文件URI ID列表和需要转换格式的文件URI列表
	return
}
