import {TextDocument} from "vscode-languageserver-textdocument";
import {wait} from "../../../utils/common";
import {GetSymbolDefSource} from "../../share/definition";
import {FileSymbolDef, ResolvedTypeInfo} from "../../share/common";
import {getCachedDefinitionKey, hasDefinitionInfoCached, updateDefinitionInfoCache} from "../../cache/definitionCache";
import {updateFileSymbolDefCache} from "../../cache/symbolDefCache";
import {Logger} from "../../../utils/logger";

export const EVENT_PROCESS_FILE_ACTIVE = "file_active";
export const EVENT_PROCESS_FILE_EDIT = "file_edit";
export const EVENT_PROCESS_CURSOR_MOVE = "cursor_move";
export const EVENT_PROCESS_QUERY = "query";

export const EVENT_VALUE_SYMBOL_FILE_CACHE_MISS = "symbol_file_cache_miss";

export interface DefinitionAnalysisTask {
  id: number;
  source: GetSymbolDefSource;
  document: TextDocument;
  taskFunc: () => Promise<FileSymbolDef>;
}

export const definitionAnalysisQueue: DefinitionAnalysisTask[] = [];

let definitionAnalysisStarted = false;

export function addDefinitionAnalysisTask(task: DefinitionAnalysisTask): void {
  definitionAnalysisQueue.push(task);
  if (!definitionAnalysisStarted) {
    definitionAnalysisStarted = true;
    processDefinitionAnalysisTask().then();
  }
}

export function hasIncrementalAnalysisTask(): boolean {
  return definitionAnalysisQueue.filter(task => task.source === GetSymbolDefSource.Incremental).length > 0;
}

export const definitionHandler = (defInfo: ResolvedTypeInfo): string => {
  if (!hasDefinitionInfoCached(defInfo)) {
    return updateDefinitionInfoCache(defInfo);
  } else {
    return getCachedDefinitionKey(defInfo);
  }
};

async function processDefinitionAnalysisTask(): Promise<void> {
  while (definitionAnalysisQueue) {
    while (definitionAnalysisQueue.length > 0) {
      const task = definitionAnalysisQueue.shift();

      try {
        const result = await task.taskFunc();
        updateFileSymbolDefCache(task.document, result);
      } catch (e) {
        Logger.error(`processDefinitionAnalysisTask: exception for '${task.document.uri}': ${e}`);
        Logger.error(e.stack);
      }
    }

    await wait(10);
  }
}

// This function is used for UT.
export async function processDefinitionAnalysisTaskForTest(): Promise<number> {
  let indexLength = -1;

  while (definitionAnalysisQueue.length > 0) {
    const task = definitionAnalysisQueue.shift();
    const result = await task.taskFunc();
    updateFileSymbolDefCache(task.document, result);

    indexLength = result.indexDefInfo.length;
  }

  return indexLength;
}