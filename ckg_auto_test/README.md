### ckg接口更新
- 执行rpc_gen.sh，生成python的grpc文件
- 由于python的编译行为，需要修改生成的codekg_pb2_grpc.py，把import codekg_pb2 as codekg__pb2修改成from . import codekg_pb2 as codekg__pb2
- 如果ckg有接口新增，需要在ckg_sdk/ckg_client.py中新增对应的接口

### 用例添加和修改
- 新增和修改用例的执行统一添加至main文件
- 如果要新增测试代码仓库，则在utils/const.py中添加url以及commit，调用clone_repo接口进行仓库clone，这里建议把测试仓库上传到codebase后进行clone，直接从github上clone会很慢
- utils/const.py存放用例的预期结果值，在对应的注释下进行添加
- test_case.py为用例的实现文件
- 添加和修改用例请在[用例设计文档](https://bytedance.larkoffice.com/docx/IjQjdc4rAoVbyOxxt5JcGPlUnRd)添加或修改用例说明

### 用例部署
- 变更用例后提交代码，拿到commit
- 到[插件管理](https://bits.bytedance.net/devops_open/market/plugin/manage)中升级mac linux win的测试插件
- 到[Job管理](https://bits.bytedance.net/devops/************/pipeline/marketplace?devops_space_type=client)的流程编排中更新插件的使用版本

### 新增依赖
- 新增用例如果要import库，则需要在用例执行环境中添加该库，方法有两种  
  - 方法一：制作镜像，变更[Job管理](https://bits.bytedance.net/devops/************/pipeline/marketplace?devops_space_type=client)中对应os的测试job的环境镜像
  - 方法二：在test_ckg_binary脚本中install库（ps:这个方法可能对镜像一些基本库有依赖,如果安装失败，则需要按照方法一制作镜像）

### 制作及上传镜像
- 制作云构建镜像[参考文档](https://cloud.bytedance.net/docs/bits/docs/6480709f2207580224c6b2d2/6620bddd48b41f02f0d82922?x-resource-account=public&x-bc-region-id=bytedance)
- 制作好的windows镜像push到ckg_test命名空间才能在流水线中使用,上传的镜像[查看链接](https://luban.bytedance.net/docker/source/search-artifacts?group_prefix=ckg_test)，上传指令
docker push hub.byted.org/ckg_test/ckg_windows_test:$version

### 本地调试
- cd到ckg目录下，执行指令python3 ckg_auto_test/main.py,这里建议使用python3.10
- 用例详细信息请查阅[用例设计文档](https://bytedance.larkoffice.com/docx/IjQjdc4rAoVbyOxxt5JcGPlUnRd)


