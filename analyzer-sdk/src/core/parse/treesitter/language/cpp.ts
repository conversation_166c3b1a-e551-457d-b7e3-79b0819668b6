import {SyntaxNode} from "web-tree-sitter";
import {TreeSitterParser} from "../parser";
import {DefNodeInfo, DefNodeKindEnum, LanguageIdEnum} from "../../share";
import {Range} from "vscode-languageserver-textdocument";
import {<PERSON><PERSON>itter<PERSON>and<PERSON>} from "./common";
import {DocumentUri} from "vscode-languageserver-types";

export class CppTreeSitterHandler extends TreeSitterHandler {
  static INSTANCE = new CppTreeSitterHandler();

  languageId = LanguageIdEnum.Cpp;

  funcTypes = ["function_definition"];
  typeTypes = ["class_specifier", "struct_specifier", "union_specifier", "enum_specifier", "namespace_definition", "alias_declaration", "template_declaration"];
  fileTypes = ["translation_unit"];
  varTypes = ["declaration", "field_declaration", "enumerator"];

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
    super();
  }

  getParentNodeForKeywordSearch(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, [
      ...this.funcTypes,
      ...this.typeTypes,
      ...this.fileTypes,
    ]);
  }

  getFuncParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.funcTypes);
  }

  getTypeParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.typeTypes);
  }

  getTypeParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.typeTypes);
  }

  getVarParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.varTypes);
  }

  getVarParentNodeForNode(targetNode: SyntaxNode): SyntaxNode {
    return this.getParentNodeWithTypeListForNode(targetNode, this.varTypes);
  }

  getMethodInRange(rootNode: SyntaxNode, range: Range, startIndex: number, endIndex: number, completelyIncluded: boolean): SyntaxNode[] {
    return this.getElementInRangeWithTypeList(rootNode, range, startIndex, endIndex, completelyIncluded, this.funcTypes);
  }

  getImportFileOrModule(node: SyntaxNode): string[] {
    const result: string[] = [];

    const queryStr = `
(preproc_include
  [
    path: (system_lib_string) @import
    path: (string_literal) @import
  ]
)
`;

    const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(this.languageId, queryStr, node);

    for (const match of matchResult) {
      const importInfoNode = match.matches.get("import");

      if (importInfoNode.length === 1) {
        result.push(importInfoNode[0].text);
      }
    }

    return result;
  }

  getValidAliasInfoInFile(node: SyntaxNode): string[] {
    const result: string[] = [];

    let curNode = node;
    while (curNode) {
      if (curNode.parent?.childForFieldName("body")?.id === curNode.id || curNode.type === "translation_unit") {
        for (const child of curNode.namedChildren) {
          if (child.type === "using_declaration") {
            const queryString = `
(using_declaration
  [
    (identifier) @alias_name
    (qualified_identifier) @alias_name
  ]
)
`;
            const matchResult = TreeSitterParser.INSTANCE.queryAndHandleMatchForNode(LanguageIdEnum.Cpp, queryString, child);

            for (const match of matchResult) {
              const aliasNode = match.matches.get("alias_name");

              if (aliasNode.length === 1) {
                result.push(aliasNode[0].text);
              }
            }
          }
        }
      }

      curNode = curNode.parent;
    }

    return result;
  }

  genTypeInfoFromDefNode(nodeUri: DocumentUri, node: SyntaxNode, typeName?: string): DefNodeInfo {
    let typeKind: DefNodeKindEnum = DefNodeKindEnum.Unknown;
    switch (node.type) {
      case "class_specifier":
        typeKind = DefNodeKindEnum.Class;
        break;
      case "struct_specifier":
        typeKind = DefNodeKindEnum.Struct;
        break;
      case "enum_specifier":
        typeKind = DefNodeKindEnum.Enum;
        break;
      case "namespace_definition":
        typeKind = DefNodeKindEnum.Namespace;
        break;
      case "union_specifier":
        typeKind = DefNodeKindEnum.Union;
        break;
      case "alias_declaration":
        typeKind = DefNodeKindEnum.Alias;
        break;
      case "template_declaration":
        typeKind = DefNodeKindEnum.Template;
        break;
    }

    let name = typeName;
    if (!name) {
      name = node.childForFieldName("name")?.text ?? "";
    }

    return {
      languageId: this.languageId,
      uri: nodeUri,
      kind: typeKind,
      name,
      defNode: node,
      subDefNodeInfo: null,
    };
  }

  getImportInfo(rootNode: SyntaxNode): any {
    // TODO
    return null;
  }
}