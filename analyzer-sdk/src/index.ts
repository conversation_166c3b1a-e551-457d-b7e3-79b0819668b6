import {initA<PERSON>yzer as ia, initTreeSitter<PERSON>arser as itsp} from "./api/worker/common/init.js";
import type {InitOptions as io} from "./core/common/init";
import type {LSPProxy as lspp} from "./core/parse/lsp/parser";
import type {PSIProxy as psip} from "./core/parse/psi/parser";
import type {Logger as lg} from "./utils/logger";
import type {EventData as ed} from "./utils/event";
import {setConfigById as scbi, openFeature as of, closeFeature as cf} from "./api/worker/common/config";
import {fileActiveListener as fal, fileEditListener as fel, editorSelectionListener as esl} from "./api/worker/common/listener";
import type {FileEditInfo as fei, TextDocumentContentChange as tdcc} from "./utils/common";
import {completionSyntaxCheck as csc, completionSyntaxFix as csf, extractCompletionContext as ecc} from "./api/worker/feat/completion";
import type {SyntaxCheckResult as scr} from "./core/feat/completion/post/syntaxCheck";
import type {SyntaxFixResult as sfr} from "./core/feat/completion/post/syntaxFix";
import type {CompletionContextResult as ccr, ContextItem as ci} from "./core/feat/completion/context/share";
import {ParserTimeout as pt} from "./core/parse/treesitter/parser";
import {parse as tsfp, queryAndMatchForText as tsqamft, queryAndMatchForNode as tsqamfn, queryAndCaptureForText as tsfqacft, queryAndCaptureForNode as tsfqacfn} from "./api/direct/treesitter";
import type {Point as tsp, Range as tsr, SyntaxNode as tssn, Tree as tst, Query as tsq, QueryCapture as tsqc, Input as tsi, QueryMatch as tsqm} from "web-tree-sitter";
import {getCodeStructNodeForKeywords as gcsnfk, getMethodInRange as gmir, getSymbolPositionInRange as gspir, getDefinitionNodeRange as gdnr} from "./api/direct/general";
import {getDefinitionsForRange as gdfr} from "./api/worker/feat/definition";
import {extractUnitTestContext as eutc} from "./api/worker/feat/utgen";

export {FeatureName as AnalyzerFeatureName} from "./core/common/config";

export namespace CkgAnalyzer {
  export const initAnalyzer = ia;
  export const initTreeSitterParser = itsp;
  export type InitOptions = io;
  export type Logger = lg;
  export type EventData = ed;
  export type LSPProxy = lspp;
  export type PSIProxy = psip;

  export const setConfigById = scbi;
  export const openFeature = of;
  export const closeFeature = cf;

  export const fileActiveListener = fal;
  export const fileEditListener = fel;
  export const editorSelectionListener = esl;
  export type FileEditInfo = fei;
  export type TextDocumentContentChange = tdcc;

  export const completionSyntaxCheck = csc;
  export type SyntaxCheckResult = scr;

  export const completionSyntaxFix = csf;
  export type SyntaxFixResult = sfr;

  export const extractCompletionContext = ecc;
  export type CompletionContextResult = ccr;
  export type ContextItem = ci;

  export const ParserTimeout = pt;
  export const parseTree = tsfp;
  export const queryAndMatchForText = tsqamft;
  export const queryAndMatchForNode = tsqamfn;
  export const queryAndCaptureForText = tsfqacft;
  export const queryAndCaptureForNode = tsfqacfn;

  export const getCodeStructNodeForKeywords = gcsnfk;
  export const getMethodInRange = gmir;
  export const getSymbolPositionInRange = gspir;
  export const getDefinitionNodeRange = gdnr;

  export const getDefinitionsForRange = gdfr;

  export const extractUnitTestContext = eutc;

  export type Point = tsp;
  export type Range = tsr;
  export type SyntaxNode = tssn;
  export type Tree = tst;
  export type Query = tsq;
  export type QueryCapture = tsqc;
  export type QueryMatch = tsqm;
  export type Input = tsi;
}