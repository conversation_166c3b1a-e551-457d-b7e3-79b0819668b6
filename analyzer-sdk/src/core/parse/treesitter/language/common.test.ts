import {beforeAll, describe, expect, it} from "vitest";
import {TreeSitterParser} from "../parser";
import {LanguageIdEnum} from "../../share";
import {JavascriptTreeSitterHandler} from "./javascript";

describe("test_getOutermostParentNodeWithTypeListForRange", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_JS", async () => {
    const docText = `
module.exports = async function f1() {
  const f2 = () => {
    const f3 = function() {
      const i = 1;
    }
  }
}
`;

    const tree = TreeSitterParser.INSTANCE.parse(LanguageIdEnum.Javascript, docText);

    const result = JavascriptTreeSitterHandler.JAVASCRIPT_INSTANCE.getOutermostFuncParentNodeForRange(tree.rootNode, {
      start: {
        line: 4,
        character: 10,
      }, end: {
        line: 4,
        character: 11,
      }
    });

    const expectedResult = `
async function f1() {
  const f2 = () => {
    const f3 = function() {
      const i = 1;
    }
  }
}
`;

    expect(result.text).toEqual(expectedResult.trim());
  });
});