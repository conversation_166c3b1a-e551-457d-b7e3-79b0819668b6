export enum TreeSitterFunctionalityEnum {
  GetInvokeInFile = "getInvokeInFile",
  GetTypeInFile = "getTypeInFile",
  GetDeclVariableInFile = "getDeclVariableInFile",
  GetUsageVariableInFile = "getUsageVariableInFile",
  GetVarTypeNameNode = "getVarTypeNameNode",
  GetParentNodeWithTypeListForRange = "getParentNodeWithTypeListForRange",
  GetParentNodeWithTypeListForNode = "getParentNodeWithTypeListForNode",
  GetParentNodeForKeywordSearch = "getParentNodeForKeywordSearch",
  GetFuncParentNodeForRange = "getFuncParentNodeForRange",
  GetFuncParentNodeForNode = "getFuncParentNodeForNode",
  GetOutermostFuncParentNodeForRange = "getOutermostFuncParentNodeForRange",
  GetTypeParentNodeForRange = "getTypeParentNodeForRange",
  GetTypeParentNodeForNode = "getTypeParentNodeForNode",
  GetVarParentNodeForRange = "getVarParentNodeForRange",
  GetVarParentNodeForNode = "getVarParentNodeForNode",
  GetImportFileOrModule = "getImportFileOrModule",
  GetValidAliasInfoInFile = "getValidAliasInfoInFile",
  GetFuncSignatureString = "getFuncSignatureString",
  GetReturnTypeNodes = "getReturnTypeNodes",
  GenTypeInfoFromDefNode = "genTypeInfoFromDefNode",
  GetSubTypeNodes = "getSubTypeNodes",
  GenResolvedTypeInfoFromDefNode = "genResolvedTypeInfoFromDefNode",
  GetTypeFullString = "getTypeFullString",
  GetVarNameNodeFromVarDeclNode = "getVarNameNodeFromVarDeclNode",
  GetMethodInRange = "getMethodInRange",
  GetIdentifierInRange = "getIdentifierInRange",
  GetTreeSitterNodeTypes = "getTreeSitterNodeTypes",
  GetNotProcessTypeNode = "getNotProcessTypeNode",
  GetImportInfo = "getImportInfo",
}