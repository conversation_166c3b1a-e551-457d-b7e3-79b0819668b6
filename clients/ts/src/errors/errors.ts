import { Code } from "../protocol/codekg_pb";


export class NetworkError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "NetworkError";
  }
}

// ckg binary error
export class UnsupportedOSArchError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "UnsupportedOSArchError";
  }
}

export class UserEnvUnsupportedCKG extends Error {
  constructor(message: string) {
    super(message);
    this.name = "UserEnvUnsupportedCKG";
  }
}

export class DownloadRetryMaxError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "DownloadRetryMaxError";
  }
}

export class GlibcVersionError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "GlibcVersionError";
  }
}

export class BinaryExecCheckError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "BinaryExecCheckError";
  }
}

export class FileLimitExceedError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "FileLimitExceedError";
  }
}

export class InvalidTokenError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "InvalidTokenError";
  }
}

export class UserIdIsMissingError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "UserIdIsMissingError";
  }
}

export class GoPanicError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "GoPanicError";
  }
}

export class UnknownError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "UnknownError";
  }
}

export class KilledError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "KilledError";
  }
}

// API Error
export class CKGAPIError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "CKGAPIError";
  }
}

export class AccessBeforeInitError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AccessBeforeInitError";
  }
}

export class AccessDeadProcessError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AccessDeadProcessError";
  }
}

export class ProjectNotIndexedError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "ProjectNotIndexedError";
  }
}

export class LockReleaseError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "LockReleaseError";
  }
}


export enum APIErrorCode{
  // 非独立进程错误
  ProjectNotIndexedError = -1,
  AccessDeadProcessError = -2,
  AccessBeforeInitError = -3,
  
  // 独立进程错误
  GoPanicError = 100,
  UserIdIsMissingError = 101,
  FileLimitExceedError = 102,
  InvalidTokenError = 103,
  UnknownError = 104,

  Success = 0,
}

export enum CKGStartErrorCode{
  UnknownError = 1001,
  // user env error
  UserEnvUnsupportedCKG = 1002,

  // download error
  NetworkError = 2001,
  DownloadRetryMaxError = 2002,
  LockReleaseError = 2004,
  TimeoutError = 2005, // connect ETIMEDOUT 10.8.7.221:443
  DisconnectBeforeTLSError = 2006, // Client network socket disconnected before secure TLS connection was established
  DNSError = 2007, // getaddrinfo ENOTFOUND lf-cdn.marscode.cn
  NoPermissionError = 2008, // EPERM: operation not permitted

  // binary check error
  GlibcVersionError = 3001,
  BinaryExecCheckError = 3002,
  UnsupportedOSArchError = 3003,

  // start error
  CKGBinaryStartError = 4001,
  CKGKilledError = 4002,

  // terminated
  CKGTerminated = 5001,

  // 成功不需要上报error code
}

export const errorMap = new Map([
  [Code.FILE_LIMIT_EXCEED.valueOf(), { apiCode: APIErrorCode.FileLimitExceedError, errorClass: FileLimitExceedError, message: "File limit exceed" }],
  [Code.INVALID_TOKEN.valueOf(), { apiCode: APIErrorCode.InvalidTokenError, errorClass: InvalidTokenError, message: "Invalid token" }],
  [Code.PANIC.valueOf(), { apiCode: APIErrorCode.GoPanicError, errorClass: GoPanicError, message: "Go panic" }],
  [Code.USER_ID_IS_MISSING.valueOf(), { apiCode: APIErrorCode.UserIdIsMissingError, errorClass: UserIdIsMissingError, message: "Network error" }],
]);
