syntax = "proto3";

option go_package = "./protocol";

package protocol;

message Empty {}

message Error {
  string message = 1;
  string stack = 2;
}

enum Code {
  succeed = 0;
  unknown_error = 1;
  panic = 100;
  user_id_is_missing = 101;
  file_limit_exceed = 102;
  invalid_token = 103;
  nil_client = 104;
}

message RefreshTokenRequest {
  string token = 1;
  string user_id = 10;
}

message RefreshTokenResponse {
  Code code = 100;
  Error error = 101;
}

message Project {
  string project_id = 1;
  string storage_path = 2;
  string ignore_file = 3;
}

message InitRequest {
  repeated string project_ids = 1; // deprecated
  bool ignore_file_limit = 2; // deprecated
  repeated Project projects = 3;
  string user_id = 10;
}

message InitResponse {
  Code code = 100;
  Error error = 101;
}

message VirtualProject {
  string project_id = 1;
  string uri = 2;
  repeated string relative_globs_to_load = 3;
}

message InitVirtualProjectsRequest {
  repeated VirtualProject projects = 1;
  bool load_files_from_fs = 2;
  string user_id = 10;
}

message InitVirtualProjectsResponse {
  Code code = 100;
  Error error = 101;
}

message Document {
  string uri = 1;
  string name = 2;
  string content = 3;
  string project_id = 4;
}

message DocumentRequest {
  repeated string file_paths = 1;
  repeated Document documents = 2;
  string userID = 10;
}

message DocumentResponse {
  map<string, string> err_msgs = 1;

  Code code = 100;
  Error error = 101;
}

message DeleteIndexRequest {
  string project_id = 1;
}

message DeleteIndexResponse {
  bool succeed = 1;
  Code code = 100;
  Error error = 101;
}

message CurrentEditorInfo {
  string file_path = 1;
  string project_id = 2;
  int32 cursor_line = 10;
  Range select_code_range = 20;
  string select_code_content = 21;
  Range visible_code_range = 30;
  string visible_code_content = 31;
  string user_file_content = 40;
}

enum EntityType {
  folder = 0;
  file = 1;
  class = 2;
  method = 3;
  text = 4;
}

message RetrieveEntityRequest {
  repeated string project_ids = 1;
  string user_message = 2;
  CurrentEditorInfo editor_info = 3;
  repeated EntityType expect_entity_types = 4;
  int32 entity_num = 5;
  string intent = 6;
  repeated string folder_paths = 7;
  string user_id = 10;
  string session_id = 20;
}

message Entity {
  string project_id = 1;
  string entity_id = 2;
}

message RetrieveEntityResponse {
  repeated Entity entities_by_user_message = 1;
  repeated string query_embedding_models = 2;
  repeated string vector_embedding_models = 3;
  repeated string entity_chunking_methods = 4;
  string empty_recall_reason = 10;
  Code code = 100;
  Error error = 101;
}

message RetrieveRelationRequest {
  repeated Entity entities_by_user_message = 1;
  CurrentEditorInfo editor_info = 2;
  string user_id = 10;
}

message CodeChunkVariable {
  string file_path = 1 [json_name="file_path"];
  int32 start_line = 2 [json_name="start_line"];
  int32 end_line = 3 [json_name="end_line"];
  string content = 4 [json_name="content"];
  string provider = 30 [json_name="provider"];
}

message TextVariable {
  string file_path = 1 [json_name="file_path"];
  int32 start_line = 2 [json_name="start_line"];
  int32 end_line = 3 [json_name="end_line"];
  string content = 4 [json_name="content"];
  string provider = 30 [json_name="provider"];
}

message UsefulFileInfo {
  string file_path = 1 [json_name="file_path"];
  string content = 2 [json_name="content"];
}

message FolderVariable {
  string file_path = 1 [json_name="file_path"];
  string folder_tree = 2 [json_name="folder_tree"];
  repeated UsefulFileInfo useful_files = 3 [json_name="useful_files"];
  string provider = 30 [json_name="provider"];
}

message FileVariable {
  string file_path = 1 [json_name="file_path"];
  string content = 2 [json_name="content"];
  string comment = 3 [json_name="comment"];
  string provider = 30 [json_name="provider"];
}

message FileTopLevelVariable {
  string file_path = 1 [json_name="file_path"];
  int32 start_line = 2 [json_name="start_line"];
  int32 end_line = 3 [json_name="end_line"];
  string content = 4 [json_name="content"];
  string provider = 30 [json_name="provider"];
}

message Member {
  string content = 1 [json_name="content"];
}

message ClassVariable {
  string file_path = 1 [json_name="file_path"];
  int32 start_line = 2 [json_name="start_line"];
  int32 end_line = 3 [json_name="end_line"];
  string name = 4 [json_name="name"];
  string comment = 5 [json_name="comment"];
  string content = 6 [json_name="content"];
  repeated Member members = 7 [json_name="member"];
  repeated MethodInfo methods = 8 [json_name="methods"];
  repeated MethodInfo test_functions = 9 [json_name="test_functions"];
  string provider = 30 [json_name="provider"];
}

message ClassTopLevelVariable {
  string file_path = 1 [json_name="file_path"];
  int32 start_line = 2 [json_name="start_line"];
  int32 end_line = 3 [json_name="end_line"];
  string content = 4 [json_name="content"];
  string provider = 30 [json_name="provider"];
}

message MethodInfo {
  string file_path = 1 [json_name="file_path"];
  int32 start_line = 2 [json_name="start_line"];
  int32 end_line = 3 [json_name="end_line"];
  string name = 4 [json_name="name"];
  string signature = 5 [json_name="signature"];
  string comment = 6 [json_name="comment"];
  string content = 7 [json_name="content"];
}

message MethodVariable {
  string file_path = 1 [json_name="file_path"];
  int32 start_line = 2 [json_name="start_line"];
  int32 end_line = 3 [json_name="end_line"];
  string name = 4 [json_name="name"];
  string signature = 5 [json_name="signature"];
  string comment = 6 [json_name="comment"];
  string content = 7 [json_name="content"];
  repeated MethodInfo callers = 8 [json_name="callers"];
  repeated MethodInfo callees = 9 [json_name="callees"];
  repeated MethodInfo test_functions = 10 [json_name="test_functions"];
  string provider = 30 [json_name="provider"];
}

message Variable {
  CodeChunkVariable code_chunk = 1 [json_name="code_chunk"];
  TextVariable text = 2 [json_name="text"];
  FolderVariable folder = 3 [json_name="folder"];
  FileVariable file = 4 [json_name="file"];
  FileTopLevelVariable file_top_level = 5 [json_name="file_top_level"];
  ClassVariable class = 6 [json_name="class"];
  ClassTopLevelVariable class_top_level= 7 [json_name="class_top_level"];
  MethodVariable method = 8 [json_name="method"];
}

message SelectedMethodInfo {
  int32 start_line = 1 [json_name="start_line"];
  int32 end_line = 2 [json_name="end_line"];
  string name = 3 [json_name="name"];
  string comment = 4 [json_name="comment"];
  string content = 5 [json_name="content"];
}

message SelectedClassInfo {
  int32 start_line = 1 [json_name="start_line"];
  int32 end_line = 2 [json_name="end_line"];
  string name = 3 [json_name="name"];
  string comment = 4 [json_name="comment"];
  string content = 5 [json_name="content"];
  repeated SelectedMethodInfo selected_methods = 6 [json_name="selected_methods"];
}

message CurrentEditorEntity {
  repeated SelectedClassInfo selected_classes = 1 [json_name="selected_classes"];
  repeated SelectedMethodInfo selected_methods = 2 [json_name="selected_methods"];
}

message Range {
  int32 start_line = 1 [json_name="start_line"];
  int32 end_line = 2 [json_name="end_line"];
}

message RefClassInfo {
  string file_path = 1 [json_name="file_path"];
  string start_line = 2 [json_name="start_line"];
  string end_line = 3 [json_name="end_line"];
  string name = 4 [json_name="name"];
  string content = 5 [json_name="content"];
}

message RefTypeInfo {
  string file_path = 1 [json_name="file_path"];
  string start_line = 2 [json_name="start_line"];
  string end_line = 3 [json_name="end_line"];
  string name = 4 [json_name="name"];
  string content = 5 [json_name="content"];
}

message CurrentEditorVariable {
  string file_path = 1 [json_name="file_path"];
  string content = 2 [json_name="content"];
  int32 cursor_line = 3 [json_name="cursor_line"];
  Range select_range = 4 [json_name="select_range"];
  Range visible_range = 5 [json_name="visible_range"];
  CurrentEditorEntity entity = 6 [json_name="entity"];
  repeated MethodInfo callees = 7 [json_name="callees"];
  repeated RefClassInfo ref_classes = 8 [json_name="ref_classes"];
  repeated RefTypeInfo ref_types = 9 [json_name="ref_types"];
}

message Reference {
  string file_path = 1;
  int32 start_line = 2;
  int32 end_line = 3;
  string uri = 4;
  string name = 5;
}

message RetrieveRelationResponse {
  CurrentEditorVariable current_editor = 1 [json_name="current_editor"];
  repeated Variable variables = 2 [json_name="user_message_entities"];
  repeated Reference references = 10;
  string json_result = 50;
  string err_metric = 51;
  Code code = 100;
  Error error = 101;
}

message GetBuildStatusRequest {
  repeated string project_ids = 1;
}

enum BuildStatus {
  building = 0;
  finished = 1;
  failed = 2;
}

message DocumentBuildStatus {
  BuildStatus status = 1;
  string uri = 2;
  string name = 3;
}

message ProjectBuildStatus {
  BuildStatus status = 1;
  float progress = 2;
  int64 success_index_files = 3;
  int64 failed_index_files = 4;
  int64 total_index_files = 5;
  bool empty_project = 6;
  repeated DocumentBuildStatus documents_status = 7;
}

message GetBuildStatusResponse {
  map<string, ProjectBuildStatus> status = 1;
  Code code = 100;
  Error error = 101;
}

message GetDocumentsIndexStatusRequest {
  repeated string project_ids = 1;
}

message ProjectDocumentsIndexStatus {
  BuildStatus status = 1;
  repeated DocumentIndexStatus documents_status = 7;
}

message DocumentIndexStatus {
  BuildStatus status = 1;
  string uri = 2;
  string name = 3;
}

message GetDocumentsIndexStatusResponse {
  map<string, ProjectDocumentsIndexStatus> projects_status = 1;
  Code code = 100;
  Error error = 101;
}

message IsVersionMatchedRequest {
  string version = 1;
}

message IsVersionMatchedResponse {
  bool matched = 1;
  Code code = 100;
  Error error = 101;
}

message CancelIndexRequest {
  string project_id = 1;
}

message CancelIndexResponse {
  bool succeed = 1;
  Code code = 100;
  Error error = 101;
}

message ImportAnalysisRequest {
  string project_id = 1;
  string file = 5;
  string import_statement = 6;
}

message FilesImportAnalysisRequest {
  string project_id = 1;
  repeated string rela_files = 2;
}

message ImportAnalysisResponse {
  Code code = 100;
  Error error = 101;

  ImportAnalysisResult result = 3;
}

message ImportAnalysisResult {
  string message = 1;
  string data = 2;
}

message Symbol {
  string name = 1;
  string file_path = 2;
  int32 start_line = 3;
  int32 end_line = 4;
}

message SearchCKGDBRequest {
  string project_id = 1;
  string symbol_identifier = 2;
  string symbol_regex = 3;
  repeated EntityType types = 4;
  int32 entity_num = 5;
}

message SearchCKGDBResponse {
  repeated Variable variables = 2;
  repeated Reference references = 10;
  string json_result = 50;
  Code code = 100;
  Error error = 101;
}

message CodeSnippet {
  string file_path = 1;
  int32 start_line = 2;
  int32 end_line = 3;
  string content = 4;
}

message RetrieveRelevantSnippetRequest {
  string project_id = 1;
  string user_query = 2;

  // 下面是用户选中的 context
  bool workspace = 10;
  repeated string selected_folders = 11;
  repeated string selected_files = 12;
  repeated CodeSnippet selected_codes = 13;
  CurrentEditorInfo current_editor = 14;

  // 版本号
  string version = 30;

  string user_id = 100;
  string session_id = 101;
}

enum RecallType {
  recall_type_user_specified = 0;
  recall_type_embedding = 1;
  recall_type_ner = 2;
  recall_type_relation_by_user_action_trace = 20;
  recall_type_relation_by_git_relevance = 30;
}

enum SnippetType {
  snippet_type_code = 0;
  snippet_type_folder_tree = 1;
  snippet_type_file = 2;
}

message Snippet {
  string project_id = 1;
  SnippetType type = 2;
  string file_path = 3;
  int32 start_line = 4;
  int32 end_line = 5;
  string content = 6;
  RecallType recall_type = 101;
  string ckg_entity_id = 102;
}

message RetrieveRelevantSnippetResponse {
  repeated Snippet snippets_by_workspace = 1;
  repeated Snippet snippets_by_folder = 2;
  repeated Snippet snippets_by_file = 3;
  repeated Snippet snippets_by_code = 4;
  repeated Snippet snippets_by_current_file = 5;
  repeated Snippet snippets_by_interaction = 6;
  Code code = 100;
  Error error = 101;
}

message RerankSnippetRequest {
  repeated Snippet snippets_by_workspace = 1;
  repeated Snippet snippets_by_folder = 2;
  repeated Snippet snippets_by_file = 3;
  repeated Snippet snippets_by_code = 4;
  repeated Snippet snippets_by_current_file = 5;
  string token = 100;
  string session_id = 101;
}

message SnippetWithScore {
  Snippet snippet = 1;
  float score = 2;
}

message RerankSnippetResponse {
  repeated SnippetWithScore snippets = 1;
  Code code = 100;
  Error error = 101;
}

message CursorMoveRequest {
  string project_id = 1;
  string file = 2;
  int32 line = 3;

  string user_id =  9;
  string version = 10;
}

message IsCKGEnabledForNonWorkspaceScenarioRequest {
  string user_id = 1;
}

message IsCKGEnabledForNonWorkspaceScenarioResponse {
  bool is_enabled = 1;
  string version = 2;

  Code code = 100;
  Error error = 101;
}

message SetUpRequest {
  string host = 1;
  string region = 2;
  string source_product = 3;
  string device_cpu = 4;
  string device_id = 5;
  string machine_id = 6;
  string device_brand = 7;
  string device_type = 8;
  string os_version = 9;
}

message SetUpResponse {
  Code code = 100;
  Error error = 101;
}

// CodeKG service definition.
service CodeKG {
  rpc Ping(Empty) returns (Empty) {}
  rpc SetUp(SetUpRequest) returns (SetUpResponse) {}
  rpc Init(InitRequest) returns (InitResponse) {}
  rpc InitVirtualProjects(InitVirtualProjectsRequest) returns (InitVirtualProjectsResponse) {}
  rpc DocumentCreate(DocumentRequest) returns (DocumentResponse) {}
  rpc DocumentChange(DocumentRequest) returns (DocumentResponse) {}
  rpc DocumentDelete(DocumentRequest) returns (DocumentResponse) {}
  rpc DocumentSelect(DocumentRequest) returns (DocumentResponse) {}
  rpc CursorMove(CursorMoveRequest) returns (Empty) {}
  rpc GetBuildStatus(GetBuildStatusRequest) returns (GetBuildStatusResponse) {}
  rpc GetDocumentsIndexStatus(GetDocumentsIndexStatusRequest) returns (GetDocumentsIndexStatusResponse) {}
  rpc CancelIndex(CancelIndexRequest) returns (CancelIndexResponse) {}
  rpc DeleteIndex(DeleteIndexRequest) returns (DeleteIndexResponse) {}
  rpc RetrieveRelation(RetrieveRelationRequest) returns (RetrieveRelationResponse) {}
  rpc RetrieveEntity(RetrieveEntityRequest) returns (RetrieveEntityResponse) {}
  rpc RetrieveRelevantSnippet(RetrieveRelevantSnippetRequest) returns (RetrieveRelevantSnippetResponse) {}
  rpc RerankSnippet(RerankSnippetRequest) returns (RerankSnippetResponse) {}
  rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse) {}
  rpc IsVersionMatched(IsVersionMatchedRequest) returns (IsVersionMatchedResponse) {}
  rpc ImportAnalysis (ImportAnalysisRequest) returns (ImportAnalysisResponse) {}
  rpc FilesImportAnalysis (FilesImportAnalysisRequest) returns (ImportAnalysisResponse) {}
  rpc SearchCKGDB(SearchCKGDBRequest) returns (SearchCKGDBResponse) {}
  rpc IsCKGEnabledForNonWorkspaceScenario(IsCKGEnabledForNonWorkspaceScenarioRequest) returns (IsCKGEnabledForNonWorkspaceScenarioResponse) {}
}
