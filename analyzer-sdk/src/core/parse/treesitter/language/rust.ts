import {SyntaxNode} from "web-tree-sitter";
import {Range} from "vscode-languageserver-textdocument";
import {<PERSON><PERSON>itter<PERSON>andler} from "./common";
import {LanguageIdEnum} from "../../share";

export class RustTreeSitterHandler extends TreeSitterHandler {
  static INSTANCE = new RustTreeSitterHandler();

  languageId = LanguageIdEnum.Rust;

  funcTypes = [
    "function_item",
    "function_signature_item",
    "closure_expression",
    "macro_definition",
  ];
  typeTypes = [
    "struct_item",
    "enum_item",
    "trait_item",
    "impl_item",
    "type_item",
    "union_item",
    "mod_item",
  ];
  fileTypes = ["source_file"];
  varTypes = [];

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
    super();
  }

  getParentNodeForKeywordSearch(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, [
      ...this.funcTypes,
      ...this.typeTypes,
      ...this.fileTypes,
    ]);
  }

  getFuncParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.funcTypes);
  }

  getTypeParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.typeTypes);
  }

  getMethodInRange(rootNode: SyntaxNode, range: Range, startIndex: number, endIndex: number, completelyIncluded: boolean): SyntaxNode[] {
    return this.getElementInRangeWithTypeList(rootNode, range, startIndex, endIndex, completelyIncluded, this.funcTypes);
  }

  getImportInfo(rootNode: SyntaxNode): any {
    // TODO
    return null;
  }
}