package ignore_rule_checker

import (
	"context"
	"ide/ckg/codekg/components/logs"
	"ide/ckg/codekg/components/validator"
	"ide/ckg/codekg/util"
	"os"
)

const (
	FileContentRule     = "FileContentRuleChecker"
	FileContentRuleRank = 500
)

type FileContentRuleChecker struct {
	whitelist IncludeRuleChecker
	is        IgnoreServiceV2
}

func NewFileContentRuleChecker(whitelist IncludeRuleChecker) *FileContentRuleChecker {
	return &FileContentRuleChecker{
		whitelist: whitelist,
	}
}

func (r *FileContentRuleChecker) Name() CheckerName                   { return FileContentRule }
func (r *FileContentRuleChecker) Rank() int                           { return FileContentRuleRank }
func (r *FileContentRuleChecker) Kind() CheckerKind                   { return File }
func (r *FileContentRuleChecker) SetIgnoreService(is IgnoreServiceV2) { r.is = is }
func (r *FileContentRuleChecker) AddRules(_ context.Context, _, _ string, _ []IgnoreRule) error {
	return nil
}
func (r *FileContentRuleChecker) UpdateRulesFromTCC(_ context.Context, _, _ string) error { return nil }

// IsIgnore for FileMetadataRuleChecker
func (r *FileContentRuleChecker) IsIgnore(ctx context.Context, _, _ string, path string, info os.FileInfo) (ignored bool, skip error, err error) {
	// 如果传入文件夹，该 checker 不负责判断，直接返回 false
	if info.IsDir() {
		return false, nil, nil
	}
	file, err := os.Open(path)
	if err != nil {
		// 如果打开文件失败，默认过滤该文件
		logs.CtxWarn(ctx, "[FileContentRuleChecker] open file %s failed, err: %+v", path, err)
		return true, nil, err
	}
	var include bool
	if include, err = r.whitelist.IsInclude(ctx, path, info); err != nil {
		// 检查命中白名单时 err
		logs.CtxWarn(ctx, "[FileContentRuleChecker] check IsInclude file %s failed, err: %+v", path, err)
		return true, nil, err
	} else if !include {
		// 无 err，但是未命中白名单：检查 “不可见字符”
		// 但是不检查 utf-8 编码，因为截断字符可能导致 “原本合法的内容” 反而不合法
		var content string
		content, err = util.ReadFirstCharFromFile(file, 1024)
		if err != nil {
			logs.CtxWarn(ctx, "[FileContentRuleChecker] read file %s failed, err: %+v", path, err)
			return true, nil, err
		}
		if validator.FileContainsNonPrintableChar(ctx, path, content) {
			logs.CtxWarn(ctx, "[FileContentRuleChecker] file %s contains non printable characters", path)
			return true, nil, nil
		}
	}
	return false, nil, nil
}
