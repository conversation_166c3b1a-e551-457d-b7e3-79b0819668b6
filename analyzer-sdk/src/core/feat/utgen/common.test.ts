import {describe, expect, it} from "vitest";
import {LanguageIdEnum} from "../../parse/share";
import {adjustIndex} from "./common";
import {createDocument} from "../../../utils/common";

describe("test_adjustIndex", () => {
  it("case1_normal", async () => {
    const docText = `
module.exports = async function f1() {
  const f2 = () => {
    const f3 = function() {
      const i = 1;
    }
  }
}
`;

    const document = createDocument(docText, LanguageIdEnum.Javascript, "file:///test.js");

    const result = adjustIndex(document, 0, document.getText().length);

    expect(result).toEqual({startIndex: 1, endIndex: document.getText().length - 1});
  });
});