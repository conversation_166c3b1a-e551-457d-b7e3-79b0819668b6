import {describe, expect, it, beforeAll} from "vitest";
import {getActiveFileCachedTreeAndDocument, completeUpdateCachedTree} from './astCache';
import {treeNodeToString} from "../../utils/tree";
import {TreeSitterParser} from "../parse/treesitter/parser";
import {createDocument, wait} from "../../utils/common";
import {LRUCache} from "lru-cache";

describe("test_LRUCache", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", () => {
    const testCache = new LRUCache<string, string>({
      max: 2,
      ttl: 10,
      ttlAutopurge: true,
      updateAgeOnGet: true,
    });

    testCache.set("1", "1");
    testCache.set("2", "2");
    testCache.set("3", "3");

    expect(testCache.get("1")).toBeUndefined();
    expect(testCache.get("2")).toEqual("2");
    expect(testCache.get("3")).toEqual("3");
  });

  it("case2_get", () => {
    const testCache = new LRUCache<string, string>({
      max: 2,
      ttl: 10,
      ttlAutopurge: true,
      updateAgeOnGet: true,
    });

    testCache.set("1", "1");
    testCache.set("2", "2");
    testCache.get("1");
    testCache.set("3", "3");

    expect(testCache.get("1")).toEqual("1");
    expect(testCache.get("2")).toBeUndefined();
    expect(testCache.get("3")).toEqual("3");
  });

  it("case3_ttl", async () => {
    const testCache = new LRUCache<string, string>({
      max: 2,
      ttl: 10,
      ttlAutopurge: true,
      updateAgeOnGet: true,
    });

    testCache.set("1", "1");
    testCache.set("2", "2");

    await wait(20);

    expect(testCache.get("1")).toBeUndefined();
    expect(testCache.get("2")).toBeUndefined();
  });

  it("case4_update_ttl", async () => {
    const testCache = new LRUCache<string, string>({
      max: 2,
      ttl: 10,
      ttlAutopurge: true,
      updateAgeOnGet: true,
    });

    testCache.set("1", "1");
    testCache.set("2", "2");

    await wait(5);
    testCache.get("1");

    await wait(5);
    testCache.get("1");

    await wait(5);
    testCache.get("1");

    expect(testCache.get("1")).toEqual("1");
    expect(testCache.get("2")).toBeUndefined();
  });
});

describe("test_incrementalUpdateCachedTree", () => {
  beforeAll(async () => {
    await TreeSitterParser.INSTANCE.init("./resources");
  });

  it("case1_normal", async () => {
    const docText = `
func main() {
  fmt.Println();
}
`;

    const textDocument = createDocument(docText, "go", "file:///test.go");

    completeUpdateCachedTree(textDocument);

    const cachedTree = getActiveFileCachedTreeAndDocument(textDocument)?.tree;

    const expectedTreeString = `
0 source_file [1-34]
1   function_declaration [1-33]
2     func [1-5]
2     identifier [6-10] -- main
2     parameter_list [10-12]
3       ( [10-11]
3       ) [11-12]
2     block [13-33]
3       { [13-14]
3       expression_statement [17-30]
4         call_expression [17-30]
5           selector_expression [17-28]
6             identifier [17-20] -- fmt
6             . [20-21]
6             field_identifier [21-28] -- Println
5           argument_list [28-30]
6             ( [28-29]
6             ) [29-30]
3       ; [30-31]
3       } [32-33]
`;

    expect(treeNodeToString(cachedTree.rootNode)).toEqual(expectedTreeString);
  });
});