package data_manager

import (
	"sync/atomic"
)

type InitProjectOption struct {
	ProjectID            string
	UserID               string
	StoragePath          string
	IgnoreFileLimit      bool
	CustomizedIgnoreFile string
	ProjectType          int32
	ProjectURI           string
}

type projectBuildProgress struct {
	totalFiles        int64
	successIndexFiles *atomic.Int64
	failedIndexFiles  *atomic.Int64
	builtFiles        *atomic.Int64
	building          *atomic.Bool
}

type EmbeddingStorageType string

const (
	EmbeddingStorageTypeLocal  EmbeddingStorageType = "local"
	EmbeddingStorageTypeRemote EmbeddingStorageType = "remote"
)

type fileInformation struct {
	hash      string
	uniqueID  string
	lineCount int
	size      int
}

const (
	initErrStepCheckProjectID           = "init_check_project_id"
	initErrStepEnvStorage               = "init_env_storage"
	initErrStepInitIgnoreService        = "init_ignore_service"
	initErrStepCollectFiles             = "collect_files"
	initErrStepCreateStorage            = "create_storage"
	initErrStepCreateABStorage          = "create_ab_storage"
	initErrStepStoreDBPath              = "store_db_path"
	initErrStepStoreABDBPath            = "store_ab_db_path"
	initErrStepClearProjectNotExistData = "clear_project_not_exist_data"
	initErrStepInitKnowledgebaseId      = "init_knowledgebase_id"
	initErrStepInitRemoteKnowledgebase  = "init_remote_knowledgebase"
	initErrStepDownloadCachedData       = "download_cached_data"
	initErrStepGetUnIndexedFile         = "get_unindexed_file"
	initErrStepGetAllUriMeta            = "get_uri_meta"
)
