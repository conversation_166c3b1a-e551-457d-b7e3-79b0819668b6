import {SyntaxNode} from "web-tree-sitter";
import {Range} from "vscode-languageserver-textdocument";
import {<PERSON><PERSON>itter<PERSON>and<PERSON>} from "./common";
import {LanguageIdEnum} from "../../share";

export class CSharpTreeSitterHandler extends TreeSitterHandler {
  static INSTANCE = new CSharpTreeSitterHandler();

  languageId = LanguageIdEnum.CSharp;

  funcTypes = ["constructor_declaration", "destructor_declaration", "method_declaration","operator_declaration","local_function_statement"];
  typeTypes = ["interface_declaration", "enum_declaration"];
  fileTypes = ["compilation_unit"];
  varTypes = [];

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {
    super();
  }

  getParentNodeForKeywordSearch(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, [
      ...this.funcTypes,
      ...this.typeTypes,
      ...this.fileTypes,
    ]);
  }

  getFuncParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.funcTypes);
  }

  getTypeParentNodeForRange(node: SyntaxNode, range: Range): SyntaxNode {
    return this.getParentNodeWithTypeListForRange(node, range, this.typeTypes);
  }

  getMethodInRange(rootNode: SyntaxNode, range: Range, startIndex: number, endIndex: number, completelyIncluded: boolean): SyntaxNode[] {
    return this.getElementInRangeWithTypeList(rootNode, range, startIndex, endIndex, completelyIncluded, this.funcTypes);
  }

  getImportInfo(rootNode: SyntaxNode): any {
    // TODO
    return null;
  }
}