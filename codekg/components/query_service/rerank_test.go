package query_service

import (
	"context"
	pb "ide/ckg/codekg/protocol"
	"ide/ckg/codekg/util"
	"sync"
	"testing"
	"time"

	"google.golang.org/grpc"
)

const (
	address     = "localhost:50051"
	timeoutSecs = 500
	project     = "/Users/<USER>/projects/discover/priomptipy"
	appId       = "b070098d-978d-4274-ae6b-b76da2570112"
	userID      = "xxx"
	testToken   = "Cloud-IDE-JWT xxx"
)

type testClient struct {
	t      *testing.T
	client pb.CodeKGClient
	ctx    context.Context
	cancel context.CancelFunc
}

func newTestClient(t *testing.T) *testClient {
	conn, err := grpc.Dial(address, grpc.WithInsecure())
	if err != nil {
		t.Fatalf("did not connect: %v", err)
	}

	ctx, cancel := context.WithTimeout(util.NewBackgroundContext(""), timeoutSecs*time.Second)

	return &testClient{
		t:      t,
		client: pb.NewCodeKGClient(conn),
		ctx:    ctx,
		cancel: cancel,
	}
}

func (tc *testClient) close() {
	tc.cancel()
}

func (tc *testClient) refreshToken() {
	resp, err := tc.client.RefreshToken(tc.ctx, &pb.RefreshTokenRequest{
		Token:  testToken,
		UserId: userID,
	})
	if err != nil {
		tc.t.Fatalf("RefreshToken failed: %v", err)
	}
	if resp.GetCode() != pb.Code_succeed {
		tc.t.Fatalf("RefreshToken failed: %s\n%s", resp.GetError().Message, resp.GetError().Stack)
	} else {
		// print all res
		tc.t.Logf("RefreshToken result: %v", resp.GetCode())
	}
}

func (tc *testClient) initProject(project string) {
	r, err := tc.client.Init(tc.ctx, &pb.InitRequest{
		ProjectIds: []string{project},
		UserId:     userID,
	})
	if err != nil {
		tc.t.Fatalf("Init failed: %v", err)
	}
	if r.GetCode() == -1 {
		tc.t.Fatalf("Init failed: %s\n%s", r.GetError().Message, r.GetError().Stack)
	}

	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		for {
			r, err := tc.client.GetBuildStatus(tc.ctx, &pb.Empty{})
			if err != nil {
				tc.t.Fatalf("GetBuildStatus failed: %v", err)
			}
			if r.GetCode() == -1 {
				tc.t.Fatalf("GetBuildStatus failed: %s\n%s", r.GetError().Message, r.GetError().Stack)
			}

			tc.t.Logf("build status: %f%%", r.Status[project].Progress*100)
			time.Sleep(10 * time.Millisecond)
			if r.Status[project].Progress == 1 {
				break
			}
		}
	}()
	wg.Wait()
}

func (tc *testClient) testRerank(project string) {
	res, err := tc.client.RetrieveEntity(tc.ctx, &pb.RetrieveEntityRequest{
		ProjectIds:  []string{project},
		UserMessage: "这个仓库是干啥的?",
		EntityNum:   100,
		UserId:      userID,
	})
	if err != nil {
		tc.t.Fatalf("RetrieveEntity failed: %v", err)
	}
	if res.GetCode() != pb.Code_succeed {
		tc.t.Fatalf("RetrieveEntity failed: %s\n%s", res.GetError().Message, res.GetError().Stack)
	}
	tc.t.Logf("RetrieveEntity result length: %d", len(res.EntitiesByUserMessage))

	for i, entity := range res.EntitiesByUserMessage {
		tc.t.Logf("  [%d] ProjectId: %s, EntityId: %s",
			i, entity.GetProjectId(), entity.GetEntityId())
	}

	// 验证连续两次 rerank 结果是否一致
	res2, err := tc.client.RetrieveEntity(tc.ctx, &pb.RetrieveEntityRequest{
		ProjectIds:  []string{project},
		UserMessage: "这个仓库是干啥的?",
		EntityNum:   100,
		UserId:      userID,
	})
	if err != nil {
		tc.t.Fatalf("RetrieveEntity failed: %v", err)
	}
	if res.GetCode() != pb.Code_succeed {
		tc.t.Fatalf("RetrieveEntity failed: %s\n%s", res2.GetError().Message, res2.GetError().Stack)
	}
	tc.t.Logf("RetrieveEntity result length: %d", len(res2.EntitiesByUserMessage))

	for i, entity := range res2.EntitiesByUserMessage {
		tc.t.Logf("  [%d] ProjectId: %s, EntityId: %s",
			i, entity.GetProjectId(), entity.GetEntityId())
	}
	// 比较两次结果是否一致
	if len(res.EntitiesByUserMessage) != len(res2.EntitiesByUserMessage) {
		tc.t.Fatalf("RetrieveEntity result length not equal")
	}
	for i, entity := range res.EntitiesByUserMessage {
		if entity.GetProjectId() != res2.EntitiesByUserMessage[i].GetProjectId() {
			tc.t.Fatalf("ProjectId not equal")
		}
		if entity.GetEntityId() != res2.EntitiesByUserMessage[i].GetEntityId() {
			tc.t.Fatalf("EntityId not equal")
		}
	}
	tc.t.Logf("Got %v len result in RetrieveEntity, 2 times, all results are same.", len(res2.EntitiesByUserMessage))
}

func TestRerankMain(t *testing.T) {
	client := newTestClient(t)
	defer client.close()

	// Test cases
	tests := []struct {
		name    string
		project string
	}{
		{
			name:    "Test Rerank in EntityRetrieval, Project " + project,
			project: project,
		},
		// Add more test cases here
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client.refreshToken()
			client.initProject(tt.project)
			client.testRerank(tt.project)
		})
	}
}
